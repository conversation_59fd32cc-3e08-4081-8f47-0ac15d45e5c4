#!/usr/bin/env python
"""
Test script to simulate the exact user workflow: Create company → View settings.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.forms import CompanyCreationForm, CompanySettingsForm
from accounts.models import Company, CompanyInformation
import datetime

User = get_user_model()

def test_user_workflow():
    """Test the exact user workflow from creation to settings."""
    
    print("🧪 Testing User Workflow: Create Company → View Settings")
    print("=" * 60)
    
    # Create a test user
    test_user, created = User.objects.get_or_create(
        username='workflow_test_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Workflow',
            'last_name': 'Test'
        }
    )
    
    # Simulate user filling out company creation form
    user_input = {
        'name': 'My Awesome Startup',
        'entity_type': 'company',
        'mission': 'To revolutionize the way people work with AI assistants',
        'description': 'We build cutting-edge AI solutions for businesses',
        'website': 'https://myawesomestartup.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '******-AWESOME',
        'timezone': 'America/New_York',
        'language': 'en',
        'industry': 'Artificial Intelligence',
        'size': '1-10',
        'city': 'San Francisco',
        'country': 'United States',
        'founded': 2024,
        'list_in_directory': True,
        'address_line1': '123 Innovation Street',
        'address_line2': 'Suite 456',
        'postal_code': '94105',
        'linkedin': 'https://linkedin.com/company/myawesomestartup',
        'twitter': 'https://twitter.com/myawesomestartup',
        'categories': 'AI, Technology, Startups'
    }
    
    print("👤 Step 1: User creates company through creation wizard...")
    creation_form = CompanyCreationForm(data=user_input, user=test_user)
    
    if creation_form.is_valid():
        company = creation_form.save()
        print(f"   ✅ Company '{company.name}' created successfully (ID: {company.id})")
        
        print("\n🔍 Step 2: User navigates to company settings...")
        company_info = company.info
        
        # Verify data was saved correctly
        print("   📊 Checking saved data:")
        saved_data = {
            'mission': company_info.mission,
            'website': company_info.website,
            'contact_email': company_info.contact_email,
            'industry': company_info.industry,
            'city': company_info.city,
            'country': company_info.country,
            'address_line1': company_info.address_line1,
            'founded': company_info.founded
        }
        
        all_saved_correctly = True
        for field, value in saved_data.items():
            expected = user_input[field]
            if str(value) != str(expected):
                print(f"   ❌ {field}: Expected '{expected}', got '{value}'")
                all_saved_correctly = False
            else:
                print(f"   ✅ {field}: '{value}'")
        
        if all_saved_correctly:
            print("\n   🎉 All user input data saved correctly!")
        else:
            print("\n   ❌ Some data was not saved correctly")
            return
        
        print("\n📝 Step 3: User opens company settings form...")
        settings_form = CompanySettingsForm(
            instance=company_info, 
            current_year=datetime.date.today().year
        )
        
        print("   🔍 Checking if form shows user's data:")
        form_data_correct = True
        key_fields = ['mission', 'website', 'contact_email', 'industry', 'city', 'country']
        
        for field in key_fields:
            form_value = settings_form.initial.get(field, '')
            expected_value = user_input[field]
            if form_value != expected_value:
                print(f"   ❌ {field}: Form shows '{form_value}', expected '{expected_value}'")
                form_data_correct = False
            else:
                print(f"   ✅ {field}: '{form_value}'")
        
        if form_data_correct:
            print("\n   🎉 Settings form correctly displays all user data!")
        else:
            print("\n   ❌ Settings form not displaying user data correctly")
            return
        
        print("\n🖥️  Step 4: Testing form HTML rendering...")
        # Test that the HTML actually contains the user's data
        mission_field = str(settings_form['mission'])
        website_field = str(settings_form['website'])
        industry_field = str(settings_form['industry'])
        
        mission_correct = user_input['mission'] in mission_field
        website_correct = f"value=\"{user_input['website']}\"" in website_field
        industry_correct = f"value=\"{user_input['industry']}\"" in industry_field
        
        print(f"   Mission in HTML: {'✅' if mission_correct else '❌'}")
        print(f"   Website in HTML: {'✅' if website_correct else '❌'}")
        print(f"   Industry in HTML: {'✅' if industry_correct else '❌'}")
        
        if mission_correct and website_correct and industry_correct:
            print("\n   🎉 HTML rendering perfect - user will see their data!")
        else:
            print("\n   ❌ HTML rendering issues - user might see placeholders")
        
        print("\n🧹 Step 5: Cleaning up test data...")
        company.delete()
        print("   ✅ Test company deleted")
        
        # Final success message
        if all_saved_correctly and form_data_correct and mission_correct and website_correct and industry_correct:
            print("\n" + "🎉" * 20)
            print("🎉 COMPLETE SUCCESS! 🎉")
            print("🎉 User workflow works perfectly! 🎉")
            print("🎉 Creation → Settings integration fixed! 🎉")
            print("🎉" * 20)
        else:
            print("\n❌ Some issues found in the workflow")
        
    else:
        print("   ❌ Company creation form is invalid:")
        for field, errors in creation_form.errors.items():
            print(f"     {field}: {errors}")
    
    # Clean up test user
    if created:
        test_user.delete()
        print("   ✅ Test user deleted")

if __name__ == '__main__':
    test_user_workflow()
