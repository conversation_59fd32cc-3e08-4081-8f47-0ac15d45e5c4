from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from accounts.models import Company, CompanyInformation, Membership
from directory.models import CompanyListing
from assistants.models import Assistant
import os
from django.conf import settings


class Command(BaseCommand):
    help = 'Create test featured companies with logos for carousel testing'

    def handle(self, *args, **options):
        # Test company data with existing logo files
        test_companies = [
            {
                'name': 'TechCorp Solutions',
                'slug': 'techcorp-solutions',
                'logo': 'company_logos/blue.png',
                'description': 'Leading technology solutions provider'
            },
            {
                'name': 'Digital Innovations',
                'slug': 'digital-innovations',
                'logo': 'company_logos/Blue_Robot_with_Laptop_and_Document.png',
                'description': 'Cutting-edge digital transformation services'
            },
            {
                'name': 'MTN Communications',
                'slug': 'mtn-communications',
                'logo': 'company_logos/250px-New-mtn-logo.webp',
                'description': 'Global telecommunications leader'
            },
            {
                'name': 'LogoTech Systems',
                'slug': 'logotech-systems',
                'logo': 'company_logos/logo-2.png',
                'description': 'Advanced system integration solutions'
            },
            {
                'name': 'Creative Media',
                'slug': 'creative-media',
                'logo': 'company_logos/download_7.jpeg',
                'description': 'Professional media and design services'
            }
        ]

        # Get or create a test user to own these companies
        test_user, created = User.objects.get_or_create(
            username='testowner',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'Owner'
            }
        )
        if created:
            test_user.set_password('testpass123')
            test_user.save()
            self.stdout.write(self.style.SUCCESS(f'Created test user: {test_user.username}'))

        created_count = 0
        updated_count = 0

        for company_data in test_companies:
            # Check if logo file exists
            logo_path = os.path.join(settings.MEDIA_ROOT, company_data['logo'])
            if not os.path.exists(logo_path):
                self.stdout.write(
                    self.style.WARNING(f'Logo file not found: {logo_path}, skipping {company_data["name"]}')
                )
                continue

            # Create or get company
            company, company_created = Company.objects.get_or_create(
                slug=company_data['slug'],
                defaults={
                    'name': company_data['name'],
                    'entity_type': 'company',
                    'tier': 'basic',
                    'is_featured': True,
                    'is_active': True,
                    'owner': test_user
                }
            )

            if company_created:
                created_count += 1
                self.stdout.write(f'Created company: {company.name}')
            else:
                # Update existing company to be featured
                company.is_featured = True
                company.is_active = True
                company.save()
                updated_count += 1
                self.stdout.write(f'Updated company: {company.name}')

            # Create or update company information
            info, info_created = CompanyInformation.objects.get_or_create(
                company=company,
                defaults={
                    'mission': f'Mission of {company.name}',
                    'description': company_data['description'],
                    'website': f'https://www.{company.slug}.com',
                    'contact_email': f'contact@{company.slug}.com',
                    'contact_phone': '******-0123',
                    'timezone': 'UTC',
                    'language': 'en',
                    'list_in_directory': True,
                    'logo': company_data['logo'],
                    'industry': 'Technology',
                    'size': '50-100',
                    'founded': 2020,
                    'address_line1': '123 Tech Street',
                    'city': 'Tech City',
                    'postal_code': '12345',
                    'country': 'USA'
                }
            )

            if not info_created and info.logo != company_data['logo']:
                info.logo = company_data['logo']
                info.description = company_data['description']
                info.list_in_directory = True
                info.save()
                self.stdout.write(f'Updated logo for: {company.name}')

            # Create or update company listing
            listing, listing_created = CompanyListing.objects.get_or_create(
                company=company,
                defaults={
                    'is_listed': True,
                    'featured': True,
                    'description': company_data['description'],
                    'website': f'https://www.{company.slug}.com',
                    'social_links': {},
                    'tags': ['technology', 'innovation']
                }
            )

            if not listing_created:
                listing.featured = True
                listing.is_listed = True
                listing.save()

            # Create membership for the owner
            Membership.objects.get_or_create(
                user=test_user,
                company=company
            )

            # Create a test assistant for each company to show assistant count
            Assistant.objects.get_or_create(
                name=f'{company.name} Assistant',
                company=company,
                defaults={
                    'description': f'AI assistant for {company.name}',
                    'is_public': True,
                    'is_active': True,
                    'created_by': test_user
                }
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully processed companies: {created_count} created, {updated_count} updated'
            )
        )

        # Show final count of featured companies
        featured_count = Company.objects.filter(is_featured=True, is_active=True).count()
        self.stdout.write(
            self.style.SUCCESS(f'Total featured companies: {featured_count}')
        )
