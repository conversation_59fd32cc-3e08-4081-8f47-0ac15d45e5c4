# Django Settings
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# Encoding Settings (Important for Unicode support)
PYTHONIOENCODING=utf-8
LC_ALL=en_US.UTF-8
LANG=en_US.UTF-8

# Database Settings (Optional - SQLite is default)
# DATABASE_URL=postgres://user:password@localhost:5432/dbname

# =============================================================================
# LLM PROVIDER CONFIGURATIONS
# =============================================================================
# Configure all LLM providers and models used by the application

# OpenAI Settings
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_DEFAULT_MODEL=gpt-4

# Groq Settings (for Llama models)
GROQ_API_KEY=your-groq-api-key
GROQ_BASE_URL=https://api.groq.com/openai/v1
GROQ_DEFAULT_MODEL=llama-3.3-70b-versatile

# Anthropic Settings
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_DEFAULT_MODEL=claude-3-sonnet-20240229

# Gemini Settings
GEMINI_API_KEY=your-gemini-api-key
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai/
GEMINI_DEFAULT_MODEL=gemini-2.0-flash

# Model Name Configurations
# Customize the exact model names used by the application
MODEL_GPT35=gpt-3.5-turbo
MODEL_GPT4=gpt-4
MODEL_GPT4_TURBO=gpt-4-turbo
MODEL_CLAUDE=claude-3-sonnet-20240229
MODEL_CLAUDE_INSTANT=claude-3-haiku-20240307
MODEL_GEMINI_FLASH=gemini-2.0-flash
MODEL_LLAMA_70B=llama-3.3-70b-versatile

# Additional LLM Provider Settings
# Ollama (for local models)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_DEFAULT_MODEL=llama2

# Custom LLM Provider (for OpenAI-compatible APIs)
CUSTOM_LLM_BASE_URL=https://api.openai.com/v1
CUSTOM_LLM_API_KEY=your-custom-api-key
CUSTOM_LLM_DEFAULT_MODEL=gpt-3.5-turbo

# LLM Performance and Caching Settings
LLM_CACHE_TTL=3600                    # Cache time-to-live in seconds
LLM_CACHE_MAX_SIZE=1000               # Maximum number of cached responses
LLM_CACHE_SIMILARITY_THRESHOLD=0.85   # Similarity threshold for cache hits
LLM_REQUEST_TIMEOUT=30                # Request timeout in seconds
LLM_MAX_RETRIES=3                     # Maximum number of retries on failure
LLM_RETRY_DELAY=1.0                   # Delay between retries in seconds

# Email Settings (cPanel Configuration for 24seven.site)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=mail.24seven.site
EMAIL_PORT=465
EMAIL_USE_SSL=True
EMAIL_USE_TLS=False
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-actual-email-password
DEFAULT_FROM_EMAIL=24seven <<EMAIL>>

# cPanel Email Server Details:
# Incoming: mail.24seven.site (IMAP: 993, POP3: 995)
# Outgoing: mail.24seven.site (SMTP: 465 SSL/TLS)
# Authentication: Required for all protocols

# AWS Settings (Optional - for production file storage)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=your-region

# Redis Settings (Optional - for caching and Celery)
# REDIS_URL=redis://localhost:6379/0
# CELERY_BROKER_URL=redis://localhost:6379/1

# Security Settings
CSRF_COOKIE_SECURE=False  # Set to True in production
SESSION_COOKIE_SECURE=False  # Set to True in production
SECURE_SSL_REDIRECT=False  # Set to True in production
SECURE_HSTS_SECONDS=0  # Set to 31536000 in production
SECURE_HSTS_INCLUDE_SUBDOMAINS=False  # Set to True in production
SECURE_HSTS_PRELOAD=False  # Set to True in production

# Application Settings
MAX_UPLOAD_SIZE=5242880  # 5MB in bytes
ALLOWED_CONTENT_TYPES=application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# Development Tools
DJANGO_DEBUG_TOOLBAR=True

# Google Analytics (Optional)
# GA_TRACKING_ID=your-ga-tracking-id

# Sentry Error Tracking (Optional)
# SENTRY_DSN=your-sentry-dsn

# Payment Processing (Optional)
# STRIPE_PUBLIC_KEY=your-stripe-public-key
# STRIPE_SECRET_KEY=your-stripe-secret-key
# STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
