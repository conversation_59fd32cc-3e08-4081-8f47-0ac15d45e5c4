#!/usr/bin/env python
"""
Comprehensive email system test for Django application.
Tests all email functionality including configuration, sending, and Django email utilities.
"""

import os
import sys
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    
    from django.core.mail import send_mail, EmailMessage, get_connection
    from django.conf import settings
    from django.contrib.auth import get_user_model
    from accounts.email_utils import send_html_email, get_site_url
    
    User = get_user_model()
    
    print("🧪 Comprehensive Email System Test")
    print("=" * 60)
    
    # Test 1: Configuration Verification
    print("\n1️⃣ Testing Email Configuration...")
    print("-" * 40)
    
    config_issues = []
    
    print(f"✅ EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"✅ EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"✅ EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"✅ EMAIL_USE_SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    print(f"✅ EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', False)}")
    print(f"✅ EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"✅ DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    
    # Check critical settings
    if settings.EMAIL_HOST != 'mail.24seven.site':
        config_issues.append("EMAIL_HOST should be 'mail.24seven.site'")
    
    if settings.EMAIL_PORT != 465:
        config_issues.append("EMAIL_PORT should be 465 for SSL")
    
    if not getattr(settings, 'EMAIL_USE_SSL', False):
        config_issues.append("EMAIL_USE_SSL should be True")
    
    if settings.EMAIL_HOST_USER != '<EMAIL>':
        config_issues.append("EMAIL_HOST_USER should be '<EMAIL>'")
    
    if not settings.EMAIL_HOST_PASSWORD:
        config_issues.append("EMAIL_HOST_PASSWORD is not set")
    elif settings.EMAIL_HOST_PASSWORD == 'your-actual-email-password':
        config_issues.append("EMAIL_HOST_PASSWORD is still placeholder value")
    else:
        print("✅ EMAIL_HOST_PASSWORD: Configured")
    
    if config_issues:
        print("\n❌ Configuration Issues Found:")
        for issue in config_issues:
            print(f"   - {issue}")
        print("\nPlease fix configuration issues before testing email sending.")
    else:
        print("\n✅ Email configuration looks good!")
    
    # Test 2: SMTP Connection Test
    print("\n2️⃣ Testing SMTP Connection...")
    print("-" * 40)
    
    try:
        connection = get_connection()
        connection.open()
        print("✅ SMTP connection successful!")
        connection.close()
        smtp_working = True
    except Exception as e:
        print(f"❌ SMTP connection failed: {e}")
        smtp_working = False
        
        # Provide specific error guidance
        error_str = str(e).lower()
        if '535' in error_str or 'authentication' in error_str:
            print("   💡 This is likely an authentication issue:")
            print("      - <NAME_EMAIL> email account exists in cPanel")
            print("      - Verify EMAIL_HOST_PASSWORD is correct")
        elif 'timeout' in error_str or 'connection' in error_str:
            print("   💡 This is likely a connection issue:")
            print("      - Check if mail.24seven.site resolves correctly")
            print("      - Verify firewall allows port 465 outbound")
        elif 'ssl' in error_str or 'certificate' in error_str:
            print("   💡 This is likely an SSL issue:")
            print("      - Check SSL certificate for mail.24seven.site")
    
    # Test 3: Basic Email Sending
    if smtp_working:
        print("\n3️⃣ Testing Basic Email Sending...")
        print("-" * 40)
        
        test_recipient = input("Enter test email address (or press <NAME_EMAIL>): ").strip()
        if not test_recipient:
            test_recipient = '<EMAIL>'
        
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            subject = f'Django Email Test - {timestamp}'
            message = f"""
Django Email System Test

This is a test email sent at {timestamp} to verify the email system is working.

Configuration:
- From: {settings.DEFAULT_FROM_EMAIL}
- SMTP: {settings.EMAIL_HOST}:{settings.EMAIL_PORT}
- SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}

If you receive this email, the basic email sending is working correctly!

Best regards,
Django Application
"""
            
            result = send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[test_recipient],
                fail_silently=False
            )
            
            if result == 1:
                print(f"✅ Basic email sent successfully to {test_recipient}!")
                basic_email_working = True
            else:
                print("❌ Email sending failed (returned 0)")
                basic_email_working = False
                
        except Exception as e:
            print(f"❌ Error sending basic email: {e}")
            basic_email_working = False
    else:
        basic_email_working = False
        print("\n3️⃣ Skipping email sending tests (SMTP connection failed)")
    
    # Test 4: HTML Email Sending
    if basic_email_working:
        print("\n4️⃣ Testing HTML Email Sending...")
        print("-" * 40)
        
        try:
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Django HTML Email Test</title>
    <style>
        body {{ font-family: Arial, sans-serif; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; }}
        .success {{ color: #28a745; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📧 Django HTML Email Test</h1>
    </div>
    <div class="content">
        <p>This is an HTML email test sent at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}.</p>
        <p class="success">✅ If you can see this styled content, HTML emails are working!</p>
        <p>Configuration: {settings.EMAIL_HOST}:{settings.EMAIL_PORT}</p>
    </div>
</body>
</html>
"""
            
            email = EmailMessage(
                subject=f'Django HTML Email Test - {datetime.now().strftime("%H:%M:%S")}',
                body=html_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[test_recipient]
            )
            email.content_subtype = 'html'
            email.send()
            
            print(f"✅ HTML email sent successfully to {test_recipient}!")
            html_email_working = True
            
        except Exception as e:
            print(f"❌ Error sending HTML email: {e}")
            html_email_working = False
    else:
        html_email_working = False
        print("\n4️⃣ Skipping HTML email test (basic email failed)")
    
    # Test 5: Custom Email Utility Test
    if basic_email_working:
        print("\n5️⃣ Testing Custom Email Utilities...")
        print("-" * 40)
        
        try:
            # Test get_site_url function
            site_url = get_site_url()
            print(f"✅ Site URL function: {site_url}")
            
            # Test custom email function (check if it exists and works)
            try:
                from accounts.email_utils import send_html_email
                
                # Check function signature
                import inspect
                sig = inspect.signature(send_html_email)
                print(f"✅ send_html_email function found with parameters: {list(sig.parameters.keys())}")
                
                # Try to send using custom function
                send_html_email(
                    to_email=test_recipient,
                    subject=f'Custom Email Utility Test - {datetime.now().strftime("%H:%M:%S")}',
                    html_content=f"""
<h2>Custom Email Utility Test</h2>
<p>This email was sent using the custom email utility function.</p>
<p>Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
<p>Site URL: {site_url}</p>
<p style="color: green;">✅ Custom email utilities are working!</p>
"""
                )
                
                print(f"✅ Custom email utility sent successfully to {test_recipient}!")
                custom_email_working = True
                
            except TypeError as te:
                print(f"⚠️  Custom email function signature issue: {te}")
                print("   The function exists but may need parameter adjustment")
                custom_email_working = False
            except Exception as e:
                print(f"❌ Custom email utility error: {e}")
                custom_email_working = False
                
        except ImportError:
            print("⚠️  Custom email utilities not found or not importable")
            custom_email_working = False
    else:
        custom_email_working = False
        print("\n5️⃣ Skipping custom email utility test (basic email failed)")
    
    # Test 6: Django Email Features Test
    if basic_email_working:
        print("\n6️⃣ Testing Django Email Features...")
        print("-" * 40)
        
        try:
            # Test email with attachments capability
            from django.core.mail import EmailMultiAlternatives
            
            msg = EmailMultiAlternatives(
                subject=f'Django Features Test - {datetime.now().strftime("%H:%M:%S")}',
                body='This is a plain text email with HTML alternative.',
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[test_recipient]
            )
            
            html_content = """
<h2>Django Email Features Test</h2>
<p>This email demonstrates Django's advanced email features:</p>
<ul>
    <li>✅ Plain text and HTML alternatives</li>
    <li>✅ Custom headers</li>
    <li>✅ Multiple recipients support</li>
</ul>
<p style="color: blue;">Django email features are working correctly!</p>
"""
            msg.attach_alternative(html_content, "text/html")
            msg.send()
            
            print(f"✅ Advanced Django email features working!")
            django_features_working = True
            
        except Exception as e:
            print(f"❌ Django email features error: {e}")
            django_features_working = False
    else:
        django_features_working = False
        print("\n6️⃣ Skipping Django email features test (basic email failed)")
    
    # Final Summary
    print("\n" + "=" * 60)
    print("📊 EMAIL SYSTEM TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Configuration", len(config_issues) == 0),
        ("SMTP Connection", smtp_working),
        ("Basic Email Sending", basic_email_working),
        ("HTML Email Sending", html_email_working),
        ("Custom Email Utilities", custom_email_working),
        ("Django Email Features", django_features_working)
    ]
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name:<25} {status}")
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Email system is fully functional!")
        print("\n✅ Your Django application can now:")
        print("   - Send user registration emails")
        print("   - Send password reset emails")
        print("   - Send team invitation emails")
        print("   - Send system notifications")
        print("   - Send HTML formatted emails")
    elif passed >= 3:
        print("\n⚠️  PARTIAL SUCCESS: Basic email functionality is working")
        print("   Some advanced features may need attention")
    else:
        print("\n❌ MAJOR ISSUES: Email system needs configuration fixes")
        print("   Please address the failed tests above")
    
    print(f"\n📧 Test emails sent to: {test_recipient}")
    print("   Check your inbox (and spam folder) for test messages")
    
    print("\n🔧 Next Steps:")
    if not smtp_working:
        print("   1. Fix SMTP connection issues")
        print("   2. Verify email account exists in cPanel")
        print("   3. Check EMAIL_HOST_PASSWORD in .env file")
    else:
        print("   1. Check email delivery in recipient inbox")
        print("   2. Test user registration email flow")
        print("   3. Test password reset email flow")
        print("   4. Monitor email logs for any issues")

except Exception as e:
    print(f"❌ Error running email system test: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
