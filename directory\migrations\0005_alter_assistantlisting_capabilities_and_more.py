# Generated by Django 4.2.21 on 2025-05-26 07:56

import directory.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("directory", "0004_merge_20250525_2318"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="assistantlisting",
            name="capabilities",
            field=models.J<PERSON><PERSON><PERSON>(blank=True, default=directory.models.default_list),
        ),
        migrations.Alter<PERSON>ield(
            model_name="assistantlisting",
            name="categories",
            field=models.<PERSON><PERSON><PERSON>ield(blank=True, default=directory.models.default_list),
        ),
        migrations.Alter<PERSON>ield(
            model_name="assistantlisting",
            name="tags",
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, default=directory.models.default_list),
        ),
        migrations.Alter<PERSON>ield(
            model_name="companylisting",
            name="social_links",
            field=models.JSONField(blank=True, default=directory.models.default_dict),
        ),
        migrations.AlterField(
            model_name="companylisting",
            name="tags",
            field=models.J<PERSON><PERSON>ield(blank=True, default=directory.models.default_list),
        ),
    ]
