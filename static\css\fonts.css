
/* Web Fonts for cPanel Deployment */

@font-face {
    font-family: 'Liberation Sans';
    src: url('../fonts/system/LiberationSans-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Liberation Sans';
    src: url('../fonts/system/LiberationSans-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DejaVu Sans';
    src: url('../fonts/system/DejaVuSans.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DejaVu Sans';
    src: url('../fonts/system/DejaVuSans-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* Font fallback stack for better compatibility */
.font-system {
    font-family: 'Liberation Sans', 'DejaVu Sans', -apple-system, BlinkMacSystemFont, 
                 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.font-monospace {
    font-family: 'Liberation Mono', 'DejaVu Sans Mono', 'Courier New', monospace;
}
