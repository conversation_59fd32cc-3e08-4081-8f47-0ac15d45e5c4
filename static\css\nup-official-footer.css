/**
 * NUP Official Footer CSS
 * Based on the official nupuganda.org footer design
 */

/* ===== MAIN FOOTER STRUCTURE ===== */
#nup-main-footer,
.nup-main-footer {
    background-color: #252638 !important;  /* Updated background color */
    color: #ffffff !important;
    font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    position: relative;
}

/* ===== NUP FOOTER LOGO IMAGE ===== */
#nup-footer-logo-img,
.nup-footer-logo-img {
    max-height: 80px !important;
    width: auto !important;
    height: auto !important;
    max-width: 200px !important;
    object-fit: contain !important;
    filter: brightness(1.1) !important;
}

/* ===== NUP LOGO FALLBACK ===== */
#nup-logo-fallback,
.nup-logo-fallback {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #ffffff !important;
}

#nup-logo-circle,
.nup-logo-circle {
    width: 50px;
    height: 50px;
    background-color: #cf2e2e !important;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

#nup-logo-text,
.nup-logo-text {
    color: #ffffff !important;
    font-weight: bold;
    font-size: 16px;
    font-family: 'Merriweather Sans', sans-serif;
}

#nup-logo-name,
.nup-logo-name {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
}

#nup-logo-name span,
.nup-logo-name span {
    font-family: 'Merriweather Sans', sans-serif;
    font-weight: 700;
    font-size: 12px;
    color: #ffffff !important;
    letter-spacing: 0.5px;
}

#nup-logo-national,
.nup-logo-national { color: #cf2e2e !important; }
#nup-logo-unity,
.nup-logo-unity { color: #ffffff !important; }
#nup-logo-platform,
.nup-logo-platform { color: #f7bd00 !important; }

/* Footer Top Section */
#nup-footer-top-section,
.nup-footer-top-section {
    padding: 80px 0 50px 0;
    position: relative;
    background-color: #252638 !important;  /* Updated background color */
}

#nup-footer-container,
.nup-footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Footer Columns */
.nup-footer-logo-column,
.nup-footer-social-column,
.nup-footer-news-column {
    margin-bottom: 40px;
}

#nup-footer-row,
.nup-footer-row,
.nup-clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* ===== LOGO WIDGET ===== */
#nup-footer-logo-widget,
.nup-footer-logo-widget {
    padding-right: 20px;
}

#nup-footer-logo-figure,
.nup-footer-logo-figure {
    margin-bottom: 25px;
}

#nup-footer-logo-img,
.nup-footer-logo-img {
    max-height: 60px;
    width: auto;
}

#nup-footer-about-paragraph,
.nup-footer-about-paragraph {
    color: #ffffff !important;  /* Pure white text */
    line-height: 1.6;
    font-size: 14px;
    margin-bottom: 0;
}

/* ===== WIDGET TITLES ===== */
.widget-title h4 {
    color: #ffffff;
    font-family: 'Merriweather Sans', 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 25px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== SOCIAL ICONS ===== */
.cnss-social-icon {
    list-style: none;
    padding: 0;
    margin: 20px 0 0 0;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.cnss-social-icon li {
    display: inline-block;
}

.cnss-social-icon a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.08);
    color: #ffffff;  /* Pure white text */
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    text-decoration: none;
}

.cnss-social-icon a:hover {
    background-color: #cf2e2e;
    color: #ffffff;
    border-color: #cf2e2e;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(207, 46, 46, 0.4);
}

.cnss-social-icon i {
    font-size: 16px;
}

/* Specific social media colors on hover */
.cnss-facebook:hover { background-color: #1877f2 !important; border-color: #1877f2 !important; }
.cnss-x-twitter:hover { background-color: #000000 !important; border-color: #000000 !important; }
.cnss-youtube:hover { background-color: #ff0000 !important; border-color: #ff0000 !important; }
.cnss-instagram:hover { background-color: #e4405f !important; border-color: #e4405f !important; }
.cnss-tiktok:hover { background-color: #000000 !important; border-color: #000000 !important; }

/* ===== UNIQUE NUP FOOTER CLASSES ===== */
/* Widget Titles with Unique Classes */
#nup-social-title-heading,
#nup-news-title-heading,
.nup-social-title-heading,
.nup-news-title-heading {
    color: #ffffff !important;
    font-family: 'Merriweather Sans', 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 25px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Social Media with Unique Classes */
#nup-social-icon-list,
.nup-social-icon-list {
    list-style: none;
    padding: 0;
    margin: 20px 0 0 0;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.nup-social-icon-item {
    display: inline-block;
}

.nup-social-facebook-link,
.nup-social-twitter-link,
.nup-social-youtube-link,
.nup-social-instagram-link,
.nup-social-tiktok-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: rgba(255, 255, 255, 0.08) !important;
    color: #ffffff !important;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    text-decoration: none;
}

.nup-social-facebook-link:hover,
.nup-social-twitter-link:hover,
.nup-social-youtube-link:hover,
.nup-social-instagram-link:hover,
.nup-social-tiktok-link:hover {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
    border-color: #cf2e2e !important;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(207, 46, 46, 0.4);
}

.nup-social-facebook-icon,
.nup-social-twitter-icon,
.nup-social-youtube-icon,
.nup-social-instagram-icon,
.nup-social-tiktok-icon {
    font-size: 16px;
    color: #ffffff !important;
}

/* ===== RECENT NEWS WIDGET ===== */
.post-widget .widget-content {
    margin-top: 0;
}

.post-inner .post {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.post-inner .post:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.post-date {
    flex-shrink: 0;
    margin-right: 15px;
    text-align: center;
    min-width: 50px;
}

.post-date h3 {
    background-color: #cf2e2e;  /* NUP Vivid Red */
    color: #ffffff;
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    padding: 8px 6px 4px 6px;
    border-radius: 4px 4px 0 0;
    line-height: 1;
}

.post-date span {
    background-color: #b82626;  /* Darker red */
    color: #ffffff;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 4px 6px;
    display: block;
    border-radius: 0 0 4px 4px;
    letter-spacing: 0.5px;
}

.post-content {
    flex: 1;
}

.post p {
    color: #ffffff;  /* Pure white text */
    font-size: 12px;
    margin: 0 0 8px 0;
}

.post h5 {
    margin: 0;
}

.post h5 a {
    color: #ffffff;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    text-decoration: none;
    transition: color 0.3s ease;
}

.post h5 a:hover {
    color: #cf2e2e;  /* NUP Vivid Red */
}

/* ===== UNIQUE NEWS CLASSES ===== */
#nup-news-post-widget,
.nup-news-post-widget {
    padding-left: 20px;
}

#nup-news-widget-content,
.nup-news-widget-content {
    margin-top: 0;
}

#nup-news-post-inner,
.nup-news-post-inner {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.nup-news-post {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nup-news-post:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.nup-news-post-date {
    flex-shrink: 0;
    margin-right: 15px;
    text-align: center;
    min-width: 50px;
}

.nup-news-post-date-number {
    background-color: #cf2e2e !important;  /* NUP Vivid Red */
    color: #ffffff !important;
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    padding: 8px 6px 4px 6px;
    border-radius: 4px 4px 0 0;
    line-height: 1;
}

.nup-news-post-date-month {
    background-color: #b82626 !important;  /* Darker red */
    color: #ffffff !important;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    padding: 4px 6px;
    display: block;
    border-radius: 0 0 4px 4px;
    letter-spacing: 0.5px;
}

.nup-news-post-content {
    flex: 1;
}

.nup-news-post-author {
    color: #ffffff !important;  /* Pure white text */
    font-size: 12px;
    margin: 0 0 8px 0;
}

.nup-news-post-author-icon {
    color: #ffffff !important;
}

.nup-news-post-title {
    margin: 0;
}

.nup-news-post-link {
    color: #ffffff !important;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nup-news-post-link:hover {
    color: #cf2e2e !important;  /* NUP Vivid Red */
}

/* ===== FOOTER BOTTOM ===== */
.footer-bottom {
    background-color: #252638;  /* Same as main footer */
    padding: 25px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.footer-bottom.alternat-2 {
    background-color: #252638;  /* Same as main footer */
}

/* ===== UNIQUE FOOTER BOTTOM CLASSES ===== */
#nup-footer-bottom-section,
.nup-footer-bottom-section {
    background-color: #252638 !important;  /* Same as main footer */
    padding: 25px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.nup-footer-bottom-alternat {
    background-color: #252638 !important;  /* Same as main footer */
}

#nup-footer-bottom-container,
.nup-footer-bottom-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

#nup-footer-bottom-inner,
.nup-footer-bottom-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

#nup-footer-copyright,
.nup-footer-copyright {
    flex: 1;
}

#nup-footer-copyright-text,
.nup-footer-copyright-text {
    color: #ffffff !important;  /* Pure white text */
    font-size: 13px;
    margin: 0;
    font-weight: 400;
}

#nup-footer-nav-list,
.nup-footer-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    gap: 20px;
}

.nup-footer-nav-item {
    margin: 0;
}

.nup-footer-nav-link {
    color: #ffffff !important;  /* Pure white text */
    font-size: 13px;
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
    padding: 8px 20px;
    display: block;
}

.nup-footer-nav-link:hover {
    color: #cf2e2e !important;  /* NUP red on hover */
}

.nup-hvr-underline {
    position: relative;
    overflow: hidden;
}

.nup-hvr-underline::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #cf2e2e;
    transition: width 0.3s ease;
}

.nup-hvr-underline:hover::before {
    width: 100%;
}

.bottom-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.copyright {
    color: #ffffff;  /* Pure white text */
    font-size: 13px;
    margin: 0;
    font-weight: 400;
}

.copyright p {
    margin: 0;
}

.footer-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 0;
}

.footer-nav li {
    margin: 0;
}

.footer-nav a {
    color: #ffffff;  /* Pure white text */
    font-size: 13px;
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
    padding: 8px 20px;
    display: block;
}

.footer-nav a:hover,
.footer-nav a.hvr-underline-from-left1:hover {
    color: #ffffff;
}

.pull-left {
    float: left;
}

.pull-right {
    float: right;
}

/* Hover underline effect */
.hvr-underline-from-left1::before {
    content: "";
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: #cf2e2e;
    transition: width 0.3s ease;
}

.hvr-underline-from-left1:hover::before {
    width: 100%;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991.98px) {
    .footer-top-two {
        padding: 40px 0 30px 0;
    }
    
    .footer-column {
        margin-bottom: 40px;
    }
    
    .logo-widget {
        padding-right: 0;
        margin-bottom: 30px;
    }
}

@media (max-width: 767.98px) {
    .footer-top-two {
        padding: 30px 0 20px 0;
    }
    
    .bottom-inner {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .footer-nav {
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
    }
    
    .cnss-social-icon {
        justify-content: center;
    }
    
    .post-inner .post {
        margin-bottom: 20px;
        padding-bottom: 15px;
    }
    
    .post-date {
        margin-right: 12px;
        min-width: 45px;
    }
    
    .post-date h3 {
        font-size: 16px;
        padding: 6px 4px 3px 4px;
    }
    
    .post-date span {
        font-size: 9px;
        padding: 3px 4px;
    }
}

/* ===== UTILITY CLASSES ===== */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

.pull-left {
    float: left;
}

.pull-right {
    float: right;
}

@media (max-width: 767.98px) {
    .pull-left,
    .pull-right {
        float: none;
    }
}

/* ===== OVERRIDE EXISTING FOOTER STYLES ===== */
footer.footer,
.footer {
    display: none !important;
}

.main-footer {
    display: block !important;
}

/* ===== FORCE DARK FOOTER EVERYWHERE ===== */
footer, footer.footer, .footer, .main-footer,
[data-theme="dark"] footer, [data-theme="dark"] footer.footer, [data-theme="dark"] .footer, [data-theme="dark"] .main-footer,
[data-theme="light"] footer, [data-theme="light"] footer.footer, [data-theme="light"] .footer, [data-theme="light"] .main-footer {
    background-color: #252638 !important;
    background: #252638 !important;
    color: #ffffff !important;
    border-top-color: #252638 !important;
}

/* Force all footer children to have white text */
footer *, footer.footer *, .footer *, .main-footer *,
[data-theme="dark"] footer *, [data-theme="dark"] footer.footer *, [data-theme="dark"] .footer *, [data-theme="dark"] .main-footer *,
[data-theme="light"] footer *, [data-theme="light"] footer.footer *, [data-theme="light"] .footer *, [data-theme="light"] .main-footer * {
    color: #ffffff !important;
}

/* Override any white backgrounds in footer */
footer [style*="background"], footer.footer [style*="background"], .footer [style*="background"], .main-footer [style*="background"],
footer .bg-white, footer.footer .bg-white, .footer .bg-white, .main-footer .bg-white,
footer .bg-light, footer.footer .bg-light, .footer .bg-light, .main-footer .bg-light {
    background-color: #252638 !important;
    background: #252638 !important;
}

/* Force footer visibility on all screen sizes */
@media (max-width: 767.98px) {
    .main-footer,
    .nup-main-footer {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        overflow: visible !important;
        margin: 0 !important;
        padding: 0 !important;
        position: static !important;
        left: auto !important;
        top: auto !important;
        z-index: auto !important;
    }
}

/* ===== RESPONSIVE DESIGN FOR UNIQUE CLASSES ===== */
@media (max-width: 991.98px) {
    .nup-footer-logo-column,
    .nup-footer-social-column,
    .nup-footer-news-column {
        margin-bottom: 50px;
    }

    .nup-footer-logo-widget {
        padding-right: 0;
    }

    .nup-news-post-widget {
        padding-left: 0;
    }
}

@media (max-width: 767.98px) {
    .nup-footer-top-section {
        padding: 60px 0 40px 0;
    }

    .nup-footer-logo-column,
    .nup-footer-social-column,
    .nup-footer-news-column {
        margin-bottom: 40px;
        text-align: center;
    }

    .nup-footer-bottom-section {
        padding: 20px 0;
        text-align: center;
    }

    .nup-footer-bottom-inner {
        flex-direction: column;
        gap: 15px;
    }

    .nup-footer-copyright,
    .nup-footer-nav-list {
        width: 100%;
        text-align: center;
    }

    .nup-pull-left,
    .nup-pull-right {
        float: none !important;
    }
}
