from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView, RedirectView
from . import views

# Debug view for impersonation removed

# Initialize URL patterns
urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # Home and core pages
    path('', views.home, name='home'),
    path('about/', TemplateView.as_view(template_name='about.html'), name='about'),
    path('pricing/', TemplateView.as_view(template_name='pricing.html'), name='pricing'),
    path('contact/', TemplateView.as_view(template_name='contact.html'), name='contact'),
    path('demo/', TemplateView.as_view(template_name='demo.html'), name='demo'),
    path('features/', TemplateView.as_view(template_name='features.html'), name='features'),
    path('security/', TemplateView.as_view(template_name='security.html'), name='security'),
    path('careers/', TemplateView.as_view(template_name='careers.html'), name='careers'),
    path('cookies/', TemplateView.as_view(template_name='cookies.html'), name='cookies'), # Added cookies URL
    path('privacy/', TemplateView.as_view(template_name='privacy.html'), name='privacy'),
    path('terms/', TemplateView.as_view(template_name='terms.html'), name='terms'),
    path('terms2/', TemplateView.as_view(template_name='./terms.html'), name='terms2'),
    path('test/', TemplateView.as_view(template_name='test.html'), name='test'),
    path('tinymce-example/', TemplateView.as_view(template_name='tinymce_example.html'), name='tinymce_example'),

    # Global functionality
    path('search/', views.search, name='search'),
    path('theme/', views.switch_theme, name='switch_theme'),
    path('test-media/', views.test_media, name='test_media'),

    # App URLs
    path('accounts/', include('accounts.urls', namespace='accounts')),
    # Add a redirect for the bare /assistants URL
    path('assistants/', RedirectView.as_view(url='/', permanent=False)),
    # Add direct path for TinyMCE upload to avoid redirect issues
    path('assistants/tinymce/upload/', views.tinymce_image_upload_proxy, name='tinymce_upload_proxy'),
    # Add direct path for assistant chat to avoid redirect issues
    path('assistants/assistants/assistants/chat/', views.assistant_redirect, name='assistant_redirect'),
    # Add a redirect for all other /assistants/* URLs to /assistant/*
    path('assistants/<path:url>', RedirectView.as_view(url='/assistant/%(url)s', permanent=False)),
    # Add a special case for nested assistants paths
    path('assistants/assistants/<path:url>', RedirectView.as_view(url='/assistant/%(url)s', permanent=False)),
    path('assistants/assistants/assistants/<path:url>', RedirectView.as_view(url='/assistant/%(url)s', permanent=False)),
    # Include the assistants app URLs with a different prefix
    path('assistant/', include('assistants.urls', namespace='assistants')),
    path('content/', include('content.urls', namespace='content')),
    path('directory/', include('directory.urls', namespace='directory')),
    path('superadmin/', include('superadmin.urls', namespace='superadmin')), # Added superadmin URLs
    path('tinymce/', include('tinymce.urls')), # Added django-tinymce URLs
]

# Conditionally include impersonate URLs (not in cPanel environment)
IN_CPANEL = getattr(settings, 'IN_CPANEL', False)
if not IN_CPANEL and 'impersonate' in settings.INSTALLED_APPS:
    urlpatterns.append(path('impersonate/', include('impersonate.urls')))

# Error handlers
handler404 = 'company_assistant.views.handler404'
handler500 = 'company_assistant.views.handler500'
handler403 = 'company_assistant.views.handler403'

# Development settings
if settings.DEBUG:
    # Serve media files in development
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    # Static files are automatically served by runserver when DEBUG=True and django.contrib.staticfiles is installed.

    # Debug toolbar removed
