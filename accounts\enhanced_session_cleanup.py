"""
Enhanced session cleanup middleware for user isolation and security.
"""
from django.utils.deprecation import MiddlewareMixin
from django.contrib.sessions.models import Session
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.contrib.auth.signals import user_logged_out
from django.dispatch import receiver
import logging
import hashlib
import random

logger = logging.getLogger(__name__)
User = get_user_model()


class EnhancedSessionCleanupMiddleware(MiddlewareMixin):
    """
    Enhanced middleware to clean up expired sessions and user-specific data.
    """
    
    def process_response(self, request, response):
        """Clean up sessions on logout and periodically."""
        # Only run cleanup on logout or specific conditions
        if (hasattr(request, 'user') and 
            request.user.is_authenticated and 
            request.path == '/accounts/logout/'):
            
            # Clean up user's other sessions on logout
            try:
                user_sessions = Session.objects.filter(
                    expire_date__gte=timezone.now()
                )
                
                for session in user_sessions:
                    session_data = session.get_decoded()
                    if session_data.get('_auth_user_id') == str(request.user.id):
                        if session.session_key != request.session.session_key:
                            session.delete()
                
                # Clean up user-specific cache data
                self._cleanup_user_cache_data(request.user)
                            
            except Exception as e:
                logger.error(f"Error cleaning up user sessions: {e}")
        
        # Periodic cleanup (every 100 requests approximately)
        if random.randint(1, 100) == 1:
            self._periodic_cleanup()
        
        return response
    
    def _cleanup_user_cache_data(self, user):
        """
        Clean up user-specific cache data.
        
        Args:
            user: User object
        """
        try:
            # Import here to avoid circular imports
            from .session_management import UserSessionManager
            UserSessionManager.cleanup_user_sessions(user)
            
            logger.info(f"Cleaned up cache data for user {user.username}")
            
        except Exception as e:
            logger.error(f"Error cleaning up user cache data: {e}")
    
    def _periodic_cleanup(self):
        """
        Perform periodic cleanup of expired sessions and cache data.
        """
        try:
            # Clean up expired sessions
            expired_sessions = Session.objects.filter(
                expire_date__lt=timezone.now()
            )
            expired_count = expired_sessions.count()
            if expired_count > 0:
                expired_sessions.delete()
                logger.info(f"Cleaned up {expired_count} expired sessions")
            
            # Clean up old cache entries (this is a simplified approach)
            # In production, you might want to implement a more sophisticated cleanup
            
        except Exception as e:
            logger.error(f"Error in periodic cleanup: {e}")


# Signal handler to clean up user-specific data on logout
@receiver(user_logged_out)
def cleanup_user_data_on_logout(sender, request, user, **kwargs):
    """Clean up user-specific data when a user logs out."""
    try:
        # Clean up impersonation session data
        if 'impersonate_id' in request.session:
            del request.session['impersonate_id']
        
        # Clean up user-specific cache data
        from .session_management import UserSessionManager
        UserSessionManager.cleanup_user_sessions(user)
        
        logger.info(f"Cleaned up user data on logout for {user.username}")
        
    except Exception as e:
        logger.error(f"Error cleaning up user data on logout: {e}")
