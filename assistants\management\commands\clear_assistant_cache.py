"""
Management command to clear assistant-related cache entries.
This helps resolve any corrupted cache data that might cause the
'bytes' object has no attribute 'order_by' error.
"""

from django.core.management.base import BaseCommand
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clear assistant-related cache entries to resolve caching issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Clear cache only for specific company ID',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Clear all cache entries (use with caution)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleared without actually clearing',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting cache clearing operation...'))
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No cache will actually be cleared'))
        
        if options['all']:
            if options['dry_run']:
                self.stdout.write('Would clear ALL cache entries')
            else:
                cache.clear()
                self.stdout.write(self.style.SUCCESS('Cleared all cache entries'))
            return
        
        company_id = options.get('company_id')
        
        # Clear assistant-related cache patterns
        cache_patterns = [
            'company_assistants_ids:*',
            'company_assistants:*',  # Old pattern that might still exist
            'assistant_stats:*',
            'llm_response:*',
        ]
        
        if company_id:
            # Clear only for specific company
            specific_patterns = [
                f'company_assistants_ids:{company_id}:*',
                f'company_assistants:{company_id}:*',
            ]
            cache_patterns = specific_patterns
            self.stdout.write(f'Clearing cache for company ID: {company_id}')
        else:
            self.stdout.write('Clearing all assistant-related cache entries')
        
        cleared_count = 0
        
        # Note: Django's cache framework doesn't have built-in pattern matching
        # This is a simplified implementation. In production, you might want to use
        # Redis-specific commands or implement a more sophisticated cache key tracking system.
        
        try:
            from assistants.advanced_caching import clear_assistant_cache
            
            if options['dry_run']:
                self.stdout.write(f'Would clear assistant cache entries matching patterns: {cache_patterns}')
            else:
                clear_assistant_cache(company_id)
                self.stdout.write(self.style.SUCCESS('Cleared assistant cache using advanced caching system'))
                cleared_count += 1
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error clearing advanced cache: {e}'))
        
        # Also try to clear using Django's cache framework
        try:
            # Clear some known problematic keys
            problematic_keys = []
            
            if company_id:
                # Generate possible cache keys for the company
                for user_id in range(1, 1000):  # Adjust range as needed
                    for can_manage in [True, False]:
                        for is_member in [True, False]:
                            key = f"company_assistants:{company_id}:{user_id}:{can_manage}:{is_member}"
                            problematic_keys.append(key)
                            key_ids = f"company_assistants_ids:{company_id}:{user_id}:{can_manage}:{is_member}"
                            problematic_keys.append(key_ids)
            
            if options['dry_run']:
                self.stdout.write(f'Would attempt to clear {len(problematic_keys)} specific cache keys')
            else:
                for key in problematic_keys:
                    cache.delete(key)
                    cleared_count += 1
                
                self.stdout.write(self.style.SUCCESS(f'Attempted to clear {len(problematic_keys)} specific cache keys'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error clearing specific cache keys: {e}'))
        
        if options['dry_run']:
            self.stdout.write(self.style.SUCCESS('Dry run completed - no cache was actually cleared'))
        else:
            self.stdout.write(self.style.SUCCESS(f'Cache clearing completed. Processed {cleared_count} operations.'))
            self.stdout.write('The assistant list should now work without the QuerySet serialization error.')
