#!/usr/bin/env python
"""
Fix script for django-impersonate app_label error on cPanel.
This script addresses the RuntimeError about ImpersonationLog model.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment for cPanel."""
    # Set cPanel environment variables
    os.environ['CPANEL_ENV'] = 'True'
    os.environ['PRODUCTION'] = 'True'
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
    django.setup()

def check_impersonate_installation():
    """Check if django-impersonate is properly installed."""
    print("🔍 Checking django-impersonate installation...")
    
    try:
        import impersonate
        print(f"✅ django-impersonate version: {impersonate.__version__}")
        return True
    except ImportError as e:
        print(f"❌ django-impersonate not installed: {e}")
        return False

def check_installed_apps():
    """Check if impersonate is in INSTALLED_APPS."""
    print("📋 Checking INSTALLED_APPS configuration...")
    
    from django.conf import settings
    
    installed_apps = settings.INSTALLED_APPS
    print(f"📦 Total installed apps: {len(installed_apps)}")
    
    if 'impersonate' in installed_apps:
        print("✅ 'impersonate' found in INSTALLED_APPS")
        return True
    else:
        print("❌ 'impersonate' NOT found in INSTALLED_APPS")
        print("📋 Current INSTALLED_APPS:")
        for app in installed_apps:
            print(f"   - {app}")
        return False

def check_impersonate_migrations():
    """Check if impersonate migrations exist and are applied."""
    print("🔄 Checking impersonate migrations...")
    
    try:
        from django.db import connection
        
        # Check if impersonate_impersonationlog table exists
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'impersonate_impersonationlog'
                );
            """)
            table_exists = cursor.fetchone()[0]
            
            if table_exists:
                print("✅ impersonate_impersonationlog table exists")
                
                # Check table structure
                cursor.execute("SELECT COUNT(*) FROM impersonate_impersonationlog;")
                count = cursor.fetchone()[0]
                print(f"📊 Table contains {count} records")
                return True
            else:
                print("❌ impersonate_impersonationlog table does not exist")
                return False
                
    except Exception as e:
        print(f"❌ Error checking migrations: {e}")
        return False

def run_impersonate_migrations():
    """Run migrations for the impersonate app."""
    print("🔄 Running impersonate migrations...")
    
    try:
        # Run migrations specifically for impersonate
        execute_from_command_line(['manage.py', 'migrate', 'impersonate'])
        print("✅ Impersonate migrations completed!")
        return True
    except Exception as e:
        print(f"❌ Error running impersonate migrations: {e}")
        return False

def create_impersonate_table_manually():
    """Create the impersonate table manually if migrations fail."""
    print("🔧 Creating impersonate table manually...")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            # Create the impersonate_impersonationlog table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS impersonate_impersonationlog (
                    id SERIAL PRIMARY KEY,
                    session_key VARCHAR(40) NOT NULL,
                    session_started_at TIMESTAMP WITH TIME ZONE NOT NULL,
                    session_ended_at TIMESTAMP WITH TIME ZONE,
                    impersonating_id INTEGER,
                    impersonator_id INTEGER NOT NULL
                );
            """)
            
            # Create indexes
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS impersonate_impersonationlog_session_key 
                ON impersonate_impersonationlog (session_key);
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS impersonate_impersonationlog_impersonating_id 
                ON impersonate_impersonationlog (impersonating_id);
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS impersonate_impersonationlog_impersonator_id 
                ON impersonate_impersonationlog (impersonator_id);
            """)
            
            print("✅ Impersonate table created manually!")
            return True
            
    except Exception as e:
        print(f"❌ Error creating table manually: {e}")
        return False

def disable_impersonate_temporarily():
    """Provide instructions to temporarily disable impersonate."""
    print("\n🚨 TEMPORARY WORKAROUND:")
    print("If the issue persists, you can temporarily disable impersonate:")
    print("\n1. Comment out these lines in settings.py:")
    print("   # 'impersonate',  # in INSTALLED_APPS")
    print("   # 'impersonate.middleware.ImpersonateMiddleware',  # in MIDDLEWARE")
    print("\n2. Comment out this line in urls.py:")
    print("   # path('impersonate/', include('impersonate.urls')),")
    print("\n3. Restart your cPanel application")
    print("\n4. Re-enable after fixing the database issue")

def main():
    """Main function to fix impersonate issues."""
    print("🔧 Django Impersonate Fix for cPanel")
    print("=" * 45)
    
    # Setup Django
    try:
        setup_django()
        print("✅ Django environment setup successful")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False
    
    # Check installation
    if not check_impersonate_installation():
        print("\n💡 SOLUTION: Install django-impersonate")
        print("   pip install django-impersonate")
        return False
    
    # Check INSTALLED_APPS
    if not check_installed_apps():
        print("\n💡 SOLUTION: Add 'impersonate' to INSTALLED_APPS in settings.py")
        return False
    
    # Check migrations
    migrations_exist = check_impersonate_migrations()
    
    if not migrations_exist:
        print("\n🔄 Attempting to fix migrations...")
        
        # Try running migrations
        if run_impersonate_migrations():
            # Verify migrations worked
            if check_impersonate_migrations():
                print("✅ Migrations successful!")
            else:
                print("⚠️  Migrations ran but table still missing")
                # Try manual creation
                if create_impersonate_table_manually():
                    print("✅ Manual table creation successful!")
                else:
                    print("❌ Manual table creation failed")
                    disable_impersonate_temporarily()
                    return False
        else:
            print("⚠️  Migration failed, trying manual creation...")
            if create_impersonate_table_manually():
                print("✅ Manual table creation successful!")
            else:
                print("❌ All attempts failed")
                disable_impersonate_temporarily()
                return False
    
    print("\n" + "=" * 45)
    print("🎉 Impersonate fix completed!")
    print("\n📋 Next steps:")
    print("1. Restart your cPanel application")
    print("2. Test the impersonate functionality")
    print("3. Check for any remaining errors")
    
    return True

if __name__ == '__main__':
    main()
