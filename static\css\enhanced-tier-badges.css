/**
 * Enhanced Tier Badges CSS
 * Ensures proper colors for Gold, Silver, Bronze, and Featured badges
 * with high specificity to override any conflicting styles
 */

/* Base tier badge styling with high specificity */
.badge.tier-badge,
.tier-badge.badge,
span.tier-badge,
.tier-badge {
    position: absolute !important;
    top: 0.5rem !important;
    left: 0.5rem !important;
    z-index: 15 !important;
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    padding: 0.35rem 0.65rem !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(5px) !important;
    pointer-events: none !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    line-height: 1 !important;
    white-space: nowrap !important;
}

/* Gold Tier Badge - Shiny Gold Color */
.badge.tier-badge.tier-gold,
.tier-badge.tier-gold,
span.tier-badge.tier-gold,
.tier-gold.badge,
.tier-gold {
    background: linear-gradient(135deg, #ffd700 0%, #ffb300 50%, #ff8f00 100%) !important;
    color: #1a1a1a !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3) !important;
    box-shadow: 
        0 4px 12px rgba(255, 215, 0, 0.4) !important,
        0 2px 6px rgba(255, 193, 7, 0.3) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
    border: 1px solid rgba(255, 215, 0, 0.6) !important;
}

.badge.tier-badge.tier-gold:before,
.tier-badge.tier-gold:before,
span.tier-badge.tier-gold:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
    border-radius: inherit;
    pointer-events: none;
}

/* Silver Tier Badge - Metallic Silver Color */
.badge.tier-badge.tier-silver,
.tier-badge.tier-silver,
span.tier-badge.tier-silver,
.tier-silver.badge,
.tier-silver {
    background: linear-gradient(135deg, #e8e8e8 0%, #c0c0c0 50%, #a8a8a8 100%) !important;
    color: #2c2c2c !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;
    box-shadow: 
        0 4px 12px rgba(192, 192, 192, 0.4) !important,
        0 2px 6px rgba(169, 169, 169, 0.3) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    border: 1px solid rgba(192, 192, 192, 0.6) !important;
}

.badge.tier-badge.tier-silver:before,
.tier-badge.tier-silver:before,
span.tier-badge.tier-silver:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.4) 50%, transparent 70%);
    border-radius: inherit;
    pointer-events: none;
}

/* Bronze Tier Badge - Rich Bronze Color */
.badge.tier-badge.tier-bronze,
.tier-badge.tier-bronze,
span.tier-badge.tier-bronze,
.tier-bronze.badge,
.tier-bronze {
    background: linear-gradient(135deg, #cd7f32 0%, #b8860b 50%, #a0522d 100%) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4) !important;
    box-shadow: 
        0 4px 12px rgba(205, 127, 50, 0.4) !important,
        0 2px 6px rgba(184, 134, 11, 0.3) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(205, 127, 50, 0.6) !important;
}

.badge.tier-badge.tier-bronze:before,
.tier-badge.tier-bronze:before,
span.tier-badge.tier-bronze:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    border-radius: inherit;
    pointer-events: none;
}

/* Featured Badge - Vibrant Green Color */
.badge.featured-badge,
.featured-badge.badge,
span.featured-badge,
.featured-badge,
.badge.tier-featured,
.tier-featured.badge,
span.tier-featured,
.tier-featured {
    background: linear-gradient(135deg, #28a745 0%, #20c997 50%, #17a2b8 100%) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    box-shadow: 
        0 4px 12px rgba(40, 167, 69, 0.4) !important,
        0 2px 6px rgba(32, 201, 151, 0.3) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(40, 167, 69, 0.6) !important;
    position: absolute !important;
    top: 0.5rem !important;
    right: 0.5rem !important;
    left: auto !important;
    z-index: 15 !important;
    font-size: 0.75rem !important;
    font-weight: 700 !important;
    padding: 0.35rem 0.65rem !important;
    border-radius: 0.375rem !important;
    backdrop-filter: blur(5px) !important;
    pointer-events: none !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    line-height: 1 !important;
    white-space: nowrap !important;
}

.badge.featured-badge:before,
.featured-badge:before,
.badge.tier-featured:before,
.tier-featured:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    border-radius: inherit;
    pointer-events: none;
}

/* Standard Tier Badge - Subtle Gray */
.badge.tier-badge.tier-standard,
.tier-badge.tier-standard,
span.tier-badge.tier-standard,
.tier-standard.badge,
.tier-standard {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 50%, #495057 100%) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    box-shadow: 
        0 4px 12px rgba(108, 117, 125, 0.3) !important,
        0 2px 6px rgba(90, 98, 104, 0.2) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(108, 117, 125, 0.5) !important;
}

/* Hover effects for interactive badges */
.tier-badge:hover,
.featured-badge:hover {
    transform: translateY(-1px) scale(1.05) !important;
    transition: all 0.2s ease !important;
}

.tier-gold:hover {
    box-shadow: 
        0 6px 16px rgba(255, 215, 0, 0.5) !important,
        0 3px 8px rgba(255, 193, 7, 0.4) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

.tier-silver:hover {
    box-shadow: 
        0 6px 16px rgba(192, 192, 192, 0.5) !important,
        0 3px 8px rgba(169, 169, 169, 0.4) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
}

.tier-bronze:hover {
    box-shadow: 
        0 6px 16px rgba(205, 127, 50, 0.5) !important,
        0 3px 8px rgba(184, 134, 11, 0.4) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.featured-badge:hover,
.tier-featured:hover {
    box-shadow: 
        0 6px 16px rgba(40, 167, 69, 0.5) !important,
        0 3px 8px rgba(32, 201, 151, 0.4) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* Dark mode compatibility */
[data-theme="dark"] .tier-badge,
[data-theme="dark"] .featured-badge,
.dark-mode .tier-badge,
.dark-mode .featured-badge {
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.4) !important,
        0 2px 6px rgba(0, 0, 0, 0.3) !important,
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .tier-badge,
    .featured-badge {
        font-size: 0.7rem !important;
        padding: 0.3rem 0.5rem !important;
        top: 0.25rem !important;
        left: 0.25rem !important;
        right: 0.25rem !important;
    }
}

@media (max-width: 576px) {
    .tier-badge,
    .featured-badge {
        font-size: 0.65rem !important;
        padding: 0.25rem 0.4rem !important;
    }
}

/* Ensure badges are visible in all contexts */
.list-group-item .tier-badge,
.list-group-item .featured-badge,
.directory-card .tier-badge,
.directory-card .featured-badge,
.company-logo-item .tier-badge,
.company-logo-item .featured-badge,
.featured-carousel-item .tier-badge,
.featured-carousel-item .featured-badge {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Animation for badge appearance */
.tier-badge,
.featured-badge {
    animation: badgeAppear 0.3s ease-out !important;
}

@keyframes badgeAppear {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}
