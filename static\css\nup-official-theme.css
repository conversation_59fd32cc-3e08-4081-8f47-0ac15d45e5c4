/**
 * NUP Official Theme CSS
 * Based on nupuganda.org design system
 * Implements the exact color hierarchy and distribution from the official website
 */

/* ===== CORE COLOR PALETTE ===== */
:root {
    /* Primary Colors - The Foundation */
    --color-primary-accent: #cf2e2e;        /* NUP Vivid Red - Strategic use only */
    --color-secondary-accent: #f7bd00;      /* NUP Strong Yellow/Gold - Decorative */
    --color-primary-background: #ffffff;     /* Neutral White - Dominant background */
    --color-secondary-background: #f7f7f7;   /* Light Gray - Section separators */
    --color-text-main: #242424;             /* Dark Charcoal - Main text & headings */
    --color-secondary-text: #797979;        /* Medium Gray - Meta info & captions */
    --color-footer-background: #252638;     /* New Dark Blue-Gray - Footer grounding */
    --color-dark-accent: #252638;           /* New Dark Blue-Gray - Replaces all blues/purples */

    /* Hover and Interactive States */
    --color-primary-accent-hover: #b82626;  /* Darker red for hover states */
    --color-secondary-accent-hover: #e6aa00; /* Darker yellow for hover states */
    --color-dark-accent-hover: #1e1f2e;     /* Darker blue-gray for hover states */

    /* Utility Colors */
    --color-border-light: #e5e5e5;          /* Light borders and dividers */
    --color-shadow: rgba(37, 38, 56, 0.1);  /* Subtle shadows with new color */
    --color-shadow-strong: rgba(37, 38, 56, 0.15); /* Stronger shadows for cards */

    /* Text visibility fixes */
    --color-text-on-white: #242424;         /* Dark text for white backgrounds */
    --color-text-on-light: #242424;         /* Dark text for light backgrounds */
}

/* ===== TYPOGRAPHY FOUNDATION ===== */
/* Following NUP's typography hierarchy */
body {
    font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--color-text-main);
    background-color: var(--color-primary-background);
    line-height: 1.6;
    font-size: 16px;
}

/* Headings use Merriweather Sans for authority */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Merriweather Sans', 'Montserrat', 'Poppins', sans-serif;
    color: var(--color-text-main);
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.1rem; }

/* Body text and paragraphs */
p, li, span {
    color: var(--color-text-main);
    line-height: 1.6;
}

/* Secondary text for meta information */
.text-meta, .meta-info, .secondary-text {
    color: var(--color-secondary-text);
    font-size: 0.9rem;
}

/* ===== BUTTON SYSTEM ===== */
/* Primary CTA buttons - Strategic red usage */
.btn-nup-primary, .theme-btn, .btn-primary-cta {
    background-color: var(--color-primary-accent);
    color: var(--color-primary-background);
    border: 2px solid var(--color-primary-accent);
    font-weight: 700;
    font-family: 'Merriweather Sans', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 12px 24px;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.btn-nup-primary:hover, .theme-btn:hover, .btn-primary-cta:hover {
    background-color: var(--color-primary-accent-hover);
    border-color: var(--color-primary-accent-hover);
    color: var(--color-primary-background);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3);
}

/* Secondary buttons - White with red border */
.btn-nup-secondary, .btn-secondary-cta {
    background-color: var(--color-primary-background);
    color: var(--color-primary-accent);
    border: 2px solid var(--color-primary-accent);
    font-weight: 700;
    font-family: 'Merriweather Sans', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 12px 24px;
    border-radius: 4px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
}

.btn-nup-secondary:hover, .btn-secondary-cta:hover {
    background-color: var(--color-primary-accent);
    color: var(--color-primary-background);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3);
}

/* ===== SECTION BACKGROUNDS ===== */
/* 80% Foundation - Clean and uncluttered */
.nup-section-white, .section-primary {
    background-color: var(--color-primary-background);
    padding: 80px 0;
}

.nup-section-light, .section-secondary {
    background-color: var(--color-secondary-background);
    padding: 80px 0;
}

/* Hero sections can use primary accent strategically */
.nup-hero-section, .hero-primary {
    background-color: var(--color-primary-background);
    padding: 100px 0;
    position: relative;
}

/* ===== CARD SYSTEM ===== */
/* News/Blog cards following NUP design */
.nup-card, .content-card {
    background-color: var(--color-primary-background);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--color-shadow-strong);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--color-border-light);
}

.nup-card:hover, .content-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px var(--color-shadow-strong);
}

.nup-card-date, .card-date {
    background-color: var(--color-primary-accent);
    color: var(--color-primary-background);
    padding: 8px 12px;
    font-weight: 700;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nup-card-title, .card-title {
    color: var(--color-text-main);
    font-family: 'Merriweather Sans', sans-serif;
    font-weight: 700;
    margin: 16px 0 8px 0;
    line-height: 1.3;
}

.nup-card-meta, .card-meta {
    color: var(--color-secondary-text);
    font-size: 0.9rem;
    margin-bottom: 12px;
}

/* ===== ACCENT ELEMENTS ===== */
/* 15% Primary Accent Usage - Strategic red highlights */
.nup-accent-text, .highlight-primary {
    color: var(--color-primary-accent);
    font-weight: 700;
}

.nup-accent-border, .border-primary {
    border-color: var(--color-primary-accent);
}

.nup-accent-bg, .bg-primary-accent {
    background-color: var(--color-primary-accent);
    color: var(--color-primary-background);
}

/* 5% Secondary Accent Usage - Decorative yellow/gold */
.nup-gold-accent, .accent-secondary {
    color: var(--color-secondary-accent);
}

.nup-gold-bg, .bg-secondary-accent {
    background-color: var(--color-secondary-accent);
    color: var(--color-text-main);
}

/* Icon styling with gold accent */
.nup-icon-gold, .icon-accent {
    color: var(--color-secondary-accent);
    font-size: 1.2rem;
}

/* ===== LINK SYSTEM ===== */
a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--color-primary-accent);
    text-decoration: none;
}

/* Navigation links */
.nav-link {
    color: var(--color-text-main);
    font-weight: 600;
    transition: color 0.3s ease;
}

.nav-link:hover, .nav-link.active {
    color: var(--color-primary-accent);
}

/* ===== FOOTER SYSTEM ===== */
.nup-footer, .site-footer, .main-footer {
    background-color: var(--color-footer-background);
    color: var(--color-primary-background);
    padding: 60px 0 30px 0;
}

.nup-footer h3, .nup-footer h4, .nup-footer h5,
.main-footer h3, .main-footer h4, .main-footer h5 {
    color: var(--color-primary-background);
    margin-bottom: 20px;
}

.nup-footer p, .nup-footer li,
.main-footer p, .main-footer li {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.nup-footer a, .main-footer a {
    color: rgba(255, 255, 255, 0.9);
    transition: color 0.3s ease;
}

.nup-footer a:hover, .main-footer a:hover {
    color: var(--color-primary-accent);
}

/* Fix white text visibility on white backgrounds */
.text-white, .text-light {
    color: var(--color-text-main) !important;
}

/* Ensure proper text contrast */
.bg-white .text-white,
.bg-light .text-white,
[style*="background-color: white"] .text-white,
[style*="background-color: #fff"] .text-white,
[style*="background: white"] .text-white,
[style*="background: #fff"] .text-white {
    color: var(--color-text-main) !important;
}

/* ===== FORM ELEMENTS ===== */
.form-control, input, textarea, select {
    border: 2px solid var(--color-border-light);
    border-radius: 4px;
    padding: 12px 16px;
    font-size: 16px;
    color: var(--color-text-main);
    background-color: var(--color-primary-background);
    transition: border-color 0.3s ease;
}

.form-control:focus, input:focus, textarea:focus, select:focus {
    border-color: var(--color-primary-accent);
    outline: none;
    box-shadow: 0 0 0 3px rgba(207, 46, 46, 0.1);
}

.form-control::placeholder, input::placeholder, textarea::placeholder {
    color: var(--color-secondary-text);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .nup-section-white, .nup-section-light, .nup-hero-section {
        padding: 40px 0;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    
    .btn-nup-primary, .btn-nup-secondary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}
