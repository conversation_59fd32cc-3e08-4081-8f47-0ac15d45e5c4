"""
LLM Response Caching System
Optimizes LLM response times by caching similar queries and responses.
"""

import hashlib
import json
import time
import zlib
from typing import Dict, Any, Optional, List
from django.core.cache import cache
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class LLMCache:
    """
    Intelligent caching system for LLM responses.
    Uses content-based cache keys and compressed storage.
    """

    # Cache configuration
    DEFAULT_TTL = getattr(settings, 'LLM_CACHE_TTL', 3600)  # 1 hour default
    CACHE_KEY_PREFIX = 'llm_cache'
    SIMILARITY_THRESHOLD = 0.85  # Threshold for considering queries similar
    MAX_CACHE_SIZE = getattr(settings, 'LLM_CACHE_MAX_SIZE', 1000)

    @classmethod
    def _generate_cache_key(cls, assistant_id: int, messages: List[Dict],
                           temperature: float, max_tokens: int) -> str:
        """
        Generate a deterministic cache key based on input parameters.
        Uses content hashing for consistent keys across similar requests.
        """
        # Create a normalized representation of the input
        cache_data = {
            'assistant_id': assistant_id,
            'messages': cls._normalize_messages(messages),
            'temperature': round(temperature, 2),  # Round to avoid float precision issues
            'max_tokens': max_tokens
        }

        # Create hash of the normalized data
        content_str = json.dumps(cache_data, sort_keys=True, separators=(',', ':'))
        content_hash = hashlib.sha256(content_str.encode()).hexdigest()[:16]

        return f"{cls.CACHE_KEY_PREFIX}:{content_hash}"

    @classmethod
    def _normalize_messages(cls, messages: List[Dict]) -> List[Dict]:
        """
        Normalize messages for consistent caching.
        Removes timestamps and other variable data.
        """
        normalized = []
        for msg in messages:
            if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                # Only include essential fields for caching
                normalized_msg = {
                    'role': msg['role'],
                    'content': msg['content'].strip()
                }
                # Include name if present (for function calls)
                if 'name' in msg:
                    normalized_msg['name'] = msg['name']
                normalized.append(normalized_msg)
        return normalized

    @classmethod
    def _compress_data(cls, data: Dict[str, Any]) -> bytes:
        """Compress response data for efficient storage."""
        json_str = json.dumps(data, separators=(',', ':'))
        return zlib.compress(json_str.encode('utf-8'))

    @classmethod
    def _decompress_data(cls, compressed_data: bytes) -> Dict[str, Any]:
        """Decompress stored response data."""
        json_str = zlib.decompress(compressed_data).decode('utf-8')
        return json.loads(json_str)

    @classmethod
    def get_cached_response(cls, assistant_id: int, messages: List[Dict],
                          temperature: float, max_tokens: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached response if available.

        Returns:
            Cached response dict or None if not found
        """
        try:
            cache_key = cls._generate_cache_key(assistant_id, messages, temperature, max_tokens)
            compressed_data = cache.get(cache_key)

            if compressed_data is not None:
                response_data = cls._decompress_data(compressed_data)

                # Add cache hit metadata
                response_data['cached'] = True
                response_data['cache_hit_time'] = time.time()

                logger.info(f"LLM cache hit for assistant {assistant_id}")
                return response_data

        except Exception as e:
            logger.warning(f"Error retrieving from LLM cache: {e}")

        return None

    @classmethod
    def cache_response(cls, assistant_id: int, messages: List[Dict],
                      temperature: float, max_tokens: int,
                      response_data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        Cache an LLM response.

        Args:
            assistant_id: ID of the assistant
            messages: Input messages
            temperature: Model temperature
            max_tokens: Max tokens setting
            response_data: Response to cache
            ttl: Time to live in seconds (uses default if None)

        Returns:
            True if successfully cached, False otherwise
        """
        try:
            cache_key = cls._generate_cache_key(assistant_id, messages, temperature, max_tokens)

            # Prepare data for caching (remove non-essential fields)
            cache_data = {
                'content': response_data.get('content', ''),
                'token_count': response_data.get('token_count', 0),
                'model': response_data.get('model', ''),
                'cached_at': time.time(),
                'assistant_id': assistant_id
            }

            # Compress and store
            compressed_data = cls._compress_data(cache_data)
            cache_ttl = ttl or cls.DEFAULT_TTL

            cache.set(cache_key, compressed_data, cache_ttl)
            logger.info(f"Cached LLM response for assistant {assistant_id}")
            return True

        except Exception as e:
            logger.warning(f"Error caching LLM response: {e}")
            return False

    @classmethod
    def invalidate_assistant_cache(cls, assistant_id: int) -> None:
        """
        Invalidate all cached responses for a specific assistant.
        Useful when assistant configuration changes.
        """
        try:
            # Since we can't easily iterate cache keys, we'll use a pattern-based approach
            # This is a simplified version - in production you might want a more sophisticated approach
            cache_pattern = f"{cls.CACHE_KEY_PREFIX}:*"

            # For now, we'll just log the invalidation request
            # In a production environment with Redis, you could use SCAN to find and delete keys
            logger.info(f"Cache invalidation requested for assistant {assistant_id}")

            # Alternative: Use a version-based approach for future cache keys
            version_key = f"{cls.CACHE_KEY_PREFIX}:version:{assistant_id}"
            current_version = cache.get(version_key, 0)
            cache.set(version_key, current_version + 1, None)  # Never expires

            logger.info(f"Invalidated cache for assistant {assistant_id}")

        except Exception as e:
            logger.warning(f"Error invalidating assistant cache: {e}")

    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """
        Get cache statistics for monitoring.

        Returns:
            Dictionary with cache statistics
        """
        try:
            # This is a simplified version - in production you might want more detailed stats
            stats = {
                'cache_backend': cache.__class__.__name__,
                'default_ttl': cls.DEFAULT_TTL,
                'max_cache_size': cls.MAX_CACHE_SIZE,
                'timestamp': time.time()
            }
            return stats

        except Exception as e:
            logger.warning(f"Error getting cache stats: {e}")
            return {'error': str(e)}


class SmartLLMCache(LLMCache):
    """
    Enhanced LLM cache with similarity detection and intelligent prefetching.
    """

    @classmethod
    def find_similar_cached_response(cls, assistant_id: int, messages: List[Dict],
                                   temperature: float, max_tokens: int) -> Optional[Dict[str, Any]]:
        """
        Find similar cached responses using content similarity.
        This is a simplified version - in production you might use embeddings.
        """
        try:
            # First try exact match
            exact_match = cls.get_cached_response(assistant_id, messages, temperature, max_tokens)
            if exact_match:
                return exact_match

            # For now, we'll implement a simple text similarity check
            # In production, you might use embeddings or more sophisticated similarity
            current_content = ' '.join([msg.get('content', '') for msg in messages if msg.get('role') == 'user'])

            # This is a placeholder for similarity search
            # In a real implementation, you'd store and search through cached queries
            logger.debug(f"No similar cached response found for assistant {assistant_id}")
            return None

        except Exception as e:
            logger.warning(f"Error finding similar cached response: {e}")
            return None


# Convenience functions for easy integration
def get_cached_llm_response(assistant_id: int, messages: List[Dict],
                          temperature: float = 0.7, max_tokens: int = 1000) -> Optional[Dict[str, Any]]:
    """Convenience function to get cached LLM response."""
    return LLMCache.get_cached_response(assistant_id, messages, temperature, max_tokens)


def cache_llm_response(assistant_id: int, messages: List[Dict], response_data: Dict[str, Any],
                      temperature: float = 0.7, max_tokens: int = 1000, ttl: Optional[int] = None) -> bool:
    """Convenience function to cache LLM response."""
    return LLMCache.cache_response(assistant_id, messages, temperature, max_tokens, response_data, ttl)


def invalidate_assistant_llm_cache(assistant_id: int) -> None:
    """Convenience function to invalidate assistant cache."""
    LLMCache.invalidate_assistant_cache(assistant_id)
