/**
 * Navigation Message Scroll Test
 * This file contains test functions to verify that navigation messages
 * properly display scroll bars when content exceeds page length
 */

// Test function to create a long navigation message
function testNavigationMessageScroll() {
    console.log('Testing navigation message scroll functionality...');
    
    // Create a very long content string to test scrolling
    const longContent = `
        <h2>Test Navigation Content</h2>
        <p>This is a test of the navigation message scroll functionality. This content is intentionally very long to test whether scroll bars appear when the content exceeds the page length.</p>
        
        <h3>Section 1: Introduction</h3>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        
        <h3>Section 2: Details</h3>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        
        <h3>Section 3: More Information</h3>
        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
        
        <h3>Section 4: Additional Content</h3>
        <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
        
        <h3>Section 5: Even More Content</h3>
        <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.</p>
        
        <h3>Section 6: Continuing</h3>
        <p>Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur?</p>
        
        <h3>Section 7: More Text</h3>
        <p>Quis autem vel eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem eum fugiat quo voluptas nulla pariatur?</p>
        
        <h3>Section 8: Additional Information</h3>
        <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>
        
        <h3>Section 9: More Details</h3>
        <p>Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga. Et harum quidem rerum facilis est et expedita distinctio.</p>
        
        <h3>Section 10: Final Section</h3>
        <p>Nam libero tempore, cum soluta nobis est eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas assumenda est, omnis dolor repellendus.</p>
        
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Column 1</th>
                    <th>Column 2</th>
                    <th>Column 3</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Row 1, Cell 1</td>
                    <td>Row 1, Cell 2</td>
                    <td>Row 1, Cell 3</td>
                </tr>
                <tr>
                    <td>Row 2, Cell 1</td>
                    <td>Row 2, Cell 2</td>
                    <td>Row 2, Cell 3</td>
                </tr>
                <tr>
                    <td>Row 3, Cell 1</td>
                    <td>Row 3, Cell 2</td>
                    <td>Row 3, Cell 3</td>
                </tr>
            </tbody>
        </table>
        
        <p>This is the end of the test content. If the scroll functionality is working correctly, you should see a scroll bar on this message when it exceeds the page height.</p>
    `;
    
    // Check if we have the navigation handler's addMessage function
    if (typeof window.showNavContent === 'function') {
        // Use the navigation handler to add the message
        console.log('Using navigation handler to add test message...');
        
        // Create a test message using the navigation handler's addMessage function
        const chatBox = document.getElementById('chat-box');
        if (chatBox) {
            // Create message div
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message', 'assistant-message', 'nav-content-bubble');
            
            // Create message content span
            const messageContentSpan = document.createElement('span');
            messageContentSpan.classList.add('message-content', 'tinymce-content', 'nav-content-bubble');
            messageContentSpan.innerHTML = longContent;
            
            // Create avatar container
            const avatarContainer = document.createElement('div');
            avatarContainer.classList.add('chat-avatar-container', 'me-2');
            
            // Create avatar element
            const avatarElement = document.createElement('div');
            avatarElement.classList.add('chat-avatar-icon');
            const iconElement = document.createElement('i');
            iconElement.classList.add('bi', 'bi-robot');
            avatarElement.appendChild(iconElement);
            avatarContainer.appendChild(avatarElement);
            
            // Assemble the message
            messageDiv.appendChild(avatarContainer);
            messageDiv.appendChild(messageContentSpan);
            
            // Add to chat box
            chatBox.appendChild(messageDiv);
            
            // Scroll to the message
            messageDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
            
            console.log('Test navigation message added successfully!');
            console.log('Check if the message content has a scroll bar when it exceeds the page height.');
        } else {
            console.error('Chat box not found!');
        }
    } else {
        console.error('Navigation handler not available!');
    }
}

// Function to test scroll behavior on different screen sizes
function testScrollOnDifferentSizes() {
    console.log('Testing scroll behavior on different screen sizes...');
    
    // Get current viewport dimensions
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    
    console.log(`Current viewport: ${viewportWidth}x${viewportHeight}`);
    
    // Check if we're on mobile, tablet, or desktop
    if (viewportWidth <= 768) {
        console.log('Mobile viewport detected - max-height should be calc(100vh - 250px)');
    } else if (viewportWidth <= 1024) {
        console.log('Tablet viewport detected - max-height should be calc(100vh - 280px)');
    } else {
        console.log('Desktop viewport detected - max-height should be calc(100vh - 300px)');
    }
    
    // Check if navigation message scroll CSS is loaded
    const navScrollCSS = document.querySelector('link[href*="navigation-message-scroll.css"]');
    if (navScrollCSS) {
        console.log('Navigation message scroll CSS is loaded');
    } else {
        console.warn('Navigation message scroll CSS may not be loaded');
    }
}

// Function to check if CSS classes are properly applied
function checkCSSClasses() {
    console.log('Checking CSS classes on navigation messages...');
    
    const navMessages = document.querySelectorAll('.nav-content-bubble');
    console.log(`Found ${navMessages.length} navigation messages`);
    
    navMessages.forEach((message, index) => {
        console.log(`Message ${index + 1}:`);
        console.log('  - Classes:', message.className);
        console.log('  - Has nav-content-bubble class:', message.classList.contains('nav-content-bubble'));
        
        // Check computed styles
        const computedStyle = window.getComputedStyle(message);
        console.log('  - Max height:', computedStyle.maxHeight);
        console.log('  - Overflow Y:', computedStyle.overflowY);
        console.log('  - Overflow X:', computedStyle.overflowX);
    });
}

// Export test functions to global scope
window.testNavigationMessageScroll = testNavigationMessageScroll;
window.testScrollOnDifferentSizes = testScrollOnDifferentSizes;
window.checkCSSClasses = checkCSSClasses;

// Auto-run tests when DOM is loaded (for debugging)
document.addEventListener('DOMContentLoaded', function() {
    console.log('Navigation scroll test functions are available:');
    console.log('- testNavigationMessageScroll(): Creates a long test message');
    console.log('- testScrollOnDifferentSizes(): Checks viewport-specific behavior');
    console.log('- checkCSSClasses(): Inspects CSS classes on existing messages');
    
    // Automatically check CSS classes if navigation messages exist
    setTimeout(checkCSSClasses, 1000);
    setTimeout(testScrollOnDifferentSizes, 1000);
});
