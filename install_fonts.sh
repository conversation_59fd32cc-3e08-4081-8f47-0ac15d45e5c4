#!/bin/bash
# Font Installation Script for cPanel Linux
# Run this script to install fonts system-wide

echo "Installing fonts for cPanel Linux..."

# Create user fonts directory
mkdir -p ~/.fonts
mkdir -p ~/fonts
mkdir -p ~/public_html/fonts

# Copy fonts to user directory
if [ -d "static/fonts/system" ]; then
    cp static/fonts/system/*.ttf ~/.fonts/ 2>/dev/null || true
    cp static/fonts/system/*.ttf ~/fonts/ 2>/dev/null || true
    cp static/fonts/system/*.ttf ~/public_html/fonts/ 2>/dev/null || true
    echo "Fonts copied to user directories"
fi

# Update font cache (if fc-cache is available)
if command -v fc-cache &> /dev/null; then
    fc-cache -f -v ~/.fonts
    echo "Font cache updated"
else
    echo "fc-cache not available, fonts may need manual refresh"
fi

# Set proper permissions
chmod 644 ~/.fonts/*.ttf 2>/dev/null || true
chmod 644 ~/fonts/*.ttf 2>/dev/null || true
chmod 644 ~/public_html/fonts/*.ttf 2>/dev/null || true

echo "Font installation complete!"
echo "Available fonts:"
if command -v fc-list &> /dev/null; then
    fc-list | grep -E "(Liberation|DejaVu)" | head -10
else
    ls -la ~/.fonts/
fi
