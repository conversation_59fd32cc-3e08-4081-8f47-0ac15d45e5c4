"""
WSGI config for company_assistant project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/howto/deployment/wsgi/
"""

# Load .env file as early as possible
from dotenv import load_dotenv
load_dotenv()

import os
import sys
from django.core.wsgi import get_wsgi_application

# Ensure UTF-8 encoding for WSGI environment
if hasattr(sys.stdout, 'encoding') and sys.stdout.encoding != 'utf-8':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
if hasattr(sys.stderr, 'encoding') and sys.stderr.encoding != 'utf-8':
    import codecs
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# Set environment variables for UTF-8
os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
os.environ.setdefault('LC_ALL', 'en_US.UTF-8')
os.environ.setdefault('LANG', 'en_US.UTF-8')

# load_dotenv() # Moved to top
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

application = get_wsgi_application()
