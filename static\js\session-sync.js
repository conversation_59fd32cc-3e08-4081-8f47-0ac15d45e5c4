/**
 * Session synchronization script to handle logout across tabs/windows
 */

class SessionSync {
    constructor() {
        this.checkInterval = 30000; // Check every 30 seconds
        this.lastActivity = Date.now();
        this.isAuthenticated = document.body.dataset.authenticated === 'true';
        this.csrfToken = this.getCSRFToken();
        
        if (this.isAuthenticated) {
            this.init();
        }
    }
    
    init() {
        // Start session monitoring
        this.startSessionMonitoring();
        
        // Listen for storage events (logout in other tabs)
        window.addEventListener('storage', (e) => {
            if (e.key === 'user_logged_out' && e.newValue === 'true') {
                this.handleLogoutEvent();
            }
        });
        
        // Listen for beforeunload to clean up
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // Track user activity
        this.trackActivity();
    }
    
    startSessionMonitoring() {
        setInterval(() => {
            this.checkSessionStatus();
        }, this.checkInterval);
    }
    
    async checkSessionStatus() {
        try {
            const response = await fetch('/accounts/api/check-session-status/', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': this.csrfToken
                },
                credentials: 'same-origin'
            });
            
            if (response.status === 401 || response.status === 403) {
                // Session is invalid
                this.handleSessionExpired();
            } else if (response.ok) {
                const data = await response.json();
                if (!data.authenticated) {
                    this.handleSessionExpired();
                }
            }
        } catch (error) {
            console.warn('Session check failed:', error);
        }
    }
    
    handleSessionExpired() {
        // Mark as logged out in localStorage
        localStorage.setItem('user_logged_out', 'true');
        
        // Show notification
        this.showLogoutNotification();
        
        // Redirect to login after a short delay
        setTimeout(() => {
            window.location.href = '/accounts/login/';
        }, 2000);
    }
    
    handleLogoutEvent() {
        // Another tab logged out
        this.showLogoutNotification('You have been logged out in another tab.');
        
        // Redirect to login
        setTimeout(() => {
            window.location.href = '/accounts/login/';
        }, 2000);
    }
    
    showLogoutNotification(message = 'Your session has expired. Redirecting to login...') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <strong>Session Expired</strong><br>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    trackActivity() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.lastActivity = Date.now();
            }, { passive: true });
        });
    }
    
    getCSRFToken() {
        // Try to get CSRF token from various sources
        let token = null;
        
        // From meta tag
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
            token = metaTag.getAttribute('content');
        }
        
        // From cookie
        if (!token) {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    token = value;
                    break;
                }
            }
        }
        
        // From hidden input (if on a form page)
        if (!token) {
            const hiddenInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
            if (hiddenInput) {
                token = hiddenInput.value;
            }
        }
        
        return token;
    }
    
    cleanup() {
        // Clean up localStorage on normal page unload
        localStorage.removeItem('user_logged_out');
    }
    
    // Method to manually trigger logout sync
    static triggerLogout() {
        localStorage.setItem('user_logged_out', 'true');
    }
}

// Initialize session sync when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.sessionSync = new SessionSync();
});

// Enhance logout forms to trigger sync
document.addEventListener('DOMContentLoaded', () => {
    const logoutForms = document.querySelectorAll('form[action*="logout"]');
    
    logoutForms.forEach(form => {
        form.addEventListener('submit', () => {
            // Trigger logout sync before form submission
            SessionSync.triggerLogout();
        });
    });
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SessionSync;
}
