#!/usr/bin/env python
"""
Test script for cPanel email configuration.
Verifies that Django email settings match the cPanel configuration.
"""

import os
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    from django.conf import settings
    from django.core.mail import send_mail
    
    print("📧 cPanel Email Configuration Test")
    print("=" * 50)
    
    # Display current configuration
    print("✅ Current Django Email Settings:")
    print(f"  EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"  EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"  EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"  EMAIL_USE_SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    print(f"  EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', False)}")
    print(f"  EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"  DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    print(f"  EMAIL_TIMEOUT: {getattr(settings, 'EMAIL_TIMEOUT', 'Not set')}")
    
    # Verify cPanel configuration
    print("\n🔍 cPanel Configuration Verification:")
    
    # Check server settings
    if settings.EMAIL_HOST == 'mail.24seven.site':
        print("  ✅ Email host correctly set to mail.24seven.site")
    else:
        print(f"  ❌ Email host should be 'mail.24seven.site', got '{settings.EMAIL_HOST}'")
    
    # Check port settings
    if settings.EMAIL_PORT == 465:
        print("  ✅ SMTP port correctly set to 465 (SSL)")
    else:
        print(f"  ❌ SMTP port should be 465, got {settings.EMAIL_PORT}")
    
    # Check SSL settings
    if getattr(settings, 'EMAIL_USE_SSL', False):
        print("  ✅ SSL correctly enabled")
    else:
        print("  ❌ SSL should be enabled for cPanel")
    
    # Check TLS settings
    if not getattr(settings, 'EMAIL_USE_TLS', True):
        print("  ✅ TLS correctly disabled (using SSL)")
    else:
        print("  ⚠️  TLS should be disabled when using SSL")
    
    # Check username
    if settings.EMAIL_HOST_USER == '<EMAIL>':
        print("  ✅ Email username correctly <NAME_EMAIL>")
    else:
        print(f"  ❌ Email username should be '<EMAIL>', got '{settings.EMAIL_HOST_USER}'")
    
    # Check from email
    if '<EMAIL>' in settings.DEFAULT_FROM_EMAIL:
        print("  ✅ Default from <NAME_EMAIL>")
    else:
        print(f"  ❌ Default from email should contain '<EMAIL>', got '{settings.DEFAULT_FROM_EMAIL}'")
    
    print("\n📋 cPanel Email Server Details:")
    print("  📥 Incoming Server: mail.24seven.site")
    print("     - IMAP Port: 993 (SSL)")
    print("     - POP3 Port: 995 (SSL)")
    print("  📤 Outgoing Server: mail.24seven.site")
    print("     - SMTP Port: 465 (SSL/TLS)")
    print("  🔐 Authentication: Required for all protocols")
    
    print("\n🌐 Calendar & Contacts:")
    print("  📅 CalDAV: https://mail.24seven.site:2080/calendars/<EMAIL>/calendar")
    print("  📇 CardDAV: https://mail.24seven.site:2080/addressbooks/<EMAIL>/addressbook")
    
    # Test email connection (without sending)
    print("\n🔧 Connection Test:")
    try:
        from django.core.mail import get_connection
        connection = get_connection()
        
        # Check if password is set
        if not settings.EMAIL_HOST_PASSWORD:
            print("  ⚠️  EMAIL_HOST_PASSWORD not set - update your .env file")
            print("     Add: EMAIL_HOST_PASSWORD=your-actual-email-password")
        else:
            print("  ✅ EMAIL_HOST_PASSWORD is set")
            
            # Try to open connection (this will test authentication)
            try:
                connection.open()
                print("  ✅ SMTP connection successful!")
                connection.close()
            except Exception as e:
                print(f"  ❌ SMTP connection failed: {e}")
                print("     Check your email password and cPanel email account")
    
    except Exception as e:
        print(f"  ❌ Connection test error: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Next Steps:")
    print("1. Create '<EMAIL>' email account in cPanel")
    print("2. Update EMAIL_HOST_PASSWORD in your .env file")
    print("3. Test email sending with: python manage.py shell")
    print("   >>> from django.core.mail import send_mail")
    print("   >>> send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])")
    
    # Check environment file
    if os.path.exists('.env'):
        print("\n📄 .env File Status:")
        with open('.env', 'r') as f:
            env_content = f.read()
            if 'EMAIL_HOST_PASSWORD=your-actual-email-password' in env_content:
                print("  ⚠️  Please update EMAIL_HOST_PASSWORD in .env file")
            elif 'EMAIL_HOST_PASSWORD=' in env_content:
                print("  ✅ EMAIL_HOST_PASSWORD found in .env file")
            else:
                print("  ❌ EMAIL_HOST_PASSWORD not found in .env file")

except Exception as e:
    print(f"❌ Error testing email configuration: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
