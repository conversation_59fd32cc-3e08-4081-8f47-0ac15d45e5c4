#!/usr/bin/env python
"""
Comprehensive script to update all URLs for cPanel production deployment.
This script updates:
1. Django Site framework domain
2. ALLOWED_HOSTS in settings.py
3. CSRF_TRUSTED_ORIGINS in settings.py
4. Email utilities for production domain handling
5. Environment variables for cPanel
6. Passenger WSGI configuration
"""

import os
import sys
import django
import re
from pathlib import Path

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.sites.models import Site

def update_django_site(new_domain, new_name=None):
    """Update the Django Site framework domain."""
    try:
        site = Site.objects.get_current()
        old_domain = site.domain
        old_name = site.name

        site.domain = new_domain
        site.name = new_name if new_name else new_domain
        site.save()

        print(f"✅ Django Site updated:")
        print(f"   Old domain: {old_domain}")
        print(f"   New domain: {site.domain}")
        print(f"   Old name: {old_name}")
        print(f"   New name: {site.name}")

        return True
    except Exception as e:
        print(f"❌ Error updating Django Site: {e}")
        return False

def update_settings_file(new_domain):
    """Update domains in settings.py for cPanel production."""
    settings_path = Path('company_assistant/settings.py')

    if not settings_path.exists():
        print(f"❌ Settings file not found: {settings_path}")
        return False

    try:
        with open(settings_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Update ALLOWED_HOSTS
        allowed_hosts_pattern = r"ALLOWED_HOSTS = \[([^\]]+)\]"
        new_allowed_hosts = f"ALLOWED_HOSTS = ['*', '{new_domain}', 'www.{new_domain}', 'localhost', '127.0.0.1']"
        content = re.sub(allowed_hosts_pattern, new_allowed_hosts, content)

        # Update CSRF_TRUSTED_ORIGINS
        csrf_pattern = r"CSRF_TRUSTED_ORIGINS = \[([^\]]+)\]"
        new_csrf_origins = f"CSRF_TRUSTED_ORIGINS = ['http://localhost:8000', 'http://127.0.0.1:8000', 'https://{new_domain}', 'http://{new_domain}', 'https://www.{new_domain}', 'http://www.{new_domain}']"
        content = re.sub(csrf_pattern, new_csrf_origins, content)

        # Write back if changes were made
        if content != original_content:
            with open(settings_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Updated settings.py with domain: {new_domain}")
            return True
        else:
            print(f"ℹ️  No changes needed in settings.py")
            return True

    except Exception as e:
        print(f"❌ Error updating settings.py: {e}")
        return False

def update_email_utils(new_domain):
    """Update email utilities fallback domain."""
    email_utils_path = Path('accounts/email_utils.py')

    if not email_utils_path.exists():
        print(f"❌ Email utils file not found: {email_utils_path}")
        return False

    try:
        with open(email_utils_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Update the fallback domain in get_site_url function (line 41)
        fallback_pattern = r'site_url = "https://24seven\.site"'
        new_fallback = f'site_url = "https://{new_domain}"'
        content = re.sub(fallback_pattern, new_fallback, content)

        # Also update any other hardcoded 24seven.site references
        content = re.sub(r'24seven\.site', new_domain, content)

        # Write back if changes were made
        if content != original_content:
            with open(email_utils_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Updated email_utils.py with domain: {new_domain}")
            return True
        else:
            print(f"ℹ️  No changes needed in email_utils.py")
            return True

    except Exception as e:
        print(f"❌ Error updating email_utils.py: {e}")
        return False

def update_passenger_wsgi(new_domain):
    """Update passenger_wsgi.py for cPanel production."""
    wsgi_path = Path('passenger_wsgi.py')

    if not wsgi_path.exists():
        print(f"❌ passenger_wsgi.py not found")
        return False

    try:
        with open(wsgi_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Ensure cPanel environment variables are set
        if 'CPANEL_ENV' not in content:
            # Add cPanel environment setup
            env_setup = '''
# Set cPanel environment variables
os.environ['CPANEL_ENV'] = 'True'
os.environ['PRODUCTION'] = 'True'
os.environ['DEBUG'] = 'False'

'''
            # Insert after the imports but before Django setup
            django_setup_pattern = r"(os\.environ\.setdefault\('DJANGO_SETTINGS_MODULE')"
            content = re.sub(django_setup_pattern, env_setup + r'\1', content)

        # Write back if changes were made
        if content != original_content:
            with open(wsgi_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Updated passenger_wsgi.py for cPanel production")
            return True
        else:
            print(f"ℹ️  passenger_wsgi.py already configured")
            return True

    except Exception as e:
        print(f"❌ Error updating passenger_wsgi.py: {e}")
        return False

def set_force_production_domain():
    """Set environment variable to force production domain even in DEBUG mode."""
    try:
        os.environ['FORCE_PRODUCTION_DOMAIN'] = 'True'
        print("✅ Set FORCE_PRODUCTION_DOMAIN=True for this session")
        return True
    except Exception as e:
        print(f"❌ Error setting FORCE_PRODUCTION_DOMAIN: {e}")
        return False

def create_cpanel_env_file(new_domain):
    """Create .env file for cPanel deployment."""
    env_content = f"""# cPanel Production Environment Variables
DEBUG=False
CPANEL_ENV=True
PRODUCTION=True

# Domain Configuration
ALLOWED_HOSTS={new_domain},www.{new_domain}

# Security (UPDATE THESE WITH YOUR ACTUAL VALUES)
SECRET_KEY=your-secret-key-here

# Database (UPDATE THESE WITH YOUR ACTUAL CREDENTIALS)
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=5432

# Email Configuration (UPDATE WITH YOUR EMAIL SETTINGS)
EMAIL_HOST=mail.{new_domain}
EMAIL_PORT=465
EMAIL_USE_SSL=True
EMAIL_HOST_USER=system@{new_domain}
EMAIL_HOST_PASSWORD=your_email_password
DEFAULT_FROM_EMAIL={new_domain} <system@{new_domain}>

# Performance Optimization for cPanel
CACHE_TIMEOUT=3600
SESSION_COOKIE_AGE=86400
DATA_UPLOAD_MAX_MEMORY_SIZE=5242880
FILE_UPLOAD_MAX_MEMORY_SIZE=5242880
"""

    try:
        with open('.env.cpanel', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print(f"✅ Created .env.cpanel file with domain: {new_domain}")
        print(f"⚠️  IMPORTANT: Update the credentials in .env.cpanel before deployment!")
        return True
    except Exception as e:
        print(f"❌ Error creating .env.cpanel: {e}")
        return False

def create_development_env_file(new_domain):
    """Create or update .env file for development with FORCE_PRODUCTION_DOMAIN."""
    env_file = Path('.env')

    # Read existing .env file if it exists
    existing_content = ""
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                existing_content = f.read()
        except Exception as e:
            print(f"⚠️  Warning: Could not read existing .env file: {e}")

    # Check if FORCE_PRODUCTION_DOMAIN is already set
    if 'FORCE_PRODUCTION_DOMAIN' not in existing_content:
        # Add FORCE_PRODUCTION_DOMAIN to the file
        new_line = "\n# Force production domain even in DEBUG mode\nFORCE_PRODUCTION_DOMAIN=True\n"

        try:
            with open(env_file, 'a', encoding='utf-8') as f:
                f.write(new_line)
            print(f"✅ Added FORCE_PRODUCTION_DOMAIN=True to .env file")
            return True
        except Exception as e:
            print(f"❌ Error updating .env file: {e}")
            return False
    else:
        print(f"ℹ️  FORCE_PRODUCTION_DOMAIN already exists in .env file")
        return True

def show_deployment_instructions(new_domain):
    """Show cPanel deployment instructions."""
    print(f"\n" + "=" * 60)
    print("🚀 cPanel DEPLOYMENT INSTRUCTIONS")
    print("=" * 60)

    print(f"\n📋 STEP 1: Upload Files to cPanel")
    print(f"   - Upload all project files to your cPanel hosting")
    print(f"   - Ensure passenger_wsgi.py is in the root directory")
    print(f"   - Upload the .htaccess file")

    print(f"\n📋 STEP 2: Set Environment Variables in cPanel")
    print(f"   In your cPanel Python App settings, add these environment variables:")
    print(f"   CPANEL_ENV=True")
    print(f"   PRODUCTION=True")
    print(f"   DEBUG=False")
    print(f"   SECRET_KEY=your-actual-secret-key")
    print(f"   DB_NAME=your-actual-database-name")
    print(f"   DB_USER=your-actual-database-user")
    print(f"   DB_PASSWORD=your-actual-database-password")

    print(f"\n📋 STEP 3: Configure Database")
    print(f"   - Create a PostgreSQL database in cPanel")
    print(f"   - Update the database credentials in environment variables")
    print(f"   - Run migrations: python manage.py migrate")

    print(f"\n📋 STEP 4: Collect Static Files")
    print(f"   - Run: python manage.py collectstatic --noinput")

    print(f"\n📋 STEP 5: Create Superuser")
    print(f"   - Run: python manage.py createsuperuser")

    print(f"\n📋 STEP 6: Test Your Application")
    print(f"   - Visit: https://{new_domain}")
    print(f"   - Check admin: https://{new_domain}/admin/")
    print(f"   - Test email functionality")

    print(f"\n🔧 TROUBLESHOOTING:")
    print(f"   - Check error logs in cPanel")
    print(f"   - Verify file permissions")
    print(f"   - Ensure all dependencies are installed")
    print(f"   - Check that passenger_wsgi.py is executable")

def main():
    """Main function to update all URLs for cPanel deployment."""
    print("🚀 cPanel URL Update Script")
    print("=" * 60)

    # Get domain from command line arguments or use default
    if len(sys.argv) > 1:
        new_domain = sys.argv[1].strip()
    else:
        # Default to 24seven.site if no argument provided
        new_domain = "24seven.site"

    # Remove protocol and trailing slash if present
    new_domain = new_domain.replace('https://', '').replace('http://', '').rstrip('/')

    print(f"🎯 Target domain: {new_domain}")

    # Get site name from command line or use default
    if len(sys.argv) > 2:
        site_name = sys.argv[2].strip()
    else:
        # Default site name
        site_name = "24seven Platform" if new_domain == "24seven.site" else new_domain

    print(f"🏷️  Site name: {site_name}")
    print()

    # Set force production domain for this session
    print("1️⃣ Setting force production domain...")
    force_success = set_force_production_domain()

    # Update Django Site framework
    print("\n2️⃣ Updating Django Site framework...")
    site_success = update_django_site(new_domain, site_name)

    # Update settings.py
    print("\n3️⃣ Updating settings.py...")
    settings_success = update_settings_file(new_domain)

    # Update email_utils.py
    print("\n4️⃣ Updating email utilities...")
    email_success = update_email_utils(new_domain)

    # Update passenger_wsgi.py
    print("\n5️⃣ Updating passenger_wsgi.py...")
    wsgi_success = update_passenger_wsgi(new_domain)

    # Create cPanel environment file
    print("\n6️⃣ Creating cPanel environment file...")
    env_success = create_cpanel_env_file(new_domain)

    # Create/update development environment file
    print("\n7️⃣ Updating development environment file...")
    dev_env_success = create_development_env_file(new_domain)

    # Summary
    print("\n" + "=" * 60)
    print("📊 UPDATE SUMMARY")
    print("=" * 60)

    if force_success:
        print("✅ Force production domain set")
    else:
        print("❌ Force production domain setting failed")

    if site_success:
        print("✅ Django Site framework updated")
    else:
        print("❌ Django Site framework update failed")

    if settings_success:
        print("✅ Settings.py updated")
    else:
        print("❌ Settings.py update failed")

    if email_success:
        print("✅ Email utilities updated")
    else:
        print("❌ Email utilities update failed")

    if wsgi_success:
        print("✅ Passenger WSGI updated")
    else:
        print("❌ Passenger WSGI update failed")

    if env_success:
        print("✅ cPanel environment file created")
    else:
        print("❌ cPanel environment file creation failed")

    if dev_env_success:
        print("✅ Development environment file updated")
    else:
        print("❌ Development environment file update failed")

    if all([force_success, site_success, settings_success, email_success, wsgi_success, env_success, dev_env_success]):
        print("\n🎉 All updates completed successfully!")
        show_deployment_instructions(new_domain)
    else:
        print("\n⚠️  Some updates failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
