#!/usr/bin/env python3
"""
Comprehensive script to fix issues with existing companies and assistants.
This script addresses various data integrity and consistency issues.
"""

import os
import sys
import django
from django.db import transaction
from django.utils.text import slugify

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company
from assistants.models import Assistant
from django.contrib.auth.models import User


class CompanyAssistantFixer:
    """Fix various issues with companies and assistants."""

    def __init__(self, dry_run=True):
        self.dry_run = dry_run
        self.issues_found = []
        self.fixes_applied = []

    def log_issue(self, issue_type, description, item_id=None):
        """Log an issue found."""
        issue = {
            'type': issue_type,
            'description': description,
            'item_id': item_id
        }
        self.issues_found.append(issue)
        print(f"❌ ISSUE [{issue_type}]: {description}")

    def log_fix(self, fix_type, description, item_id=None):
        """Log a fix applied."""
        fix = {
            'type': fix_type,
            'description': description,
            'item_id': item_id
        }
        self.fixes_applied.append(fix)
        if self.dry_run:
            print(f"🔧 WOULD FIX [{fix_type}]: {description}")
        else:
            print(f"✅ FIXED [{fix_type}]: {description}")

    def check_company_slugs(self):
        """Check and fix company slug issues."""
        print("\n=== CHECKING COMPANY SLUGS ===")

        for company in Company.objects.all():
            issues = []

            # Check if slug exists
            if not company.slug:
                issues.append("Missing slug")
            else:
                # Check slug format
                expected_slug = slugify(company.name)
                if company.slug != expected_slug:
                    issues.append(f"Slug mismatch: '{company.slug}' vs expected '{expected_slug}'")

                # Check for spaces or invalid characters
                if ' ' in company.slug or company.slug != company.slug.lower():
                    issues.append("Invalid slug format")

            if issues:
                self.log_issue("COMPANY_SLUG", f"Company '{company.name}' (ID: {company.id}): {', '.join(issues)}", company.id)

                # Fix the slug
                new_slug = slugify(company.name)
                if not self.dry_run:
                    try:
                        with transaction.atomic():
                            company.slug = new_slug
                            company.save(update_fields=['slug'])
                        self.log_fix("COMPANY_SLUG", f"Updated slug for '{company.name}' to '{new_slug}'", company.id)
                    except Exception as e:
                        print(f"❌ ERROR fixing company slug: {e}")
                else:
                    self.log_fix("COMPANY_SLUG", f"Would update slug for '{company.name}' to '{new_slug}'", company.id)

    def check_duplicate_companies(self):
        """Check for duplicate company names."""
        print("\n=== CHECKING DUPLICATE COMPANIES ===")

        from django.db.models import Count
        duplicates = Company.objects.values('name').annotate(count=Count('name')).filter(count__gt=1)

        for dup in duplicates:
            companies = Company.objects.filter(name=dup['name']).order_by('created_at')
            self.log_issue("DUPLICATE_COMPANY", f"Company name '{dup['name']}' appears {dup['count']} times")

            # Keep the oldest one, suggest renaming others
            for i, company in enumerate(companies):
                if i == 0:
                    print(f"  ✅ Keeping: ID {company.id} (created {company.created_at})")
                else:
                    new_name = f"{company.name} ({company.id})"
                    self.log_fix("DUPLICATE_COMPANY", f"Would rename company ID {company.id} to '{new_name}'", company.id)

                    if not self.dry_run:
                        try:
                            with transaction.atomic():
                                company.name = new_name
                                company.slug = slugify(new_name)
                                company.save(update_fields=['name', 'slug'])
                            self.log_fix("DUPLICATE_COMPANY", f"Renamed company ID {company.id} to '{new_name}'", company.id)
                        except Exception as e:
                            print(f"❌ ERROR renaming company: {e}")

    def check_assistant_slugs(self):
        """Check and fix assistant slug issues."""
        print("\n=== CHECKING ASSISTANT SLUGS ===")

        for assistant in Assistant.objects.select_related('company').all():
            issues = []

            # Check if slug exists
            if not assistant.slug:
                issues.append("Missing slug")
            else:
                # Check if slug follows new format: company-assistant-uniqueid
                slug_parts = assistant.slug.split('-')
                if len(slug_parts) < 3:
                    issues.append("Slug doesn't follow new format (company-assistant-uniqueid)")
                else:
                    # Check if last part is 8-character unique ID
                    unique_id = slug_parts[-1]
                    if len(unique_id) != 8:
                        issues.append(f"Unique ID part '{unique_id}' is not 8 characters")

                    # Check if company part matches
                    company_slug = slugify(assistant.company.name) if assistant.company else 'unknown'
                    if not assistant.slug.startswith(f'{company_slug}-'):
                        issues.append(f"Slug doesn't start with company slug '{company_slug}'")

            if issues:
                self.log_issue("ASSISTANT_SLUG", f"Assistant '{assistant.name}' (ID: {assistant.id}): {', '.join(issues)}", assistant.id)

                # Fix by regenerating slug
                if not self.dry_run:
                    try:
                        with transaction.atomic():
                            # Clear slug to trigger regeneration
                            assistant.slug = None
                            assistant.save()
                        self.log_fix("ASSISTANT_SLUG", f"Regenerated slug for '{assistant.name}': {assistant.slug}", assistant.id)
                    except Exception as e:
                        print(f"❌ ERROR fixing assistant slug: {e}")
                else:
                    self.log_fix("ASSISTANT_SLUG", f"Would regenerate slug for '{assistant.name}'", assistant.id)

    def check_orphaned_assistants(self):
        """Check for assistants without companies."""
        print("\n=== CHECKING ORPHANED ASSISTANTS ===")

        orphaned = Assistant.objects.filter(company__isnull=True)

        for assistant in orphaned:
            self.log_issue("ORPHANED_ASSISTANT", f"Assistant '{assistant.name}' (ID: {assistant.id}) has no company", assistant.id)

            # Try to find a suitable company or create a default one
            if assistant.created_by:
                # Try to find a company owned by the creator
                user_companies = Company.objects.filter(owner=assistant.created_by)
                if user_companies.exists():
                    target_company = user_companies.first()
                    self.log_fix("ORPHANED_ASSISTANT", f"Would assign to company '{target_company.name}'", assistant.id)

                    if not self.dry_run:
                        try:
                            with transaction.atomic():
                                assistant.company = target_company
                                assistant.save(update_fields=['company'])
                            self.log_fix("ORPHANED_ASSISTANT", f"Assigned to company '{target_company.name}'", assistant.id)
                        except Exception as e:
                            print(f"❌ ERROR assigning company: {e}")
                else:
                    self.log_fix("ORPHANED_ASSISTANT", f"Would create default company for user {assistant.created_by.username}", assistant.id)
            else:
                self.log_fix("ORPHANED_ASSISTANT", f"Would assign to default 'Unknown' company", assistant.id)

    def check_company_ownership(self):
        """Check company ownership issues."""
        print("\n=== CHECKING COMPANY OWNERSHIP ===")

        for company in Company.objects.all():
            issues = []

            # Check if company has an owner
            if not company.owner or company.owner.username == 'AnonymousUser':
                issues.append("No valid owner")

            # Check if owner exists and is active
            if company.owner and company.owner.username != 'AnonymousUser':
                if not company.owner.is_active:
                    issues.append("Owner is inactive")

            if issues:
                self.log_issue("COMPANY_OWNERSHIP", f"Company '{company.name}' (ID: {company.id}): {', '.join(issues)}", company.id)

                # Try to find a suitable owner
                suitable_owner = None

                # First, try to find active users who created assistants for this company
                assistant_creators = User.objects.filter(
                    created_assistants__company=company,
                    is_active=True
                ).distinct()

                if assistant_creators.exists():
                    suitable_owner = assistant_creators.first()
                else:
                    # If no assistant creators, try to find any active user
                    active_users = User.objects.filter(is_active=True, is_staff=False).exclude(username='AnonymousUser')
                    if active_users.exists():
                        suitable_owner = active_users.first()

                if suitable_owner:
                    if not self.dry_run:
                        try:
                            with transaction.atomic():
                                company.owner = suitable_owner
                                company.save(update_fields=['owner'])
                            self.log_fix("COMPANY_OWNERSHIP", f"Assigned owner '{suitable_owner.username}' to company '{company.name}'", company.id)
                        except Exception as e:
                            print(f"❌ ERROR assigning owner: {e}")
                    else:
                        self.log_fix("COMPANY_OWNERSHIP", f"Would assign owner '{suitable_owner.username}' to company '{company.name}'", company.id)
                else:
                    self.log_fix("COMPANY_OWNERSHIP", f"No suitable owner found for company '{company.name}' - consider manual assignment", company.id)

    def check_assistant_activation(self):
        """Check assistant activation status."""
        print("\n=== CHECKING ASSISTANT ACTIVATION ===")

        for assistant in Assistant.objects.select_related('company').all():
            # Check if assistant should be active but isn't
            if not assistant.is_active and assistant.company and assistant.company.owner:
                # If assistant has proper setup, suggest activation
                if assistant.name and assistant.slug and assistant.company:
                    self.log_issue("ASSISTANT_INACTIVE", f"Assistant '{assistant.name}' (ID: {assistant.id}) appears ready but is inactive", assistant.id)

                    if not self.dry_run:
                        try:
                            with transaction.atomic():
                                assistant.is_active = True
                                assistant.save(update_fields=['is_active'])
                            self.log_fix("ASSISTANT_INACTIVE", f"Activated assistant '{assistant.name}'", assistant.id)
                        except Exception as e:
                            print(f"❌ ERROR activating assistant: {e}")
                    else:
                        self.log_fix("ASSISTANT_INACTIVE", f"Would activate assistant '{assistant.name}'", assistant.id)

    def check_test_companies(self):
        """Check for test/dummy companies that might need cleanup."""
        print("\n=== CHECKING TEST/DUMMY COMPANIES ===")

        # Patterns that suggest test companies
        test_patterns = [
            'test', 'dummy', 'example', 'sample', 'demo', 'temp', 'temporary',
            'fixed test company', 'web test company', 'com', 'new', 'tit', 'jklmj'
        ]

        for company in Company.objects.all():
            company_name_lower = company.name.lower()

            # Check if company name suggests it's a test company
            is_test_company = any(pattern in company_name_lower for pattern in test_patterns)

            # Also check for very short names or names with random characters
            if len(company.name) <= 3 or company.name.lower() in ['com', 'new', 'tit']:
                is_test_company = True

            # Check for companies with no assistants and suspicious names
            assistant_count = Assistant.objects.filter(company=company).count()
            if assistant_count == 0 and is_test_company:
                self.log_issue("TEST_COMPANY", f"Potential test company '{company.name}' (ID: {company.id}) with no assistants", company.id)
                self.log_fix("TEST_COMPANY", f"Could delete test company '{company.name}' (has no assistants)", company.id)

                if not self.dry_run:
                    # Only delete if it has no assistants and looks like a test company
                    try:
                        with transaction.atomic():
                            company.delete()
                        self.log_fix("TEST_COMPANY", f"Deleted test company '{company.name}'", company.id)
                    except Exception as e:
                        print(f"❌ ERROR deleting test company: {e}")
            elif is_test_company and assistant_count > 0:
                self.log_issue("TEST_COMPANY", f"Potential test company '{company.name}' (ID: {company.id}) has {assistant_count} assistants", company.id)
                self.log_fix("TEST_COMPANY", f"Consider reviewing test company '{company.name}' (has assistants)", company.id)

    def check_data_consistency(self):
        """Check overall data consistency."""
        print("\n=== CHECKING DATA CONSISTENCY ===")

        # Check for assistants with missing QR codes
        assistants_without_qr = Assistant.objects.filter(qr_code__isnull=True).count()
        if assistants_without_qr > 0:
            self.log_issue("DATA_CONSISTENCY", f"{assistants_without_qr} assistants missing QR codes")
            self.log_fix("DATA_CONSISTENCY", f"Would regenerate QR codes for {assistants_without_qr} assistants")

        # Check for companies with very old creation dates but no activity
        from django.utils import timezone
        from datetime import timedelta

        old_threshold = timezone.now() - timedelta(days=30)
        old_inactive_companies = Company.objects.filter(
            created_at__lt=old_threshold,
            assistants__isnull=True
        ).count()

        if old_inactive_companies > 0:
            self.log_issue("DATA_CONSISTENCY", f"{old_inactive_companies} old companies with no assistants")
            self.log_fix("DATA_CONSISTENCY", f"Consider reviewing {old_inactive_companies} old inactive companies")

        # Check for duplicate slugs (shouldn't happen but good to verify)
        from django.db.models import Count
        duplicate_assistant_slugs = Assistant.objects.values('slug').annotate(
            count=Count('slug')
        ).filter(count__gt=1)

        if duplicate_assistant_slugs.exists():
            for dup in duplicate_assistant_slugs:
                self.log_issue("DATA_CONSISTENCY", f"Duplicate assistant slug: '{dup['slug']}' ({dup['count']} times)")
                self.log_fix("DATA_CONSISTENCY", f"Would regenerate slugs for duplicate '{dup['slug']}'")

        duplicate_company_slugs = Company.objects.values('slug').annotate(
            count=Count('slug')
        ).filter(count__gt=1)

        if duplicate_company_slugs.exists():
            for dup in duplicate_company_slugs:
                self.log_issue("DATA_CONSISTENCY", f"Duplicate company slug: '{dup['slug']}' ({dup['count']} times)")
                self.log_fix("DATA_CONSISTENCY", f"Would regenerate slugs for duplicate '{dup['slug']}'")

    def run_all_checks(self):
        """Run all checks and fixes."""
        print("🔍 Starting comprehensive company and assistant fix...")
        print(f"Mode: {'DRY RUN' if self.dry_run else 'APPLY FIXES'}")

        self.check_company_slugs()
        self.check_duplicate_companies()
        self.check_assistant_slugs()
        self.check_orphaned_assistants()
        self.check_company_ownership()
        self.check_assistant_activation()
        self.check_test_companies()
        self.check_data_consistency()

        self.generate_summary_report()

    def generate_summary_report(self):
        """Generate a summary report of all issues and fixes."""
        print("\n" + "="*60)
        print("SUMMARY REPORT")
        print("="*60)

        print(f"\n📊 ISSUES FOUND: {len(self.issues_found)}")
        issue_types = {}
        for issue in self.issues_found:
            issue_type = issue['type']
            issue_types[issue_type] = issue_types.get(issue_type, 0) + 1

        for issue_type, count in issue_types.items():
            print(f"  - {issue_type}: {count}")

        print(f"\n🔧 FIXES {'WOULD BE APPLIED' if self.dry_run else 'APPLIED'}: {len(self.fixes_applied)}")
        fix_types = {}
        for fix in self.fixes_applied:
            fix_type = fix['type']
            fix_types[fix_type] = fix_types.get(fix_type, 0) + 1

        for fix_type, count in fix_types.items():
            print(f"  - {fix_type}: {count}")

        if self.dry_run:
            print(f"\n⚠️  This was a DRY RUN. No changes were made.")
            print(f"Run with --apply to actually fix the issues.")
        else:
            print(f"\n✅ Changes have been applied to the database.")

    def run_all_checks(self):
        """Run all checks and fixes."""
        print("🔍 Starting comprehensive company and assistant fix...")
        print(f"Mode: {'DRY RUN' if self.dry_run else 'APPLY FIXES'}")

        self.check_company_slugs()
        self.check_duplicate_companies()
        self.check_assistant_slugs()
        self.check_orphaned_assistants()
        self.check_company_ownership()
        self.check_assistant_activation()

        self.generate_summary_report()


def main():
    """Main function to run the fixer."""
    import argparse

    parser = argparse.ArgumentParser(description='Fix companies and assistants data issues')
    parser.add_argument('--apply', action='store_true', help='Apply fixes (default is dry run)')
    parser.add_argument('--companies-only', action='store_true', help='Only check companies')
    parser.add_argument('--assistants-only', action='store_true', help='Only check assistants')

    args = parser.parse_args()

    fixer = CompanyAssistantFixer(dry_run=not args.apply)

    if args.companies_only:
        fixer.check_company_slugs()
        fixer.check_duplicate_companies()
        fixer.check_company_ownership()
    elif args.assistants_only:
        fixer.check_assistant_slugs()
        fixer.check_orphaned_assistants()
        fixer.check_assistant_activation()
    else:
        fixer.run_all_checks()


if __name__ == '__main__':
    main()
