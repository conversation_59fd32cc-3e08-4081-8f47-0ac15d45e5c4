"""
Advanced multi-level caching system with intelligent invalidation and warming strategies.
"""

import time
import threading
import pickle
import zlib
import hashlib
from typing import Dict, Any, Optional, List, Set, Callable
from collections import defaultdict
import logging
from django.core.cache import cache
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

logger = logging.getLogger(__name__)


class CacheLevel:
    """Enumeration for cache levels."""
    L1_MEMORY = 1
    L2_REDIS = 2
    L3_DATABASE = 3


class CacheEntry:
    """Cache entry with metadata."""

    def __init__(self, value: Any, ttl: int, dependencies: Set[str] = None):
        self.value = value
        self.created_at = time.time()
        self.ttl = ttl
        self.access_count = 0
        self.last_accessed = time.time()
        self.dependencies = dependencies or set()
        self.compressed = False

    def is_expired(self) -> bool:
        """Check if entry has expired."""
        return time.time() > (self.created_at + self.ttl)

    def access(self) -> Any:
        """Access the cached value and update metadata."""
        self.access_count += 1
        self.last_accessed = time.time()
        return self.value

    def should_compress(self, threshold: int = 1024) -> bool:
        """Determine if value should be compressed."""
        try:
            return len(pickle.dumps(self.value)) > threshold
        except:
            return False


class MultiLevelCache:
    """
    Multi-level cache with L1 (memory), L2 (Redis), and L3 (database) levels.
    """

    def __init__(self, l1_size: int = 1000, compression_threshold: int = 1024):
        self.l1_cache = {}  # In-memory cache
        self.l1_size = l1_size
        self.compression_threshold = compression_threshold
        self.lock = threading.RLock()
        self.access_order = []  # For LRU eviction
        self.dependencies = defaultdict(set)  # Key -> dependent keys
        self.reverse_dependencies = defaultdict(set)  # Dependent key -> keys it depends on

    def get(self, key: str) -> Optional[Any]:
        """Get value from cache, checking all levels."""
        with self.lock:
            # Try L1 (memory) first
            if key in self.l1_cache:
                entry = self.l1_cache[key]
                if not entry.is_expired():
                    self._update_access_order(key)
                    return entry.access()
                else:
                    self._remove_from_l1(key)

            # Try L2 (Redis/Django cache)
            l2_value = self._get_from_l2(key)
            if l2_value is not None:
                # Promote to L1
                self._set_l1(key, l2_value['value'], l2_value['ttl'], l2_value.get('dependencies'))
                return l2_value['value']

            # Try L3 (database cache)
            l3_value = self._get_from_l3(key)
            if l3_value is not None:
                # Promote to L1 and L2
                self._set_l1(key, l3_value['value'], l3_value['ttl'], l3_value.get('dependencies'))
                self._set_l2(key, l3_value)
                return l3_value['value']

            return None

    def set(self, key: str, value: Any, ttl: int = 3600, dependencies: Set[str] = None) -> None:
        """Set value in all cache levels."""
        with self.lock:
            # Set in L1
            self._set_l1(key, value, ttl, dependencies)

            # Set in L2
            cache_data = {
                'value': value,
                'ttl': ttl,
                'created_at': time.time(),
                'dependencies': list(dependencies) if dependencies else []
            }
            self._set_l2(key, cache_data)

            # Set in L3 for persistence
            self._set_l3(key, cache_data)

            # Update dependency tracking
            if dependencies:
                for dep in dependencies:
                    self.dependencies[dep].add(key)
                    self.reverse_dependencies[key].add(dep)

    def delete(self, key: str) -> None:
        """Delete key from all cache levels."""
        with self.lock:
            self._remove_from_l1(key)
            self._remove_from_l2(key)
            self._remove_from_l3(key)
            self._remove_dependencies(key)

    def invalidate_dependencies(self, dependency_key: str) -> None:
        """Invalidate all keys that depend on the given key."""
        with self.lock:
            dependent_keys = self.dependencies.get(dependency_key, set()).copy()
            for key in dependent_keys:
                logger.info(f"Invalidating dependent key: {key}")
                self.delete(key)

    def _set_l1(self, key: str, value: Any, ttl: int, dependencies: Set[str] = None) -> None:
        """Set value in L1 cache."""
        # Check if we need to evict
        if len(self.l1_cache) >= self.l1_size and key not in self.l1_cache:
            self._evict_lru()

        entry = CacheEntry(value, ttl, dependencies)

        # Compress if beneficial
        if entry.should_compress(self.compression_threshold):
            try:
                compressed_value = zlib.compress(pickle.dumps(value))
                entry.value = compressed_value
                entry.compressed = True
            except Exception as e:
                logger.warning(f"Failed to compress cache entry: {e}")

        self.l1_cache[key] = entry
        self._update_access_order(key)

    def _get_from_l2(self, key: str) -> Optional[Dict]:
        """Get value from L2 cache (Django cache)."""
        try:
            return cache.get(f"l2_{key}")
        except Exception as e:
            logger.warning(f"L2 cache get error: {e}")
            return None

    def _set_l2(self, key: str, cache_data: Dict) -> None:
        """Set value in L2 cache."""
        try:
            cache.set(f"l2_{key}", cache_data, cache_data['ttl'])
        except Exception as e:
            logger.warning(f"L2 cache set error: {e}")

    def _get_from_l3(self, key: str) -> Optional[Dict]:
        """Get value from L3 cache (database)."""
        try:
            from .models import CacheEntry as DBCacheEntry
            entry = DBCacheEntry.objects.filter(key=key).first()
            if entry and not entry.is_expired():
                return {
                    'value': pickle.loads(entry.value),
                    'ttl': entry.ttl,
                    'dependencies': entry.dependencies or []
                }
        except Exception as e:
            logger.warning(f"L3 cache get error: {e}")
        return None

    def _set_l3(self, key: str, cache_data: Dict) -> None:
        """Set value in L3 cache."""
        try:
            from .models import CacheEntry as DBCacheEntry
            serialized_value = pickle.dumps(cache_data['value'])

            DBCacheEntry.objects.update_or_create(
                key=key,
                defaults={
                    'value': serialized_value,
                    'ttl': cache_data['ttl'],
                    'created_at': time.time(),
                    'dependencies': cache_data.get('dependencies', [])
                }
            )
        except Exception as e:
            logger.warning(f"L3 cache set error: {e}")

    def _remove_from_l1(self, key: str) -> None:
        """Remove key from L1 cache."""
        self.l1_cache.pop(key, None)
        if key in self.access_order:
            self.access_order.remove(key)

    def _remove_from_l2(self, key: str) -> None:
        """Remove key from L2 cache."""
        try:
            cache.delete(f"l2_{key}")
        except Exception as e:
            logger.warning(f"L2 cache delete error: {e}")

    def _remove_from_l3(self, key: str) -> None:
        """Remove key from L3 cache."""
        try:
            from .models import CacheEntry as DBCacheEntry
            DBCacheEntry.objects.filter(key=key).delete()
        except Exception as e:
            logger.warning(f"L3 cache delete error: {e}")

    def _remove_dependencies(self, key: str) -> None:
        """Remove dependency tracking for key."""
        # Remove from dependencies
        for dep in self.reverse_dependencies.get(key, set()):
            self.dependencies[dep].discard(key)

        # Remove reverse dependencies
        self.reverse_dependencies.pop(key, None)

        # Remove as dependency
        self.dependencies.pop(key, None)

    def _update_access_order(self, key: str) -> None:
        """Update LRU access order."""
        if key in self.access_order:
            self.access_order.remove(key)
        self.access_order.append(key)

    def _evict_lru(self) -> None:
        """Evict least recently used item."""
        if self.access_order:
            lru_key = self.access_order[0]
            self._remove_from_l1(lru_key)

    def cleanup_expired(self) -> int:
        """Remove expired entries from L1 cache."""
        with self.lock:
            expired_keys = [
                key for key, entry in self.l1_cache.items()
                if entry.is_expired()
            ]

            for key in expired_keys:
                self._remove_from_l1(key)

            return len(expired_keys)

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            total_entries = len(self.l1_cache)
            total_access_count = sum(entry.access_count for entry in self.l1_cache.values())

            return {
                'l1_entries': total_entries,
                'l1_capacity': self.l1_size,
                'l1_utilization': total_entries / self.l1_size if self.l1_size > 0 else 0,
                'total_accesses': total_access_count,
                'dependencies_tracked': len(self.dependencies)
            }


class CacheWarmer:
    """
    Proactively warms cache with frequently accessed data.
    """

    def __init__(self, cache_instance: MultiLevelCache):
        self.cache = cache_instance
        self.warming_strategies = {}
        self.warming_active = False

    def register_warming_strategy(self, name: str, strategy: Callable) -> None:
        """Register a cache warming strategy."""
        self.warming_strategies[name] = strategy

    def warm_cache(self, strategy_names: List[str] = None) -> None:
        """Execute cache warming strategies."""
        if self.warming_active:
            logger.info("Cache warming already in progress")
            return

        self.warming_active = True
        strategies = strategy_names or list(self.warming_strategies.keys())

        try:
            for strategy_name in strategies:
                if strategy_name in self.warming_strategies:
                    logger.info(f"Executing warming strategy: {strategy_name}")
                    self.warming_strategies[strategy_name](self.cache)
        except Exception as e:
            logger.error(f"Error during cache warming: {e}")
        finally:
            self.warming_active = False

    def warm_popular_assistants(self, cache_instance: MultiLevelCache) -> None:
        """Warm cache with popular assistants."""
        try:
            from .models import Assistant
            from .optimized_queries import OptimizedAssistantQueries

            popular_assistants = Assistant.objects.filter(
                is_active=True, is_public=True
            ).order_by('-total_interactions')[:20]

            for assistant in popular_assistants:
                cache_key = f"assistant_stats:{assistant.id}"
                stats = OptimizedAssistantQueries.get_assistant_with_stats(assistant.id)
                if stats:
                    cache_instance.set(cache_key, stats, ttl=1800)  # 30 minutes

            logger.info(f"Warmed cache with {len(popular_assistants)} popular assistants")

        except Exception as e:
            logger.error(f"Error warming popular assistants: {e}")

    def warm_featured_content(self, cache_instance: MultiLevelCache) -> None:
        """Warm cache with featured content."""
        try:
            from .models import Assistant

            featured_assistants = Assistant.objects.filter(
                is_featured=True, is_active=True
            )[:10]

            for assistant in featured_assistants:
                cache_key = f"featured_assistant:{assistant.id}"
                cache_instance.set(cache_key, assistant, ttl=3600)  # 1 hour

            logger.info(f"Warmed cache with {len(featured_assistants)} featured assistants")

        except Exception as e:
            logger.error(f"Error warming featured content: {e}")


# Global cache instance
multi_level_cache = MultiLevelCache()
cache_warmer = CacheWarmer(multi_level_cache)

# Register warming strategies
cache_warmer.register_warming_strategy('popular_assistants', cache_warmer.warm_popular_assistants)
cache_warmer.register_warming_strategy('featured_content', cache_warmer.warm_featured_content)


# Signal handlers for automatic cache invalidation
@receiver(post_save, sender='assistants.Assistant')
def invalidate_assistant_cache(sender, instance, **kwargs):
    """Invalidate assistant-related cache entries."""
    cache_keys = [
        f"assistant_stats:{instance.id}",
        f"assistant:{instance.id}",
        f"company_assistants:{instance.company_id}",
        "featured_assistants",
        "popular_assistants"
    ]

    for key in cache_keys:
        multi_level_cache.delete(key)

    logger.info(f"Invalidated cache for assistant {instance.id}")


@receiver(post_delete, sender='assistants.Assistant')
def invalidate_assistant_cache_on_delete(sender, instance, **kwargs):
    """Invalidate cache when assistant is deleted."""
    invalidate_assistant_cache(sender, instance, **kwargs)


@receiver(post_save, sender='assistants.Interaction')
def invalidate_interaction_cache(sender, instance, **kwargs):
    """Invalidate interaction-related cache entries."""
    multi_level_cache.invalidate_dependencies(f"assistant:{instance.assistant_id}")
    logger.info(f"Invalidated interaction cache for assistant {instance.assistant_id}")


# Utility functions
def get_cached(key: str) -> Optional[Any]:
    """Get value from multi-level cache."""
    return multi_level_cache.get(key)


def set_cached(key: str, value: Any, ttl: int = 3600, dependencies: Set[str] = None) -> None:
    """Set value in multi-level cache."""
    multi_level_cache.set(key, value, ttl, dependencies)


def cache_queryset_ids(key: str, queryset, ttl: int = 3600) -> None:
    """
    Safely cache QuerySet by storing only the IDs.
    This avoids serialization issues with complex QuerySets.
    """
    try:
        from django.db import models
        if hasattr(queryset, 'values_list'):
            ids = list(queryset.values_list('id', flat=True))
            set_cached(key, ids, ttl)
        else:
            logger.warning(f"Attempted to cache non-QuerySet object: {type(queryset)}")
    except Exception as e:
        logger.error(f"Failed to cache QuerySet IDs for key {key}: {e}")


def get_cached_queryset(key: str, model_class) -> Optional[Any]:
    """
    Retrieve cached QuerySet by reconstructing from cached IDs.
    """
    try:
        cached_ids = get_cached(key)
        if cached_ids and isinstance(cached_ids, list):
            return model_class.objects.filter(id__in=cached_ids)
        return None
    except Exception as e:
        logger.error(f"Failed to retrieve cached QuerySet for key {key}: {e}")
        return None


def delete_cached(key: str) -> None:
    """Delete value from multi-level cache."""
    multi_level_cache.delete(key)


def warm_cache_async() -> None:
    """Warm cache asynchronously."""
    import threading
    threading.Thread(target=cache_warmer.warm_cache, daemon=True).start()


def cleanup_expired_cache() -> int:
    """Clean up expired cache entries."""
    return multi_level_cache.cleanup_expired()


def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics."""
    return multi_level_cache.get_stats()


def clear_assistant_cache(company_id: int = None) -> None:
    """
    Clear all assistant-related cache entries.
    If company_id is provided, only clear cache for that company.
    """
    try:
        if company_id:
            # Clear specific company cache
            pattern = f"company_assistants_ids:{company_id}:*"
            logger.info(f"Clearing assistant cache for company {company_id}")
        else:
            # Clear all assistant cache
            pattern = "company_assistants_ids:*"
            logger.info("Clearing all assistant cache")

        # Note: This is a simplified implementation
        # In a production environment, you might want to implement
        # pattern-based cache clearing in the MultiLevelCache class

    except Exception as e:
        logger.error(f"Failed to clear assistant cache: {e}")


def validate_cache_integrity() -> Dict[str, Any]:
    """
    Validate cache integrity and report any issues.
    Returns a report of cache health.
    """
    report = {
        'total_entries': 0,
        'corrupted_entries': 0,
        'valid_entries': 0,
        'errors': []
    }

    try:
        # This would need to be implemented based on your specific cache backend
        # For now, just return basic stats
        stats = get_cache_stats()
        report['total_entries'] = stats.get('total_entries', 0)
        report['valid_entries'] = report['total_entries']

    except Exception as e:
        report['errors'].append(str(e))
        logger.error(f"Cache integrity validation failed: {e}")

    return report
