/**
 * Navigation Message Scroll CSS
 * Adds scroll bars to navigation item messages when they exceed page length
 */

/* Target navigation item messages specifically */
.message.assistant-message .message-content.nav-content-bubble,
.message.assistant-message.nav-message .message-content,
.nav-content-bubble .message-content,
.message-content.nav-content {
    max-height: calc(100vh - 300px) !important; /* Limit height to viewport minus header/footer space */
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
    scrollbar-width: thin !important;
    scrollbar-color: #c1c1c1 #f1f1f1 !important;
}

/* Custom scrollbar styling for navigation messages */
.message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar,
.message.assistant-message.nav-message .message-content::-webkit-scrollbar,
.nav-content-bubble .message-content::-webkit-scrollbar,
.message-content.nav-content::-webkit-scrollbar {
    width: 8px !important;
}

.message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar-track,
.message.assistant-message.nav-message .message-content::-webkit-scrollbar-track,
.nav-content-bubble .message-content::-webkit-scrollbar-track,
.message-content.nav-content::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 10px !important;
}

.message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar-thumb,
.message.assistant-message.nav-message .message-content::-webkit-scrollbar-thumb,
.nav-content-bubble .message-content::-webkit-scrollbar-thumb,
.message-content.nav-content::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 10px !important;
}

.message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar-thumb:hover,
.message.assistant-message.nav-message .message-content::-webkit-scrollbar-thumb:hover,
.nav-content-bubble .message-content::-webkit-scrollbar-thumb:hover,
.message-content.nav-content::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1 !important;
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
    .message.assistant-message .message-content.nav-content-bubble,
    .message.assistant-message.nav-message .message-content,
    .nav-content-bubble .message-content,
    .message-content.nav-content {
        max-height: calc(100vh - 250px) !important; /* Smaller height on mobile */
    }
}

/* Tablet specific adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    .message.assistant-message .message-content.nav-content-bubble,
    .message.assistant-message.nav-message .message-content,
    .nav-content-bubble .message-content,
    .message-content.nav-content {
        max-height: calc(100vh - 280px) !important;
    }
}

/* Ensure content inside scrollable navigation messages is properly contained */
.message.assistant-message .message-content.nav-content-bubble *,
.message.assistant-message.nav-message .message-content *,
.nav-content-bubble .message-content *,
.message-content.nav-content * {
    max-width: 100% !important;
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    box-sizing: border-box !important;
}

/* Special handling for tables in navigation messages */
.message.assistant-message .message-content.nav-content-bubble table,
.message.assistant-message.nav-message .message-content table,
.nav-content-bubble .message-content table,
.message-content.nav-content table {
    width: 100% !important;
    table-layout: auto !important;
    overflow-x: auto !important;
    display: block !important;
    white-space: nowrap !important;
}

/* Special handling for images in navigation messages */
.message.assistant-message .message-content.nav-content-bubble img,
.message.assistant-message.nav-message .message-content img,
.nav-content-bubble .message-content img,
.message-content.nav-content img {
    max-width: 100% !important;
    height: auto !important;
    object-fit: contain !important;
}

/* Special handling for code blocks in navigation messages */
.message.assistant-message .message-content.nav-content-bubble pre,
.message.assistant-message.nav-message .message-content pre,
.nav-content-bubble .message-content pre,
.message-content.nav-content pre {
    overflow-x: auto !important;
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
}

/* Dark mode support */
[data-theme="dark"] .message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar-track,
[data-theme="dark"] .message.assistant-message.nav-message .message-content::-webkit-scrollbar-track,
[data-theme="dark"] .nav-content-bubble .message-content::-webkit-scrollbar-track,
[data-theme="dark"] .message-content.nav-content::-webkit-scrollbar-track {
    background: #2a2a2a !important;
}

[data-theme="dark"] .message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar-thumb,
[data-theme="dark"] .message.assistant-message.nav-message .message-content::-webkit-scrollbar-thumb,
[data-theme="dark"] .nav-content-bubble .message-content::-webkit-scrollbar-thumb,
[data-theme="dark"] .message-content.nav-content::-webkit-scrollbar-thumb {
    background: #555555 !important;
}

[data-theme="dark"] .message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar-thumb:hover,
[data-theme="dark"] .message.assistant-message.nav-message .message-content::-webkit-scrollbar-thumb:hover,
[data-theme="dark"] .nav-content-bubble .message-content::-webkit-scrollbar-thumb:hover,
[data-theme="dark"] .message-content.nav-content::-webkit-scrollbar-thumb:hover {
    background: #777777 !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar-thumb,
    .message.assistant-message.nav-message .message-content::-webkit-scrollbar-thumb,
    .nav-content-bubble .message-content::-webkit-scrollbar-thumb,
    .message-content.nav-content::-webkit-scrollbar-thumb {
        background: #000000 !important;
    }
    
    .message.assistant-message .message-content.nav-content-bubble::-webkit-scrollbar-track,
    .message.assistant-message.nav-message .message-content::-webkit-scrollbar-track,
    .nav-content-bubble .message-content::-webkit-scrollbar-track,
    .message-content.nav-content::-webkit-scrollbar-track {
        background: #ffffff !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .message.assistant-message .message-content.nav-content-bubble,
    .message.assistant-message.nav-message .message-content,
    .nav-content-bubble .message-content,
    .message-content.nav-content {
        -webkit-overflow-scrolling: auto !important;
    }
}
