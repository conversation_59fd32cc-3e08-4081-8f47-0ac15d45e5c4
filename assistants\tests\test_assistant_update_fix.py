"""
Test for the assistant_update view KeyError fix.

This test verifies that the assistant_update view properly handles cases where
the assistant_type field is removed from the form for community entities.
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from accounts.models import Company
from assistants.models import Assistant


class AssistantUpdateKeyErrorFixTest(TestCase):
    """Test the fix for KeyError in assistant_update view."""

    def setUp(self):
        """Set up test data."""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create community company (this triggers the KeyError scenario)
        self.community_company = Company.objects.create(
            name='Test Community Company',
            owner=self.user,
            entity_type='community'  # This is the key - community entities
        )

        # Create regular company for comparison
        self.regular_company = Company.objects.create(
            name='Test Regular Company',
            owner=self.user,
            entity_type='company'  # Regular company
        )

        # Create test assistants
        self.community_assistant = Assistant.objects.create(
            name='Community Test Assistant',
            company=self.community_company,
            assistant_type=Assistant.TYPE_COMMUNITY,
            system_prompt='Test prompt',
            is_public=True,
            is_active=True
        )

        self.regular_assistant = Assistant.objects.create(
            name='Regular Test Assistant',
            company=self.regular_company,
            assistant_type=Assistant.TYPE_GENERAL,
            system_prompt='Test prompt',
            is_public=True,
            is_active=True
        )

        # Create client and login
        self.client = Client()
        self.client.login(username='testuser', password='testpass123')

    def test_community_assistant_update_no_keyerror(self):
        """
        Test that updating a community assistant doesn't raise KeyError.
        
        This test reproduces the scenario where the assistant_type field
        is removed from the form for community entities, which was causing
        a KeyError when the view tried to access form.fields['assistant_type'].
        """
        # Test GET request (loading the form)
        response = self.client.get(reverse('assistants:update', kwargs={
            'company_id': self.community_company.id,
            'assistant_id': self.community_assistant.id
        }))
        
        # Should not raise KeyError and should return 200
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Community Test Assistant')

    def test_regular_assistant_update_still_works(self):
        """
        Test that regular assistant updates still work normally.
        
        This ensures our fix doesn't break the normal functionality.
        """
        # Test GET request (loading the form)
        response = self.client.get(reverse('assistants:update', kwargs={
            'company_id': self.regular_company.id,
            'assistant_id': self.regular_assistant.id
        }))
        
        # Should work normally
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Regular Test Assistant')

    def test_community_assistant_post_update(self):
        """
        Test that POST updates to community assistants work without KeyError.
        """
        # Test POST request (updating the assistant)
        response = self.client.post(reverse('assistants:update', kwargs={
            'company_id': self.community_company.id,
            'assistant_id': self.community_assistant.id
        }), {
            'name': 'Updated Community Assistant',
            'persona_name': 'Updated Community Assistant',
            'description': 'Updated description',
            'model': 'gemini-1.0-pro',
            'temperature': '0.7',
            'max_tokens': '2048',
            'system_prompt': 'Updated system prompt',
            'greeting_message': 'Hello! How can I help you?',
            'is_active': 'on',
            'is_public': 'on',
        })
        
        # Should not raise KeyError and should either redirect or return 200
        self.assertIn(response.status_code, [200, 302])
        
        # Verify the assistant was updated
        self.community_assistant.refresh_from_db()
        self.assertEqual(self.community_assistant.name, 'Updated Community Assistant')

    def test_wizard_view_hasattr_fix(self):
        """
        Test that the wizard view also uses the correct field existence check.
        
        This tests the fix for the hasattr vs 'in' check in the wizard view.
        """
        # Test the wizard view for community company
        response = self.client.get(reverse('assistants:wizard', kwargs={
            'company_id': self.community_company.id
        }))
        
        # Should not raise any errors
        self.assertEqual(response.status_code, 200)
