/*
 * Chat Solid Colors CSS
 * This file overrides gradient backgrounds with solid colors for chat messages
 * High specificity selectors to ensure these styles take precedence
 */

/* User message styles - matching dark mode - LIGHT MODE ONLY */
html[data-theme="light"] body .user-message,
html[data-theme="light"] body .user-message .message-content,
html[data-theme="light"] body .message.user-message,
html[data-theme="light"] body .message.user-message .message-content,
html[data-theme="light"] body div.user-message,
html[data-theme="light"] body div.user-message .message-content,
html[data-theme="light"] body .chat-box .user-message,
html[data-theme="light"] body .chat-box .user-message .message-content,
html[data-theme="light"] body #chat-box .user-message,
html[data-theme="light"] body #chat-box .user-message .message-content,
html[data-theme="light"] body .user-message[style*="background"],
html[data-theme="light"] body .user-message .message-content[style*="background"],
html[data-theme="light"] body .message.user-message[style*="background"],
html[data-theme="light"] body .message.user-message .message-content[style*="background"] {
    background: linear-gradient(145deg, #0066ff, #0055cc) !important;
    background-color: #0066ff !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 5px 15px rgba(0, 102, 255, 0.3) !important;
    border-radius: 18px 18px 4px 18px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 500 !important;
    letter-spacing: 0.01em !important;
}

/* Assistant message styles - matching dark mode - LIGHT MODE ONLY */
html[data-theme="light"] body .assistant-message,
html[data-theme="light"] body .assistant-message .message-content,
html[data-theme="light"] body .message.assistant-message,
html[data-theme="light"] body .message.assistant-message .message-content,
html[data-theme="light"] body div.assistant-message,
html[data-theme="light"] body div.assistant-message .message-content,
html[data-theme="light"] body .chat-box .assistant-message,
html[data-theme="light"] body .chat-box .assistant-message .message-content,
html[data-theme="light"] body #chat-box .assistant-message,
html[data-theme="light"] body #chat-box .assistant-message .message-content,
html[data-theme="light"] body .assistant-message[style*="background"],
html[data-theme="light"] body .assistant-message .message-content[style*="background"],
html[data-theme="light"] body .message.assistant-message[style*="background"],
html[data-theme="light"] body .message.assistant-message .message-content[style*="background"],
html[data-theme="light"] body .nav-content-bubble .message-content,
html[data-theme="light"] body .nav-content-bubble.assistant-message .message-content {
    background: linear-gradient(145deg, #2a2a2a, #222222) !important;
    background-color: #2a2a2a !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    border-radius: 18px 18px 18px 4px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 400 !important;
    letter-spacing: 0.01em !important;
}

/* Override any inline styles that might be applied by JavaScript */
html body [style*="background"] .message-content,
html body .message-content[style*="background"],
html body [style*="linear-gradient"] .message-content,
html body .message-content[style*="linear-gradient"],
html body [style*="radial-gradient"] .message-content,
html body .message-content[style*="radial-gradient"] {
    background: inherit !important;
    background-color: inherit !important;
    background-image: none !important;
    background-blend-mode: normal !important;
}

/* Ensure the message container itself has no background */
html body .message,
html body .message[style*="background"] {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    background-blend-mode: normal !important;
}

/* Override any gradient backgrounds that might be applied */
html body [style*="linear-gradient"],
html body [style*="radial-gradient"],
html body [style*="gradient"] {
    background: inherit !important;
    background-color: inherit !important;
    background-image: none !important;
    background-blend-mode: normal !important;
}

/* Ensure these styles are applied even when new messages are added */
html body #chat-box .message-content,
html body .chat-box .message-content {
    transition: none !important;
}

/* Override any JavaScript-applied styles */
html body .message-content[style],
html body .user-message .message-content[style],
html body .assistant-message .message-content[style] {
    background: inherit !important;
    background-color: inherit !important;
    background-image: none !important;
}

/* Ensure navigation content bubbles also have solid colors - MATCHING DARK MODE - LIGHT MODE ONLY */
html[data-theme="light"] body .nav-content-bubble,
html[data-theme="light"] body .nav-content-bubble .message-content {
    background: linear-gradient(145deg, #252525, #1e1e1e) !important;
    background-color: #252525 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    border-left: 4px solid #0077ff !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
    border-radius: 8px !important;
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
    transition: background 0.3s ease, background-color 0.3s ease, color 0.3s ease, border 0.3s ease, box-shadow 0.3s ease !important;
}

/* Override any base styles that might be applied */
html body .message.user-message .message-content::before,
html body .message.assistant-message .message-content::before,
html body .message.user-message .message-content::after,
html body .message.assistant-message .message-content::after {
    display: none !important;
    content: none !important;
    background: none !important;
    background-image: none !important;
}

/* Chat container with light blue glass background - LIGHT MODE */
html[data-theme="light"] body .chat-container,
html[data-theme="light"] body .general-chat-container,
html[data-theme="light"] body div.chat-container,
html[data-theme="light"] body div.general-chat-container {
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(214, 240, 253, 0.85)) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.5) !important;
    box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.7) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Chat box with subtle background - LIGHT MODE */
html[data-theme="light"] body .chat-box,
html[data-theme="light"] body .general-chat-box,
html[data-theme="light"] body #chat-box,
html[data-theme="light"] body div.chat-box,
html[data-theme="light"] body div.general-chat-box {
    background: rgba(250, 253, 255, 0.7) !important;
    border: none !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.03) !important;
    padding: 1.5rem !important;
}

/* DARK MODE OVERRIDES - Force dark backgrounds */

/* Force dark background on html and body */
html[data-theme="dark"],
html[data-theme="dark"] body,
html,
body {
    background-color: #121212 !important;
    background: #121212 !important;
    background-image: none !important;
    color: #ffffff !important;
}

/* Force dark chat container */
html[data-theme="dark"] .chat-container,
html[data-theme="dark"] .general-chat-container,
.chat-container,
.general-chat-container {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    background-image: none !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Force dark chat box */
html[data-theme="dark"] .chat-box,
html[data-theme="dark"] .general-chat-box,
html[data-theme="dark"] #chat-box,
.chat-box,
.general-chat-box,
#chat-box {
    background-color: #252525 !important;
    background: #252525 !important;
    background-image: none !important;
    border: 1px solid #333333 !important;
}

/* Force dark assistant messages */
html[data-theme="dark"] .assistant-message,
html[data-theme="dark"] .assistant-message .message-content,
.assistant-message,
.assistant-message .message-content {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    background-image: none !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
}

/* Force dark input field */
html[data-theme="dark"] #message-input,
#message-input {
    background-color: #252525 !important;
    background: #252525 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
}
