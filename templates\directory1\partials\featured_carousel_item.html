{% comment %}
This template is used for displaying items in the continuous scrolling featured carousel.
It can be used for companies, assistants, or community assistants.
{% endcomment %}
<!-- DEBUG: item_name={{ item_name|default:'None' }}, item_type={{ item_type|default:'None' }}, item_id={{ item_id|default:'None' }} -->
<!-- DEBUG: item_logo={{ item_logo|default:'None' }}, item_icon={{ item_icon|default:'None' }} -->
<!-- DEBUG: item_description={{ item_description|truncatechars:30|default:'None' }} -->
<!-- DEBUG: item_rating={{ item_rating|default:'None' }}, item_rating_int={{ item_rating_int|default:'None' }}, item_rating_count={{ item_rating_count|default:'None' }} -->
<!-- DEBUG: item_meta={{ item_meta|default:'None' }}, item_meta_icon={{ item_meta_icon|default:'None' }} -->
<!-- DEBUG: is_saved={{ is_saved|default:'None' }} -->
<div class="featured-carousel-item" style="flex: 0 0 auto; margin: 0 30px; display: flex; align-items: flex-start; justify-content: center;">
    <div class="featured-item-wrapper" style="position: relative; width: 360px; min-height: 400px; display: flex; flex-direction: column; align-items: center; background-color: #222222; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); padding: 25px; transition: all 0.3s ease; border: 1px solid #333333;">
        <span class="featured-badge" style="position: absolute; top: 10px; right: 10px; background-color: #198754; color: white; font-size: 0.8rem; padding: 0.25rem 0.5rem; border-radius: 4px; z-index: 10;">Featured</span>
        <a href="{{ item_url|default:'#' }}" title="{{ item_name|default:'Assistant' }}" class="text-center">
            <!-- DEBUG: item_logo={{ item_logo|default:'None' }} -->
            {% if item_logo and item_logo != 'None' and item_logo != '' %}
                <div class="logo-container" style="height: 240px; width: 240px; min-height: 240px; min-width: 240px; max-height: 240px; max-width: 240px; display: flex; align-items: center; justify-content: center; background-color: #252525; border-radius: 4px; overflow: hidden; transition: all 0.3s ease; border: 1px solid #333333; position: relative; aspect-ratio: 1/1; margin: 0 auto 15px; z-index: 2; flex-shrink: 0;">
                    <img src="{{ item_logo }}" alt="{{ item_name|default:'Assistant' }} Logo"
                         onerror="
                            console.error('[DEBUG] Logo load error:', {
                                src: this.src,
                                alt: this.alt,
                                parentElement: this.parentNode.className,
                                item_name: '{{ item_name|default:'Unknown' }}',
                                item_type: '{{ item_type|default:'Unknown' }}',
                                item_id: '{{ item_id|default:'0' }}'
                            });
                            this.style.display='none';
                            this.parentNode.innerHTML='<div class=\'logo-placeholder\'><i class=\'{{ item_icon|default:\"bi bi-building\" }}\'></i></div>';
                         ">
                </div>
            {% else %}
                <div class="logo-container" style="height: 240px; width: 240px; min-height: 240px; min-width: 240px; max-height: 240px; max-width: 240px; display: flex; align-items: center; justify-content: center; background-color: #252525; border-radius: 4px; overflow: hidden; transition: all 0.3s ease; border: 1px solid #333333; position: relative; aspect-ratio: 1/1; margin: 0 auto 15px; z-index: 2; flex-shrink: 0;">
                    <div class="logo-placeholder" style="color: #4da3ff; display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; overflow: visible; padding: 0; background-color: rgba(13, 110, 253, 0.1);">
                        <i class="{{ item_icon|default:'bi bi-building' }}" style="font-size: 180px; line-height: 1; display: block; text-align: center; margin: 0; padding: 0;"></i>
                    </div>
                </div>
            {% endif %}
            <div class="item-info" style="text-align: center; width: 100%;">
                <h5 style="color: #4da3ff; font-size: 1.2rem; font-weight: 600; line-height: 1.3; margin-bottom: 0.5rem; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ item_name|default:'Assistant' }}</h5>

                <!-- Community badge -->
                <span class="badge" style="background-color: #333333; color: #adb5bd; font-size: 0.8rem; padding: 0.25rem 0.5rem; border-radius: 4px; margin-bottom: 10px;">Community</span>
                {% if item_description %}
                    <p style="font-size: 0.9rem; line-height: 1.3; color: #adb5bd; margin-bottom: 0.5rem; white-space: normal; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">{{ item_description|truncatechars:60|safe }}</p>
                {% endif %}
                {% if item_rating %}
                    <div class="star-rating" style="display: inline-flex; align-items: center; font-size: 1em;">
                        {% for i in "12345" %}
                            {% if forloop.counter <= item_rating_int %}
                                <i class="bi bi-star-fill" style="color: #ffc107; margin: 0 1px;"></i>
                            {% else %}
                                <i class="bi bi-star" style="color: #6c757d; margin: 0 1px;"></i>
                            {% endif %}
                        {% endfor %}
                        <span class="rating-count" style="font-size: 0.8em; margin-left: 0.5em; color: #adb5bd;">({{ item_rating_count|default:'0' }})</span>
                    </div>
                {% else %}
                    <!-- No ratings yet text -->
                    <div class="rating-text" style="font-size: 0.8rem; color: #adb5bd; margin-top: 5px; text-align: right; width: 100%;">(No ratings yet)</div>
                {% endif %}
                {% if item_meta %}
                    <p style="font-size: 0.85rem; line-height: 1.3; color: #adb5bd; margin-bottom: 0.5rem;">
                        <i class="{{ item_meta_icon|default:'bi bi-info-circle' }}" style="color: #4da3ff; margin-right: 0.25rem;"></i>
                        {{ item_meta }}
                    </p>
                {% endif %}
            </div>
        </a>

        <!-- Chat Now button -->
        <a href="{{ item_url|default:'#' }}" class="btn btn-primary w-100 mb-2" style="background-color: #0d6efd; border-color: #0d6efd; color: white; width: 100%; margin-bottom: 10px; border-radius: 4px; padding: 8px 16px; font-size: 0.9rem;">
            <i class="bi bi-chat-dots-fill me-1"></i> Chat Now
        </a>

        <!-- Rate button -->
        <button class="btn btn-secondary w-100" style="background-color: #333333; border-color: #444444; color: white; width: 100%; border-radius: 4px; padding: 8px 16px; font-size: 0.9rem;">
            <i class="bi bi-star me-1"></i> Rate
        </button>

        {% if item_id and item_type %}
            <button class="like-button btn btn-icon {{ is_saved|yesno:'text-danger,text-secondary' }}"
                    data-item-id="{{ item_id }}"
                    data-item-type="{{ item_type }}"
                    title="{{ is_saved|yesno:'Unlike,Like' }}"
                    style="background-color: #333333; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); border: 1px solid #444444; width: 40px; height: 40px; padding: 0; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease; z-index: 10; position: absolute; bottom: 15px; right: 15px; color: white; border-radius: 50%;">
                <i class="bi bi-heart-fill"></i>
            </button>
        {% else %}
            <!-- DEBUG: Missing item_id or item_type for like button: item_id={{ item_id|default:'None' }}, item_type={{ item_type|default:'None' }} -->
        {% endif %}
    </div>
</div>
