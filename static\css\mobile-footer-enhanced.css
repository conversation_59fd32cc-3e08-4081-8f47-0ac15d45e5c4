/**
 * Mobile Footer Enhanced CSS
 * Completely hides footer on mobile devices to maximize chat space
 */

/* Completely hide footer on mobile devices */
@media (max-width: 767.98px) {
  /* Hide entire footer on mobile devices */
  footer.footer,
  footer,
  .footer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1000 !important;
  }

  /* Hide all footer content including mobile accordion */
  footer.footer *,
  footer *,
  .footer * {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Ensure body doesn't have bottom margin/padding for footer */
  body {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Ensure main content takes full height without footer space */
  main {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }

  /* Ensure container takes full height without footer space */
  .container-fluid {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }
}

/* Also hide footer on tablet in portrait mode for more space */
@media (min-width: 768px) and (max-width: 991.98px) and (orientation: portrait) {
  footer.footer,
  footer,
  .footer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}
