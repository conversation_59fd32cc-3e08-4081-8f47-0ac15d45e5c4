/**
 * Candidate Cards Hover Fix
 * Fixes the problematic hover effect that highlights all cards instead of individual cards
 */

/* ===== RESET PROBLEMATIC HOVER EFFECTS ===== */

/* Remove all problematic hover effects that affect multiple cards */
.list-group-item,
.list-group-item:hover,
.list-group-item:focus,
.list-group-item:active,
.directory-card,
.directory-card:hover,
.directory-card:focus,
.directory-card:active,
.directory-card.list-group-item,
.directory-card.list-group-item:hover,
.directory-card.list-group-item:focus,
.directory-card.list-group-item:active {
    /* Remove all problematic transforms and 3D effects */
    transform: none !important;
    perspective: none !important;
    backdrop-filter: none !important;
    transform-style: flat !important;
    
    /* Force clean white background - no color changes */
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    
    /* Clean, consistent border */
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    
    /* Professional shadow - no dramatic effects */
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    
    /* Smooth transition only for subtle effects */
    transition: box-shadow 0.2s ease, border-color 0.2s ease !important;
}

/* ===== INDIVIDUAL CARD HOVER EFFECT ===== */

/* Only apply hover effects to individual cards, not all cards */
.directory-card:hover:not(.directory-card:active):not(.directory-card:focus) {
    /* Very subtle shadow increase only */
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
    
    /* Very subtle border color change */
    border-color: rgba(207, 46, 46, 0.2) !important;
    
    /* NO background color change - keep white */
    background: #ffffff !important;
    background-color: #ffffff !important;
    
    /* NO transform effects */
    transform: none !important;
}

/* ===== PREVENT HOVER BLEEDING BETWEEN CARDS ===== */

/* Ensure each card is isolated from others */
.list-group,
.company-cards-container,
.tier-section {
    /* Prevent hover effects from bleeding between cards */
    isolation: isolate !important;
}

.directory-card {
    /* Each card is completely isolated */
    isolation: isolate !important;
    position: relative !important;
    z-index: 1 !important;
    contain: layout style !important;
}

.directory-card:hover {
    /* Slightly higher z-index on hover but no dramatic effects */
    z-index: 2 !important;
}

/* ===== REMOVE ALL PINK/RED BACKGROUND EFFECTS ===== */

/* Force white background on all card states */
.directory-card,
.directory-card:hover,
.directory-card:focus,
.directory-card:active,
.list-group-item,
.list-group-item:hover,
.list-group-item:focus,
.list-group-item:active {
    /* Force white background always - no pink/red tints */
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    background-attachment: scroll !important;
    background-position: 0% 0% !important;
    background-repeat: no-repeat !important;
    background-size: auto !important;
    
    /* Remove any filters that might cause color tints */
    filter: none !important;
    backdrop-filter: none !important;
}

/* ===== OVERRIDE PROBLEMATIC CSS SELECTORS ===== */

/* Override any CSS that might be causing the pink/red hover effect */
.tier-section .directory-card,
.tier-section .directory-card:hover,
.tier-section .list-group-item,
.tier-section .list-group-item:hover,
.company-cards-container .directory-card,
.company-cards-container .directory-card:hover,
.company-cards-container .list-group-item,
.company-cards-container .list-group-item:hover {
    /* Force clean appearance */
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    
    /* Clean border */
    border: 1px solid #e0e0e0 !important;
    
    /* NO transform effects */
    transform: none !important;
    perspective: none !important;
}

/* ===== CLEAN LOGO CONTAINER HOVER ===== */

.directory-card .logo-container {
    transition: border-color 0.2s ease !important;
}

.directory-card:hover .logo-container {
    /* Very subtle border color change only */
    border-color: rgba(207, 46, 46, 0.15) !important;
    
    /* NO transform or dramatic effects */
    transform: none !important;
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.directory-card .logo-container img {
    transition: none !important;
}

.directory-card:hover .logo-container img {
    /* NO transform effects on images */
    transform: none !important;
}

/* ===== CLEAN BUTTON HOVER EFFECTS ===== */

.directory-card .btn {
    transition: background-color 0.2s ease, border-color 0.2s ease !important;
}

.directory-card .btn:hover {
    /* NO transform effects on buttons */
    transform: none !important;
    box-shadow: none !important;
}

.directory-card .rate-company-btn:hover,
.directory-card .action-btn:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    
    /* NO transform or shadow effects */
    transform: none !important;
    box-shadow: none !important;
}

/* ===== REMOVE ALL TRANSFORM EFFECTS ===== */

/* Global reset for any problematic transforms */
.directory-card,
.directory-card *,
.directory-card:hover,
.directory-card:hover *,
.list-group-item,
.list-group-item *,
.list-group-item:hover,
.list-group-item:hover * {
    /* NO 3D transforms anywhere */
    transform: none !important;
    perspective: none !important;
    transform-style: flat !important;
    backface-visibility: visible !important;
}

/* ===== CLEAN FOCUS STATES ===== */

.directory-card:focus,
.directory-card:focus-within {
    /* Clean focus state */
    outline: 2px solid rgba(207, 46, 46, 0.3) !important;
    outline-offset: 2px !important;
    
    /* NO background changes */
    background: #ffffff !important;
    background-color: #ffffff !important;
}

/* ===== ENSURE INDIVIDUAL CARD ISOLATION ===== */

.directory-card {
    /* Each card is completely isolated */
    contain: layout style !important;
    will-change: auto !important;
}

.directory-card:hover {
    /* Minimal hover state */
    will-change: auto !important;
}

/* ===== FINAL OVERRIDE FOR CLEAN APPEARANCE ===== */

.directory-card,
.directory-card:hover,
.directory-card:focus,
.directory-card:active,
.directory-card.list-group-item,
.directory-card.list-group-item:hover,
.directory-card.list-group-item:focus,
.directory-card.list-group-item:active {
    /* Final override to ensure clean appearance */
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    
    /* Clean border */
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    
    /* Minimal shadow */
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    
    /* NO transforms */
    transform: none !important;
    
    /* Clean transition */
    transition: box-shadow 0.2s ease, border-color 0.2s ease !important;
}

/* ===== VERY SUBTLE HOVER ENHANCEMENT ===== */

.directory-card:hover {
    /* Only very subtle shadow and border changes */
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
}

/* ===== OVERRIDE ANY REMAINING PROBLEMATIC STYLES ===== */

/* Ensure no CSS from other files interferes */
.list-group-item.directory-card,
.list-group-item.directory-card:hover,
.company-cards-container .list-group-item,
.company-cards-container .list-group-item:hover,
.tier-section .list-group-item,
.tier-section .list-group-item:hover {
    /* Force clean state */
    background: #ffffff !important;
    background-color: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    transform: none !important;
    perspective: none !important;
    backdrop-filter: none !important;
}

/* ===== PREVENT CASCADING HOVER EFFECTS ===== */

/* Ensure parent containers don't affect child cards */
.tier-section:hover .directory-card,
.company-cards-container:hover .directory-card,
.list-group:hover .list-group-item {
    /* Prevent parent hover from affecting children */
    background: #ffffff !important;
    background-color: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    transform: none !important;
}
