#!/usr/bin/env python
"""
Test Django application email features like user registration, password reset, etc.
"""

import os
import sys
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    
    from django.core.mail import send_mail
    from django.conf import settings
    from django.contrib.auth import get_user_model
    from django.contrib.sites.models import Site
    from accounts.email_utils import get_site_url
    
    User = get_user_model()
    
    print("🧪 Testing Django Application Email Features")
    print("=" * 50)
    
    # Test 1: Site URL function
    print("\n1️⃣ Testing Site URL Function...")
    try:
        site_url = get_site_url()
        print(f"✅ Site URL: {site_url}")
        
        # Check current site configuration
        try:
            current_site = Site.objects.get_current()
            print(f"✅ Current Site: {current_site.domain} ({current_site.name})")
        except Exception as e:
            print(f"⚠️  Site framework issue: {e}")
            
    except Exception as e:
        print(f"❌ Error getting site URL: {e}")
    
    # Test 2: Email utilities import
    print("\n2️⃣ Testing Email Utilities Import...")
    try:
        from accounts.email_utils import send_html_email
        print("✅ send_html_email imported successfully")
        
        # Check function signature
        import inspect
        sig = inspect.signature(send_html_email)
        params = list(sig.parameters.keys())
        print(f"✅ Function parameters: {params}")
        
    except ImportError as e:
        print(f"❌ Error importing email utilities: {e}")
        send_html_email = None
    except Exception as e:
        print(f"❌ Error checking email utilities: {e}")
        send_html_email = None
    
    # Test 3: User registration email simulation
    print("\n3️⃣ Testing User Registration Email Simulation...")
    
    recipient = input("Enter test email address: ").strip()
    if not recipient:
        print("❌ No email address provided, skipping tests")
        sys.exit(1)
    
    try:
        # Simulate user registration email
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        registration_subject = "Welcome to 24seven - Account Created"
        registration_message = f"""
Welcome to 24seven!

Your account has been successfully created at {timestamp}.

Account Details:
- Email: {recipient}
- Platform: 24seven.site
- Registration Time: {timestamp}

You can now:
- Access AI-powered assistants
- Collaborate with your team
- Manage your company profile
- Create and share knowledge

Get started by logging in at: {get_site_url()}

If you have any questions, please contact us.

Best regards,
The 24seven Team
"""
        
        result = send_mail(
            subject=registration_subject,
            message=registration_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient],
            fail_silently=False
        )
        
        if result == 1:
            print(f"✅ Registration email sent to {recipient}")
        else:
            print("❌ Registration email failed")
            
    except Exception as e:
        print(f"❌ Error sending registration email: {e}")
    
    # Test 4: Password reset email simulation
    print("\n4️⃣ Testing Password Reset Email Simulation...")
    try:
        reset_subject = "Password Reset Request - 24seven"
        reset_message = f"""
Password Reset Request

We received a request to reset your password for your 24seven account.

Request Details:
- Email: {recipient}
- Time: {timestamp}
- IP: [Test IP]

To reset your password, click the link below:
{get_site_url()}/accounts/password-reset/confirm/[token]/

This link will expire in 24 hours for security reasons.

If you didn't request this password reset, please ignore this email.

Best regards,
The 24seven Team
"""
        
        result = send_mail(
            subject=reset_subject,
            message=reset_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient],
            fail_silently=False
        )
        
        if result == 1:
            print(f"✅ Password reset email sent to {recipient}")
        else:
            print("❌ Password reset email failed")
            
    except Exception as e:
        print(f"❌ Error sending password reset email: {e}")
    
    # Test 5: Team invitation email simulation
    print("\n5️⃣ Testing Team Invitation Email Simulation...")
    try:
        invitation_subject = "You're Invited to Join a Team on 24seven"
        invitation_message = f"""
Team Invitation

You've been invited to join a team on 24seven!

Invitation Details:
- Invited by: Test User
- Company: Test Company
- Platform: 24seven.site
- Invitation Time: {timestamp}

24seven helps teams collaborate effectively using AI-powered tools and knowledge management.

What you'll get access to:
• AI-powered team assistants
• Shared knowledge base
• Team collaboration tools
• And much more!

To accept this invitation, visit:
{get_site_url()}/accounts/invitations/accept/[token]/

IMPORTANT: This invitation will expire in 7 days.

Questions or concerns?
If you didn't expect this invitation, please contact the person who invited you.

Best regards,
The 24seven Team
"""
        
        result = send_mail(
            subject=invitation_subject,
            message=invitation_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient],
            fail_silently=False
        )
        
        if result == 1:
            print(f"✅ Team invitation email sent to {recipient}")
        else:
            print("❌ Team invitation email failed")
            
    except Exception as e:
        print(f"❌ Error sending team invitation email: {e}")
    
    # Test 6: HTML email with custom utility (if available)
    if send_html_email:
        print("\n6️⃣ Testing HTML Email with Custom Utility...")
        try:
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>24seven Email Test</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f8f9fa; }}
        .success {{ color: #28a745; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>24seven Email System Test</h1>
        </div>
        <div class="content">
            <p>This HTML email was sent using the custom email utility.</p>
            <p><strong>Test Time:</strong> {timestamp}</p>
            <p><strong>Site URL:</strong> {get_site_url()}</p>
            <p class="success">✅ HTML email functionality is working!</p>
        </div>
    </div>
</body>
</html>
"""
            
            send_html_email(
                to_email=recipient,
                subject=f"24seven HTML Email Test - {timestamp}",
                html_content=html_content
            )
            
            print(f"✅ HTML email sent using custom utility to {recipient}")
            
        except Exception as e:
            print(f"❌ Error sending HTML email with custom utility: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 EMAIL FEATURE TEST SUMMARY")
    print("=" * 50)
    print(f"📧 Test emails sent to: {recipient}")
    print("\n✅ Email types tested:")
    print("   1. User Registration Email")
    print("   2. Password Reset Email")
    print("   3. Team Invitation Email")
    if send_html_email:
        print("   4. HTML Email (Custom Utility)")
    
    print(f"\n🔧 Configuration used:")
    print(f"   From: {settings.DEFAULT_FROM_EMAIL}")
    print(f"   SMTP: {settings.EMAIL_HOST}:{settings.EMAIL_PORT}")
    print(f"   Site URL: {get_site_url()}")
    
    print("\n📋 Next Steps:")
    print("1. Check your email inbox for all test messages")
    print("2. Verify emails are not in spam/junk folder")
    print("3. Test actual user registration flow")
    print("4. Test actual password reset flow")
    print("5. Test team invitation functionality")
    
    print("\n🎉 If you received all emails, your Django email system is fully functional!")

except Exception as e:
    print(f"❌ Error running Django email feature test: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
