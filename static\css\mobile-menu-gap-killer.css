/**
 * Mobile Menu Gap Killer CSS
 * MAXIMUM PRIORITY fixes to eliminate all gaps in mobile menu
 * This file should be loaded LAST to override all other styles
 */

/* NUCLEAR OPTION - Remove ALL gaps from unified header mobile menu */
@media (max-width: 991.98px) {
    /* Force remove all margins and padding from navbar elements */
    .unified-header .navbar-collapse,
    .unified-header .navbar-nav,
    .unified-header .nav-item,
    .unified-header .nav-link,
    .unified-header .dropdown,
    .unified-header .dropdown-menu {
        margin: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }

    /* CRITICAL: Hide dropdown menus completely when not shown */
    .unified-header .dropdown-menu:not(.show) {
        display: none !important;
        height: 0 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        position: absolute !important;
        left: -9999px !important;
    }

    /* Specific navbar collapse spacing */
    .unified-header .navbar-collapse {
        margin-top: 0.5rem !important;
        padding-top: 0.5rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    }

    /* Navigation list - remove all default spacing */
    .unified-header .navbar-nav {
        list-style: none !important;
        padding-left: 0 !important;
        margin: 0 !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 0 !important;
    }

    /* Navigation items - zero spacing */
    .unified-header .navbar-nav .nav-item {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        display: block !important;
    }

    /* Navigation links - minimal padding only */
    .unified-header .nav-link {
        padding: 0.5rem 0.75rem !important;
        margin: 0 !important;
        border-radius: 0.375rem !important;
        font-size: 0.95rem !important;
        line-height: 1.2 !important;
        display: flex !important;
        align-items: center !important;
        text-decoration: none !important;
    }

    /* Remove any inherited spacing from Bootstrap */
    .unified-header .navbar-nav > li,
    .unified-header .navbar-nav > .nav-item,
    .unified-header ul.navbar-nav li {
        margin: 0 !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
    }

    /* Dropdown containers - ZERO spacing when collapsed */
    .unified-header .dropdown {
        margin: 0 !important;
        padding: 0 !important;
        height: auto !important;
        min-height: 0 !important;
    }

    /* Dropdown nav items - force compact */
    .unified-header .nav-item.dropdown {
        margin: 0 !important;
        padding: 0 !important;
        height: auto !important;
        min-height: 0 !important;
        display: block !important;
    }

    /* Dropdown menus - no extra spacing and hidden by default */
    .unified-header .dropdown-menu {
        margin: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding: 0.5rem !important;
        border-radius: 0.5rem !important;
        display: none !important;
        position: static !important;
        width: 100% !important;
        box-shadow: none !important;
        border: none !important;
        background-color: rgba(255, 255, 255, 0.05) !important;
    }

    /* Show dropdown menu only when active */
    .unified-header .dropdown-menu.show {
        display: block !important;
        margin-top: 0.25rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Dropdown items */
    .unified-header .dropdown-item {
        padding: 0.5rem 0.75rem !important;
        margin: 0 !important;
        border-radius: 0.375rem !important;
        font-size: 0.9rem !important;
    }

    /* Dropdown toggle - no extra spacing */
    .unified-header .dropdown-toggle {
        margin: 0 !important;
        padding: 0.5rem 0.75rem !important;
    }

    /* Remove any spacing from dropdown wrapper */
    .unified-header .nav-item.dropdown {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* OVERRIDE navbar-dropdown-fix.css for unified header - MAXIMUM PRIORITY */
    .unified-header.navbar-collapse .dropdown-menu,
    .unified-header .navbar-collapse .dropdown-menu,
    .unified-header .navbar .dropdown-menu,
    .unified-header.navbar .dropdown-menu {
        margin: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding: 0 !important;
        background-color: rgba(255, 255, 255, 0.05) !important;
        border: none !important;
        box-shadow: none !important;
        position: static !important;
        width: 100% !important;
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        transform: none !important;
        transition: none !important;
        height: 0 !important;
        overflow: hidden !important;
        max-height: 0 !important;
        min-height: 0 !important;
    }

    /* Show dropdown when expanded - OVERRIDE ALL MARGINS */
    .unified-header.navbar-collapse .dropdown-menu.show,
    .unified-header .navbar-collapse .dropdown-menu.show,
    .unified-header .navbar .dropdown-menu.show,
    .unified-header.navbar .dropdown-menu.show {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        height: auto !important;
        max-height: none !important;
        min-height: 0 !important;
        overflow: visible !important;
        padding: 0.5rem !important;
        margin: 0 !important;
        margin-top: 0.25rem !important;
        margin-bottom: 0.25rem !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    /* Override dropdown items from navbar-dropdown-fix.css */
    .unified-header.navbar-collapse .dropdown-item,
    .unified-header .navbar-collapse .dropdown-item {
        padding: 0.5rem 0.75rem !important;
        margin: 0 !important;
        white-space: normal !important;
    }

    /* OVERRIDE mobile-navbar.css dropdown styles for unified header */
    .unified-header .navbar-collapse .dropdown-menu {
        background-color: rgba(255, 255, 255, 0.05) !important;
        border: none !important;
        padding: 0.5rem !important;
        margin: 0 !important;
        box-shadow: none !important;
        position: static !important;
        float: none !important;
        width: 100% !important;
        border-radius: 0.5rem !important;
        transform: none !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: none !important;
        transition: none !important;
    }

    .unified-header .navbar-collapse .dropdown-menu.show {
        display: block !important;
        margin-top: 0.25rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Override any Bootstrap dropdown spacing */
    .unified-header .dropdown-menu[data-bs-popper] {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    /* Company actions section */
    .unified-header .navbar-company-actions {
        margin-top: 0.5rem !important;
        padding-top: 0.5rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
    }

    /* User menu section */
    .unified-header .navbar-nav:last-child {
        margin-top: 0.5rem !important;
        padding-top: 0.5rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
    }

    /* Override any flex gaps */
    .unified-header .d-flex {
        gap: 0 !important;
    }

    /* Override Bootstrap utility classes that might add spacing */
    .unified-header .mb-1,
    .unified-header .mb-2,
    .unified-header .mb-3,
    .unified-header .mb-4,
    .unified-header .mb-5,
    .unified-header .my-1,
    .unified-header .my-2,
    .unified-header .my-3,
    .unified-header .my-4,
    .unified-header .my-5 {
        margin-bottom: 0 !important;
    }

    .unified-header .mt-1,
    .unified-header .mt-2,
    .unified-header .mt-3,
    .unified-header .mt-4,
    .unified-header .mt-5,
    .unified-header .my-1,
    .unified-header .my-2,
    .unified-header .my-3,
    .unified-header .my-4,
    .unified-header .my-5 {
        margin-top: 0 !important;
    }

    /* Remove any padding utilities that create gaps */
    .unified-header .pb-1,
    .unified-header .pb-2,
    .unified-header .pb-3,
    .unified-header .pb-4,
    .unified-header .pb-5,
    .unified-header .py-1,
    .unified-header .py-2,
    .unified-header .py-3,
    .unified-header .py-4,
    .unified-header .py-5 {
        padding-bottom: 0 !important;
    }

    .unified-header .pt-1,
    .unified-header .pt-2,
    .unified-header .pt-3,
    .unified-header .pt-4,
    .unified-header .pt-5,
    .unified-header .py-1,
    .unified-header .py-2,
    .unified-header .py-3,
    .unified-header .py-4,
    .unified-header .py-5 {
        padding-top: 0 !important;
    }

    /* Force compact layout */
    .unified-header .navbar-collapse > * {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    .unified-header .navbar-collapse > *:not(:first-child) {
        margin-top: 0 !important;
    }

    .unified-header .navbar-collapse > *:not(:last-child) {
        margin-bottom: 0 !important;
    }

    /* TARGETED: Fix dropdown menu spacing issues */
    .unified-header .dropdown-menu:not(.show) {
        display: none !important;
        height: 0 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    /* Ensure dropdown containers don't take extra space */
    .unified-header .nav-item.dropdown {
        position: relative !important;
    }

    /* Force dropdown toggles to have no extra height */
    .unified-header .dropdown-toggle {
        height: auto !important;
        min-height: 0 !important;
        line-height: 1.2 !important;
    }
}

/* Extra small screens - even more compact */
@media (max-width: 576px) {
    .unified-header .nav-link {
        padding: 0.4rem 0.6rem !important;
        font-size: 0.9rem !important;
    }

    .unified-header .dropdown-item {
        padding: 0.4rem 0.6rem !important;
        font-size: 0.85rem !important;
    }

    .unified-header .navbar-collapse {
        margin-top: 0.4rem !important;
        padding-top: 0.4rem !important;
    }

    .unified-header .navbar-company-actions,
    .unified-header .navbar-nav:last-child {
        margin-top: 0.4rem !important;
        padding-top: 0.4rem !important;
    }
}

/* Dark theme compatibility */
[data-theme="dark"] .unified-header .navbar-collapse,
[data-theme="dark"] .unified-header .navbar-company-actions,
[data-theme="dark"] .unified-header .navbar-nav:last-child {
    border-top-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="light"] .unified-header .navbar-collapse,
[data-theme="light"] .unified-header .navbar-company-actions,
[data-theme="light"] .unified-header .navbar-nav:last-child {
    border-top-color: rgba(0, 0, 0, 0.1) !important;
}

/* Dark theme dropdown overrides */
[data-theme="dark"] .unified-header .navbar-collapse .dropdown-menu {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

[data-theme="light"] .unified-header .navbar-collapse .dropdown-menu {
    background-color: rgba(0, 0, 0, 0.02) !important;
}

/* Landscape mode - even more compact */
@media (max-width: 991.98px) and (orientation: landscape) and (max-height: 500px) {
    .unified-header .navbar-collapse {
        margin-top: 0.3rem !important;
        padding-top: 0.3rem !important;
    }

    .unified-header .nav-link {
        padding: 0.3rem 0.5rem !important;
        font-size: 0.85rem !important;
    }

    .unified-header .navbar-company-actions,
    .unified-header .navbar-nav:last-child {
        margin-top: 0.3rem !important;
        padding-top: 0.3rem !important;
    }
}
