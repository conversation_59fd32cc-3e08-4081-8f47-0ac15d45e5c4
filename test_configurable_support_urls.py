#!/usr/bin/env python
"""
Test script to verify configurable support URLs in email templates.
"""

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.email_utils import send_enhanced_email, send_signin_approval_email
from accounts.auth_utils import store_signin_approval
from django.urls import reverse
from site_settings.models import SiteConfiguration

User = get_user_model()

print("🔧 Testing Configurable Support URLs in Email Templates")
print("=" * 70)

# Get or create site configuration
site_config = SiteConfiguration.load()
print(f"✅ Site Configuration loaded: {site_config.site_name}")

# Display current configuration
print(f"📧 Support Email: {site_config.support_email or 'Not set (will use default)'}")
print(f"🔗 Support URL: {site_config.support_url or 'Not set (will use contact_url)'}")
print(f"📞 Contact URL: {site_config.contact_url or 'Not set (will use default)'}")

# Update site configuration for testing
print("\n🔧 Setting test configuration...")
site_config.support_email = "<EMAIL>"
site_config.support_url = "https://24seven.site/help/"
site_config.contact_url = "https://24seven.site/contact/"
site_config.save()

print(f"✅ Updated Support Email: {site_config.support_email}")
print(f"✅ Updated Support URL: {site_config.support_url}")
print(f"✅ Updated Contact URL: {site_config.contact_url}")

# Create test user
user, created = User.objects.get_or_create(
    username='configtest', 
    defaults={'email': '<EMAIL>', 'is_active': True}
)
print(f"\n✅ Test user: {user.username} ({user.email})")

# Test 1: Welcome Email with configurable URLs
print("\n1️⃣ Testing Welcome Email with configurable support URLs...")
try:
    success = send_enhanced_email(
        to_email=user.email,
        subject="Welcome - Testing Configurable URLs",
        email_type="welcome",
        user=user,
        platform_name="24seven",
        getting_started_url="https://24seven.site/getting-started/",
        dashboard_url="https://24seven.site/dashboard/"
    )
    if success:
        print("✅ Welcome email sent with configurable URLs!")
    else:
        print("❌ Failed to send welcome email")
except Exception as e:
    print(f"❌ Error sending welcome email: {e}")

# Test 2: Sign-in Approval Email with configurable URLs
print("\n2️⃣ Testing Sign-in Approval Email with configurable support URLs...")
try:
    token = store_signin_approval(user, 24)
    approval_url = f"http://127.0.0.1:8000{reverse('accounts:approve_signin', kwargs={'token': token})}"
    
    success = send_signin_approval_email(user, approval_url, 24)
    if success:
        print("✅ Sign-in approval email sent with configurable URLs!")
        print(f"🔗 Approval URL: {approval_url}")
    else:
        print("❌ Failed to send sign-in approval email")
except Exception as e:
    print(f"❌ Error sending sign-in approval email: {e}")

# Test 3: Notification Email with configurable URLs
print("\n3️⃣ Testing Notification Email with configurable support URLs...")
try:
    notification_data = {
        'title': 'Account Settings Updated',
        'message': 'Your account settings have been successfully updated.',
        'type': 'success',
        'action_url': 'https://24seven.site/settings/',
        'action_text': 'View Settings',
        'timestamp': '2025-05-26 08:30:00'
    }
    
    success = send_enhanced_email(
        to_email=user.email,
        subject="Notification - Testing Configurable URLs",
        email_type="notification",
        user=user,
        notification=notification_data
    )
    if success:
        print("✅ Notification email sent with configurable URLs!")
    else:
        print("❌ Failed to send notification email")
except Exception as e:
    print(f"❌ Error sending notification email: {e}")

# Test 4: Password Reset Email with configurable URLs
print("\n4️⃣ Testing Password Reset Email with configurable support URLs...")
try:
    success = send_enhanced_email(
        to_email=user.email,
        subject="Password Reset - Testing Configurable URLs",
        email_type="password_reset",
        user=user,
        reset_url="https://24seven.site/reset/abc123/",
        protocol="https",
        domain="24seven.site"
    )
    if success:
        print("✅ Password reset email sent with configurable URLs!")
    else:
        print("❌ Failed to send password reset email")
except Exception as e:
    print(f"❌ Error sending password reset email: {e}")

print("\n" + "=" * 70)
print("📊 CONFIGURABLE SUPPORT URL TEST COMPLETE")
print("=" * 70)

print(f"📧 All test emails sent to: {user.email}")
print(f"🔗 Support URLs now use: {site_config.support_url}")
print(f"📞 Support emails now use: {site_config.support_email}")

print("\n✅ Email Template URL Configuration:")
print("   🎯 Support Center links → site_config.support_url")
print("   📧 Support email addresses → site_config.support_email")
print("   📞 Contact links → site_config.contact_url")
print("   🔄 Fallback chain: support_url → contact_url → default")

print("\n🎉 All email templates now use configurable URLs!")
print("   Admins can update these URLs in Django Admin → Site Configuration")

print(f"\n📧 Check {user.email} inbox to see emails with configurable URLs!")

# Show admin URL
print(f"\n🔧 Configure URLs at: http://127.0.0.1:8000/admin/site_settings/siteconfiguration/1/change/")

print("\n" + "=" * 70)
