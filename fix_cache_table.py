#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to fix the missing django_cache_table error on cPanel deployment.
This script creates the necessary cache table and runs migrations.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line
from django.db import connection

def setup_django():
    """Setup Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
    django.setup()

def check_cache_table_exists():
    """Check if the django_cache_table already exists."""
    print("Checking if cache table exists...")
    try:
        with connection.cursor() as cursor:
            # Check if the table exists in PostgreSQL
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = 'django_cache_table'
                );
            """)
            exists = cursor.fetchone()[0]

            if exists:
                print("✅ Cache table 'django_cache_table' already exists!")

                # Check if it has data
                cursor.execute("SELECT COUNT(*) FROM django_cache_table;")
                count = cursor.fetchone()[0]
                print(f"📊 Cache table contains {count} entries")

                return True
            else:
                print("❌ Cache table 'django_cache_table' does not exist")
                return False

    except Exception as e:
        print(f"❌ Error checking cache table: {e}")
        return False

def create_cache_table():
    """Create the database cache table."""
    print("Creating database cache table...")
    try:
        execute_from_command_line(['manage.py', 'createcachetable'])
        print("✅ Cache table created successfully!")
        return True
    except Exception as e:
        print(f"❌ Error creating cache table: {e}")
        return False

def run_migrations():
    """Run database migrations."""
    print("Running database migrations...")
    try:
        execute_from_command_line(['manage.py', 'migrate'])
        print("✅ Migrations completed successfully!")
        return True
    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        return False

def collect_static():
    """Collect static files."""
    print("Collecting static files...")
    try:
        execute_from_command_line(['manage.py', 'collectstatic', '--noinput'])
        print("✅ Static files collected successfully!")
        return True
    except Exception as e:
        print(f"❌ Error collecting static files: {e}")
        return False

def main():
    """Main function to fix cPanel deployment issues."""
    print("🔧 Fixing cPanel deployment issues...")
    print("=" * 50)

    # Setup Django
    setup_django()

    # Step 0: Check if cache table already exists
    table_exists = check_cache_table_exists()

    # Step 1: Run migrations first
    if not run_migrations():
        print("❌ Failed to run migrations. Please check your database connection.")
        return False

    # Step 2: Create cache table (only if it doesn't exist)
    if not table_exists:
        if not create_cache_table():
            print("❌ Failed to create cache table. Please check your database permissions.")
            return False
    else:
        print("⏭️  Skipping cache table creation - table already exists")

    # Step 3: Verify cache table exists after creation
    if check_cache_table_exists():
        print("✅ Cache table verification successful!")
    else:
        print("⚠️  Warning: Cache table verification failed")

    # Step 4: Collect static files
    if not collect_static():
        print("⚠️  Warning: Failed to collect static files. This may not be critical.")

    print("=" * 50)
    print("🎉 cPanel deployment fix completed!")
    print("\nNext steps:")
    print("1. Restart your cPanel application")
    print("2. Check if the error is resolved")
    print("3. If issues persist, check the database connection settings")

    return True

if __name__ == '__main__':
    main()
