/**
 * Clean Card Hover Fix
 * Removes problematic hover effects and creates clean, individual card hover states
 */

/* ===== RESET PROBLEMATIC HOVER EFFECTS - EXCLUDE DIRECTORY CARDS ===== */
.list-group-item:not(.directory-card),
.list-group-item:not(.directory-card):hover,
.list-group-item:not(.directory-card):focus,
.list-group-item:not(.directory-card):active {
    /* Remove all problematic transforms and effects */
    transform: none !important;
    perspective: none !important;
    backdrop-filter: none !important;

    /* Clean background - no color changes */
    background: #ffffff !important;
    background-color: #ffffff !important;

    /* Subtle, consistent border */
    border: 1px solid #e0e0e0 !important;

    /* Clean, professional shadow */
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;

    /* Smooth transition only for subtle effects */
    transition: box-shadow 0.2s ease, border-color 0.2s ease !important;
}

/* ===== INDIVIDUAL CARD HOVER EFFECT ===== */
.directory-card:hover:not(.directory-card:active):not(.directory-card:focus) {
    /* Very subtle shadow increase */
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
    
    /* Very subtle border color change */
    border-color: rgba(207, 46, 46, 0.2) !important;
    
    /* NO background color change */
    background: #ffffff !important;
    background-color: #ffffff !important;
    
    /* NO transform effects */
    transform: none !important;
}

/* ===== PREVENT HOVER BLEEDING TO OTHER CARDS ===== */
.list-group,
.company-cards-container,
.list-group-item + .list-group-item {
    /* Ensure hover effects don't bleed between cards */
    isolation: isolate !important;
}

.directory-card {
    /* Each card is isolated */
    isolation: isolate !important;
    position: relative !important;
    z-index: 1 !important;
}

.directory-card:hover {
    /* Slightly higher z-index on hover but no dramatic effects */
    z-index: 2 !important;
}

/* ===== CLEAN LOGO CONTAINER HOVER ===== */
.directory-card .logo-container {
    transition: border-color 0.2s ease !important;
}

.directory-card:hover .logo-container {
    /* Very subtle border color change only */
    border-color: rgba(207, 46, 46, 0.15) !important;
    
    /* NO transform or dramatic effects */
    transform: none !important;
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.directory-card .logo-container img {
    transition: none !important;
}

.directory-card:hover .logo-container img {
    /* NO transform effects on images */
    transform: none !important;
}

/* ===== CLEAN BUTTON HOVER EFFECTS ===== */
.directory-card .btn {
    transition: background-color 0.2s ease, border-color 0.2s ease !important;
}

.directory-card .btn:hover {
    /* NO transform effects on buttons */
    transform: none !important;
    box-shadow: none !important;
}

.directory-card .rate-company-btn:hover,
.directory-card .rate-assistant-btn:hover,
.directory-card .action-btn:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    
    /* NO transform or shadow effects */
    transform: none !important;
    box-shadow: none !important;
}

/* ===== REMOVE PINK/RED BACKGROUND EFFECTS - EXCLUDE DIRECTORY CARDS ===== */
.list-group-item:not(.directory-card),
.list-group-item:not(.directory-card):hover,
.list-group-item:not(.directory-card):focus,
.list-group-item:not(.directory-card):active {
    /* Force white background always for non-directory cards */
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;

    /* Remove any pink/red tints */
    filter: none !important;
    backdrop-filter: none !important;
}

/* ===== OVERRIDE ANY PROBLEMATIC GRADIENTS ===== */
.directory-card *,
.directory-card:hover *,
.list-group-item *,
.list-group-item:hover * {
    /* Ensure no child elements have problematic backgrounds */
    background-image: none !important;
    filter: none !important;
}

/* ===== CLEAN LINK WRAPPER HOVER ===== */
.directory-card .directory-item-link-wrapper {
    transition: none !important;
}

.directory-card .directory-item-link-wrapper:hover {
    /* NO effects on link wrapper */
    transform: none !important;
    background: transparent !important;
    background-color: transparent !important;
}

/* ===== TIER SECTION HOVER FIXES ===== */
.tier-section,
.tier-section:hover,
.tier-section .directory-card,
.tier-section .directory-card:hover {
    /* Ensure tier sections don't affect card hover */
    background: transparent !important;
    background-color: transparent !important;
}

/* ===== COMPANY CARDS CONTAINER FIXES ===== */
.company-cards-container,
.company-cards-container:hover,
.company-cards-container .directory-card,
.company-cards-container .directory-card:hover {
    /* Ensure container doesn't affect individual cards */
    background: transparent !important;
    background-color: transparent !important;
}

/* ===== REMOVE ALL TRANSFORM EFFECTS ===== */
* {
    /* Global reset for any problematic transforms */
    perspective: none !important;
    transform-style: flat !important;
}

.directory-card,
.directory-card *,
.directory-card:hover,
.directory-card:hover *,
.list-group-item,
.list-group-item *,
.list-group-item:hover,
.list-group-item:hover * {
    /* NO 3D transforms anywhere */
    transform: none !important;
    perspective: none !important;
    transform-style: flat !important;
    backface-visibility: visible !important;
}

/* ===== CLEAN FOCUS STATES ===== */
.directory-card:focus,
.directory-card:focus-within {
    /* Clean focus state */
    outline: 2px solid rgba(207, 46, 46, 0.3) !important;
    outline-offset: 2px !important;
    
    /* NO background changes */
    background: #ffffff !important;
    background-color: #ffffff !important;
}

/* ===== ENSURE INDIVIDUAL CARD ISOLATION ===== */
.directory-card {
    /* Each card is completely isolated */
    contain: layout style !important;
    will-change: auto !important;
}

.directory-card:hover {
    /* Minimal hover state */
    will-change: auto !important;
}

/* ===== OVERRIDE ANY REMAINING PROBLEMATIC STYLES ===== */
.directory-card,
.directory-card:hover,
.directory-card:focus,
.directory-card:active,
.directory-card.list-group-item,
.directory-card.list-group-item:hover,
.directory-card.list-group-item:focus,
.directory-card.list-group-item:active {
    /* Final override to ensure clean appearance */
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    background-attachment: scroll !important;
    background-position: 0% 0% !important;
    background-repeat: no-repeat !important;
    background-size: auto !important;
    
    /* Clean border */
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    
    /* Minimal shadow */
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    
    /* NO transforms */
    transform: none !important;
    
    /* Clean transition */
    transition: box-shadow 0.2s ease, border-color 0.2s ease !important;
}

/* ===== VERY SUBTLE HOVER ENHANCEMENT ===== */
.directory-card:hover {
    /* Only very subtle shadow and border changes */
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
}
