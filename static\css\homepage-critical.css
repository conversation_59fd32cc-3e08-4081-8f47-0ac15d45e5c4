/**
 * Homepage Critical CSS
 * This file contains only the most critical styles needed for immediate rendering
 * Must be inlined or loaded synchronously to prevent FOUC (Flash of Unstyled Content)
 */

/* ===== IMMEDIATE BACKGROUND FIX ===== */
html {
    background-color: #121212 !important;
}

body {
    background-color: #121212 !important;
    color: #ffffff !important;
    margin: 0 !important;
    padding: 0 !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif !important;
    transition: none !important;
}

/* ===== CRITICAL HERO SECTION ===== */
.hero-section,
.bg-primary,
section.py-5.bg-primary {
    background-color: #121212 !important;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 102, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 60, 180, 0.2) 0%, transparent 50%),
        linear-gradient(to bottom, #121212, #0a0a0a) !important;
    color: #ffffff !important;
    min-height: 100vh !important;
    padding: 3rem 0 !important;
}

/* ===== CRITICAL CARD STYLES ===== */
.card {
    background-color: #1e1e1e !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
    opacity: 1 !important;
    transform: none !important;
    transition: none !important;
}

.card-body {
    color: #ffffff !important;
    padding: 1.5rem !important;
}

.card-title,
.card h3,
.card h5 {
    color: #ffffff !important;
    margin-bottom: 0.75rem !important;
}

.card-text,
.card .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Feature cards - removed old styles to prevent conflicts */
/* Modern feature cards are now handled by homepage-optimized.css */

.feature-icon.bg-primary {
    background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
    box-shadow: 0 8px 25px rgba(207, 46, 46, 0.3) !important;
}

.feature-icon.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3) !important;
}

.feature-icon.bg-info {
    background: linear-gradient(135deg, #252638 0%, #1e1f2e 100%) !important;
    box-shadow: 0 8px 25px rgba(37, 38, 56, 0.3) !important;
}

/* Feature text */
.feature-title {
    color: #ffffff !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}

.feature-description {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.95rem !important;
    line-height: 1.6 !important;
    margin-bottom: 1.5rem !important;
}

/* ===== CRITICAL CONTAINER STYLES ===== */
.container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 1rem !important;
}

.row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin: 0 -0.75rem !important;
}

.col-lg-6 {
    flex: 0 0 auto !important;
    width: 100% !important;
    padding: 0 0.75rem !important;
}

@media (min-width: 992px) {
    .col-lg-6 {
        width: 50% !important;
    }
}

/* ===== CRITICAL TEXT STYLES ===== */
h1, h2, h3, h4, h5, h6 {
    color: #ffffff !important;
    margin-bottom: 1rem !important;
}

.display-4 {
    font-size: 3.5rem !important;
    font-weight: 700 !important;
    line-height: 1.2 !important;
}

.lead {
    font-size: 1.25rem !important;
    font-weight: 300 !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* ===== CRITICAL BUTTON STYLES ===== */
.btn {
    display: inline-block !important;
    padding: 0.75rem 1.5rem !important;
    margin-bottom: 0 !important;
    font-size: 1rem !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    text-align: center !important;
    text-decoration: none !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    border: 1px solid transparent !important;
    border-radius: 0.375rem !important;
    transition: none !important;
}

.btn-primary {
    background-color: #0066ff !important;
    border-color: #0066ff !important;
    color: #ffffff !important;
}

.btn-light {
    background-color: #ffffff !important;
    border-color: #ffffff !important;
    color: #000000 !important;
}

.btn-outline-light {
    background-color: transparent !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: #ffffff !important;
}

.btn-lg {
    padding: 1rem 2rem !important;
    font-size: 1.125rem !important;
}

/* ===== CRITICAL SEARCH FORM ===== */
.search-form-card {
    background-color: #1e1e1e !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    padding: 1.5rem !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3) !important;
}

.form-control {
    background-color: #2a2a2a !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
    padding: 0.75rem !important;
    border-radius: 0.375rem !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

.form-label {
    color: #ffffff !important;
    margin-bottom: 0.5rem !important;
    font-weight: 500 !important;
}

/* ===== CRITICAL UTILITIES ===== */
.py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.text-white {
    color: #ffffff !important;
}

.text-center {
    text-align: center !important;
}

.d-flex {
    display: flex !important;
}

.align-items-center {
    align-items: center !important;
}

.justify-content-center {
    justify-content: center !important;
}

/* ===== CRITICAL RESPONSIVE ===== */
@media (max-width: 767.98px) {
    .display-4 {
        font-size: 2.5rem !important;
    }

    .hero-section {
        min-height: auto !important;
        padding: 2rem 0 !important;
    }

    .container {
        padding: 0 0.5rem !important;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem !important;
        font-size: 1rem !important;
    }
}

/* ===== PREVENT LAYOUT SHIFT ===== */
* {
    box-sizing: border-box !important;
}

img {
    max-width: 100% !important;
    height: auto !important;
}

/* ===== LOADING STATE ===== */
.loading {
    opacity: 0.7 !important;
}

/* ===== DARK MODE OVERRIDES ===== */
[data-theme="dark"] body,
[data-theme="dark"] .bg-light {
    background-color: #121212 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .text-dark {
    color: #ffffff !important;
}

/* Only apply to specific bg-white sections, not all */
[data-theme="dark"] section.bg-white {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

[data-theme="dark"] .bg-white.rounded-3 {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* ===== IMMEDIATE VISIBILITY ===== */
.hero-section,
.search-form-card,
.card {
    visibility: visible !important;
    opacity: 1 !important;
}
