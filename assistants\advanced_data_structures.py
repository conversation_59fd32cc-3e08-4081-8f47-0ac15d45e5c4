"""
Advanced data structures for high-performance operations.
Implements Trie, LRU Cache, Bloom Filter, and other optimized structures.
"""

import time
import hashlib
import threading
from collections import OrderedDict, defaultdict, deque
from typing import Dict, List, Optional, Any, Set, Tuple
import heapq
import logging

logger = logging.getLogger(__name__)


class TrieNode:
    """Node for Trie data structure."""
    
    def __init__(self):
        self.children = {}
        self.is_end_word = False
        self.data = None
        self.frequency = 0


class Trie:
    """
    Trie data structure for fast prefix-based searches.
    Optimized for autocomplete and search suggestions.
    """
    
    def __init__(self):
        self.root = TrieNode()
        self.size = 0
    
    def insert(self, word: str, data: Any = None) -> None:
        """Insert a word with optional associated data."""
        node = self.root
        for char in word.lower():
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]
            node.frequency += 1
        
        if not node.is_end_word:
            self.size += 1
        node.is_end_word = True
        node.data = data
    
    def search(self, word: str) -> Optional[Any]:
        """Search for a word and return associated data."""
        node = self._find_node(word)
        return node.data if node and node.is_end_word else None
    
    def starts_with(self, prefix: str, limit: int = 10) -> List[Tuple[str, Any]]:
        """Find all words starting with prefix, sorted by frequency."""
        node = self._find_node(prefix)
        if not node:
            return []
        
        results = []
        self._collect_words(node, prefix, results)
        
        # Sort by frequency (descending) and limit results
        results.sort(key=lambda x: x[2], reverse=True)
        return [(word, data) for word, data, _ in results[:limit]]
    
    def _find_node(self, prefix: str) -> Optional[TrieNode]:
        """Find the node corresponding to a prefix."""
        node = self.root
        for char in prefix.lower():
            if char not in node.children:
                return None
            node = node.children[char]
        return node
    
    def _collect_words(self, node: TrieNode, prefix: str, results: List):
        """Recursively collect all words from a node."""
        if node.is_end_word:
            results.append((prefix, node.data, node.frequency))
        
        for char, child in node.children.items():
            self._collect_words(child, prefix + char, results)


class LRUCacheWithTTL:
    """
    LRU Cache with Time-To-Live support.
    Combines LRU eviction with automatic expiration.
    """
    
    def __init__(self, capacity: int, default_ttl: int = 3600):
        self.capacity = capacity
        self.default_ttl = default_ttl
        self.cache = OrderedDict()
        self.timestamps = {}
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache, return None if expired or not found."""
        with self.lock:
            if key not in self.cache:
                return None
            
            # Check if expired
            if self._is_expired(key):
                self._remove(key)
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            return self.cache[key]
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Put value in cache with optional custom TTL."""
        with self.lock:
            current_time = time.time()
            ttl = ttl or self.default_ttl
            
            if key in self.cache:
                # Update existing
                self.cache[key] = value
                self.timestamps[key] = current_time + ttl
                self.cache.move_to_end(key)
            else:
                # Add new
                if len(self.cache) >= self.capacity:
                    # Remove least recently used
                    oldest_key = next(iter(self.cache))
                    self._remove(oldest_key)
                
                self.cache[key] = value
                self.timestamps[key] = current_time + ttl
    
    def _is_expired(self, key: str) -> bool:
        """Check if a key has expired."""
        return time.time() > self.timestamps.get(key, 0)
    
    def _remove(self, key: str) -> None:
        """Remove a key from cache and timestamps."""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
    
    def cleanup_expired(self) -> int:
        """Remove all expired entries and return count removed."""
        with self.lock:
            current_time = time.time()
            expired_keys = [
                key for key, expiry in self.timestamps.items()
                if current_time > expiry
            ]
            
            for key in expired_keys:
                self._remove(key)
            
            return len(expired_keys)


class BloomFilter:
    """
    Bloom filter for fast membership testing.
    Useful for checking if an item might exist before expensive lookups.
    """
    
    def __init__(self, capacity: int, error_rate: float = 0.1):
        self.capacity = capacity
        self.error_rate = error_rate
        
        # Calculate optimal parameters
        self.bit_array_size = self._calculate_bit_array_size()
        self.hash_count = self._calculate_hash_count()
        
        self.bit_array = [False] * self.bit_array_size
        self.item_count = 0
    
    def add(self, item: str) -> None:
        """Add an item to the bloom filter."""
        for i in range(self.hash_count):
            index = self._hash(item, i) % self.bit_array_size
            self.bit_array[index] = True
        self.item_count += 1
    
    def might_contain(self, item: str) -> bool:
        """Check if item might be in the set (no false negatives)."""
        for i in range(self.hash_count):
            index = self._hash(item, i) % self.bit_array_size
            if not self.bit_array[index]:
                return False
        return True
    
    def _hash(self, item: str, seed: int) -> int:
        """Generate hash for item with seed."""
        return int(hashlib.md5(f"{item}{seed}".encode()).hexdigest(), 16)
    
    def _calculate_bit_array_size(self) -> int:
        """Calculate optimal bit array size."""
        import math
        return int(-self.capacity * math.log(self.error_rate) / (math.log(2) ** 2))
    
    def _calculate_hash_count(self) -> int:
        """Calculate optimal number of hash functions."""
        import math
        return int(self.bit_array_size * math.log(2) / self.capacity)


class PriorityQueue:
    """
    Thread-safe priority queue for task scheduling.
    Lower priority numbers = higher priority.
    """
    
    def __init__(self):
        self._queue = []
        self._index = 0
        self.lock = threading.Lock()
    
    def put(self, item: Any, priority: int = 0) -> None:
        """Add item with priority."""
        with self.lock:
            heapq.heappush(self._queue, (priority, self._index, item))
            self._index += 1
    
    def get(self) -> Optional[Any]:
        """Get highest priority item."""
        with self.lock:
            if self._queue:
                return heapq.heappop(self._queue)[2]
            return None
    
    def peek(self) -> Optional[Any]:
        """Peek at highest priority item without removing."""
        with self.lock:
            if self._queue:
                return self._queue[0][2]
            return None
    
    def size(self) -> int:
        """Get queue size."""
        with self.lock:
            return len(self._queue)
    
    def empty(self) -> bool:
        """Check if queue is empty."""
        with self.lock:
            return len(self._queue) == 0


class CircularBuffer:
    """
    Fixed-size circular buffer for efficient recent data storage.
    Automatically overwrites oldest data when full.
    """
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.buffer = [None] * capacity
        self.head = 0
        self.size = 0
        self.lock = threading.Lock()
    
    def append(self, item: Any) -> None:
        """Add item to buffer."""
        with self.lock:
            self.buffer[self.head] = item
            self.head = (self.head + 1) % self.capacity
            if self.size < self.capacity:
                self.size += 1
    
    def get_recent(self, count: int = None) -> List[Any]:
        """Get most recent items."""
        with self.lock:
            count = min(count or self.size, self.size)
            if count == 0:
                return []
            
            items = []
            for i in range(count):
                index = (self.head - 1 - i) % self.capacity
                if self.buffer[index] is not None:
                    items.append(self.buffer[index])
            
            return items
    
    def is_full(self) -> bool:
        """Check if buffer is full."""
        with self.lock:
            return self.size == self.capacity


class FastHashTable:
    """
    High-performance hash table with collision handling.
    Optimized for frequent lookups and updates.
    """
    
    def __init__(self, initial_capacity: int = 16, load_factor: float = 0.75):
        self.capacity = initial_capacity
        self.load_factor = load_factor
        self.size = 0
        self.buckets = [[] for _ in range(self.capacity)]
        self.lock = threading.RLock()
    
    def put(self, key: str, value: Any) -> None:
        """Insert or update key-value pair."""
        with self.lock:
            if self.size >= self.capacity * self.load_factor:
                self._resize()
            
            bucket_index = self._hash(key)
            bucket = self.buckets[bucket_index]
            
            # Update existing key
            for i, (k, v) in enumerate(bucket):
                if k == key:
                    bucket[i] = (key, value)
                    return
            
            # Add new key
            bucket.append((key, value))
            self.size += 1
    
    def get(self, key: str) -> Optional[Any]:
        """Get value for key."""
        with self.lock:
            bucket_index = self._hash(key)
            bucket = self.buckets[bucket_index]
            
            for k, v in bucket:
                if k == key:
                    return v
            return None
    
    def delete(self, key: str) -> bool:
        """Delete key-value pair."""
        with self.lock:
            bucket_index = self._hash(key)
            bucket = self.buckets[bucket_index]
            
            for i, (k, v) in enumerate(bucket):
                if k == key:
                    bucket.pop(i)
                    self.size -= 1
                    return True
            return False
    
    def _hash(self, key: str) -> int:
        """Hash function for key."""
        return hash(key) % self.capacity
    
    def _resize(self) -> None:
        """Resize hash table when load factor exceeded."""
        old_buckets = self.buckets
        self.capacity *= 2
        self.size = 0
        self.buckets = [[] for _ in range(self.capacity)]
        
        # Rehash all items
        for bucket in old_buckets:
            for key, value in bucket:
                self.put(key, value)


# Global instances for application use
assistant_search_trie = Trie()
response_cache = LRUCacheWithTTL(capacity=1000, default_ttl=3600)
query_bloom_filter = BloomFilter(capacity=10000, error_rate=0.1)
task_queue = PriorityQueue()
recent_interactions = CircularBuffer(capacity=100)
fast_lookup_table = FastHashTable()


def initialize_data_structures():
    """Initialize data structures with existing data."""
    logger.info("Initializing advanced data structures...")
    
    try:
        # Populate search trie with assistant names
        from .models import Assistant
        assistants = Assistant.objects.filter(is_active=True).values('id', 'name', 'description')
        
        for assistant in assistants:
            assistant_search_trie.insert(
                assistant['name'], 
                {'id': assistant['id'], 'description': assistant['description']}
            )
        
        logger.info(f"Initialized search trie with {len(assistants)} assistants")
        
        # Populate bloom filter with common queries
        from .models import Interaction
        common_queries = Interaction.objects.values_list('prompt', flat=True).distinct()[:1000]
        
        for query in common_queries:
            if query:
                query_bloom_filter.add(query.lower())
        
        logger.info(f"Initialized bloom filter with {len(common_queries)} queries")
        
    except Exception as e:
        logger.error(f"Error initializing data structures: {e}")


# Utility functions
def search_assistants_fast(query: str, limit: int = 10) -> List[Dict]:
    """Fast assistant search using trie."""
    return assistant_search_trie.starts_with(query, limit)


def cache_response(key: str, response: Any, ttl: int = 3600) -> None:
    """Cache a response with TTL."""
    response_cache.put(key, response, ttl)


def get_cached_response(key: str) -> Optional[Any]:
    """Get cached response."""
    return response_cache.get(key)


def might_have_seen_query(query: str) -> bool:
    """Check if we might have seen this query before."""
    return query_bloom_filter.might_contain(query.lower())


def add_task(task: Any, priority: int = 0) -> None:
    """Add task to priority queue."""
    task_queue.put(task, priority)


def get_next_task() -> Optional[Any]:
    """Get next highest priority task."""
    return task_queue.get()


def record_interaction(interaction_data: Dict) -> None:
    """Record interaction in circular buffer."""
    recent_interactions.append(interaction_data)


def get_recent_interactions(count: int = 10) -> List[Dict]:
    """Get recent interactions."""
    return recent_interactions.get_recent(count)
