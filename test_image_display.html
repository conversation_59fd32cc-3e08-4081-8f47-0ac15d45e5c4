<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Display Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .image-test {
            display: inline-block;
            margin: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .logo-container {
            width: 180px;
            height: 180px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            background: white;
        }
        .logo-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.loading { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Image Display Test</h1>
        <p>Testing various image sources to verify display functionality:</p>

        <!-- Test 1: Default Company Logo -->
        <div class="image-test">
            <h3>Default Company Logo</h3>
            <div class="logo-container">
                <img src="/static/img/default-company-logo.svg" alt="Default Company Logo"
                     onload="updateStatus('test1', 'success', 'Loaded successfully')"
                     onerror="updateStatus('test1', 'error', 'Failed to load')">
            </div>
            <div id="test1" class="status loading">Loading...</div>
        </div>

        <!-- Test 2: Existing Company Logo -->
        <div class="image-test">
            <h3>Existing Company Logo</h3>
            <div class="logo-container">
                <img src="/media/company_logos/bot.png" alt="Company Logo"
                     data-fallback-src="/static/img/default-company-logo.svg"
                     onload="updateStatus('test2', 'success', 'Loaded successfully')"
                     onerror="handleImageError(this); updateStatus('test2', 'error', 'Failed to load - fallback applied')">
            </div>
            <div id="test2" class="status loading">Loading...</div>
        </div>

        <!-- Test 3: Sample Logo -->
        <div class="image-test">
            <h3>Sample Logo</h3>
            <div class="logo-container">
                <img src="/static/img/logos/company-1.svg" alt="Sample Logo"
                     onload="updateStatus('test3', 'success', 'Loaded successfully')"
                     onerror="updateStatus('test3', 'error', 'Failed to load')">
            </div>
            <div id="test3" class="status loading">Loading...</div>
        </div>

        <!-- Test 4: Default Assistant Logo -->
        <div class="image-test">
            <h3>Default Assistant Logo</h3>
            <div class="logo-container">
                <img src="/static/img/default-assistant-logo.png" alt="Default Assistant Logo"
                     onload="updateStatus('test4', 'success', 'Loaded successfully')"
                     onerror="updateStatus('test4', 'error', 'Failed to load')">
            </div>
            <div id="test4" class="status loading">Loading...</div>
        </div>

        <!-- Test 5: Non-existent Image (should fail) -->
        <div class="image-test">
            <h3>Non-existent Image (Expected to Fail)</h3>
            <div class="logo-container">
                <img src="/static/img/non-existent.png" alt="Non-existent"
                     onload="updateStatus('test5', 'success', 'Loaded successfully')"
                     onerror="updateStatus('test5', 'error', 'Failed to load (expected)')">
            </div>
            <div id="test5" class="status loading">Loading...</div>
        </div>
    </div>

    <script>
        function updateStatus(testId, type, message) {
            const element = document.getElementById(testId);
            element.className = `status ${type}`;
            element.textContent = message;
        }

        function handleImageError(img) {
            console.error('Image failed to load:', img.src);

            if (img.dataset.errorHandled) {
                return;
            }

            img.dataset.errorHandled = 'true';
            const fallbackSrc = img.dataset.fallbackSrc || '/static/img/default-company-logo.svg';
            img.src = fallbackSrc;
            img.classList.add('fallback-image');

            console.log('Applied fallback:', fallbackSrc);
        }

        // Set timeout to mark any still-loading images as failed
        setTimeout(() => {
            const loadingElements = document.querySelectorAll('.status.loading');
            loadingElements.forEach(el => {
                el.className = 'status error';
                el.textContent = 'Timeout - Failed to load';
            });
        }, 5000);

        console.log('Image test page loaded');
    </script>
</body>
</html>
