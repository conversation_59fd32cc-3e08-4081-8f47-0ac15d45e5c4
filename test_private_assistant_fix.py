"""
Test script to verify the private assistant access fix.

This script tests that private assistants can be accessed via direct link
by authorized users but not by unauthorized users.
"""

import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from accounts.models import Company, Membership
from assistants.models import Assistant
from accounts.utils import can_access_assistant


def test_private_assistant_access():
    """Test private assistant access permissions."""
    print("Testing private assistant access fix...")
    
    # Create test users
    try:
        owner_user = User.objects.get(username='test_owner')
    except User.DoesNotExist:
        owner_user = User.objects.create_user(
            username='test_owner',
            email='<EMAIL>',
            password='testpass123'
        )
    
    try:
        external_user = User.objects.get(username='test_external')
    except User.DoesNotExist:
        external_user = User.objects.create_user(
            username='test_external',
            email='<EMAIL>',
            password='testpass123'
        )
    
    # Create test company
    try:
        company = Company.objects.get(name='Test Private Company')
    except Company.DoesNotExist:
        company = Company.objects.create(
            name='Test Private Company',
            owner=owner_user,
            entity_type='company'
        )
    
    # Create private assistant
    try:
        private_assistant = Assistant.objects.get(slug='test-private-assistant')
    except Assistant.DoesNotExist:
        private_assistant = Assistant.objects.create(
            name='Test Private Assistant',
            company=company,
            assistant_type=Assistant.TYPE_GENERAL,
            system_prompt='Test prompt for private assistant',
            is_public=False,  # This is the key - private assistant
            is_active=True,
            slug='test-private-assistant'
        )
    
    # Create public assistant for comparison
    try:
        public_assistant = Assistant.objects.get(slug='test-public-assistant')
    except Assistant.DoesNotExist:
        public_assistant = Assistant.objects.create(
            name='Test Public Assistant',
            company=company,
            assistant_type=Assistant.TYPE_GENERAL,
            system_prompt='Test prompt for public assistant',
            is_public=True,
            is_active=True,
            slug='test-public-assistant'
        )
    
    print(f"Created/found private assistant: {private_assistant.name} (is_public={private_assistant.is_public})")
    print(f"Created/found public assistant: {public_assistant.name} (is_public={public_assistant.is_public})")
    
    # Test permission function directly
    print("\n--- Testing can_access_assistant function ---")
    
    # Owner should have access to private assistant
    owner_can_access_private = can_access_assistant(owner_user, private_assistant)
    print(f"Owner can access private assistant: {owner_can_access_private}")
    
    # External user should NOT have access to private assistant
    external_can_access_private = can_access_assistant(external_user, private_assistant)
    print(f"External user can access private assistant: {external_can_access_private}")
    
    # Both should have access to public assistant
    owner_can_access_public = can_access_assistant(owner_user, public_assistant)
    external_can_access_public = can_access_assistant(external_user, public_assistant)
    print(f"Owner can access public assistant: {owner_can_access_public}")
    print(f"External user can access public assistant: {external_can_access_public}")
    
    # Test web access
    print("\n--- Testing web access ---")
    client = Client()
    
    # Test private assistant access by owner
    client.login(username='test_owner', password='testpass123')
    try:
        response = client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': private_assistant.slug
        }))
        print(f"Owner accessing private assistant via web: {response.status_code}")
        if response.status_code == 200:
            print("✓ SUCCESS: Owner can access private assistant via direct link")
        else:
            print(f"✗ FAILED: Owner got status {response.status_code}")
    except Exception as e:
        print(f"✗ ERROR: {e}")
    
    # Test private assistant access by external user
    client.login(username='test_external', password='testpass123')
    try:
        response = client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': private_assistant.slug
        }))
        print(f"External user accessing private assistant via web: {response.status_code}")
        if response.status_code == 403:
            print("✓ SUCCESS: External user correctly denied access to private assistant")
        else:
            print(f"✗ FAILED: External user got status {response.status_code} (should be 403)")
    except Exception as e:
        print(f"✗ ERROR: {e}")
    
    # Test public assistant access by external user
    try:
        response = client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': public_assistant.slug
        }))
        print(f"External user accessing public assistant via web: {response.status_code}")
        if response.status_code == 200:
            print("✓ SUCCESS: External user can access public assistant")
        else:
            print(f"✗ FAILED: External user got status {response.status_code}")
    except Exception as e:
        print(f"✗ ERROR: {e}")
    
    # Test anonymous access to private assistant
    client.logout()
    try:
        response = client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': private_assistant.slug
        }))
        print(f"Anonymous user accessing private assistant via web: {response.status_code}")
        if response.status_code == 403:
            print("✓ SUCCESS: Anonymous user correctly denied access to private assistant")
        else:
            print(f"✗ FAILED: Anonymous user got status {response.status_code} (should be 403)")
    except Exception as e:
        print(f"✗ ERROR: {e}")
    
    # Test anonymous access to public assistant
    try:
        response = client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': public_assistant.slug
        }))
        print(f"Anonymous user accessing public assistant via web: {response.status_code}")
        if response.status_code == 200:
            print("✓ SUCCESS: Anonymous user can access public assistant")
        else:
            print(f"✗ FAILED: Anonymous user got status {response.status_code}")
    except Exception as e:
        print(f"✗ ERROR: {e}")
    
    print("\n--- Test Summary ---")
    print("The fix should allow:")
    print("1. ✓ Private assistants to be accessible via direct link by authorized users")
    print("2. ✓ Private assistants to be denied to unauthorized users")
    print("3. ✓ Public assistants to be accessible by everyone")
    print("4. ✓ Private assistants to NOT appear in directory listings")
    print("\nIf all tests show SUCCESS, the fix is working correctly!")


if __name__ == '__main__':
    test_private_assistant_access()
