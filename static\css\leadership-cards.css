/**
 * Leadership Cards CSS
 * Enhanced styling for NUP leadership cards with proper image handling and animations
 */

/* ===== LEADERSHIP SECTION CONTAINER ===== */
.nup-section-white {
    padding: 4rem 0 !important;
    overflow: visible !important;
}

.nup-section-white .container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 15px !important;
}

.nup-section-white .row {
    margin: 0 -15px !important;
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: stretch !important;
}

.nup-section-white .col-md-6.col-lg-4 {
    padding: 0 15px !important;
    margin-bottom: 3rem !important;
    display: flex !important;
    align-items: stretch !important;
}

/* ===== LEADERSHIP CARD CONTAINER ===== */
.nup-leader-card {
    border: 3px solid #666666 !important;
    border-radius: 15px !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%) !important;
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.1),
        0 3px 6px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    z-index: 1 !important;
}

.nup-leader-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #cf2e2e, #252638, #cf2e2e, #252638) !important;
    background-size: 400% 400% !important;
    border-radius: 17px !important;
    z-index: -1 !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    animation: leaderGradientShift 8s ease infinite !important;
}

.nup-leader-card:hover::before {
    opacity: 0.3 !important;
}

.nup-leader-card:hover {
    border-color: #cf2e2e !important;
    transform: translateY(-3px) !important;
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.15),
        0 6px 12px rgba(0, 0, 0, 0.1),
        0 3px 6px rgba(0, 0, 0, 0.08),
        0 0 0 2px rgba(207, 46, 46, 0.2),
        inset 0 2px 0 rgba(255, 255, 255, 0.8) !important;
    background: linear-gradient(135deg, #ffffff 0%, #fff5f5 50%, #ffffff 100%) !important;
}

/* ===== LEADERSHIP CARD HEADER ===== */
.nup-leader-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%) !important;
    border-bottom: 2px solid #666666 !important;
    padding: 1.25rem 1.5rem !important;
    border-radius: 12px 12px 0 0 !important;
    position: relative !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
}

.nup-leader-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #cf2e2e, #252638, #cf2e2e) !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.nup-leader-card:hover .nup-leader-header {
    background: linear-gradient(135deg, #fff5f5 0%, #f8f9fa 50%, #fff5f5 100%) !important;
    border-bottom-color: #cf2e2e !important;
}

.nup-leader-card:hover .nup-leader-header::after {
    opacity: 1 !important;
}

.nup-leader-name {
    color: #333333 !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
    margin-bottom: 0.25rem !important;
    line-height: 1.3 !important;
}

.nup-leader-title {
    color: #cf2e2e !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6) !important;
    line-height: 1.2 !important;
}

/* ===== LEADERSHIP CARD BODY ===== */
.nup-leader-body {
    padding: 1.5rem !important;
    position: relative !important;
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
}

.nup-leader-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #cf2e2e, #252638, #cf2e2e) !important;
    border-radius: 0 0 6px 6px !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.nup-leader-card:hover .nup-leader-body::before {
    opacity: 1 !important;
}

/* ===== LEADERSHIP IMAGE CONTAINER ===== */
.nup-leader-image-container {
    position: relative !important;
    width: 100px !important;
    height: 100px !important;
    margin: 0 auto 1.25rem auto !important;
    border-radius: 50% !important;
    overflow: hidden !important;
    border: 3px solid #ffffff !important;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
    transition: all 0.3s ease !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    flex-shrink: 0 !important;
}

.nup-leader-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    transition: all 0.3s ease !important;
    border-radius: 50% !important;
    display: block !important;
}

/* ===== HOVER EFFECTS ===== */
.nup-leader-card:hover .nup-leader-image-container {
    transform: scale(1.03) !important;
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.15),
        0 3px 6px rgba(0, 0, 0, 0.1),
        0 0 0 3px rgba(207, 46, 46, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    border-color: #cf2e2e !important;
}

.nup-leader-card:hover .nup-leader-image {
    transform: scale(1.02) !important;
    filter: brightness(1.1) contrast(1.05) saturate(1.1) !important;
}

/* ===== LEADERSHIP DESCRIPTION ===== */
.nup-leader-description {
    color: #555555 !important;
    font-weight: 500 !important;
    line-height: 1.6 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6) !important;
    margin-bottom: 1.25rem !important;
    font-size: 0.9rem !important;
    flex-grow: 1 !important;
    text-align: center !important;
}

/* ===== LEADERSHIP BUTTON ===== */
.nup-leader-btn {
    border: 2px solid transparent !important;
    border-radius: 8px !important;
    padding: 0.5rem 1.25rem !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    flex-shrink: 0 !important;
    align-self: center !important;
}

.nup-leader-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
    transition: left 0.5s ease !important;
}

.nup-leader-btn:hover::before {
    left: 100% !important;
}

.nup-leader-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow:
        0 5px 10px rgba(0, 0, 0, 0.12),
        0 3px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
}

/* ===== PURPLE VARIANT ===== */
.nup-leader-card.purple .nup-leader-title {
    color: #252638 !important;
}

.nup-leader-card.purple:hover .nup-leader-overlay {
    background: linear-gradient(135deg, rgba(37, 38, 56, 0.9), rgba(207, 46, 46, 0.9)) !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 992px) {
    .nup-section-white .col-md-6.col-lg-4 {
        margin-bottom: 2.5rem !important;
    }

    .nup-leader-card:hover {
        transform: translateY(-2px) !important;
    }
}

@media (max-width: 768px) {
    .nup-section-white {
        padding: 3rem 0 !important;
    }

    .nup-section-white .col-md-6.col-lg-4 {
        margin-bottom: 2rem !important;
        padding: 0 10px !important;
    }

    .nup-leader-card {
        border-width: 2px !important;
        border-radius: 12px !important;
    }

    .nup-leader-card:hover {
        transform: translateY(-2px) !important;
        box-shadow:
            0 8px 16px rgba(0, 0, 0, 0.12),
            0 4px 8px rgba(0, 0, 0, 0.08),
            0 0 0 2px rgba(207, 46, 46, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
    }

    .nup-leader-image-container {
        width: 90px !important;
        height: 90px !important;
        border-width: 2px !important;
        margin-bottom: 1rem !important;
    }

    .nup-leader-icon {
        font-size: 1.5rem !important;
    }

    .nup-leader-header {
        padding: 1rem 1.25rem !important;
    }

    .nup-leader-body {
        padding: 1.25rem !important;
    }

    .nup-leader-name {
        font-size: 1rem !important;
    }

    .nup-leader-title {
        font-size: 0.85rem !important;
    }

    .nup-leader-description {
        font-size: 0.85rem !important;
        margin-bottom: 1rem !important;
    }
}

@media (max-width: 576px) {
    .nup-section-white {
        padding: 2.5rem 0 !important;
    }

    .nup-section-white .col-md-6.col-lg-4 {
        margin-bottom: 1.5rem !important;
        padding: 0 5px !important;
    }

    .nup-leader-card {
        border-width: 2px !important;
        border-radius: 10px !important;
    }

    .nup-leader-image-container {
        width: 80px !important;
        height: 80px !important;
        border-width: 2px !important;
        margin-bottom: 0.875rem !important;
    }

    .nup-leader-icon {
        font-size: 1.25rem !important;
    }

    .nup-leader-header {
        padding: 0.875rem 1rem !important;
    }

    .nup-leader-body {
        padding: 1rem !important;
    }

    .nup-leader-btn {
        padding: 0.4rem 1rem !important;
        font-size: 0.85rem !important;
    }

    .nup-leader-name {
        font-size: 0.95rem !important;
    }

    .nup-leader-title {
        font-size: 0.8rem !important;
    }

    .nup-leader-description {
        font-size: 0.8rem !important;
        margin-bottom: 0.875rem !important;
    }
}

/* ===== ANIMATION KEYFRAMES ===== */
@keyframes leaderGradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
.nup-leader-card:focus-within {
    outline: 3px solid #cf2e2e !important;
    outline-offset: 3px !important;
    box-shadow: 
        0 0 0 6px rgba(207, 46, 46, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.15),
        0 4px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
}

.nup-leader-btn:focus {
    outline: 2px solid #cf2e2e !important;
    outline-offset: 2px !important;
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
    .nup-leader-card {
        border-width: 4px !important;
        border-color: #000000 !important;
        background: #ffffff !important;
    }
    
    .nup-leader-name, .nup-leader-title, .nup-leader-description {
        color: #000000 !important;
        text-shadow: none !important;
    }
    
    .nup-leader-overlay {
        background: rgba(0, 0, 0, 0.8) !important;
    }
}

/* ===== ANIMATION PERFORMANCE OPTIMIZATION ===== */
.nup-leader-card,
.nup-leader-image-container,
.nup-leader-image,
.nup-leader-overlay,
.nup-leader-btn {
    will-change: transform !important;
    backface-visibility: hidden !important;
}

/* ===== Z-INDEX MANAGEMENT ===== */
.nup-leader-card {
    isolation: isolate !important;
}

.nup-leader-card:hover {
    z-index: 100 !important;
}

/* ===== PREVENT OVERLAP ISSUES ===== */
.row.g-4 .col-md-6.col-lg-4 {
    z-index: 1 !important;
    position: relative !important;
}

.row.g-4 .col-md-6.col-lg-4:hover {
    z-index: 10 !important;
}

/* ===== CONTAINER OVERFLOW MANAGEMENT ===== */
.nup-section-white .container {
    overflow: visible !important;
}

.nup-section-white .row {
    overflow: visible !important;
}

/* ===== CARD STABILITY ===== */
.nup-leader-card {
    contain: layout style !important;
    backface-visibility: hidden !important;
    perspective: 1000px !important;
}

/* ===== SMOOTH ANIMATIONS ===== */
.nup-leader-card,
.nup-leader-image-container,
.nup-leader-image,
.nup-leader-overlay,
.nup-leader-btn {
    will-change: auto !important;
}

.nup-leader-card:hover,
.nup-leader-card:hover .nup-leader-image-container,
.nup-leader-card:hover .nup-leader-image,
.nup-leader-card:hover .nup-leader-overlay,
.nup-leader-card:hover .nup-leader-btn {
    will-change: transform, opacity !important;
}
