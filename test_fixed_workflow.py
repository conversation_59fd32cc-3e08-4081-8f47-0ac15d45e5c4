#!/usr/bin/env python
"""
Test the fixed company creation and settings workflow.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from accounts.models import Company, CompanyInformation
import uuid

def test_fixed_workflow():
    """Test complete workflow after the fix"""
    print("🔧 TESTING FIXED WORKFLOW")
    print("=" * 50)

    # Create test user
    username = f"fixed_test_{uuid.uuid4().hex[:6]}"
    user = User.objects.create_user(
        username=username,
        email=f'{username}@test.com',
        password='testpass123'
    )
    print(f"✅ Created user: {user.username}")

    # Create Django test client
    client = Client()

    # Log in the user
    login_success = client.login(username=username, password='testpass123')
    if not login_success:
        print("❌ Failed to log in user")
        return False
    print("✅ User logged in successfully")

    # Test data for company creation
    company_name = f'Fixed Test Company {uuid.uuid4().hex[:4]}'
    form_data = {
        'name': company_name,
        'entity_type': 'company',
        'mission': 'Fixed workflow test mission',
        'description': 'Testing the fixed workflow',
        'website': 'https://fixed-test.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '******-FIXED',
        'timezone': 'UTC',
        'language': 'en',
        'industry': 'Fixed Testing',
        'size': '1-10',
        'city': 'Fixed City',
        'country': 'Fixed Country',
        'founded': 2024,
        'list_in_directory': True
    }

    try:
        # Step 1: Create company via web interface
        print("\n📝 Step 1: Creating company via web interface...")

        create_url = reverse('accounts:company_create')
        response = client.post(create_url, form_data)

        if response.status_code == 302:  # Redirect after successful creation
            print("✅ Company creation request successful (redirected)")
        else:
            print(f"❌ Company creation failed. Status: {response.status_code}")
            return False

        # Step 2: Find the created company
        print("\n🔍 Step 2: Finding created company...")

        try:
            # Try to find by exact name first
            company = Company.objects.get(owner=user, name=company_name)
            print(f"✅ Company found: {company.name}")
        except Company.DoesNotExist:
            # If not found, try to find any company by this user (in case name was modified)
            companies = Company.objects.filter(owner=user)
            if companies.exists():
                company = companies.latest('created_at')
                print(f"✅ Company found (latest): {company.name}")
            else:
                print("❌ No company found in database")
                return False

        # Step 3: Check saved data immediately after creation
        print("\n📊 Step 3: Checking data immediately after creation...")

        try:
            company_info = CompanyInformation.objects.get(company=company)
            print("✅ CompanyInformation found")
        except CompanyInformation.DoesNotExist:
            print("❌ CompanyInformation not found")
            return False

        # Check key fields
        checks = [
            ('mission', company_info.mission, form_data['mission']),
            ('website', company_info.website, form_data['website']),
            ('contact_email', company_info.contact_email, form_data['contact_email']),
            ('industry', company_info.industry, form_data['industry']),
            ('city', company_info.city, form_data['city']),
        ]

        print("Data immediately after creation:")
        creation_success = True
        for field, saved, expected in checks:
            if saved == expected:
                print(f"  ✅ {field}: '{saved}'")
            else:
                print(f"  ❌ {field}: '{saved}' (expected: '{expected}')")
                creation_success = False

        # Step 4: Access company settings page (this was causing the issue)
        print("\n⚙️ Step 4: Accessing company settings page...")

        settings_url = reverse('accounts:company_settings', kwargs={'company_id': company.id})
        response = client.get(settings_url)

        if response.status_code == 200:
            print("✅ Company settings page accessible")
        else:
            print(f"❌ Company settings page not accessible. Status: {response.status_code}")
            return False

        # Step 5: Check data after accessing settings (this should NOT change the data)
        print("\n🔍 Step 5: Checking data after accessing settings...")

        # Refresh from database
        company_info.refresh_from_db()

        print("Data after accessing settings:")
        settings_success = True
        for field, _, expected in checks:
            saved = getattr(company_info, field)
            if saved == expected:
                print(f"  ✅ {field}: '{saved}' (preserved)")
            else:
                print(f"  ❌ {field}: '{saved}' (expected: '{expected}') - DATA LOST!")
                settings_success = False

        # Step 6: Test form pre-population
        print("\n📋 Step 6: Testing form pre-population...")

        if hasattr(response, 'context') and response.context and 'form' in response.context:
            form = response.context['form']
            form_success = True

            for field, _, expected in checks:
                if field in form.initial:
                    initial_value = form.initial[field]
                    if initial_value == expected:
                        print(f"  ✅ Form {field} pre-populated: '{initial_value}'")
                    else:
                        print(f"  ❌ Form {field}: '{initial_value}' (expected: '{expected}')")
                        form_success = False
                else:
                    print(f"  ⚠️ Form {field} not in initial data")
        else:
            print("  ⚠️ Could not access form context")
            form_success = False

        # Clean up
        company.delete()
        user.delete()

        # Final result
        overall_success = creation_success and settings_success and form_success

        if overall_success:
            print("\n🎉 FIXED WORKFLOW TEST PASSED!")
            print("✅ Company creation data persists correctly")
            print("✅ Settings page preserves data")
            print("✅ Form pre-population works")
            return True
        else:
            print("\n⚠️ FIXED WORKFLOW TEST FAILED!")
            print(f"Creation success: {creation_success}")
            print(f"Settings preservation: {settings_success}")
            print(f"Form pre-population: {form_success}")
            return False

    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_workflow()
    sys.exit(0 if success else 1)
