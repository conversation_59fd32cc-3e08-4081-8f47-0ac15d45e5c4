/**
 * Button Text Force CSS
 * Maximum priority CSS to ensure ALL button text is bold and white
 * This file uses the highest specificity possible to override any conflicting styles
 */

/* ===== NUCLEAR OPTION: MAXIMUM SPECIFICITY BUTTON TEXT ===== */
html body .btn,
html body .btn *,
html body button,
html body button *,
html body input[type="button"],
html body input[type="submit"],
html body input[type="reset"],
html body .button,
html body .button *,
html body [role="button"],
html body [role="button"] *,
html body a.btn,
html body a.btn *,
html body span.btn,
html body span.btn *,
html body div.btn,
html body div.btn *,
html body [class*="btn"],
html body [class*="btn"] * {
    font-weight: 700 !important;
    color: #ffffff !important;
    text-shadow: none !important;
    font-style: normal !important;
    text-decoration: none !important;
}

/* ===== OVERRIDE ANY COMPUTED STYLES ===== */
html body main .btn,
html body main .btn *,
html body main button,
html body main button *,
html body section .btn,
html body section .btn *,
html body section button,
html body section button *,
html body div .btn,
html body div .btn *,
html body div button,
html body div button *,
html body form .btn,
html body form .btn *,
html body form button,
html body form button * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

/* ===== SPECIFIC BUTTON CLASSES ===== */
html body .btn-primary,
html body .btn-primary *,
html body .btn-secondary,
html body .btn-secondary *,
html body .btn-success,
html body .btn-success *,
html body .btn-danger,
html body .btn-danger *,
html body .btn-info,
html body .btn-info *,
html body .btn-dark,
html body .btn-dark * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

/* ===== OUTLINE BUTTON VARIANTS ===== */
html body .btn-outline-primary,
html body .btn-outline-primary *,
html body .btn-outline-secondary,
html body .btn-outline-secondary *,
html body .btn-outline-success,
html body .btn-outline-success *,
html body .btn-outline-danger,
html body .btn-outline-danger *,
html body .btn-outline-info,
html body .btn-outline-info *,
html body .btn-outline-dark,
html body .btn-outline-dark * {
    font-weight: 700 !important;
}

/* ===== LIGHT AND WARNING BUTTONS (DARK TEXT) ===== */
html body .btn-light,
html body .btn-light *,
html body .btn-warning,
html body .btn-warning *,
html body .btn-outline-light,
html body .btn-outline-light *,
html body .btn-outline-warning,
html body .btn-outline-warning * {
    font-weight: 700 !important;
    color: #333333 !important;
}

/* ===== HOVER STATES ===== */
html body .btn:hover,
html body .btn:hover *,
html body .btn:focus,
html body .btn:focus *,
html body .btn:active,
html body .btn:active *,
html body button:hover,
html body button:hover *,
html body button:focus,
html body button:focus *,
html body button:active,
html body button:active * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

/* ===== LIGHT/WARNING HOVER STATES ===== */
html body .btn-light:hover,
html body .btn-light:hover *,
html body .btn-light:focus,
html body .btn-light:focus *,
html body .btn-light:active,
html body .btn-light:active *,
html body .btn-warning:hover,
html body .btn-warning:hover *,
html body .btn-warning:focus,
html body .btn-warning:focus *,
html body .btn-warning:active,
html body .btn-warning:active * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

/* ===== FORM INPUTS ===== */
html body input[type="button"],
html body input[type="submit"],
html body input[type="reset"] {
    font-weight: 700 !important;
    color: #ffffff !important;
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
}

html body input[type="button"]:hover,
html body input[type="submit"]:hover,
html body input[type="reset"]:hover {
    font-weight: 700 !important;
    color: #ffffff !important;
    background-color: #252638 !important;
    border-color: #252638 !important;
}

/* ===== WILDCARD BUTTON SELECTORS ===== */
html body [class*="btn-primary"],
html body [class*="btn-primary"] *,
html body [class*="btn-secondary"],
html body [class*="btn-secondary"] *,
html body [class*="btn-success"],
html body [class*="btn-success"] *,
html body [class*="btn-danger"],
html body [class*="btn-danger"] *,
html body [class*="btn-info"],
html body [class*="btn-info"] *,
html body [class*="btn-dark"],
html body [class*="btn-dark"] * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

html body [class*="btn-light"],
html body [class*="btn-light"] *,
html body [class*="btn-warning"],
html body [class*="btn-warning"] * {
    font-weight: 700 !important;
    color: #333333 !important;
}

/* ===== OVERRIDE ANY INLINE STYLES ===== */
html body .btn[style],
html body .btn[style] *,
html body button[style],
html body button[style] * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

html body .btn-light[style],
html body .btn-light[style] *,
html body .btn-warning[style],
html body .btn-warning[style] * {
    font-weight: 700 !important;
    color: #333333 !important;
}

/* ===== BOOTSTRAP COMPONENT OVERRIDES ===== */
html body .navbar .btn,
html body .navbar .btn *,
html body .navbar button,
html body .navbar button *,
html body .card .btn,
html body .card .btn *,
html body .card button,
html body .card button *,
html body .modal .btn,
html body .modal .btn *,
html body .modal button,
html body .modal button * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

/* ===== ENSURE TEXT ELEMENTS INSIDE BUTTONS ===== */
html body .btn span,
html body .btn i,
html body .btn em,
html body .btn strong,
html body .btn b,
html body .btn small,
html body button span,
html body button i,
html body button em,
html body button strong,
html body button b,
html body button small {
    font-weight: 700 !important;
    color: inherit !important;
}

/* ===== FINAL CATCH-ALL ===== */
html body *[class*="btn"] {
    font-weight: 700 !important;
}

html body *[class*="btn"]:not([class*="btn-light"]):not([class*="btn-warning"]) {
    color: #ffffff !important;
}

html body *[class*="btn-light"],
html body *[class*="btn-warning"] {
    color: #333333 !important;
}

/* ===== HOVER CATCH-ALL ===== */
html body *[class*="btn"]:hover,
html body *[class*="btn"]:focus,
html body *[class*="btn"]:active {
    font-weight: 700 !important;
    color: #ffffff !important;
}

/* ===== RESPONSIVE OVERRIDES ===== */
@media (max-width: 768px) {
    html body .btn,
    html body .btn *,
    html body button,
    html body button * {
        font-weight: 700 !important;
        color: #ffffff !important;
    }
    
    html body .btn-light,
    html body .btn-light *,
    html body .btn-warning,
    html body .btn-warning * {
        font-weight: 700 !important;
        color: #333333 !important;
    }
}

/* ===== PRINT OVERRIDES ===== */
@media print {
    .btn, .btn *, button, button * {
        font-weight: 700 !important;
        color: #000000 !important;
    }
}
