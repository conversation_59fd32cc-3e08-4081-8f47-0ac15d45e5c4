/**
 * Public Company Detail Progressive Tablet & Landscape Mode Fix CSS
 * Makes tablet and landscape modes use consistent, mobile-friendly layouts
 * Progressive approach that adapts to different screen sizes
 */

/* ===== PROGRESSIVE TABLET & LANDSCAPE FIXES ===== */

/* Large tablets and landscape phones (768px - 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  /* Force mobile-style stacked layout for all tablet sizes */
  .card-body .row {
    display: flex !important;
    flex-direction: column !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Make both columns full width and stack vertically */
  .card-body .col-md-7,
  .card-body .col-md-5 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
    width: 100% !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    margin-bottom: 1.5rem !important;
  }

  /* Remove all borders and problematic styling */
  .card-body .col-md-5 {
    border-left: none !important;
    border-start: none !important;
    padding-left: 1rem !important;
    margin-top: 1rem !important;
  }

  /* Force left alignment for all contact info */
  .card-body .contact-info li {
    justify-content: flex-start !important;
    text-align: left !important;
    display: flex !important;
    align-items: flex-start !important;
  }

  /* Fix contact text to prevent cutoff */
  .card-body .contact-info .contact-text {
    text-align: left !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    word-break: break-word !important;
    max-width: calc(100% - 2rem) !important;
    flex: 1 !important;
  }

  /* Aggressive URL wrapping */
  .card-body .contact-info .website-url {
    word-break: break-all !important;
    overflow-wrap: anywhere !important;
    hyphens: auto !important;
    max-width: calc(100% - 2rem) !important;
  }

  /* Fix all headings and ratings */
  .card-body h5,
  .card-body .star-rating,
  .card-body .text-end {
    text-align: left !important;
    justify-content: flex-start !important;
  }

  /* Ensure icons don't cause overflow */
  .card-body .contact-info i {
    margin-right: 0.75rem !important;
    flex-shrink: 0 !important;
    width: 1.2rem !important;
    text-align: center !important;
  }

  /* Reduce card padding for more space */
  .card-body {
    padding: 1rem !important;
  }

  /* Ensure container doesn't cause overflow */
  .container {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
    max-width: 100% !important;
  }

  /* Prevent any horizontal overflow */
  .card,
  .card-body,
  .card-body * {
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow-wrap: break-word !important;
  }
}

/* ===== LANDSCAPE MODE SPECIFIC FIXES ===== */
@media (max-width: 991.98px) and (orientation: landscape) {
  /* Apply same progressive fixes to landscape mode */
  .card-body .row {
    display: flex !important;
    flex-direction: column !important;
  }

  .card-body .col-md-7,
  .card-body .col-md-5 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  /* Even more compact in landscape */
  .card-body {
    padding: 0.75rem !important;
  }

  .card-body .col-md-7,
  .card-body .col-md-5 {
    margin-bottom: 1rem !important;
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }
}

/* ===== SMALL TABLETS & LARGE PHONES ===== */
@media (min-width: 576px) and (max-width: 767.98px) {
  /* Ensure mobile-friendly layout */
  .card-body .row {
    flex-direction: column !important;
  }

  .card-body .col-md-7,
  .card-body .col-md-5 {
    flex: 0 0 100% !important;
    max-width: 100% !important;
    border: none !important;
    padding: 0.75rem !important;
  }

  /* Extra aggressive text wrapping for smaller screens */
  .card-body .contact-info .contact-text,
  .card-body .contact-info .website-url {
    font-size: 0.9rem !important;
    line-height: 1.4 !important;
    word-break: break-all !important;
    overflow-wrap: anywhere !important;
  }
}

/* ===== UNIVERSAL FIXES FOR ALL NON-DESKTOP SIZES ===== */
@media (max-width: 991.98px) {
  /* Force progressive layout behavior */
  .card-body .border-start,
  .card-body .border-left {
    border: none !important;
  }

  /* Ensure no content gets cut off */
  .card-body .justify-content-end {
    justify-content: flex-start !important;
  }

  /* Make sure all text is readable */
  .card-body .contact-info {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
  }

  /* Progressive spacing */
  .card-body .contact-info li {
    margin-bottom: 0.75rem !important;
    padding-right: 0.5rem !important;
  }
}
