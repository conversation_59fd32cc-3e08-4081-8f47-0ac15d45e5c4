#!/usr/bin/env python
"""
Test email system with the actual password - no user input required.
"""

import os
import sys
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    
    from django.core.mail import send_mail, get_connection
    from django.conf import settings
    
    print("🚀 Testing Email System with Actual Password")
    print("=" * 50)
    
    # Display configuration
    print("📧 Email Configuration:")
    print(f"  Host: {settings.EMAIL_HOST}")
    print(f"  Port: {settings.EMAIL_PORT}")
    print(f"  User: {settings.EMAIL_HOST_USER}")
    print(f"  From: {settings.DEFAULT_FROM_EMAIL}")
    print(f"  SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    print(f"  Password: {'Set' if settings.EMAIL_HOST_PASSWORD else 'Not set'}")
    
    # Test SMTP connection
    print("\n🔧 Testing SMTP Connection...")
    try:
        connection = get_connection()
        connection.open()
        print("✅ SMTP connection successful!")
        connection.close()
        smtp_working = True
    except Exception as e:
        print(f"❌ SMTP connection failed: {e}")
        smtp_working = False
        
        # Provide specific error guidance
        error_str = str(e).lower()
        if '535' in error_str:
            print("   💡 Authentication error - check email account and password")
        elif 'timeout' in error_str:
            print("   💡 Connection timeout - check DNS and firewall")
        elif 'ssl' in error_str:
            print("   💡 SSL error - check certificate")
    
    if smtp_working:
        print("\n📤 Sending test <NAME_EMAIL>...")
        
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            result = send_mail(
                subject=f'Django Email Test - {timestamp}',
                message=f'''
Django Email System Test - SUCCESS!

This test email was sent at {timestamp} from your Django application.

Configuration Details:
- From: {settings.DEFAULT_FROM_EMAIL}
- SMTP Server: {settings.EMAIL_HOST}:{settings.EMAIL_PORT}
- Security: SSL enabled
- User: {settings.EMAIL_HOST_USER}

✅ Your Django email system is working correctly!

Email Features Available:
- User registration emails
- Password reset emails  
- Team invitation emails
- System notifications
- HTML formatted emails

Best regards,
Your Django Application
24seven.site

Test completed at: {timestamp}
''',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=['<EMAIL>'],
                fail_silently=False
            )
            
            if result == 1:
                print("✅ Email sent successfully!")
                print(f"   📧 To: <EMAIL>")
                print(f"   📝 Subject: Django Email Test - {timestamp}")
                print(f"   🕒 Time: {timestamp}")
                
                # Try sending HTML email too
                print("\n📤 Sending HTML test email...")
                
                from django.core.mail import EmailMessage
                
                html_content = f'''
<!DOCTYPE html>
<html>
<head>
    <title>Django HTML Email Test</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; border-radius: 5px; }}
        .content {{ padding: 20px; background-color: #f8f9fa; border-radius: 5px; margin: 20px 0; }}
        .success {{ color: #28a745; font-weight: bold; font-size: 18px; }}
        .details {{ background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Django Email System Test</h1>
        </div>
        
        <div class="content">
            <p class="success">✅ HTML Email Functionality Working!</p>
            
            <p>This HTML email was sent at <strong>{timestamp}</strong> to verify that your Django application can send styled emails.</p>
            
            <div class="details">
                <h3>📋 Configuration Summary</h3>
                <ul>
                    <li><strong>SMTP Server:</strong> {settings.EMAIL_HOST}:{settings.EMAIL_PORT}</li>
                    <li><strong>From Address:</strong> {settings.DEFAULT_FROM_EMAIL}</li>
                    <li><strong>Security:</strong> SSL/TLS Enabled</li>
                    <li><strong>Authentication:</strong> Working</li>
                </ul>
            </div>
            
            <div class="details">
                <h3>🚀 Available Email Features</h3>
                <ul>
                    <li>✅ User Registration Emails</li>
                    <li>✅ Password Reset Emails</li>
                    <li>✅ Team Invitation Emails</li>
                    <li>✅ System Notifications</li>
                    <li>✅ HTML Formatted Emails</li>
                </ul>
            </div>
            
            <p>Your Django email system is now fully operational and ready for production use!</p>
        </div>
        
        <div style="text-align: center; padding: 20px; color: #666;">
            <p>Best regards,<br>
            <strong>24seven.site Django Application</strong></p>
        </div>
    </div>
</body>
</html>
'''
                
                html_email = EmailMessage(
                    subject=f'Django HTML Email Test - {timestamp}',
                    body=html_content,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    to=['<EMAIL>']
                )
                html_email.content_subtype = 'html'
                html_email.send()
                
                print("✅ HTML email sent successfully!")
                
                print("\n🎉 EMAIL SYSTEM FULLY FUNCTIONAL!")
                print("=" * 50)
                print("✅ SMTP Connection: Working")
                print("✅ Plain Text Emails: Working") 
                print("✅ HTML Emails: Working")
                print("✅ Authentication: Working")
                print("✅ SSL/TLS Security: Working")
                
                print("\n📧 Test emails sent to: <EMAIL>")
                print("   Check the inbox for both test emails")
                
                print("\n🚀 Your Django application can now send:")
                print("   - User registration emails")
                print("   - Password reset emails")
                print("   - Team invitation emails") 
                print("   - System notifications")
                print("   - HTML formatted emails")
                
            else:
                print("❌ Email sending failed (returned 0)")
                
        except Exception as e:
            print(f"❌ Error sending email: {e}")
            
            # Provide specific guidance
            error_str = str(e).lower()
            if '535' in error_str:
                print("   💡 Authentication still failing - verify email account exists")
            elif 'timeout' in error_str:
                print("   💡 Connection timeout - check network/firewall")
            else:
                print("   💡 Check email server logs for more details")
    
    else:
        print("\n❌ Cannot test email sending - SMTP connection failed")
        print("   Please check:")
        print("   1. <NAME_EMAIL> exists in cPanel")
        print("   2. Password M@kerere1 is correct")
        print("   3. mail.24seven.site DNS resolves correctly")
        print("   4. Port 465 is not blocked by firewall")

except Exception as e:
    print(f"❌ Error setting up Django: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
