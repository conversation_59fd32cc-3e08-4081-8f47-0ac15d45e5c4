/**
 * Improved Candidate Cards CSS
 * Professional styling for candidate/company cards with NUP theme
 */

/* ===== ENHANCED CARD STYLING ===== */
.directory-card.list-group-item {
    /* Clean white background instead of light blue */
    background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%) !important;
    background-color: #ffffff !important;
    
    /* Professional border and shadow */
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.04),
        0 1px 3px rgba(0, 0, 0, 0.02) !important;
    
    /* Remove the 3D rotation effect */
    transform: none !important;
    backdrop-filter: none !important;
    
    /* Better spacing */
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    
    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.directory-card.list-group-item:hover {
    /* Subtle hover effect */
    transform: translateY(-4px) !important;
    box-shadow: 
        0 8px 24px rgba(0, 0, 0, 0.12),
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.04) !important;
    
    /* NUP red accent border on hover */
    border-color: rgba(207, 46, 46, 0.3) !important;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%) !important;
}

/* ===== LOGO CONTAINER IMPROVEMENTS ===== */
.directory-card .logo-container {
    width: 120px !important;
    height: 120px !important;
    min-width: 120px !important;
    min-height: 120px !important;
    max-width: 120px !important;
    max-height: 120px !important;
    
    /* Clean background */
    background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%) !important;
    border: 2px solid #e9ecef !important;
    border-radius: 12px !important;
    
    /* Center content */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    /* Subtle shadow */
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    
    /* Smooth transition */
    transition: all 0.3s ease !important;
    overflow: hidden !important;
}

.directory-card:hover .logo-container {
    border-color: rgba(207, 46, 46, 0.2) !important;
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* ===== LOGO IMAGE STYLING ===== */
.directory-card .logo-container img {
    width: 80px !important;
    height: 80px !important;
    max-width: 80px !important;
    max-height: 80px !important;
    object-fit: contain !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
}

.directory-card:hover .logo-container img {
    transform: scale(1.05) !important;
}

/* ===== LOGO PLACEHOLDER STYLING ===== */
.directory-card .logo-placeholder {
    font-size: 3rem !important;
    color: #cf2e2e !important;
    opacity: 0.7 !important;
    transition: all 0.3s ease !important;
}

.directory-card:hover .logo-placeholder {
    opacity: 1 !important;
    transform: scale(1.1) !important;
}

/* ===== CONTENT STYLING ===== */
.directory-card h6,
.directory-card h5,
.directory-card .company-name {
    color: #242424 !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.3 !important;
}

.directory-card .text-muted {
    color: #6c757d !important;
    font-size: 0.9rem !important;
}

.directory-card .company-description,
.directory-card .assistant-description {
    color: #495057 !important;
    line-height: 1.5 !important;
    font-size: 0.95rem !important;
}

/* ===== BADGE IMPROVEMENTS ===== */
.directory-card .badge {
    font-size: 0.75rem !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    margin-right: 0.5rem !important;
    margin-bottom: 0.25rem !important;
}

.directory-card .badge.bg-success {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
    border: none !important;
}

.directory-card .badge.bg-primary {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
    border: none !important;
}

.directory-card .badge.bg-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.directory-card .badge.bg-light {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
}

/* ===== TIER BADGES ===== */
.directory-card .tier-badge {
    position: absolute !important;
    top: 1rem !important;
    left: 1rem !important;
    z-index: 10 !important;
    font-size: 0.7rem !important;
    padding: 0.3rem 0.6rem !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.directory-card .tier-gold {
    background: linear-gradient(135deg, #f7bd00 0%, #e6aa00 100%) !important;
    color: #242424 !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3) !important;
}

.directory-card .tier-silver {
    background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%) !important;
    color: #242424 !important;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3) !important;
}

.directory-card .tier-bronze {
    background: linear-gradient(135deg, #cd7f32 0%, #b8722d 100%) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
}

/* ===== CONTACT INFO STYLING ===== */
.directory-card .contact-info {
    font-size: 0.85rem !important;
    color: #6c757d !important;
}

.directory-card .contact-info i {
    color: #cf2e2e !important;
    margin-right: 0.5rem !important;
    width: 16px !important;
    text-align: center !important;
}

.directory-card .contact-info .contact-text {
    color: #495057 !important;
}

/* ===== RATING STYLING ===== */
.directory-card .rating-display-container {
    margin-bottom: 1rem !important;
}

.directory-card .star-rating .bi-star-fill {
    color: #f7bd00 !important;
}

.directory-card .star-rating .bi-star {
    color: #dee2e6 !important;
}

/* ===== BUTTON STYLING ===== */
.directory-card .btn {
    border-radius: 8px !important;
    font-weight: 500 !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease !important;
}

.directory-card .btn-primary {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.directory-card .btn-primary:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(207, 46, 46, 0.3) !important;
}

.directory-card .btn-outline-primary {
    color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    background-color: transparent !important;
}

.directory-card .btn-outline-primary:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
    .directory-card.list-group-item {
        padding: 1rem !important;
        margin-bottom: 1rem !important;
    }
    
    .directory-card .logo-container {
        width: 80px !important;
        height: 80px !important;
        min-width: 80px !important;
        min-height: 80px !important;
        max-width: 80px !important;
        max-height: 80px !important;
    }
    
    .directory-card .logo-container img {
        width: 60px !important;
        height: 60px !important;
        max-width: 60px !important;
        max-height: 60px !important;
    }
    
    .directory-card .logo-placeholder {
        font-size: 2rem !important;
    }
}

/* ===== LINK WRAPPER IMPROVEMENTS ===== */
.directory-card .directory-item-link-wrapper {
    text-decoration: none !important;
    color: inherit !important;
    transition: all 0.3s ease !important;
}

.directory-card .directory-item-link-wrapper:hover {
    text-decoration: none !important;
    color: inherit !important;
}

/* ===== REMOVE BLUE TINTS ===== */
.directory-card,
.directory-card:hover,
.directory-card:focus,
.directory-card:active {
    background-color: #ffffff !important;
    background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%) !important;
}

.directory-card:hover {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%) !important;
}

/* ===== RATE BUTTON OVERRIDES ===== */
.directory-card .btn-success,
.directory-card .rate-btn,
.directory-card .rate-button,
.directory-card .rate-company-btn,
.directory-card [class*="rate"],
button[data-bs-target="#ratingModal"],
.btn[data-bs-target="#ratingModal"],
.rate-company-btn,
.action-btn {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 600 !important;
}

.directory-card .btn-success:hover,
.directory-card .rate-btn:hover,
.directory-card .rate-button:hover,
.directory-card .rate-company-btn:hover,
.directory-card [class*="rate"]:hover,
button[data-bs-target="#ratingModal"]:hover,
.btn[data-bs-target="#ratingModal"]:hover,
.rate-company-btn:hover,
.action-btn:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(207, 46, 46, 0.3) !important;
}

/* ===== OVERRIDE OUTLINE SECONDARY RATE BUTTONS ===== */
.directory-card .btn-outline-secondary.rate-company-btn,
.directory-card .btn-outline-secondary[data-bs-target="#ratingModal"],
.btn-outline-secondary.rate-company-btn,
.btn-outline-secondary[data-bs-target="#ratingModal"] {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.directory-card .btn-outline-secondary.rate-company-btn:hover,
.directory-card .btn-outline-secondary[data-bs-target="#ratingModal"]:hover,
.btn-outline-secondary.rate-company-btn:hover,
.btn-outline-secondary[data-bs-target="#ratingModal"]:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
}

/* ===== SPECIFIC GREEN BUTTON OVERRIDES ===== */
.btn[style*="background-color: #28a745"],
.btn[style*="background: #28a745"],
.btn[style*="background-color: green"],
.btn[style*="background: green"],
.btn[style*="background-color: #198754"],
.btn[style*="background: #198754"] {
    background-color: #cf2e2e !important;
    background: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.btn[style*="background-color: #28a745"]:hover,
.btn[style*="background: #28a745"]:hover,
.btn[style*="background-color: green"]:hover,
.btn[style*="background: green"]:hover,
.btn[style*="background-color: #198754"]:hover,
.btn[style*="background: #198754"]:hover {
    background-color: #252638 !important;
    background: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
}
