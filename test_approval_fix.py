#!/usr/bin/env python
"""
Quick test to verify the approval flow fix works.
"""

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from accounts.auth_utils import store_signin_approval
from django.contrib.auth import get_user_model

User = get_user_model()

print("🔧 Testing Approval Flow Fix...")

# Create test user
user, created = User.objects.get_or_create(
    username='testuser3', 
    defaults={'email': '<EMAIL>', 'is_active': True}
)
print(f"✅ Test user: {user.username}")

# Generate token
token = store_signin_approval(user, 1)
print(f"✅ Token generated: {token}")

# Test approval
client = Client()
approval_url = reverse('accounts:approve_signin', kwargs={'token': token})
print(f"✅ Approval URL: {approval_url}")

response = client.get(approval_url, follow=True)
print(f"✅ Response status: {response.status_code}")

# Check if user is logged in
session = client.session
user_id = session.get('_auth_user_id')
if user_id:
    print(f"✅ SUCCESS: User automatically logged in! Session user ID: {user_id}")
    print(f"✅ Expected user ID: {user.id}")
    if str(user_id) == str(user.id):
        print("🎉 PERFECT: Automatic login working correctly!")
    else:
        print("⚠️  User ID mismatch")
else:
    print("❌ FAILED: User not logged in automatically")

print("\n🔧 Fix Status: Approval flow should now automatically log users in!")
