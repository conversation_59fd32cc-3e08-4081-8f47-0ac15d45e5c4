#!/usr/bin/env python
"""
Debug script to test form submission and see exactly what happens.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth.models import User
from accounts.forms import CompanyCreationForm
from accounts.models import Company, CompanyInformation
import uuid

def debug_form_submission():
    """Debug form submission step by step"""
    print("🔍 DEBUGGING FORM SUBMISSION")
    print("=" * 50)
    
    # Create test user
    username = f"debug_test_{uuid.uuid4().hex[:6]}"
    user = User.objects.create_user(
        username=username,
        email=f'{username}@test.com',
        password='testpass'
    )
    print(f"✅ Created user: {user.username}")
    
    # Test data that matches web form
    form_data = {
        'name': f'Debug Test Company {uuid.uuid4().hex[:4]}',
        'entity_type': 'company',
        'mission': 'Debug test mission',
        'description': 'Debug test description',
        'website': 'https://debug-test.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '******-DEBUG',
        'timezone': 'UTC',
        'language': 'en',
        'industry': 'Debug Testing',
        'size': '1-10',
        'city': 'Debug City',
        'country': 'Debug Country',
        'founded': 2024,
        'address_line1': '123 Debug Street',
        'address_line2': 'Suite 456',
        'postal_code': '12345',
        'linkedin': 'https://linkedin.com/company/debug',
        'twitter': 'https://twitter.com/debug',
        'facebook': 'https://facebook.com/debug',
        'custom_domain': 'debug-test.com',
        'list_in_directory': True
    }
    
    try:
        print("\n📝 Step 1: Creating form...")
        form = CompanyCreationForm(data=form_data, user=user)
        
        print(f"Form bound: {form.is_bound}")
        print(f"Form data keys: {list(form.data.keys())}")
        
        print("\n🔍 Step 2: Validating form...")
        is_valid = form.is_valid()
        print(f"Form valid: {is_valid}")
        
        if not is_valid:
            print(f"Form errors: {form.errors}")
            return False
        
        print(f"Cleaned data keys: {list(form.cleaned_data.keys())}")
        
        # Check specific fields in cleaned_data
        key_fields = ['mission', 'website', 'contact_email', 'industry', 'city']
        print("\n📊 Cleaned data for key fields:")
        for field in key_fields:
            value = form.cleaned_data.get(field, 'NOT_FOUND')
            print(f"  {field}: '{value}'")
        
        print("\n💾 Step 3: Saving form...")
        
        # Save with debug
        company = form.save()
        print(f"✅ Company saved: {company.name} (ID: {company.id})")
        
        print("\n🔍 Step 4: Checking CompanyInformation...")
        
        try:
            company_info = CompanyInformation.objects.get(company=company)
            print("✅ CompanyInformation found")
            
            # Check if data was saved
            print("\n📋 Saved data:")
            for field in key_fields:
                saved_value = getattr(company_info, field, 'NOT_FOUND')
                expected_value = form_data.get(field, 'NOT_IN_FORM_DATA')
                match = saved_value == expected_value
                status = "✅" if match else "❌"
                print(f"  {status} {field}: '{saved_value}' (expected: '{expected_value}')")
            
            # Check all fields
            print(f"\n📊 All CompanyInformation fields:")
            for field_name in ['mission', 'description', 'website', 'contact_email', 
                              'contact_phone', 'timezone', 'language', 'industry', 
                              'size', 'city', 'country', 'founded', 'address_line1', 
                              'address_line2', 'postal_code', 'linkedin', 'twitter', 
                              'facebook', 'custom_domain', 'list_in_directory']:
                value = getattr(company_info, field_name, 'NOT_FOUND')
                print(f"    {field_name}: '{value}'")
                
        except CompanyInformation.DoesNotExist:
            print("❌ CompanyInformation not found!")
            return False
        
        # Clean up
        company.delete()
        user.delete()
        
        print("\n🎉 DEBUG COMPLETE!")
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_form_submission()
    sys.exit(0 if success else 1)
