#!/usr/bin/env python
"""
Test script to verify the AssistantForm fix for community entities.
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from assistants.forms import AssistantForm
from assistants.models import Assistant

def test_community_form():
    """Test that AssistantForm correctly handles community entities."""

    print("Testing AssistantForm for community entities...")

    # Create a mock company with community entity type
    class MockCompany:
        entity_type = 'community'
        id = 1

    company = MockCompany()

    try:
        # Test form creation without instance (this should remove the assistant_type field for community entities)
        form = AssistantForm(company=company)

        # Check that assistant_type field was removed
        if 'assistant_type' not in form.fields:
            print("✓ assistant_type field correctly removed from form for community entity")
        else:
            print("✗ assistant_type field still exists in form for community entity")
            return False

        # Check that assistant_type_hidden field was added
        if 'assistant_type_hidden' in form.fields:
            print("✓ assistant_type_hidden field correctly added to form")
            hidden_field = form.fields['assistant_type_hidden']
            if hidden_field.initial == Assistant.TYPE_COMMUNITY:
                print("✓ assistant_type_hidden field has correct initial value")
            else:
                print(f"✗ assistant_type_hidden field has wrong initial value: {hidden_field.initial}")
                return False
        else:
            print("✗ assistant_type_hidden field not added to form")
            return False

        return True

    except Exception as e:
        print(f"✗ Form creation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_regular_company_form():
    """Test that regular companies still have the assistant_type field."""

    print("\nTesting AssistantForm for regular companies...")

    # Create a mock company with regular entity type
    class MockCompany:
        entity_type = 'company'  # Regular company
        id = 1

    company = MockCompany()

    try:
        # Test form creation without instance
        form = AssistantForm(company=company)

        # Check that assistant_type field still exists
        if 'assistant_type' in form.fields:
            print("✓ assistant_type field correctly preserved for regular company")
        else:
            print("✗ assistant_type field incorrectly removed from form for regular company")
            return False

        # Check that assistant_type_hidden field was NOT added
        if 'assistant_type_hidden' not in form.fields:
            print("✓ assistant_type_hidden field correctly not added for regular company")
        else:
            print("✗ assistant_type_hidden field incorrectly added for regular company")
            return False

        return True

    except Exception as e:
        print(f"✗ Form creation failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Running AssistantForm fix tests...\n")

    # Run all tests
    tests = [
        test_community_form,
        test_regular_company_form,
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)

    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print(f"{'='*50}")

    passed = sum(results)
    total = len(results)

    print(f"Tests passed: {passed}/{total}")

    if all(results):
        print("✓ All tests passed! The AssistantForm fix is working correctly.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please check the implementation.")
        sys.exit(1)
