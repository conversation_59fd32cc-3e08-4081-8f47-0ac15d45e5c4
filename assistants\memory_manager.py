"""
Advanced memory management with object pooling, lazy loading, and leak detection.
"""

import gc
import threading
import time
import weakref
from typing import Dict, Any, List, Optional, Type, Callable, Set
from collections import defaultdict, deque
import logging
import psutil
import os

logger = logging.getLogger(__name__)


class ObjectPool:
    """
    Object pool for frequently created/destroyed objects.
    Reduces garbage collection pressure and improves performance.
    """
    
    def __init__(self, factory: Callable, max_size: int = 100, reset_func: Callable = None):
        self.factory = factory
        self.max_size = max_size
        self.reset_func = reset_func
        self.pool = deque()
        self.lock = threading.Lock()
        self.created_count = 0
        self.reused_count = 0
    
    def acquire(self) -> Any:
        """Get an object from the pool or create a new one."""
        with self.lock:
            if self.pool:
                obj = self.pool.popleft()
                self.reused_count += 1
                
                # Reset object if reset function provided
                if self.reset_func:
                    self.reset_func(obj)
                
                return obj
            else:
                obj = self.factory()
                self.created_count += 1
                return obj
    
    def release(self, obj: Any) -> None:
        """Return an object to the pool."""
        with self.lock:
            if len(self.pool) < self.max_size:
                self.pool.append(obj)
    
    def get_stats(self) -> Dict[str, int]:
        """Get pool statistics."""
        with self.lock:
            return {
                'pool_size': len(self.pool),
                'max_size': self.max_size,
                'created_count': self.created_count,
                'reused_count': self.reused_count,
                'reuse_ratio': self.reused_count / max(self.created_count, 1)
            }


class LazyLoader:
    """
    Lazy loading with smart prefetching for database objects.
    """
    
    def __init__(self, loader_func: Callable, prefetch_func: Callable = None):
        self.loader_func = loader_func
        self.prefetch_func = prefetch_func
        self.cache = {}
        self.loading = set()
        self.lock = threading.RLock()
        self.access_patterns = defaultdict(list)
    
    def get(self, key: str, *args, **kwargs) -> Any:
        """Get object with lazy loading."""
        with self.lock:
            # Return cached object if available
            if key in self.cache:
                self._record_access(key)
                return self.cache[key]
            
            # Prevent duplicate loading
            if key in self.loading:
                # Wait for other thread to finish loading
                while key in self.loading:
                    time.sleep(0.001)
                return self.cache.get(key)
            
            # Load object
            self.loading.add(key)
        
        try:
            obj = self.loader_func(key, *args, **kwargs)
            
            with self.lock:
                self.cache[key] = obj
                self._record_access(key)
                
                # Trigger prefetching if configured
                if self.prefetch_func:
                    self._trigger_prefetch(key)
            
            return obj
            
        finally:
            with self.lock:
                self.loading.discard(key)
    
    def prefetch(self, keys: List[str]) -> None:
        """Prefetch multiple objects."""
        if not self.prefetch_func:
            return
        
        # Filter out already cached keys
        with self.lock:
            keys_to_fetch = [key for key in keys if key not in self.cache and key not in self.loading]
        
        if keys_to_fetch:
            threading.Thread(
                target=self._prefetch_worker,
                args=(keys_to_fetch,),
                daemon=True
            ).start()
    
    def _prefetch_worker(self, keys: List[str]) -> None:
        """Worker thread for prefetching."""
        try:
            objects = self.prefetch_func(keys)
            
            with self.lock:
                for key, obj in objects.items():
                    if key not in self.cache:
                        self.cache[key] = obj
                        
        except Exception as e:
            logger.warning(f"Prefetch error: {e}")
    
    def _record_access(self, key: str) -> None:
        """Record access pattern for smart prefetching."""
        current_time = time.time()
        self.access_patterns[key].append(current_time)
        
        # Keep only recent accesses (last hour)
        cutoff_time = current_time - 3600
        self.access_patterns[key] = [
            t for t in self.access_patterns[key] if t > cutoff_time
        ]
    
    def _trigger_prefetch(self, key: str) -> None:
        """Trigger smart prefetching based on access patterns."""
        # Simple strategy: prefetch related objects
        # This can be enhanced with ML-based prediction
        pass
    
    def clear_cache(self) -> None:
        """Clear the cache."""
        with self.lock:
            self.cache.clear()
            self.access_patterns.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get loader statistics."""
        with self.lock:
            return {
                'cached_objects': len(self.cache),
                'loading_objects': len(self.loading),
                'access_patterns': len(self.access_patterns)
            }


class MemoryLeakDetector:
    """
    Detects and reports potential memory leaks.
    """
    
    def __init__(self, check_interval: int = 300):  # 5 minutes
        self.check_interval = check_interval
        self.object_counts = defaultdict(int)
        self.memory_snapshots = deque(maxlen=20)
        self.running = False
        self.thread = None
        self.weak_refs = set()
    
    def start_monitoring(self) -> None:
        """Start memory leak monitoring."""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        logger.info("Memory leak detector started")
    
    def stop_monitoring(self) -> None:
        """Stop memory leak monitoring."""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("Memory leak detector stopped")
    
    def track_object(self, obj: Any, name: str = None) -> None:
        """Track an object for potential leaks."""
        obj_name = name or obj.__class__.__name__
        
        def cleanup_callback(ref):
            self.weak_refs.discard(ref)
            self.object_counts[obj_name] -= 1
        
        weak_ref = weakref.ref(obj, cleanup_callback)
        self.weak_refs.add(weak_ref)
        self.object_counts[obj_name] += 1
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while self.running:
            try:
                self._take_memory_snapshot()
                self._check_for_leaks()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Memory monitoring error: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _take_memory_snapshot(self) -> None:
        """Take a memory usage snapshot."""
        try:
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            snapshot = {
                'timestamp': time.time(),
                'rss': memory_info.rss,  # Resident Set Size
                'vms': memory_info.vms,  # Virtual Memory Size
                'percent': process.memory_percent(),
                'object_counts': dict(self.object_counts)
            }
            
            self.memory_snapshots.append(snapshot)
            
        except Exception as e:
            logger.warning(f"Failed to take memory snapshot: {e}")
    
    def _check_for_leaks(self) -> None:
        """Check for potential memory leaks."""
        if len(self.memory_snapshots) < 3:
            return
        
        # Check for consistent memory growth
        recent_snapshots = list(self.memory_snapshots)[-3:]
        memory_growth = []
        
        for i in range(1, len(recent_snapshots)):
            growth = recent_snapshots[i]['rss'] - recent_snapshots[i-1]['rss']
            memory_growth.append(growth)
        
        # Alert if memory consistently growing
        if all(growth > 0 for growth in memory_growth):
            total_growth = sum(memory_growth)
            if total_growth > 50 * 1024 * 1024:  # 50MB growth
                logger.warning(f"Potential memory leak detected: {total_growth / 1024 / 1024:.1f}MB growth")
                self._analyze_object_growth()
    
    def _analyze_object_growth(self) -> None:
        """Analyze which objects are growing in count."""
        if len(self.memory_snapshots) < 2:
            return
        
        current = self.memory_snapshots[-1]['object_counts']
        previous = self.memory_snapshots[-2]['object_counts']
        
        for obj_type, current_count in current.items():
            previous_count = previous.get(obj_type, 0)
            growth = current_count - previous_count
            
            if growth > 10:  # Significant growth
                logger.warning(f"Object type '{obj_type}' grew by {growth} instances")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get current memory statistics."""
        try:
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,
                'vms_mb': memory_info.vms / 1024 / 1024,
                'percent': process.memory_percent(),
                'tracked_objects': dict(self.object_counts),
                'snapshots_count': len(self.memory_snapshots)
            }
        except Exception as e:
            logger.error(f"Error getting memory stats: {e}")
            return {}


class GarbageCollectionOptimizer:
    """
    Optimizes garbage collection for better performance.
    """
    
    def __init__(self):
        self.gc_stats = defaultdict(list)
        self.last_collection = time.time()
        self.collection_threshold = 60  # seconds
    
    def optimize_gc(self) -> None:
        """Optimize garbage collection settings."""
        # Adjust GC thresholds based on application behavior
        current_thresholds = gc.get_threshold()
        
        # Increase thresholds for better performance (less frequent GC)
        new_thresholds = (
            current_thresholds[0] * 2,
            current_thresholds[1] * 2,
            current_thresholds[2] * 2
        )
        
        gc.set_threshold(*new_thresholds)
        logger.info(f"GC thresholds adjusted: {current_thresholds} -> {new_thresholds}")
    
    def force_collection_if_needed(self) -> None:
        """Force garbage collection if threshold reached."""
        current_time = time.time()
        
        if current_time - self.last_collection > self.collection_threshold:
            collected = gc.collect()
            self.last_collection = current_time
            
            if collected > 0:
                logger.info(f"Forced GC collected {collected} objects")
    
    def get_gc_stats(self) -> Dict[str, Any]:
        """Get garbage collection statistics."""
        return {
            'counts': gc.get_count(),
            'threshold': gc.get_threshold(),
            'stats': gc.get_stats() if hasattr(gc, 'get_stats') else None
        }


# Global instances
object_pools = {}
lazy_loaders = {}
memory_detector = MemoryLeakDetector()
gc_optimizer = GarbageCollectionOptimizer()


def get_object_pool(name: str, factory: Callable, max_size: int = 100, 
                   reset_func: Callable = None) -> ObjectPool:
    """Get or create an object pool."""
    if name not in object_pools:
        object_pools[name] = ObjectPool(factory, max_size, reset_func)
    return object_pools[name]


def get_lazy_loader(name: str, loader_func: Callable, 
                   prefetch_func: Callable = None) -> LazyLoader:
    """Get or create a lazy loader."""
    if name not in lazy_loaders:
        lazy_loaders[name] = LazyLoader(loader_func, prefetch_func)
    return lazy_loaders[name]


def start_memory_monitoring() -> None:
    """Start memory leak detection."""
    memory_detector.start_monitoring()
    gc_optimizer.optimize_gc()


def stop_memory_monitoring() -> None:
    """Stop memory leak detection."""
    memory_detector.stop_monitoring()


def get_memory_stats() -> Dict[str, Any]:
    """Get comprehensive memory statistics."""
    stats = {
        'memory': memory_detector.get_memory_stats(),
        'gc': gc_optimizer.get_gc_stats(),
        'object_pools': {name: pool.get_stats() for name, pool in object_pools.items()},
        'lazy_loaders': {name: loader.get_stats() for name, loader in lazy_loaders.items()}
    }
    return stats


def cleanup_memory() -> None:
    """Perform memory cleanup."""
    # Clear lazy loader caches
    for loader in lazy_loaders.values():
        loader.clear_cache()
    
    # Force garbage collection
    gc_optimizer.force_collection_if_needed()
    
    logger.info("Memory cleanup completed")


# Context manager for object pooling
class PooledObject:
    """Context manager for pooled objects."""
    
    def __init__(self, pool: ObjectPool):
        self.pool = pool
        self.obj = None
    
    def __enter__(self):
        self.obj = self.pool.acquire()
        return self.obj
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.obj is not None:
            self.pool.release(self.obj)


# Decorators for memory optimization
def pooled(pool_name: str, factory: Callable = None, max_size: int = 100):
    """Decorator to use object pooling for a function's return value."""
    def decorator(func):
        if factory is None:
            pool_factory = func
        else:
            pool_factory = factory
        
        pool = get_object_pool(pool_name, pool_factory, max_size)
        
        def wrapper(*args, **kwargs):
            with PooledObject(pool) as obj:
                return obj
        
        return wrapper
    return decorator


def track_memory(name: str = None):
    """Decorator to track memory usage of objects created by a function."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            obj_name = name or f"{func.__name__}_result"
            memory_detector.track_object(result, obj_name)
            return result
        return wrapper
    return decorator
