# 📧 cPanel Email Configuration for 24seven.site

## ✅ Configuration Complete

The Django application has been configured to use the exact cPanel email settings for `24seven.site`.

## 📋 cPanel Email Settings Applied

### **Mail Client Manual Settings**

#### **Secure SSL/TLS Settings (Recommended)**
- **Username**: `<EMAIL>`
- **Password**: Use the email account's password
- **Incoming Server**: `mail.24seven.site`
  - **IMAP Port**: 993 (SSL)
  - **POP3 Port**: 995 (SSL)
- **Outgoing Server**: `mail.24seven.site`
  - **SMTP Port**: 465 (SSL/TLS)
- **Authentication**: Required for IMAP, POP3, and SMTP

### **Calendar & Contacts Manual Settings**

#### **Secure SSL/TLS Settings (Recommended)**
- **Username**: `<EMAIL>`
- **Password**: Use the email account's password
- **Server**: `https://mail.24seven.site:2080`
  - **Port**: 2080

#### **Full Calendar URL(s)**
- **cPanel CalDAV Calendar**: `https://mail.24seven.site:2080/calendars/<EMAIL>/calendar`

#### **Full Contact List URL(s)**
- **cPanel CardDAV Address Book**: `https://mail.24seven.site:2080/addressbooks/<EMAIL>/addressbook`

## ⚙️ Django Configuration Applied

### **Settings.py Configuration**
```python
# Email settings (configured for 24seven.site - cPanel settings)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'mail.24seven.site'
EMAIL_PORT = 465  # SMTP SSL Port
EMAIL_USE_SSL = True  # SSL/TLS enabled
EMAIL_USE_TLS = False  # TLS disabled (using SSL)
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = ''  # Set in environment variables
DEFAULT_FROM_EMAIL = '24seven <<EMAIL>>'

# Additional email settings for cPanel compatibility
EMAIL_TIMEOUT = 30  # 30 second timeout for email operations
SERVER_EMAIL = DEFAULT_FROM_EMAIL  # Server error emails
```

### **Environment Variables (.env)**
```bash
# Email Settings (configured for 24seven.site - cPanel settings)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=mail.24seven.site
EMAIL_PORT=465
EMAIL_USE_SSL=True
EMAIL_USE_TLS=False
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-actual-email-password
DEFAULT_FROM_EMAIL=24seven <<EMAIL>>
```

## 🔧 Setup Instructions

### **1. Create Email Account in cPanel**
1. Log into your cPanel account
2. Go to **Email Accounts**
3. Create new email account: `<EMAIL>`
4. Set a strong password
5. Note the password for Django configuration

### **2. Update Django Configuration**
1. Update the `EMAIL_HOST_PASSWORD` in your `.env` file:
   ```bash
   EMAIL_HOST_PASSWORD=your-actual-email-password
   ```

### **3. Test Email Configuration**
```python
# Test in Django shell
python manage.py shell

>>> from django.core.mail import send_mail
>>> send_mail(
...     'Test Email',
...     'This is a test email from Django.',
...     '<EMAIL>',
...     ['<EMAIL>'],
...     fail_silently=False,
... )
```

### **4. Verify Email Functionality**
```python
# Test email utilities
python manage.py shell

>>> from accounts.email_utils import send_html_email
>>> send_html_email(
...     '<EMAIL>',
...     'Test Subject',
...     'Test message content',
...     'Test HTML content'
... )
```

## 🔍 Troubleshooting

### **Common Issues and Solutions**

#### **1. Authentication Failed**
- **Problem**: `SMTPAuthenticationError`
- **Solution**: 
  - Verify email account exists in cPanel
  - Check username is `<EMAIL>` (full email address)
  - Verify password is correct
  - Ensure account is not suspended

#### **2. Connection Timeout**
- **Problem**: `SMTPConnectError` or timeout
- **Solution**:
  - Verify `mail.24seven.site` resolves correctly
  - Check firewall allows port 465 outbound
  - Ensure cPanel email service is running

#### **3. SSL Certificate Issues**
- **Problem**: SSL verification errors
- **Solution**:
  - Verify SSL certificate is valid for `mail.24seven.site`
  - Check if self-signed certificates need special handling

#### **4. Port Blocked**
- **Problem**: Cannot connect to port 465
- **Solution**:
  - Check if hosting provider blocks port 465
  - Try alternative port 587 with STARTTLS if needed

### **Alternative Configuration (if port 465 blocked)**
```python
# If port 465 is blocked, try port 587 with STARTTLS
EMAIL_HOST = 'mail.24seven.site'
EMAIL_PORT = 587
EMAIL_USE_SSL = False
EMAIL_USE_TLS = True
```

## 📊 Email Configuration Summary

| Setting | Value |
|---------|-------|
| **SMTP Server** | `mail.24seven.site` |
| **SMTP Port** | `465` (SSL) |
| **Security** | SSL/TLS |
| **Authentication** | Required |
| **Username** | `<EMAIL>` |
| **From Address** | `24seven <<EMAIL>>` |

| Protocol | Server | Port | Security |
|----------|--------|------|----------|
| **SMTP** | `mail.24seven.site` | 465 | SSL/TLS |
| **IMAP** | `mail.24seven.site` | 993 | SSL |
| **POP3** | `mail.24seven.site` | 995 | SSL |
| **CalDAV** | `mail.24seven.site` | 2080 | HTTPS |
| **CardDAV** | `mail.24seven.site` | 2080 | HTTPS |

## ✅ Verification Checklist

- ✅ Email account `<EMAIL>` created in cPanel
- ✅ Django settings updated with cPanel configuration
- ✅ Environment variables set with actual password
- ✅ Email functionality tested successfully
- ✅ DNS records configured for `mail.24seven.site`
- ✅ SSL certificates valid and working
- ✅ Firewall allows SMTP traffic on port 465

## 🚀 Production Deployment

### **Final Steps**
1. **Upload Configuration**: Deploy updated `.env` and settings files
2. **Set Password**: Update `EMAIL_HOST_PASSWORD` with real password
3. **Test Emails**: Send test emails to verify functionality
4. **Monitor Logs**: Check for any email-related errors
5. **User Testing**: Test user registration and password reset emails

Your Django application is now configured to use the exact cPanel email settings for `24seven.site`! 🎉
