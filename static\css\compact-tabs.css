/**
 * Compact Tabs CSS
 * Makes navigation tabs more compact to fit properly in cards
 */

/* ===== COMPACT FORM CHECK RADIO BUTTONS (Search Form Tabs) ===== */
.search-form-card .form-check {
    position: relative !important;
    padding: 8px 14px !important;
    border: 3px solid #666666 !important;
    border-radius: 8px !important;
    margin-bottom: 6px !important;
    margin-right: 10px !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%) !important;
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
    transition: all 0.3s ease !important;
    min-width: auto !important;
    flex: 0 0 auto !important;
    cursor: pointer !important;
}

.search-form-card .form-check:hover {
    border-color: #cf2e2e !important;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(207, 46, 46, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    transform: translateY(-2px) scale(1.02) !important;
    background: linear-gradient(135deg, #ffffff 0%, #fff5f5 50%, #ffffff 100%) !important;
}

.search-form-card .form-check-input {
    margin-top: 0.2rem !important;
    margin-right: 0.5rem !important;
    width: 1.1rem !important;
    height: 1.1rem !important;
    border: 2px solid #666666 !important;
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.search-form-card .form-check-input:checked {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    box-shadow:
        0 0 0 3px rgba(207, 46, 46, 0.3),
        0 2px 4px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.search-form-card .form-check-input:focus {
    border-color: #cf2e2e !important;
    box-shadow:
        0 0 0 3px rgba(207, 46, 46, 0.25),
        0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.search-form-card .form-check-label {
    font-size: 0.9rem !important;
    font-weight: 700 !important;
    color: #333333 !important;
    margin-bottom: 0 !important;
    line-height: 1.3 !important;
    cursor: pointer !important;
    white-space: nowrap !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

.search-form-card .form-check-inline {
    margin-right: 0.5rem !important;
    margin-bottom: 0.25rem !important;
}

/* ===== COMPACT NAVIGATION TABS ===== */
.nav-tabs {
    border-bottom: 2px solid #dee2e6 !important;
    margin-bottom: 1rem !important;
}

.nav-tabs .nav-item {
    margin-bottom: -2px !important;
    margin-right: 2px !important;
}

.nav-tabs .nav-link {
    border: 2px solid transparent !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
    color: #666666 !important;
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    padding: 0.4rem 0.8rem !important;
    transition: all 0.3s ease !important;
    margin-right: 0.1rem !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    box-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.3px !important;
    white-space: nowrap !important;
    min-width: auto !important;
    flex: 0 0 auto !important;
}

.nav-tabs .nav-link:hover {
    border-color: rgba(207, 46, 46, 0.3) rgba(207, 46, 46, 0.3) transparent !important;
    color: #cf2e2e !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    box-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
    transform: translateY(-1px) !important;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border-color: #cf2e2e #cf2e2e #ffffff !important;
    color: #cf2e2e !important;
    font-weight: 700 !important;
    box-shadow: 
        0 3px 8px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    transform: translateY(-2px) !important;
}

/* ===== COMPACT TAB CONTENT ===== */
.tab-content {
    padding: 0.75rem !important;
    background-color: #ffffff !important;
    border: 2px solid #dee2e6 !important;
    border-top: none !important;
    border-radius: 0 0 8px 8px !important;
    box-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

.tab-pane {
    padding: 0.5rem !important;
}

/* ===== RESPONSIVE COMPACT TABS ===== */
@media (max-width: 768px) {
    .search-form-card .form-check {
        padding: 4px 8px !important;
        margin-right: 4px !important;
        margin-bottom: 4px !important;
    }
    
    .search-form-card .form-check-label {
        font-size: 0.75rem !important;
    }
    
    .search-form-card .form-check-input {
        width: 0.9rem !important;
        height: 0.9rem !important;
        margin-right: 0.3rem !important;
    }
    
    .nav-tabs .nav-link {
        font-size: 0.7rem !important;
        padding: 0.3rem 0.6rem !important;
        letter-spacing: 0.2px !important;
    }
    
    .tab-content {
        padding: 0.5rem !important;
    }
    
    .tab-pane {
        padding: 0.25rem !important;
    }
}

@media (max-width: 576px) {
    .search-form-card .form-check {
        padding: 3px 6px !important;
        margin-right: 3px !important;
        margin-bottom: 3px !important;
        border-radius: 4px !important;
    }
    
    .search-form-card .form-check-label {
        font-size: 0.7rem !important;
    }
    
    .search-form-card .form-check-input {
        width: 0.8rem !important;
        height: 0.8rem !important;
        margin-right: 0.25rem !important;
    }
    
    .nav-tabs .nav-link {
        font-size: 0.65rem !important;
        padding: 0.25rem 0.5rem !important;
        letter-spacing: 0.1px !important;
        border-radius: 4px 4px 0 0 !important;
    }
    
    .tab-content {
        padding: 0.4rem !important;
        border-radius: 0 0 6px 6px !important;
    }
}

/* ===== FLEX CONTAINER ADJUSTMENTS ===== */
.search-form-card .d-flex.flex-wrap {
    gap: 0.5rem !important;
    align-items: center !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    width: 100% !important;
}

.search-form-card .col-12.d-flex.flex-wrap {
    padding-left: 0 !important;
    padding-right: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* ===== RADIO BUTTON CONTAINER CENTERING ===== */
.search-form-card .form-check-inline {
    margin-right: 0.75rem !important;
    margin-bottom: 0.5rem !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.search-form-card .form-check-inline:last-of-type {
    margin-right: 0 !important;
}

/* ===== BUTTON CONTAINER ADJUSTMENTS ===== */
.search-form-card .ms-auto {
    margin-left: auto !important;
    margin-top: 0.5rem !important;
    flex-shrink: 0 !important;
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
}

@media (min-width: 576px) {
    .search-form-card .ms-auto {
        width: auto !important;
        margin-top: 0 !important;
        justify-content: flex-end !important;
    }

    .search-form-card .d-flex.flex-wrap {
        justify-content: space-between !important;
    }

    .search-form-card .form-check-inline {
        margin-right: 1rem !important;
    }
}

/* ===== SEARCH BUTTON COMPACT STYLING ===== */
.search-form-card .btn {
    padding: 0.4rem 0.8rem !important;
    font-size: 0.85rem !important;
    font-weight: 700 !important;
    border-radius: 6px !important;
    white-space: nowrap !important;
}

.search-form-card .ms-auto {
    margin-left: auto !important;
    flex-shrink: 0 !important;
}

/* ===== FORM LABEL COMPACT STYLING ===== */
.search-form-card .form-label {
    font-size: 0.9rem !important;
    font-weight: 700 !important;
    margin-bottom: 0.5rem !important;
    color: #333333 !important;
}

/* ===== INPUT FIELD COMPACT STYLING ===== */
.search-form-card .form-control {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.9rem !important;
    border-radius: 6px !important;
    border: 2px solid #dee2e6 !important;
}

.search-form-card .form-control:focus {
    border-color: #cf2e2e !important;
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
}

/* ===== CARD SPACING ADJUSTMENTS ===== */
.search-form-card {
    padding: 1.25rem !important;
    text-align: center !important;
}

.search-form-card .row.g-2 {
    --bs-gutter-x: 0.5rem !important;
    --bs-gutter-y: 0.75rem !important;
    justify-content: center !important;
    align-items: center !important;
}

.search-form-card .mb-2 {
    margin-bottom: 0.75rem !important;
}

/* ===== RADIO BUTTON GROUP CENTERING ===== */
.search-form-card .col-12.d-flex {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
}

@media (min-width: 576px) {
    .search-form-card .col-12.d-flex {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
    }
}

/* ===== RADIO BUTTON WRAPPER ===== */
.search-form-card .d-flex.flex-wrap {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 0.75rem !important;
    margin-bottom: 0.5rem !important;
    width: 100% !important;
}

@media (min-width: 576px) {
    .search-form-card .d-flex.flex-wrap {
        justify-content: flex-start !important;
        margin-bottom: 0 !important;
        width: auto !important;
        flex: 1 !important;
    }
}

/* ===== ICON SPACING ===== */
.search-form-card .btn i {
    margin-right: 0.3rem !important;
}

/* ===== OVERFLOW HANDLING ===== */
.search-form-card {
    overflow: hidden !important;
}

.search-form-card .d-flex {
    flex-wrap: wrap !important;
    overflow: visible !important;
}

/* ===== ENSURE PROPER ALIGNMENT ===== */
.search-form-card .form-check-inline:last-of-type {
    margin-right: 0 !important;
}

.search-form-card .mt-2.mt-sm-0 {
    margin-top: 0.25rem !important;
}

@media (min-width: 576px) {
    .search-form-card .mt-2.mt-sm-0 {
        margin-top: 0 !important;
    }
}

/* ===== PREVENT TEXT WRAPPING ===== */
.search-form-card .form-check-label,
.nav-tabs .nav-link {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
}

/* ===== COMPACT SPACING FOR SMALL SCREENS ===== */
@media (max-width: 480px) {
    .search-form-card {
        padding: 0.75rem !important;
    }
    
    .search-form-card .form-check {
        padding: 2px 4px !important;
        margin-right: 2px !important;
        margin-bottom: 2px !important;
    }
    
    .search-form-card .form-check-label {
        font-size: 0.65rem !important;
    }
    
    .search-form-card .btn {
        padding: 0.3rem 0.6rem !important;
        font-size: 0.75rem !important;
    }
}
