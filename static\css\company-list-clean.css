/*
 * Clean Company/Constituency List Styling
 * Consolidated and conflict-free CSS for directory pages
 * Uses official NUP colors and design system
 */

/* ===== NUP OFFICIAL COLOR VARIABLES ===== */
:root {
    /* Core Official Colors */
    --nup-primary-red: #cf2e2e;        /* NUP Vivid Red - Primary accent */
    --nup-secondary-gold: #f7bd00;     /* NUP Strong Yellow/Gold - Secondary accent */
    --nup-pure-white: #ffffff;         /* Neutral White - Primary background */
    --nup-light-gray: #f7f7f7;         /* Light Gray - Secondary background */
    --nup-dark-charcoal: #242424;      /* Dark Charcoal - Main text */
    --nup-medium-gray: #797979;        /* Medium Gray - Secondary text */
    --nup-dark-accent: #252638;        /* New Dark Blue-Gray - Replaces all blues/purples */

    /* Interactive states */
    --nup-red-hover: #b82626;
    --nup-dark-accent-hover: #1e1f2e;

    /* Utility colors */
    --nup-border-light: #e5e5e5;
    --nup-shadow: rgba(37, 38, 56, 0.1);
    --nup-shadow-strong: rgba(37, 38, 56, 0.15);
}

/* ===== BASE LAYOUT ===== */
.company-directory-page {
    background-color: var(--nup-pure-white);
    color: var(--nup-dark-charcoal);
    font-family: 'Open Sans', 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== FEATURED SECTION ===== */
.featured-section {
    background-color: var(--nup-light-gray);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--nup-border-light);
}

.featured-section h2 {
    color: var(--nup-dark-charcoal);
    font-weight: bold;
    text-align: center;
    margin-bottom: 1.5rem;
}

/* ===== COMPANY LOGO CAROUSEL ===== */
.company-logo-carousel-container {
    overflow: hidden;
    position: relative;
    margin: 2rem 0;
}

.company-logo-carousel {
    display: flex;
    animation: scroll 30s linear infinite;
    gap: 2rem;
}

.company-logo-item {
    flex: 0 0 auto;
    width: 200px;
    text-align: center;
    padding: 1rem;
    background: var(--nup-pure-white);
    border-radius: 8px;
    border: 1px solid var(--nup-border-light);
    transition: all 0.3s ease;
}

.company-logo-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--nup-shadow-strong);
    border-color: var(--nup-primary-red);
}

.company-logo-item a {
    text-decoration: none;
    color: var(--nup-dark-charcoal);
}

.company-logo-item a:hover {
    color: var(--nup-primary-red);
}

.logo-container {
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--nup-light-gray);
    border-radius: 8px;
    overflow: hidden;
}

.company-logo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.company-logo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--nup-medium-gray);
}

.company-logo-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

@keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

/* ===== FILTER FORM ===== */
.filter-form {
    background-color: var(--nup-light-gray);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--nup-border-light);
}

.form-control:focus {
    border-color: var(--nup-primary-red);
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25);
}

/* ===== BUTTONS ===== */
.btn-primary {
    background-color: var(--nup-dark-accent);
    border-color: var(--nup-dark-accent);
    color: var(--nup-pure-white);
    font-weight: 600;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: var(--nup-dark-accent-hover);
    border-color: var(--nup-dark-accent-hover);
    color: var(--nup-pure-white);
}

.btn-outline-secondary {
    border-color: var(--nup-dark-accent);
    color: var(--nup-dark-accent);
    background-color: transparent;
}

.btn-outline-secondary:hover,
.btn-outline-secondary:focus,
.btn-outline-secondary:active {
    background-color: var(--nup-dark-accent);
    border-color: var(--nup-dark-accent);
    color: var(--nup-pure-white);
}

/* ===== TIER SECTIONS ===== */
.tier-section {
    margin-bottom: 3rem;
}

.tier-section h3 {
    color: var(--nup-dark-charcoal);
    font-weight: bold;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--nup-border-light);
}

.tier-section.gold h3 {
    color: #ffc107;
}

.tier-section.silver h3 {
    color: #6c757d;
}

.tier-section.bronze h3 {
    color: #cd7f32;
}

/* ===== COMPANY CARDS ===== */
.company-cards-container {
    display: grid;
    gap: 1.5rem;
}

.list-group-item.directory-card {
    background-color: var(--nup-pure-white);
    border: 1px solid var(--nup-border-light);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: var(--nup-shadow);
    cursor: pointer;
}

.list-group-item.directory-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--nup-shadow-strong);
    border-color: var(--nup-primary-red);
    background: linear-gradient(135deg, 
        rgba(207, 46, 46, 0.02) 0%, 
        var(--nup-pure-white) 100%);
}

.directory-item-link-wrapper {
    text-decoration: none;
    color: inherit;
}

.directory-item-link-wrapper:hover {
    text-decoration: none;
    color: inherit;
}

/* ===== COMPANY LOGO IN CARDS ===== */
.company-logo-img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 8px;
    background: var(--nup-light-gray);
    padding: 8px;
}

/* ===== CONTACT INFO ===== */
.contact-info {
    font-size: 0.9rem;
}

.contact-info i {
    color: var(--nup-primary-red);
    width: 16px;
    text-align: center;
    margin-right: 0.5rem;
}

.contact-info a {
    color: var(--nup-dark-charcoal);
    text-decoration: none;
}

.contact-info a:hover {
    color: var(--nup-primary-red);
    text-decoration: underline;
}

/* ===== BADGES ===== */
.badge.bg-secondary {
    background-color: var(--nup-primary-red) !important;
    color: var(--nup-pure-white);
}

.badge.bg-light {
    background-color: var(--nup-light-gray) !important;
    color: var(--nup-dark-charcoal);
    border: 1px solid var(--nup-border-light);
}

.tier-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.tier-badge.tier-gold {
    background-color: #ffc107;
    color: #000;
}

.tier-badge.tier-silver {
    background-color: #6c757d;
    color: #fff;
}

.tier-badge.tier-bronze {
    background-color: #cd7f32;
    color: #fff;
}

/* ===== LIKE BUTTONS ===== */
.like-button {
    background: transparent !important;
    border: none !important;
    padding: 4px !important;
    color: var(--nup-medium-gray);
    transition: color 0.3s ease;
}

.like-button.text-danger {
    color: var(--nup-primary-red) !important;
}

.like-button:hover {
    color: var(--nup-primary-red) !important;
}

/* ===== RATING DISPLAY ===== */
.rating-display-container .text-warning {
    color: var(--nup-secondary-gold) !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .company-logo-item {
        width: 150px;
    }
    
    .logo-container {
        width: 80px;
        height: 80px;
    }
    
    .company-logo-img {
        width: 60px;
        height: 60px;
    }
    
    .list-group-item.directory-card {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .featured-section {
        padding: 1rem;
    }
    
    .filter-form {
        padding: 1rem;
    }
    
    .company-logo-carousel {
        gap: 1rem;
    }
    
    .company-logo-item {
        width: 120px;
        padding: 0.75rem;
    }
}
