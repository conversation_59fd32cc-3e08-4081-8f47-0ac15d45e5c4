/**
 * Enhanced Card Visibility CSS
 * Improves contrast and visibility of cards, especially core values cards
 */

/* ===== CORE VALUES CARDS ENHANCEMENT ===== */
/* ONLY target specific card types, NOT parent containers */
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5),
.card:not(.company-cards-container):not(.tier-section):not(.mt-5),
.values-card,
.core-values-card {
    border: 3px solid #666666 !important;
    border-radius: 12px !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%) !important;
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.15),
        0 3px 6px rgba(0, 0, 0, 0.1),
        0 1px 3px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    overflow: hidden !important;
}

.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5)::before,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5)::before,
.values-card::before,
.core-values-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #cf2e2e, #252638, #cf2e2e, #252638) !important;
    background-size: 400% 400% !important;
    border-radius: 15px !important;
    z-index: -1 !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    animation: gradientShift 8s ease infinite !important;
}

.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5):hover::before,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5):hover::before,
.values-card:hover::before,
.core-values-card:hover::before {
    opacity: 0.3 !important;
}

.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5):hover,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5):hover,
.values-card:hover,
.core-values-card:hover {
    border-color: #cf2e2e !important;
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow: 
        0 12px 24px rgba(0, 0, 0, 0.2),
        0 6px 12px rgba(0, 0, 0, 0.15),
        0 3px 6px rgba(0, 0, 0, 0.1),
        0 0 0 2px rgba(207, 46, 46, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    background: linear-gradient(135deg, #ffffff 0%, #fff5f5 50%, #ffffff 100%) !important;
}

/* ===== CARD CONTENT ENHANCEMENT ===== */
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) h1,
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) h2,
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) h3,
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) h4,
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) h5,
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) h6,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) h1,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) h2,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) h3,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) h4,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) h5,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) h6,
.values-card h1, .values-card h2, .values-card h3, .values-card h4, .values-card h5, .values-card h6,
.core-values-card h1, .core-values-card h2, .core-values-card h3, .core-values-card h4, .core-values-card h5, .core-values-card h6 {
    color: #333333 !important;
    font-weight: 700 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
    margin-bottom: 1rem !important;
}

.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) p,
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) span,
.nup-card:not(.company-cards-container):not(.tier-section):not(.mt-5) div,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) p,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) span,
.card:not(.company-cards-container):not(.tier-section):not(.mt-5) div,
.values-card p, .values-card span, .values-card div,
.core-values-card p, .core-values-card span, .core-values-card div {
    color: #555555 !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6) !important;
    line-height: 1.5 !important;
}

/* ===== CARD ICONS ENHANCEMENT ===== */
.nup-card i, .nup-card .bi,
.card i, .card .bi,
[class*="card"] i, [class*="card"] .bi,
.values-card i, .values-card .bi,
.core-values-card i, .core-values-card .bi {
    color: #cf2e2e !important;
    font-size: 1.5rem !important;
    margin-bottom: 0.75rem !important;
    text-shadow: 0 2px 4px rgba(207, 46, 46, 0.3) !important;
    transition: all 0.3s ease !important;
}

.nup-card:hover i, .nup-card:hover .bi,
.card:hover i, .card:hover .bi,
[class*="card"]:hover i, [class*="card"]:hover .bi,
.values-card:hover i, .values-card:hover .bi,
.core-values-card:hover i, .core-values-card:hover .bi {
    color: #252638 !important;
    transform: scale(1.1) !important;
    text-shadow: 0 3px 6px rgba(37, 38, 56, 0.4) !important;
}

/* ===== ICON CIRCLE ENHANCEMENT ===== */
.nup-icon-circle,
.icon-circle,
[class*="icon-circle"] {
    width: 60px !important;
    height: 60px !important;
    background: linear-gradient(135deg, #cf2e2e 0%, #252638 100%) !important;
    border: 3px solid #ffffff !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 1rem auto !important;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    transition: all 0.3s ease !important;
}

.nup-icon-circle i, .nup-icon-circle .bi,
.icon-circle i, .icon-circle .bi,
[class*="icon-circle"] i, [class*="icon-circle"] .bi {
    color: #ffffff !important;
    font-size: 1.8rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.nup-card:hover .nup-icon-circle,
.card:hover .icon-circle,
[class*="card"]:hover [class*="icon-circle"],
.values-card:hover .icon-circle,
.core-values-card:hover .icon-circle {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.2),
        0 3px 6px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.4) !important;
    background: linear-gradient(135deg, #252638 0%, #cf2e2e 100%) !important;
}

/* ===== CARD BODY ENHANCEMENT ===== */
.card-body,
.nup-card-body,
[class*="card-body"] {
    padding: 1.5rem !important;
    position: relative !important;
}

.card-body::before,
.nup-card-body::before,
[class*="card-body"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #cf2e2e, #252638, #cf2e2e) !important;
    border-radius: 0 0 6px 6px !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.nup-card:hover .card-body::before,
.card:hover .card-body::before,
[class*="card"]:hover [class*="card-body"]::before,
.values-card:hover .card-body::before,
.core-values-card:hover .card-body::before {
    opacity: 1 !important;
}

/* ===== CARD HEADER ENHANCEMENT ===== */
.card-header,
.nup-card-header,
[class*="card-header"] {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%) !important;
    border-bottom: 3px solid #666666 !important;
    padding: 1rem 1.5rem !important;
    font-weight: 700 !important;
    color: #333333 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

.nup-card:hover .card-header,
.card:hover .card-header,
[class*="card"]:hover [class*="card-header"],
.values-card:hover .card-header,
.core-values-card:hover .card-header {
    background: linear-gradient(135deg, #fff5f5 0%, #f8f9fa 50%, #fff5f5 100%) !important;
    border-bottom-color: #cf2e2e !important;
}

/* ===== CARD FOOTER ENHANCEMENT ===== */
.card-footer,
.nup-card-footer,
[class*="card-footer"] {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #f8f9fa 100%) !important;
    border-top: 3px solid #666666 !important;
    padding: 1rem 1.5rem !important;
    color: #555555 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.6) !important;
}

.nup-card:hover .card-footer,
.card:hover .card-footer,
[class*="card"]:hover [class*="card-footer"],
.values-card:hover .card-footer,
.core-values-card:hover .card-footer {
    background: linear-gradient(135deg, #fff5f5 0%, #f8f9fa 50%, #fff5f5 100%) !important;
    border-top-color: #cf2e2e !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .nup-card, .card, [class*="card"], .values-card, .core-values-card {
        border-width: 2px !important;
        border-radius: 8px !important;
        box-shadow: 
            0 4px 8px rgba(0, 0, 0, 0.12),
            0 2px 4px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    }
    
    .nup-card:hover, .card:hover, [class*="card"]:hover, .values-card:hover, .core-values-card:hover {
        transform: translateY(-2px) scale(1.01) !important;
        box-shadow: 
            0 8px 16px rgba(0, 0, 0, 0.15),
            0 4px 8px rgba(0, 0, 0, 0.1),
            0 0 0 1px rgba(207, 46, 46, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
    }
    
    .nup-icon-circle, .icon-circle, [class*="icon-circle"] {
        width: 50px !important;
        height: 50px !important;
    }
    
    .nup-icon-circle i, .nup-icon-circle .bi,
    .icon-circle i, .icon-circle .bi,
    [class*="icon-circle"] i, [class*="icon-circle"] .bi {
        font-size: 1.5rem !important;
    }
    
    .card-body, .nup-card-body, [class*="card-body"] {
        padding: 1rem !important;
    }
    
    .card-header, .nup-card-header, [class*="card-header"],
    .card-footer, .nup-card-footer, [class*="card-footer"] {
        padding: 0.75rem 1rem !important;
    }
}

@media (max-width: 576px) {
    .nup-card, .card, [class*="card"], .values-card, .core-values-card {
        border-width: 2px !important;
        border-radius: 6px !important;
        margin-bottom: 1rem !important;
    }
    
    .nup-icon-circle, .icon-circle, [class*="icon-circle"] {
        width: 45px !important;
        height: 45px !important;
    }
    
    .nup-icon-circle i, .nup-icon-circle .bi,
    .icon-circle i, .icon-circle .bi,
    [class*="icon-circle"] i, [class*="icon-circle"] .bi {
        font-size: 1.3rem !important;
    }
}

/* ===== ANIMATION KEYFRAMES ===== */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
.nup-card:focus, .card:focus, [class*="card"]:focus,
.values-card:focus, .core-values-card:focus {
    outline: 3px solid #cf2e2e !important;
    outline-offset: 2px !important;
    box-shadow: 
        0 0 0 5px rgba(207, 46, 46, 0.3),
        0 6px 12px rgba(0, 0, 0, 0.15),
        0 3px 6px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
    .nup-card, .card, [class*="card"], .values-card, .core-values-card {
        border-width: 4px !important;
        border-color: #000000 !important;
        background: #ffffff !important;
    }
    
    .nup-card h1, .nup-card h2, .nup-card h3, .nup-card h4, .nup-card h5, .nup-card h6,
    .card h1, .card h2, .card h3, .card h4, .card h5, .card h6,
    [class*="card"] h1, [class*="card"] h2, [class*="card"] h3, [class*="card"] h4, [class*="card"] h5, [class*="card"] h6 {
        color: #000000 !important;
        text-shadow: none !important;
    }
    
    .nup-card p, .nup-card span, .nup-card div,
    .card p, .card span, .card div,
    [class*="card"] p, [class*="card"] span, [class*="card"] div {
        color: #000000 !important;
        text-shadow: none !important;
    }
}
