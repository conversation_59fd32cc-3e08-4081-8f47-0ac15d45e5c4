"""
Context Preloader for LLM Assistants
Proactively loads and caches context data to improve response times.
"""

import asyncio
import json
import logging
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional, Any
from django.core.cache import cache
from django.conf import settings
from django.db.models import Q, Prefetch

from .models import Assistant, CommunityContext, NavigationItem
from .llm_utils_optimized import _get_system_context_cached
from .advanced_data_structures import response_cache
from .async_processors import submit_async_task, TaskPriority

logger = logging.getLogger(__name__)


class ContextPreloader:
    """
    Proactively preloads and caches context data for faster LLM responses.
    """
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.preload_cache_ttl = getattr(settings, 'CONTEXT_PRELOAD_TTL', 1800)  # 30 minutes
        self.enabled = getattr(settings, 'ENABLE_CONTEXT_PRELOADING', True)
    
    def preload_assistant_context(self, assistant_id: int, user=None) -> Dict[str, Any]:
        """
        Preload all context data for an assistant.
        
        Args:
            assistant_id: ID of the assistant
            user: User object (optional)
            
        Returns:
            Dictionary containing preloaded context data
        """
        if not self.enabled:
            return {}
        
        cache_key = f"preloaded_context:{assistant_id}:{user.id if user else 'anon'}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            logger.info(f"Context cache hit for assistant {assistant_id}")
            return cached_data
        
        try:
            assistant = Assistant.objects.select_related(
                'company', 'company__info', 'folder'
            ).prefetch_related(
                'navigation_items',
                Prefetch(
                    'communitycontext_set',
                    queryset=CommunityContext.objects.filter(is_active=True).order_by('-created_at')[:10]
                )
            ).get(id=assistant_id)
            
            # Preload different types of context
            context_data = {
                'assistant_info': self._preload_assistant_info(assistant),
                'system_context': self._preload_system_context(assistant),
                'navigation_context': self._preload_navigation_context(assistant),
                'community_context': self._preload_community_context(assistant),
                'website_data': self._preload_website_data(assistant),
                'preloaded_at': time.time()
            }
            
            # Cache the preloaded context
            cache.set(cache_key, context_data, self.preload_cache_ttl)
            logger.info(f"Preloaded context for assistant {assistant_id}")
            
            return context_data
            
        except Assistant.DoesNotExist:
            logger.warning(f"Assistant {assistant_id} not found for context preloading")
            return {}
        except Exception as e:
            logger.error(f"Error preloading context for assistant {assistant_id}: {e}")
            return {}
    
    def _preload_assistant_info(self, assistant: Assistant) -> Dict[str, Any]:
        """Preload basic assistant information."""
        return {
            'id': assistant.id,
            'name': assistant.name,
            'description': assistant.description,
            'assistant_type': assistant.assistant_type,
            'model': assistant.model,
            'temperature': assistant.temperature,
            'max_tokens': assistant.max_tokens,
            'company_name': assistant.company.name if assistant.company else None,
            'company_mission': getattr(assistant.company.info, 'mission', '') if assistant.company and hasattr(assistant.company, 'info') else ''
        }
    
    def _preload_system_context(self, assistant: Assistant) -> str:
        """Preload and cache system context."""
        try:
            return _get_system_context_cached(assistant)
        except Exception as e:
            logger.warning(f"Error preloading system context: {e}")
            return ""
    
    def _preload_navigation_context(self, assistant: Assistant) -> Dict[str, Any]:
        """Preload navigation context for support assistants."""
        if assistant.assistant_type != Assistant.TYPE_SUPPORT:
            return {}
        
        try:
            nav_items = assistant.navigation_items.filter(visible=True).order_by('order')
            navigation_data = []
            
            for item in nav_items:
                navigation_data.append({
                    'id': item.id,
                    'title': item.title,
                    'content': item.content,
                    'url': item.url,
                    'order': item.order,
                    'section_id': getattr(item, 'unique_id', f"section_{item.id}")
                })
            
            return {
                'items': navigation_data,
                'count': len(navigation_data)
            }
            
        except Exception as e:
            logger.warning(f"Error preloading navigation context: {e}")
            return {}
    
    def _preload_community_context(self, assistant: Assistant) -> Dict[str, Any]:
        """Preload community context for community assistants."""
        if assistant.assistant_type != Assistant.TYPE_COMMUNITY:
            return {}
        
        try:
            contexts = assistant.communitycontext_set.all()[:10]  # Already prefetched
            context_data = []
            
            for context in contexts:
                context_data.append({
                    'id': context.id,
                    'title': context.title or f"Context #{context.id}",
                    'text_content': context.text_content[:500],  # Truncate for preloading
                    'created_by': context.created_by.username if context.created_by else "Anonymous",
                    'created_at': context.created_at.isoformat(),
                    'times_used': context.times_used
                })
            
            return {
                'contexts': context_data,
                'count': len(context_data)
            }
            
        except Exception as e:
            logger.warning(f"Error preloading community context: {e}")
            return {}
    
    def _preload_website_data(self, assistant: Assistant) -> Dict[str, Any]:
        """Preload website data."""
        try:
            website_data = assistant.website_data or {}
            
            # Extract key information for faster access
            return {
                'navigation_items': website_data.get('navigation_items', []),
                'company_info': website_data.get('company_info', {}),
                'contact_info': website_data.get('contact_info', {}),
                'has_data': bool(website_data)
            }
            
        except Exception as e:
            logger.warning(f"Error preloading website data: {e}")
            return {}
    
    def preload_assistant_context_async(self, assistant_id: int, user=None) -> str:
        """
        Asynchronously preload assistant context.
        
        Returns:
            Task ID for tracking the preload operation
        """
        if not self.enabled:
            return ""
        
        return submit_async_task(
            self.preload_assistant_context,
            assistant_id,
            user,
            priority=TaskPriority.HIGH
        )
    
    def get_preloaded_context(self, assistant_id: int, user=None) -> Optional[Dict[str, Any]]:
        """
        Get preloaded context if available.
        
        Returns:
            Preloaded context data or None if not available
        """
        cache_key = f"preloaded_context:{assistant_id}:{user.id if user else 'anon'}"
        return cache.get(cache_key)
    
    def warm_popular_assistants(self, limit: int = 20) -> None:
        """
        Warm context cache for popular assistants.
        """
        try:
            popular_assistants = Assistant.objects.filter(
                is_active=True,
                is_public=True
            ).order_by('-total_interactions')[:limit]
            
            for assistant in popular_assistants:
                submit_async_task(
                    self.preload_assistant_context,
                    assistant.id,
                    priority=TaskPriority.LOW
                )
            
            logger.info(f"Started warming context for {len(popular_assistants)} popular assistants")
            
        except Exception as e:
            logger.error(f"Error warming popular assistants: {e}")
    
    def invalidate_assistant_context(self, assistant_id: int) -> None:
        """
        Invalidate preloaded context for an assistant.
        """
        # Clear all cached contexts for this assistant
        cache_pattern = f"preloaded_context:{assistant_id}:*"
        
        # Since Django cache doesn't support pattern deletion, we'll use a different approach
        # Store a list of cache keys for each assistant
        keys_cache_key = f"context_keys:{assistant_id}"
        cached_keys = cache.get(keys_cache_key, [])
        
        for key in cached_keys:
            cache.delete(key)
        
        cache.delete(keys_cache_key)
        logger.info(f"Invalidated preloaded context for assistant {assistant_id}")


# Global instance
context_preloader = ContextPreloader()


def preload_context_for_assistant(assistant_id: int, user=None) -> Dict[str, Any]:
    """
    Convenience function to preload context for an assistant.
    """
    return context_preloader.preload_assistant_context(assistant_id, user)


def get_preloaded_context(assistant_id: int, user=None) -> Optional[Dict[str, Any]]:
    """
    Convenience function to get preloaded context.
    """
    return context_preloader.get_preloaded_context(assistant_id, user)


def preload_context_async(assistant_id: int, user=None) -> str:
    """
    Convenience function to preload context asynchronously.
    """
    return context_preloader.preload_assistant_context_async(assistant_id, user)
