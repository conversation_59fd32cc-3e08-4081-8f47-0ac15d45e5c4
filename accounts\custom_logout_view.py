"""
Custom logout view with enhanced session cleanup.
"""
from django.contrib.auth.views import LogoutView
from django.contrib.auth import logout
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect
import logging

logger = logging.getLogger(__name__)


@method_decorator(never_cache, name='dispatch')
@method_decorator(csrf_protect, name='dispatch')
class CustomLogoutView(LogoutView):
    """
    Custom logout view that ensures proper session cleanup.
    """
    next_page = reverse_lazy('home')

    def dispatch(self, request, *args, **kwargs):
        """
        Handle logout with enhanced session cleanup.
        """
        user_to_cleanup = None

        if request.user.is_authenticated:
            try:
                # Store user for cleanup after logout
                user_to_cleanup = request.user

                # Log the logout event
                logger.info(f"User {request.user.username} is logging out")

                # Mark request for cleanup after response
                request._logout_cleanup_needed = True
                request._logout_user = user_to_cleanup

            except Exception as e:
                logger.error(f"Error preparing logout cleanup for {request.user}: {e}")

        # Call the parent logout method
        response = super().dispatch(request, *args, **kwargs)

        # Clean up after logout is complete to avoid CSRF issues
        if user_to_cleanup:
            try:
                from .unified_session_manager import UnifiedSessionManager
                UnifiedSessionManager.cleanup_user_cache(user_to_cleanup)
            except Exception as e:
                logger.error(f"Error during post-logout cleanup: {e}")

        # Add success message if this is a POST request
        if request.method == 'POST':
            messages.success(request, "You have been successfully logged out.")

        return response

    def get(self, request, *args, **kwargs):
        """
        Handle GET requests to logout (for compatibility).
        """
        if request.user.is_authenticated:
            # Log the user out
            logout(request)
            messages.success(request, "You have been successfully logged out.")

        return redirect(self.get_success_url())

    def post(self, request, *args, **kwargs):
        """
        Handle POST requests to logout.
        """
        return super().post(request, *args, **kwargs)

    def get_success_url(self):
        """
        Get the URL to redirect to after logout.
        """
        return str(self.next_page)
