#!/usr/bin/env python
"""Simple check for email configuration changes."""

import os

# Check settings.py file
print("📧 Checking Email Configuration Changes")
print("=" * 50)

try:
    with open('company_assistant/settings.py', 'r') as f:
        settings_content = f.read()
    
    print("✅ Settings.py Email Configuration:")
    
    # Check for old domain
    if 'smartlib.site' in settings_content:
        print("  ❌ Found 'smartlib.site' in settings.py")
        # Find the lines
        lines = settings_content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'smartlib.site' in line:
                print(f"    Line {i}: {line.strip()}")
    else:
        print("  ✅ No 'smartlib.site' found in settings.py")
    
    # Check for new domain
    if '24seven.site' in settings_content:
        print("  ✅ Found '24seven.site' in settings.py")
        # Find the email-related lines
        lines = settings_content.split('\n')
        for i, line in enumerate(lines, 1):
            if '24seven.site' in line and ('EMAIL' in line or 'DEFAULT_FROM' in line):
                print(f"    Line {i}: {line.strip()}")
    else:
        print("  ⚠️  No '24seven.site' found in settings.py")
    
    print("\n✅ .env File Email Configuration:")
    
    # Check .env file
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            env_content = f.read()
        
        if 'smartlib.site' in env_content:
            print("  ❌ Found 'smartlib.site' in .env file")
        else:
            print("  ✅ No 'smartlib.site' found in .env file")
        
        if '24seven.site' in env_content:
            print("  ✅ Found '24seven.site' in .env file")
            # Show email lines
            lines = env_content.split('\n')
            for line in lines:
                if '24seven.site' in line:
                    print(f"    {line.strip()}")
        else:
            print("  ⚠️  No '24seven.site' found in .env file")
    else:
        print("  ℹ️  .env file not found")
    
    print("\n✅ .env.example File Email Configuration:")
    
    # Check .env.example file
    if os.path.exists('.env.example'):
        with open('.env.example', 'r') as f:
            example_content = f.read()
        
        if 'smartlib.site' in example_content:
            print("  ❌ Found 'smartlib.site' in .env.example file")
        else:
            print("  ✅ No 'smartlib.site' found in .env.example file")
        
        if '24seven.site' in example_content:
            print("  ✅ Found '24seven.site' in .env.example file")
        else:
            print("  ⚠️  No '24seven.site' found in .env.example file")
    
    print("\n" + "=" * 50)
    print("🎉 Email Domain Change Summary:")
    print("  📧 Changed: <EMAIL> → <EMAIL>")
    print("  🌐 Changed: mail.smartlib.site → mail.24seven.site")
    print("  ✉️  Changed: DEFAULT_FROM_EMAIL updated")
    print("\n🔧 Next Steps:")
    print("  1. Configure mail.24seven.site on your email server")
    print("  2. Create <EMAIL> email account")
    print("  3. Update EMAIL_HOST_PASSWORD in .env file")
    print("  4. Test email functionality")

except Exception as e:
    print(f"❌ Error: {e}")
