/* Modern Featured Carousel Styles - Redesigned from scratch */
.featured-carousel-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    padding: 40px 0 80px 0;
    margin-bottom: 40px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.featured-carousel-items {
    display: flex;
    animation: modernScroll 30s linear infinite;
    width: max-content;
    min-height: 350px;
    animation-play-state: running;
    padding: 20px 0;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    align-items: center;
}

.featured-carousel-item {
    flex: 0 0 auto;
    margin: 0 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    perspective: 1000px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.featured-carousel-item:hover {
    transform: scale(1.02);
    z-index: 10;
}

.featured-item-wrapper {
    position: relative;
    width: 320px;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* Light background for NUP theme */
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 249, 250, 0.9) 50%,
        rgba(255, 255, 255, 0.85) 100%) !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-radius: 24px;
    /* Light border effect for NUP theme */
    border: 3px solid rgba(222, 226, 230, 0.6) !important;
    border-top: 3px solid rgba(222, 226, 230, 0.8) !important;
    border-left: 3px solid rgba(222, 226, 230, 0.8) !important;
    border-right: 3px solid rgba(222, 226, 230, 0.4) !important;
    border-bottom: 3px solid rgba(222, 226, 230, 0.4) !important;
    /* Enhanced shadow for depth - exact copy from company list */
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        inset 0 3px 0 rgba(255, 255, 255, 0.3),
        inset 3px 0 0 rgba(255, 255, 255, 0.2),
        inset 0 -2px 0 rgba(0, 0, 0, 0.2),
        0 0 20px rgba(255, 255, 255, 0.05) !important;
    padding: 30px 25px;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: visible;
    /* Glass effect backdrop filter - exact copy from company list */
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
    transform: translateZ(0);
}

.featured-item-wrapper:hover {
    transform: translateY(-12px) rotateX(5deg) scale(1.02);
    /* Enhanced hover effect for NUP theme */
    background: linear-gradient(145deg,
        rgba(248, 249, 250, 0.95) 0%,
        rgba(241, 245, 249, 0.9) 50%,
        rgba(248, 249, 250, 0.85) 100%) !important;
    background-color: rgba(248, 249, 250, 0.9) !important;

    /* Enhanced hover border for NUP theme */
    border-top: 3px solid rgba(227, 27, 35, 0.6) !important;
    border-left: 3px solid rgba(227, 27, 35, 0.6) !important;
    border-right: 3px solid rgba(107, 70, 193, 0.3) !important;
    border-bottom: 3px solid rgba(107, 70, 193, 0.3) !important;

    /* Enhanced hover shadow - exact copy from company list */
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.6),
        inset 0 4px 0 rgba(255, 255, 255, 0.4),
        inset 4px 0 0 rgba(255, 255, 255, 0.3),
        inset 0 -3px 0 rgba(0, 0, 0, 0.3),
        0 0 25px rgba(255, 255, 255, 0.1) !important;
}

.featured-carousel-item a {
    display: flex;
    color: #ffffff !important;
    text-decoration: none;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-decoration: none;
    width: 100%; /* Take full width of parent */
    color: #333;
    transition: all 0.3s ease;
}

.featured-carousel-item a:hover {
    color: #0d6efd;
    text-decoration: none;
    transform: translateY(-5px);
}

.featured-carousel-item .logo-container {
    height: 200px;
    width: 200px;
    min-height: 200px;
    min-width: 200px;
    max-height: 200px;
    max-width: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.08),
        0 4px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(255, 255, 255, 0.5);
    position: relative;
    aspect-ratio: 1/1;
    margin: 0 auto 25px;
    z-index: 2;
    flex-shrink: 0;
    backdrop-filter: blur(10px);
}

.featured-carousel-item .logo-container:hover {
    transform: scale(1.08) rotateY(5deg);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.12),
        0 8px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
}

.featured-carousel-item .logo-container img {
    max-height: 100%;
    max-width: 100%;
    height: 100%;
    width: 100%;
    object-fit: contain;
    display: block;
    padding: 10px; /* Increased padding to prevent image from touching container edges */
    transition: all 0.3s ease;
}

.featured-carousel-item .item-info {
    text-align: center;
    width: 100%;
}

.featured-carousel-item .item-info h5 {
    color: #cf2e2e;
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.75rem;
    letter-spacing: -0.02em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.featured-carousel-item .item-info p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #64748b;
    margin-bottom: 0.75rem;
    font-weight: 400;
}

.featured-carousel-item .item-info p i {
    color: #0d6efd;
    margin-right: 0.25rem;
}

.featured-carousel-item .logo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: visible;
    padding: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
    border-radius: 16px;
}

.featured-carousel-item .logo-placeholder i {
    font-size: 120px;
    line-height: 1;
    display: block;
    text-align: center;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #3b82f6 0%, #9333ea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 4px 8px rgba(59, 130, 246, 0.3));
}

.featured-carousel-item .star-rating {
    display: inline-flex;
    align-items: center;
    font-size: 1.2em;
}

.featured-carousel-item .star-rating .bi-star-fill {
    color: #ffc107;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2));
    margin: 0 1px;
}

.featured-carousel-item .star-rating .bi-star {
    color: #dee2e6;
    filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    margin: 0 1px;
}

.featured-carousel-item .star-rating .rating-count {
    font-size: 0.9em;
    margin-left: 0.5em;
    color: #495057;
    font-weight: 500;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

/* Modern Like button styling */
.featured-carousel-item .like-button {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    width: 44px;
    height: 44px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 10;
    position: absolute;
    top: 15px;
    right: 15px;
    border-radius: 50%;
    backdrop-filter: blur(10px);
}

.featured-carousel-item .like-button:hover {
    transform: scale(1.15) translateY(-2px);
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.15),
        0 4px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.featured-carousel-item .like-button.text-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
    color: #dc2626;
}

.featured-carousel-item .like-button.text-danger:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%);
    color: #b91c1c;
}

/* Modern Animation for continuous scrolling */
@keyframes modernScroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-50% - 25px)); /* Adjust for new margin */
    }
}

@-webkit-keyframes modernScroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-50% - 25px)); /* Adjust for new margin */
    }
}

/* Legacy animation for backward compatibility */
@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-50% - 30px)); /* Adjust for margin */
    }
}

@-webkit-keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-50% - 30px)); /* Adjust for margin */
    }
}

/* Fallback animation for browsers that don't support calc */
@keyframes scroll-fallback {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

@-webkit-keyframes scroll-fallback {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Debug styles - commented out for production
.featured-carousel-container {
    border: 3px solid red;
    background-color: rgba(240, 248, 255, 0.5);
    padding: 20px;
    margin: 20px 0;
}

.featured-carousel-items {
    border: 2px dashed blue;
    padding: 10px;
}

.featured-carousel-item {
    border: 2px solid green;
    background-color: rgba(255, 255, 240, 0.5);
    padding: 10px;
}
*/

/* Responsive adjustments */
/* Tablet adjustments (between 768px and 992px) */
@media (min-width: 769px) and (max-width: 991.98px) {
    .featured-carousel-container {
        padding: 20px 0 50px 0; /* Reduced padding */
    }

    .featured-carousel-item {
        margin: 0 20px;
    }

    .featured-item-wrapper {
        width: 220px;
    }

    .featured-carousel-item .logo-container {
        height: 160px;
        width: 160px;
        min-height: 160px;
        min-width: 160px;
        max-height: 160px;
        max-width: 160px;
        margin-bottom: 15px;
    }

    .featured-carousel-item .logo-placeholder i {
        font-size: 120px;
    }

    .featured-carousel-item .item-info h5 {
        font-size: 1.2rem;
    }

    /* Faster animation speed for tablets */
    .featured-carousel-items {
        animation-duration: 30s; /* Faster animation speed */
    }
}

/* Mobile adjustments (up to 768px) */
@media (max-width: 768px) {
    .featured-carousel-container {
        padding: 20px 0 40px 0;
        margin-bottom: 30px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
        border-radius: 16px;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.15);
    }

    .featured-carousel-items {
        display: flex;
        animation: modernScroll 30s linear infinite;
        width: max-content !important;
        justify-content: flex-start;
        min-height: auto;
        align-items: center;
        padding: 15px 0;
    }

    /* Make featured items match tier card style */
    .featured-carousel-item {
        margin: 0 15px; /* Reduced spacing between items */
        width: 95vw; /* Take up most of the viewport width */
        max-width: 95vw; /* Maximum width for larger phones */
        min-width: 320px; /* Minimum width for smaller phones */
        transform: none !important; /* No scaling - use actual size */
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .featured-item-wrapper {
        width: 100%;
        padding: 25px;
        margin: 0 auto;
        border-radius: 20px;
        box-shadow:
            0 16px 32px rgba(0, 0, 0, 0.12),
            0 6px 12px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.6);
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
        border: 1px solid rgba(255, 255, 255, 0.3);
        min-width: 300px;
        backdrop-filter: blur(15px);
    }

    /* Modern mobile logo container - increased size */
    .featured-carousel-item .logo-container {
        height: 260px;
        width: 260px;
        min-height: 260px;
        min-width: 260px;
        max-height: 260px;
        max-width: 260px;
        margin: 0 auto 20px auto;
        border-radius: 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
        box-shadow:
            0 8px 16px rgba(0, 0, 0, 0.08),
            0 2px 4px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.4);
        backdrop-filter: blur(10px);
    }

    /* Modern mobile logo placeholder icon - increased size */
    .featured-carousel-item .logo-placeholder i {
        font-size: 160px;
        color: #cf2e2e;
        filter: drop-shadow(0 4px 8px rgba(207, 46, 46, 0.3));
    }

    /* Modern mobile text styling */
    .featured-carousel-item .item-info h5 {
        font-size: 1.3rem;
        margin-bottom: 0.75rem;
        text-align: center;
        font-weight: 600;
        color: #cf2e2e;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        letter-spacing: -0.01em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .featured-carousel-item .item-info p {
        font-size: 0.95rem;
        margin-bottom: 0.75rem;
        text-align: center;
        color: #64748b;
        width: 100%;
        white-space: normal;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.5;
        font-weight: 400;
    }

    /* Community badge styling */
    .featured-carousel-item .badge {
        background-color: #333333;
        color: #adb5bd;
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    /* Rating text styling */
    .featured-carousel-item .rating-text {
        font-size: 0.8rem;
        color: #adb5bd;
        margin-top: 5px;
        text-align: right;
        width: 100%;
    }

    /* Modern mobile like button styling */
    .featured-carousel-item .like-button {
        width: 44px;
        height: 44px;
        bottom: 15px;
        right: 15px;
        top: auto;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #64748b;
        box-shadow:
            0 8px 16px rgba(0, 0, 0, 0.1),
            0 2px 4px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    /* Modern mobile buttons */
    .featured-carousel-item .btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        border: none;
        color: white;
        width: 100%;
        margin-bottom: 12px;
        border-radius: 12px;
        padding: 12px 20px;
        font-size: 0.95rem;
        font-weight: 500;
        box-shadow:
            0 4px 8px rgba(59, 130, 246, 0.3),
            0 2px 4px rgba(59, 130, 246, 0.2);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .featured-carousel-item .btn-secondary {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #64748b;
        width: 100%;
        border-radius: 12px;
        padding: 12px 20px;
        font-size: 0.95rem;
        font-weight: 500;
        backdrop-filter: blur(10px);
        box-shadow:
            0 4px 8px rgba(0, 0, 0, 0.08),
            0 2px 4px rgba(0, 0, 0, 0.04);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    /* Modern featured badge styling */
    .featured-carousel-item .featured-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        font-size: 0.8rem;
        font-weight: 600;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        box-shadow:
            0 4px 8px rgba(16, 185, 129, 0.3),
            0 2px 4px rgba(16, 185, 129, 0.2);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Modern mobile featured section */
    .featured-section {
        min-height: 450px;
        padding: 2rem 1rem;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(248, 250, 252, 0.04) 100%);
        border-radius: 1.5rem;
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.15);
    }
}

/* Small mobile devices (up to 576px) */
@media (max-width: 576px) {
    .featured-carousel-container {
        padding: 15px 0 30px 0;
        max-width: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.03) 100%);
        border-radius: 14px;
        backdrop-filter: blur(12px);
        border: 1px solid rgba(255, 255, 255, 0.12);
    }

    .featured-carousel-item {
        margin: 0 10px;
        width: 95vw;
        max-width: 95vw;
        min-width: 280px;
        transform: none !important;
    }

    .featured-item-wrapper {
        width: 100%;
        padding: 20px;
        box-shadow:
            0 12px 24px rgba(0, 0, 0, 0.1),
            0 4px 8px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.5);
        border-radius: 18px;
        min-width: 280px;
        min-height: 340px;
        background: linear-gradient(145deg, rgba(255, 255, 255, 0.85) 0%, rgba(255, 255, 255, 0.75) 100%);
        border: 1px solid rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(12px);
    }

    /* Modern small mobile logo container - increased size */
    .featured-carousel-item .logo-container {
        height: 240px;
        width: 240px;
        min-height: 240px;
        min-width: 240px;
        max-height: 240px;
        max-width: 240px;
        margin: 0 auto 15px auto;
        border-radius: 16px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.75) 0%, rgba(248, 250, 252, 0.55) 100%);
        box-shadow:
            0 6px 12px rgba(0, 0, 0, 0.06),
            0 2px 4px rgba(0, 0, 0, 0.03),
            inset 0 1px 0 rgba(255, 255, 255, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.35);
        backdrop-filter: blur(8px);
    }

    .featured-carousel-item .logo-placeholder i {
        font-size: 140px;
        color: #cf2e2e;
        filter: drop-shadow(0 3px 6px rgba(207, 46, 46, 0.25));
    }

    /* Modern small mobile text styling */
    .featured-carousel-item .item-info h5 {
        font-size: 1.2rem;
        margin-bottom: 0.6rem;
        text-align: center;
        font-weight: 600;
        color: #cf2e2e;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        letter-spacing: -0.01em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .featured-carousel-item .item-info p {
        font-size: 0.9rem;
        margin-bottom: 0.6rem;
        text-align: center;
        color: #64748b;
        width: 100%;
        white-space: normal;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.4;
        font-weight: 400;
    }

    /* Community badge styling */
    .featured-carousel-item .badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
        margin-bottom: 8px;
    }

    /* Rating text styling */
    .featured-carousel-item .rating-text {
        font-size: 0.75rem;
        margin-top: 4px;
    }

    /* Smaller like button */
    .featured-carousel-item .like-button {
        width: 36px;
        height: 36px;
        bottom: 12px;
        right: 12px;
    }

    /* Chat and Rate buttons */
    .featured-carousel-item .btn-primary,
    .featured-carousel-item .btn-secondary {
        padding: 6px 12px;
        font-size: 0.85rem;
        margin-bottom: 8px;
    }

    /* Featured badge styling */
    .featured-carousel-item .featured-badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.4rem;
        top: 8px;
        right: 8px;
    }

    /* Keep original animation speed */
    .featured-carousel-items {
        animation-duration: 60s; /* Original animation speed */
        min-height: auto; /* Allow height to adapt to content */
    }

    /* Modern small mobile featured section */
    .featured-section {
        min-height: 420px;
        padding: 1.5rem 0.75rem;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.06) 0%, rgba(248, 250, 252, 0.03) 100%);
        border-radius: 1.25rem;
        backdrop-filter: blur(12px);
        border: 1px solid rgba(255, 255, 255, 0.12);
    }
}

/* Text styling for dark background */
.featured-item-wrapper h5,
.featured-item-wrapper .item-name {
    color: #ffffff !important;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.featured-item-wrapper p,
.featured-item-wrapper .item-description {
    color: #cccccc !important;
    font-size: 0.9rem;
    line-height: 1.4;
}

.featured-item-wrapper .rating-display-container .text-muted {
    color: #999999 !important;
    font-style: italic;
}

.featured-item-wrapper .assistant-count,
.featured-item-wrapper .company-count {
    color: #bbbbbb !important;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    margin-bottom: 0;
}

.featured-item-wrapper .assistant-count i,
.featured-item-wrapper .company-count i {
    color: #4dabf7 !important;
}

/* Dark mode styles for featured carousel */
/* These styles are now redundant since we're using dark mode by default for mobile */
[data-theme="dark"] .featured-item-wrapper {
    background-color: #222222 !important;
    border-color: #333333 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .featured-carousel-item .logo-container {
    background-color: #252525 !important;
    border-color: #333333 !important;
}

[data-theme="dark"] .featured-carousel-item .item-info h5 {
    color: #4da3ff !important; /* Lighter blue for dark mode */
}

[data-theme="dark"] .featured-carousel-item .item-info p {
    color: #adb5bd !important;
}

[data-theme="dark"] .featured-carousel-item .logo-placeholder {
    background-color: rgba(13, 110, 253, 0.1) !important;
}

[data-theme="dark"] .featured-carousel-item .logo-placeholder i {
    color: #4da3ff !important; /* Lighter blue for dark mode */
}

[data-theme="dark"] .featured-carousel-item .like-button {
    background-color: #333333 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .featured-carousel-item .like-button.text-danger {
    background-color: rgba(220, 53, 69, 0.2) !important;
}

[data-theme="dark"] .featured-carousel-item .btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
}

[data-theme="dark"] .featured-carousel-item .btn-secondary {
    background-color: #333333 !important;
    border-color: #444444 !important;
}

[data-theme="dark"] .featured-carousel-item .badge {
    background-color: #333333 !important;
    color: #adb5bd !important;
}

/* Light mode styles for featured carousel on mobile */
@media (max-width: 768px) {
    [data-theme="light"] .featured-item-wrapper {
        background-color: #ffffff !important;
        border-color: #dee2e6 !important;
    }

    [data-theme="light"] .featured-carousel-item .logo-container {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
    }

    [data-theme="light"] .featured-carousel-item .item-info h5 {
        color: #0d6efd !important;
    }

    [data-theme="light"] .featured-carousel-item .item-info p {
        color: #495057 !important;
    }

    [data-theme="light"] .featured-carousel-item .badge {
        background-color: #e9ecef !important;
        color: #495057 !important;
    }

    [data-theme="light"] .featured-carousel-item .like-button {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
        color: #212529 !important;
    }

    [data-theme="light"] .featured-carousel-item .btn-secondary {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
    }
}
