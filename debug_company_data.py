#!/usr/bin/env python
"""
Debug script to check company data in the database.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company, CompanyInformation

def debug_company_data():
    """Debug company data to see what's in the database."""
    
    print("Debugging Company Data...")
    print("=" * 50)
    
    companies = Company.objects.all()
    print(f"Total companies: {companies.count()}")
    
    for company in companies:
        print(f"\nCompany: {company.name} (ID: {company.id})")
        print(f"  Owner: {company.owner.username}")
        print(f"  Entity Type: {company.entity_type}")
        print(f"  Created: {company.created_at}")
        
        try:
            company_info = company.info
            print(f"  CompanyInformation exists: Yes")
            print(f"    Mission: '{company_info.mission}'")
            print(f"    Description: '{company_info.description}'")
            print(f"    Website: '{company_info.website}'")
            print(f"    Contact Email: '{company_info.contact_email}'")
            print(f"    Contact Phone: '{company_info.contact_phone}'")
            print(f"    Industry: '{company_info.industry}'")
            print(f"    Size: '{company_info.size}'")
            print(f"    City: '{company_info.city}'")
            print(f"    Founded: {company_info.founded}")
            print(f"    Address Line 1: '{company_info.address_line1}'")
            print(f"    Postal Code: '{company_info.postal_code}'")
            print(f"    Country: '{company_info.country}'")
            print(f"    LinkedIn: '{company_info.linkedin}'")
            print(f"    List in Directory: {company_info.list_in_directory}")
        except CompanyInformation.DoesNotExist:
            print(f"  CompanyInformation exists: No")

if __name__ == '__main__':
    debug_company_data()
