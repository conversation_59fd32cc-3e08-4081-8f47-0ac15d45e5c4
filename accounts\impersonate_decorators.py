"""
Decorators to preserve impersonation sessions.
"""
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def preserve_impersonation(view_func):
    """
    Decorator to preserve impersonation session data across view calls.
    This prevents logout when accessing views during impersonation.
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Store impersonation state before view execution
        impersonate_id = request.session.get('_impersonate')
        is_impersonating = getattr(request, 'is_impersonate', False)
        real_user = getattr(request, 'real_user', None)
        
        if impersonate_id or is_impersonating:
            logger.debug(f"Preserving impersonation session for view: {view_func.__name__}")
            logger.debug(f"Impersonate ID: {impersonate_id}, is_impersonating: {is_impersonating}")
            
            # Store in request for later restoration
            request._preserved_impersonate_id = impersonate_id
            request._preserved_is_impersonating = is_impersonating
            request._preserved_real_user = real_user
        
        try:
            # Execute the view
            response = view_func(request, *args, **kwargs)
            
            # Restore impersonation state after view execution
            if hasattr(request, '_preserved_impersonate_id') and request._preserved_impersonate_id:
                # Ensure session data is preserved
                if '_impersonate' not in request.session or request.session['_impersonate'] != request._preserved_impersonate_id:
                    request.session['_impersonate'] = request._preserved_impersonate_id
                    logger.debug(f"Restored impersonation session ID: {request._preserved_impersonate_id}")
                
                # Ensure request attributes are preserved
                if not getattr(request, 'is_impersonate', False):
                    request.is_impersonate = request._preserved_is_impersonating
                    logger.debug(f"Restored is_impersonate flag: {request._preserved_is_impersonating}")
                
                if hasattr(request, '_preserved_real_user') and request._preserved_real_user:
                    if not hasattr(request, 'real_user') or request.real_user != request._preserved_real_user:
                        request.real_user = request._preserved_real_user
                        logger.debug(f"Restored real_user: {request._preserved_real_user.username}")
                
                # Force save session
                request.session.save()
            
            return response
            
        except Exception as e:
            # Even if view fails, try to preserve impersonation
            if hasattr(request, '_preserved_impersonate_id') and request._preserved_impersonate_id:
                request.session['_impersonate'] = request._preserved_impersonate_id
                request.session.save()
                logger.debug(f"Preserved impersonation session after view error: {e}")
            raise
    
    return wrapper

def debug_impersonation(view_func):
    """
    Decorator to add debug logging for impersonation state.
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Log impersonation state
        impersonate_id = request.session.get('_impersonate')
        is_impersonating = getattr(request, 'is_impersonate', False)
        real_user = getattr(request, 'real_user', None)
        
        logger.debug(f"=== IMPERSONATION DEBUG for {view_func.__name__} ===")
        logger.debug(f"Session impersonate ID: {impersonate_id}")
        logger.debug(f"Request is_impersonate: {is_impersonating}")
        logger.debug(f"Request user: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        logger.debug(f"Real user: {real_user.username if real_user else 'None'}")
        logger.debug(f"Request path: {request.path}")
        logger.debug("=== END IMPERSONATION DEBUG ===")
        
        return view_func(request, *args, **kwargs)
    
    return wrapper
