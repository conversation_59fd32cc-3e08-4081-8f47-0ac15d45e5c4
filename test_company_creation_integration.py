#!/usr/bin/env python
"""
Test script to verify that the company creation wizard saves all fields
that are available in the company settings form.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.forms import CompanyCreationForm, CompanySettingsForm
from accounts.models import Company, CompanyInformation

User = get_user_model()

def test_company_creation_integration():
    """Test that company creation form saves all fields available in settings form."""

    print("Testing Company Creation and Settings Form Integration...")
    print("=" * 60)

    # Create a test user
    test_user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )

    # Test data for company creation
    creation_data = {
        'name': 'Test Integration Company',
        'entity_type': 'company',
        'mission': 'Test mission for integration',
        'description': 'Test description for directory',
        'website': 'https://testcompany.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '+**********',
        'timezone': 'UTC',
        'language': 'en',
        'industry': 'Technology',
        'size': '10-50',
        'categories': 'Software, AI',
        'address_line1': '123 Test Street',
        'address_line2': 'Suite 100',
        'city': 'Test City',
        'postal_code': '12345',
        'country': 'Test Country',
        'founded': 2020,
        'linkedin': 'https://linkedin.com/company/testcompany',
        'twitter': 'https://twitter.com/testcompany',
        'facebook': 'https://facebook.com/testcompany',
        'custom_domain': 'testcompany.com',
        'list_in_directory': True
    }

    # Test company creation form
    print("1. Testing Company Creation Form...")
    creation_form = CompanyCreationForm(data=creation_data, user=test_user)

    if creation_form.is_valid():
        print("   ✓ Company creation form is valid")

        # Save the company
        company = creation_form.save()
        print(f"   ✓ Company created: {company.name}")

        # Get the company information
        try:
            company_info = CompanyInformation.objects.get(company=company)
            print("   ✓ CompanyInformation object created")

            # Check that all fields were saved
            fields_to_check = [
                'mission', 'description', 'website', 'contact_email', 'contact_phone',
                'timezone', 'language', 'industry', 'size', 'address_line1',
                'address_line2', 'city', 'postal_code', 'country', 'founded',
                'linkedin', 'twitter', 'facebook', 'custom_domain', 'list_in_directory'
            ]

            print("\n2. Checking saved fields:")
            all_fields_saved = True
            for field in fields_to_check:
                expected_value = creation_data.get(field)
                actual_value = getattr(company_info, field)

                if expected_value and actual_value != expected_value:
                    print(f"   ✗ {field}: Expected '{expected_value}', got '{actual_value}'")
                    all_fields_saved = False
                else:
                    print(f"   ✓ {field}: {actual_value}")

            if all_fields_saved:
                print("\n   ✓ All fields saved correctly!")
            else:
                print("\n   ✗ Some fields were not saved correctly!")

        except CompanyInformation.DoesNotExist:
            print("   ✗ CompanyInformation object not created")
            return False

    else:
        print("   ✗ Company creation form is invalid:")
        for field, errors in creation_form.errors.items():
            print(f"     {field}: {errors}")
        return False

    # Test company settings form with the created company
    print("\n3. Testing Company Settings Form with created data...")

    try:
        company_info = CompanyInformation.objects.get(company=company)
        settings_form = CompanySettingsForm(instance=company_info)

        print("   ✓ Company settings form initialized successfully")

        # Check that all fields are available in the settings form
        creation_form_fields = set(creation_form.fields.keys())
        settings_form_fields = set(settings_form.fields.keys())

        # Remove fields that are only in creation form (like entity_type)
        creation_only_fields = {'entity_type', 'categories'}  # categories is handled differently
        creation_form_fields -= creation_only_fields

        missing_in_settings = creation_form_fields - settings_form_fields
        if missing_in_settings:
            print(f"   ✗ Fields missing in settings form: {missing_in_settings}")
        else:
            print("   ✓ All creation form fields are available in settings form")

        # Check that settings form can retrieve the saved data
        print("\n4. Checking field values in settings form:")
        for field_name in creation_form_fields:
            if field_name in settings_form.fields:
                field_value = getattr(company_info, field_name, None)
                print(f"   ✓ {field_name}: {field_value}")

        print("\n" + "=" * 60)
        print("✓ INTEGRATION TEST PASSED!")
        print("Company creation wizard and settings form are properly integrated.")

        # Clean up
        company.delete()
        if created:
            test_user.delete()

        return True

    except Exception as e:
        print(f"   ✗ Error testing settings form: {e}")
        return False

if __name__ == '__main__':
    success = test_company_creation_integration()
    sys.exit(0 if success else 1)
