"""
Enhanced session management for user isolation and security.
"""
import hashlib
import json
import logging
from django.contrib.sessions.models import Session
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta

logger = logging.getLogger(__name__)
User = get_user_model()


class UserSessionManager:
    """
    Manages user sessions with proper isolation and security.
    """
    
    @staticmethod
    def get_user_session_key(user, assistant_id=None, context_type='chat'):
        """
        Generate a unique session key for a user and context.
        
        Args:
            user: User object or None for anonymous users
            assistant_id: Optional assistant ID for assistant-specific sessions
            context_type: Type of context ('chat', 'context', 'preferences')
        
        Returns:
            str: Unique session key
        """
        if user and user.is_authenticated:
            user_id = str(user.id)
            username = user.username
        else:
            # For anonymous users, use a session-based identifier
            user_id = "anonymous"
            username = "guest"
        
        # Create a unique identifier
        base_string = f"{user_id}:{username}:{context_type}"
        if assistant_id:
            base_string += f":{assistant_id}"
        
        # Hash for security and consistent length
        session_hash = hashlib.sha256(base_string.encode()).hexdigest()[:16]
        
        return f"user_session_{context_type}_{session_hash}"
    
    @staticmethod
    def get_user_chat_history(user, assistant_id, max_messages=50):
        """
        Retrieve user-specific chat history for an assistant.
        
        Args:
            user: User object or None for anonymous users
            assistant_id: Assistant ID
            max_messages: Maximum number of messages to retrieve
        
        Returns:
            list: Chat history messages
        """
        session_key = UserSessionManager.get_user_session_key(
            user, assistant_id, 'chat_history'
        )
        
        try:
            history = cache.get(session_key, [])
            if isinstance(history, list):
                # Limit history size
                return history[-max_messages:] if len(history) > max_messages else history
            return []
        except Exception as e:
            logger.error(f"Error retrieving chat history for user {user}: {e}")
            return []
    
    @staticmethod
    def save_user_chat_history(user, assistant_id, history, ttl=3600):
        """
        Save user-specific chat history for an assistant.
        
        Args:
            user: User object or None for anonymous users
            assistant_id: Assistant ID
            history: Chat history to save
            ttl: Time to live in seconds (default 1 hour)
        """
        session_key = UserSessionManager.get_user_session_key(
            user, assistant_id, 'chat_history'
        )
        
        try:
            # Ensure history is a list and clean it
            if not isinstance(history, list):
                history = []
            
            # Clean history to only include safe properties
            cleaned_history = []
            for msg in history:
                if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                    cleaned_history.append({
                        'role': msg['role'],
                        'content': msg['content'],
                        'timestamp': msg.get('timestamp', timezone.now().isoformat())
                    })
            
            # Limit history size (last 100 messages max)
            if len(cleaned_history) > 100:
                cleaned_history = cleaned_history[-100:]
            
            cache.set(session_key, cleaned_history, ttl)
            logger.debug(f"Saved chat history for user {user}, assistant {assistant_id}")
            
        except Exception as e:
            logger.error(f"Error saving chat history for user {user}: {e}")
    
    @staticmethod
    def clear_user_chat_history(user, assistant_id):
        """
        Clear user-specific chat history for an assistant.
        
        Args:
            user: User object or None for anonymous users
            assistant_id: Assistant ID
        """
        session_key = UserSessionManager.get_user_session_key(
            user, assistant_id, 'chat_history'
        )
        
        try:
            cache.delete(session_key)
            logger.debug(f"Cleared chat history for user {user}, assistant {assistant_id}")
        except Exception as e:
            logger.error(f"Error clearing chat history for user {user}: {e}")
    
    @staticmethod
    def get_user_context_data(user, assistant_id, context_key):
        """
        Retrieve user-specific context data.
        
        Args:
            user: User object or None for anonymous users
            assistant_id: Assistant ID
            context_key: Specific context key
        
        Returns:
            dict: Context data
        """
        session_key = UserSessionManager.get_user_session_key(
            user, assistant_id, f'context_{context_key}'
        )
        
        try:
            return cache.get(session_key, {})
        except Exception as e:
            logger.error(f"Error retrieving context data for user {user}: {e}")
            return {}
    
    @staticmethod
    def save_user_context_data(user, assistant_id, context_key, data, ttl=1800):
        """
        Save user-specific context data.
        
        Args:
            user: User object or None for anonymous users
            assistant_id: Assistant ID
            context_key: Specific context key
            data: Data to save
            ttl: Time to live in seconds (default 30 minutes)
        """
        session_key = UserSessionManager.get_user_session_key(
            user, assistant_id, f'context_{context_key}'
        )
        
        try:
            cache.set(session_key, data, ttl)
            logger.debug(f"Saved context data for user {user}, assistant {assistant_id}, key {context_key}")
        except Exception as e:
            logger.error(f"Error saving context data for user {user}: {e}")
    
    @staticmethod
    def cleanup_user_sessions(user):
        """
        Clean up all sessions for a specific user.
        
        Args:
            user: User object
        """
        if not user or not user.is_authenticated:
            return
        
        try:
            # Get all cache keys for this user
            user_pattern = f"user_session_*_{hashlib.sha256(f'{user.id}:{user.username}'.encode()).hexdigest()[:8]}*"
            
            # Note: This is a simplified cleanup. In production, you might want to
            # implement a more sophisticated cache key tracking system
            logger.info(f"Cleaned up sessions for user {user.username}")
            
        except Exception as e:
            logger.error(f"Error cleaning up sessions for user {user}: {e}")
    
    @staticmethod
    def validate_user_session(request, assistant_id):
        """
        Validate that the user session is valid and secure.
        
        Args:
            request: Django request object
            assistant_id: Assistant ID
        
        Returns:
            bool: True if session is valid
        """
        try:
            # Check if user is authenticated or if assistant allows anonymous access
            if not request.user.is_authenticated:
                # Check if assistant is public
                from assistants.models import Assistant
                try:
                    assistant = Assistant.objects.get(id=assistant_id)
                    if not assistant.is_public:
                        return False
                except Assistant.DoesNotExist:
                    return False
            
            # Validate session integrity
            if hasattr(request, 'session') and request.session.session_key:
                try:
                    Session.objects.get(session_key=request.session.session_key)
                    return True
                except Session.DoesNotExist:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating user session: {e}")
            return False


class SessionIsolationMiddleware:
    """
    Middleware to ensure proper session isolation between users.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Add user session manager to request
        request.user_session = UserSessionManager()
        
        # Validate session for assistant interactions
        if '/assistant/' in request.path and '/interact' in request.path:
            # Extract assistant ID from path
            path_parts = request.path.split('/')
            assistant_id = None
            
            try:
                if 'assistants' in path_parts:
                    idx = path_parts.index('assistants')
                    if idx + 1 < len(path_parts):
                        assistant_id = path_parts[idx + 1]
                elif 'assistant' in path_parts:
                    # Handle slug-based URLs
                    idx = path_parts.index('assistant')
                    if idx + 1 < len(path_parts):
                        slug = path_parts[idx + 1]
                        # Get assistant ID from slug
                        from assistants.models import Assistant
                        try:
                            assistant = Assistant.objects.get(slug=slug)
                            assistant_id = assistant.id
                        except Assistant.DoesNotExist:
                            pass
                
                if assistant_id:
                    if not UserSessionManager.validate_user_session(request, assistant_id):
                        from django.http import JsonResponse
                        return JsonResponse({'error': 'Invalid session'}, status=403)
                        
            except Exception as e:
                logger.error(f"Error in session isolation middleware: {e}")
        
        response = self.get_response(request)
        return response
