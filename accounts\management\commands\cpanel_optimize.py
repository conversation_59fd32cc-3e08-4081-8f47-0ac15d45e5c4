"""
Management command to optimize Django application for cPanel hosting.
Reduces resource usage and prevents process buildup.
"""

import os
import gc
import logging
from django.core.management.base import BaseCommand
from django.core.cache import cache
from django.db import connection, transaction
from django.contrib.sessions.models import Session
from django.utils import timezone
from datetime import timedelta

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Optimize Django application for cPanel hosting'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--aggressive',
            action='store_true',
            help='Run aggressive optimizations (may affect performance temporarily)',
        )
        parser.add_argument(
            '--cleanup-only',
            action='store_true',
            help='Only run cleanup operations, skip optimizations',
        )
        parser.add_argument(
            '--skip-cache',
            action='store_true',
            help='Skip cache operations',
        )
    
    def handle(self, *args, **options):
        """Run cPanel optimizations."""
        self.stdout.write(
            self.style.SUCCESS('Starting cPanel optimization...')
        )
        
        try:
            # 1. Clean up expired sessions
            self.cleanup_sessions()
            
            # 2. Clear and optimize cache
            if not options['skip_cache']:
                self.optimize_cache()
            
            # 3. Clean up database
            self.cleanup_database()
            
            # 4. Optimize database tables
            if not options['cleanup_only']:
                self.optimize_database_tables()
            
            # 5. Create cache tables if needed
            if not options['cleanup_only']:
                self.create_cache_tables()
            
            # 6. Aggressive optimizations
            if options['aggressive']:
                self.aggressive_optimizations()
            
            # 7. Force garbage collection
            self.force_garbage_collection()
            
            # 8. Set up cPanel-specific directories
            self.setup_directories()
            
            self.stdout.write(
                self.style.SUCCESS('cPanel optimization completed successfully!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Optimization failed: {e}')
            )
            logger.error(f"cPanel optimization error: {e}")
    
    def cleanup_sessions(self):
        """Clean up expired sessions."""
        self.stdout.write('Cleaning up expired sessions...')
        
        try:
            # Delete expired sessions
            expired_count = Session.objects.filter(
                expire_date__lt=timezone.now()
            ).count()
            
            Session.objects.filter(
                expire_date__lt=timezone.now()
            ).delete()
            
            self.stdout.write(f'  Removed {expired_count} expired sessions')
            
            # Also clean up very old sessions (older than 7 days)
            old_date = timezone.now() - timedelta(days=7)
            old_count = Session.objects.filter(
                expire_date__lt=old_date
            ).count()
            
            Session.objects.filter(
                expire_date__lt=old_date
            ).delete()
            
            self.stdout.write(f'  Removed {old_count} old sessions')
            
        except Exception as e:
            self.stdout.write(f'  Error cleaning sessions: {e}')
    
    def optimize_cache(self):
        """Optimize cache for cPanel."""
        self.stdout.write('Optimizing cache...')
        
        try:
            # Clear all cache
            cache.clear()
            self.stdout.write('  Cache cleared')
            
            # Test cache functionality
            cache.set('cpanel_test', 'working', 60)
            if cache.get('cpanel_test') == 'working':
                self.stdout.write('  Cache is working correctly')
                cache.delete('cpanel_test')
            else:
                self.stdout.write('  Warning: Cache may not be working')
                
        except Exception as e:
            self.stdout.write(f'  Error optimizing cache: {e}')
    
    def cleanup_database(self):
        """Clean up database for better performance."""
        self.stdout.write('Cleaning up database...')
        
        try:
            with connection.cursor() as cursor:
                # Clean up cache tables if they exist
                cache_tables = [
                    'django_cache_table',
                    'django_session_tokens_cache',
                    'django_llm_responses',
                    'django_query_cache'
                ]
                
                for table in cache_tables:
                    try:
                        # Check if table exists
                        cursor.execute(
                            "SELECT name FROM sqlite_master WHERE type='table' AND name=%s",
                            [table]
                        )
                        if cursor.fetchone():
                            # Clean expired entries
                            cursor.execute(f"DELETE FROM {table} WHERE expires < datetime('now')")
                            deleted = cursor.rowcount
                            self.stdout.write(f'  Cleaned {deleted} entries from {table}')
                    except Exception as e:
                        # Table might not exist or different database
                        pass
                
        except Exception as e:
            self.stdout.write(f'  Error cleaning database: {e}')
    
    def optimize_database_tables(self):
        """Optimize database tables."""
        self.stdout.write('Optimizing database tables...')
        
        try:
            with connection.cursor() as cursor:
                # For SQLite
                if 'sqlite' in connection.vendor:
                    cursor.execute("VACUUM")
                    cursor.execute("ANALYZE")
                    self.stdout.write('  SQLite optimization completed')
                
                # For PostgreSQL
                elif 'postgresql' in connection.vendor:
                    cursor.execute("VACUUM ANALYZE")
                    self.stdout.write('  PostgreSQL optimization completed')
                
                # For MySQL
                elif 'mysql' in connection.vendor:
                    cursor.execute("OPTIMIZE TABLE django_session")
                    self.stdout.write('  MySQL optimization completed')
                
        except Exception as e:
            self.stdout.write(f'  Error optimizing tables: {e}')
    
    def create_cache_tables(self):
        """Create cache tables if they don't exist."""
        self.stdout.write('Setting up cache tables...')
        
        try:
            from django.core.management import call_command
            
            # Create cache table
            call_command('createcachetable', verbosity=0)
            self.stdout.write('  Cache tables created/verified')
            
        except Exception as e:
            self.stdout.write(f'  Error creating cache tables: {e}')
    
    def aggressive_optimizations(self):
        """Run aggressive optimizations for cPanel."""
        self.stdout.write('Running aggressive optimizations...')
        
        try:
            # Force multiple garbage collections
            for i in range(3):
                collected = gc.collect()
                self.stdout.write(f'  GC pass {i+1}: collected {collected} objects')
            
            # Clear Python import cache
            import sys
            if hasattr(sys, 'path_importer_cache'):
                sys.path_importer_cache.clear()
                self.stdout.write('  Cleared import cache')
            
            # Optimize garbage collection thresholds
            gc.set_threshold(100, 5, 5)  # Very aggressive
            self.stdout.write('  Set aggressive GC thresholds')
            
        except Exception as e:
            self.stdout.write(f'  Error in aggressive optimizations: {e}')
    
    def force_garbage_collection(self):
        """Force garbage collection."""
        self.stdout.write('Running garbage collection...')
        
        try:
            collected = gc.collect()
            self.stdout.write(f'  Collected {collected} objects')
            
            # Get memory info if available
            try:
                import psutil
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                self.stdout.write(f'  Current memory usage: {memory_mb:.1f}MB')
            except ImportError:
                pass
                
        except Exception as e:
            self.stdout.write(f'  Error in garbage collection: {e}')
    
    def setup_directories(self):
        """Set up required directories for cPanel."""
        self.stdout.write('Setting up directories...')
        
        try:
            from django.conf import settings
            
            required_dirs = [
                'logs',
                'cache', 
                'session',
                'media',
                'staticfiles',
                'tmp'
            ]
            
            for directory in required_dirs:
                dir_path = os.path.join(settings.BASE_DIR, directory)
                os.makedirs(dir_path, exist_ok=True)
                
                # Set permissions if possible
                try:
                    os.chmod(dir_path, 0o755)
                except:
                    pass
                
                self.stdout.write(f'  Created/verified directory: {directory}')
                
        except Exception as e:
            self.stdout.write(f'  Error setting up directories: {e}')
