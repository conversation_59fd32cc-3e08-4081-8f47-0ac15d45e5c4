"""
Memory-optimized data structures and algorithms for better performance.
Reduces memory usage and improves efficiency for cPanel hosting constraints.
"""

import json
import gzip
import pickle
from typing import Iterator, List, Dict, Any, Optional, Generator
from collections import deque
from django.core.cache import cache
from django.db.models import QuerySet
import logging

logger = logging.getLogger(__name__)


class LazyQuerysetIterator:
    """
    Memory-efficient iterator for large querysets.
    Processes data in chunks to avoid loading everything into memory.
    """
    
    def __init__(self, queryset: QuerySet, chunk_size: int = 100):
        self.queryset = queryset
        self.chunk_size = chunk_size
        self._total_count = None
    
    def __iter__(self) -> Iterator:
        """Iterate through queryset in chunks."""
        offset = 0
        while True:
            chunk = list(self.queryset[offset:offset + self.chunk_size])
            if not chunk:
                break
            
            for item in chunk:
                yield item
            
            offset += self.chunk_size
    
    def count(self) -> int:
        """Get total count with caching."""
        if self._total_count is None:
            self._total_count = self.queryset.count()
        return self._total_count
    
    def chunks(self) -> Generator[List, None, None]:
        """Yield chunks of data instead of individual items."""
        offset = 0
        while True:
            chunk = list(self.queryset[offset:offset + self.chunk_size])
            if not chunk:
                break
            
            yield chunk
            offset += self.chunk_size


class CompressedDataCache:
    """
    Memory-efficient caching with compression for large data structures.
    """
    
    @staticmethod
    def compress_data(data: Any) -> bytes:
        """Compress data using gzip for storage efficiency."""
        try:
            # Serialize to JSON first, then compress
            json_str = json.dumps(data, separators=(',', ':'), default=str)
            return gzip.compress(json_str.encode('utf-8'))
        except (TypeError, ValueError):
            # Fallback to pickle for non-JSON serializable data
            return gzip.compress(pickle.dumps(data))
    
    @staticmethod
    def decompress_data(compressed_data: bytes) -> Any:
        """Decompress and deserialize data."""
        try:
            decompressed = gzip.decompress(compressed_data)
            # Try JSON first
            try:
                return json.loads(decompressed.decode('utf-8'))
            except (json.JSONDecodeError, UnicodeDecodeError):
                # Fallback to pickle
                return pickle.loads(decompressed)
        except Exception as e:
            logger.warning(f"Error decompressing data: {e}")
            return None
    
    @classmethod
    def set_compressed(cls, key: str, data: Any, timeout: int = 3600) -> bool:
        """Store compressed data in cache."""
        try:
            compressed = cls.compress_data(data)
            cache.set(f"compressed:{key}", compressed, timeout)
            return True
        except Exception as e:
            logger.warning(f"Error setting compressed cache: {e}")
            return False
    
    @classmethod
    def get_compressed(cls, key: str) -> Optional[Any]:
        """Retrieve and decompress data from cache."""
        try:
            compressed = cache.get(f"compressed:{key}")
            if compressed is not None:
                return cls.decompress_data(compressed)
        except Exception as e:
            logger.warning(f"Error getting compressed cache: {e}")
        return None


class MemoryEfficientPaginator:
    """
    Memory-efficient paginator that doesn't load all data at once.
    """
    
    def __init__(self, queryset: QuerySet, per_page: int = 20):
        self.queryset = queryset
        self.per_page = per_page
        self._count = None
    
    @property
    def count(self) -> int:
        """Get total count with caching."""
        if self._count is None:
            cache_key = f"paginator_count:{hash(str(self.queryset.query))}"
            self._count = cache.get(cache_key)
            if self._count is None:
                self._count = self.queryset.count()
                cache.set(cache_key, self._count, 300)  # Cache for 5 minutes
        return self._count
    
    @property
    def num_pages(self) -> int:
        """Calculate number of pages."""
        return (self.count + self.per_page - 1) // self.per_page
    
    def page(self, number: int) -> Dict[str, Any]:
        """Get a specific page of data."""
        if number < 1:
            number = 1
        elif number > self.num_pages:
            number = self.num_pages
        
        start = (number - 1) * self.per_page
        end = start + self.per_page
        
        # Use values() to reduce memory usage by not loading full model instances
        page_data = list(self.queryset[start:end])
        
        return {
            'object_list': page_data,
            'number': number,
            'has_previous': number > 1,
            'has_next': number < self.num_pages,
            'previous_page_number': number - 1 if number > 1 else None,
            'next_page_number': number + 1 if number < self.num_pages else None,
            'num_pages': self.num_pages,
            'count': self.count
        }


class CircularBuffer:
    """
    Memory-efficient circular buffer for storing recent items.
    Automatically removes old items when capacity is reached.
    """
    
    def __init__(self, maxsize: int = 1000):
        self.maxsize = maxsize
        self.buffer = deque(maxlen=maxsize)
    
    def append(self, item: Any) -> None:
        """Add item to buffer."""
        self.buffer.append(item)
    
    def extend(self, items: List[Any]) -> None:
        """Add multiple items to buffer."""
        self.buffer.extend(items)
    
    def get_recent(self, count: int = None) -> List[Any]:
        """Get recent items from buffer."""
        if count is None:
            return list(self.buffer)
        return list(self.buffer)[-count:]
    
    def clear(self) -> None:
        """Clear the buffer."""
        self.buffer.clear()
    
    def __len__(self) -> int:
        return len(self.buffer)


class MemoryEfficientDataProcessor:
    """
    Process large datasets efficiently without loading everything into memory.
    """
    
    @staticmethod
    def process_queryset_in_batches(queryset: QuerySet, batch_size: int = 100, 
                                  processor_func: callable = None) -> Generator[Any, None, None]:
        """
        Process queryset in batches to avoid memory issues.
        
        Args:
            queryset: Django queryset to process
            batch_size: Number of items to process at once
            processor_func: Function to apply to each batch
        """
        iterator = LazyQuerysetIterator(queryset, batch_size)
        
        for chunk in iterator.chunks():
            if processor_func:
                processed_chunk = processor_func(chunk)
                yield processed_chunk
            else:
                yield chunk
    
    @staticmethod
    def aggregate_data_efficiently(queryset: QuerySet, group_by_field: str, 
                                 aggregate_fields: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Efficiently aggregate data without loading all records into memory.
        """
        from django.db.models import Count, Avg, Sum, Max, Min
        
        # Use database aggregation instead of Python processing
        aggregation_map = {
            'count': Count('id'),
            'avg': Avg,
            'sum': Sum,
            'max': Max,
            'min': Min
        }
        
        # Build aggregation dict
        aggregations = {}
        for field in aggregate_fields:
            if '__' in field:
                agg_type, field_name = field.split('__', 1)
                if agg_type in aggregation_map:
                    aggregations[field] = aggregation_map[agg_type](field_name)
            else:
                aggregations[f'{field}__count'] = Count(field)
        
        # Perform database-level aggregation
        result = queryset.values(group_by_field).annotate(**aggregations)
        
        # Convert to dictionary format
        aggregated_data = {}
        for item in result:
            group_value = item.pop(group_by_field)
            aggregated_data[group_value] = item
        
        return aggregated_data


class OptimizedDataSerializer:
    """
    Memory-efficient data serialization for API responses.
    """
    
    @staticmethod
    def serialize_queryset_lazy(queryset: QuerySet, serializer_class, 
                              chunk_size: int = 100) -> Generator[Dict, None, None]:
        """
        Serialize queryset lazily to avoid memory issues with large datasets.
        """
        iterator = LazyQuerysetIterator(queryset, chunk_size)
        
        for chunk in iterator.chunks():
            serialized_chunk = serializer_class(chunk, many=True).data
            for item in serialized_chunk:
                yield item
    
    @staticmethod
    def serialize_to_json_stream(data: Iterator, output_file: str) -> None:
        """
        Serialize data to JSON file as a stream to avoid memory issues.
        """
        import json
        
        with open(output_file, 'w') as f:
            f.write('[')
            first = True
            for item in data:
                if not first:
                    f.write(',')
                json.dump(item, f, separators=(',', ':'), default=str)
                first = False
            f.write(']')


# Convenience functions for easy integration
def create_lazy_iterator(queryset: QuerySet, chunk_size: int = 100) -> LazyQuerysetIterator:
    """Create a lazy iterator for memory-efficient queryset processing."""
    return LazyQuerysetIterator(queryset, chunk_size)


def create_memory_efficient_paginator(queryset: QuerySet, per_page: int = 20) -> MemoryEfficientPaginator:
    """Create a memory-efficient paginator."""
    return MemoryEfficientPaginator(queryset, per_page)


def cache_compressed_data(key: str, data: Any, timeout: int = 3600) -> bool:
    """Cache data with compression."""
    return CompressedDataCache.set_compressed(key, data, timeout)


def get_compressed_cached_data(key: str) -> Optional[Any]:
    """Retrieve compressed cached data."""
    return CompressedDataCache.get_compressed(key)


def process_large_dataset(queryset: QuerySet, processor_func: callable = None, 
                         batch_size: int = 100) -> Generator[Any, None, None]:
    """Process large dataset efficiently."""
    return MemoryEfficientDataProcessor.process_queryset_in_batches(
        queryset, batch_size, processor_func
    )
