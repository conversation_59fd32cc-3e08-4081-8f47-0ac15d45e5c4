from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User, Group # Import Group
from django import forms
from django.conf import settings # Import settings
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User, Group # Import Group (already imported, ensure it's here)
from django.core.exceptions import ValidationError, ObjectDoesNotExist
from django.utils.translation import gettext_lazy as _
from django.utils import timezone # Import timezone for expires_at validation
import pytz # Import pytz for timezones
from django.contrib.auth import get_user_model # Import get_user_model
from .models import Company, CompanyInformation, CompanyInvitation as TeamInvitation, Membership, RegistrationLink, UserProfile # Import UserProfile
from assistants.models import AssistantFolder # Add import
from directory.models import CompanyCategory, CompanyListing # Import CompanyCategory and CompanyListing
from .utils import sanitize_company_name, validate_domain, generate_invite_token # Import generate_invite_token
from .email_utils import send_team_invitation_email, send_bulk_invitation_emails

User = get_user_model() # Get the User model correctly

class CustomUserCreationForm(UserCreationForm):
    email = forms.EmailField(
        required=True,
        help_text='Required. Enter a valid email address.'
    )
    first_name = forms.CharField(
        max_length=30,
        required=False,
        help_text='Optional.'
    )
    last_name = forms.CharField(
        max_length=30,
        required=False,
        help_text='Optional.'
    )

    class Meta(UserCreationForm.Meta):
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2') # Added password2 back

    def clean_email(self):
        email = self.cleaned_data['email']
        if User.objects.filter(email=email).exists():
            raise ValidationError('A user with that email already exists.')
        return email

class CompanyCreationForm(forms.ModelForm):
    mission = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False
        # Removed help_text to avoid parsing issue
    )
    website = forms.URLField(
        required=False,
        help_text='Website (optional).'
    )
    contact_email = forms.EmailField(
        required=False,
        help_text='Primary contact email (optional).'
    )
    contact_phone = forms.CharField(
        max_length=30, # Adjust max_length as needed
        required=True,
        help_text='Primary contact phone number.'
    )
    list_in_directory = forms.BooleanField(
        required=False,
        initial=False,
        label='List in public directory',
        help_text='Make this company visible in the public directory (if active).'
    )
    # Add timezone and language fields
    timezone = forms.ChoiceField(
        choices=[(tz, tz) for tz in pytz.common_timezones],
        initial='UTC',
        required=True,
        help_text='Select your timezone.'
    )
    language = forms.ChoiceField(
        choices=settings.LANGUAGES,
        initial='en',
        required=True,
        help_text='Select your preferred language.'
    )
    # Company description for directory
    description = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 4}),
        required=False,
        help_text='Public description for directory listing (optional).'
    )

    # Address fields
    address_line1 = forms.CharField(
        max_length=255,
        required=False,
        help_text='Street address (optional).'
    )
    address_line2 = forms.CharField(
        max_length=255,
        required=False,
        help_text='Apartment, suite, etc. (optional).'
    )
    city = forms.CharField(
        max_length=100,
        required=False,
        help_text='City (optional).'
    )
    postal_code = forms.CharField(
        max_length=20,
        required=False,
        help_text='Postal/ZIP code (optional).'
    )
    country = forms.CharField(
        max_length=100,
        required=False,
        help_text='Country (optional).'
    )

    # Company details
    logo = forms.ImageField(
        required=False,
        help_text='Company logo (optional).'
    )
    founded = forms.IntegerField(
        required=False,
        help_text='Year founded (optional).'
    )

    # Social media links
    linkedin = forms.URLField(
        required=False,
        help_text='LinkedIn profile URL (optional).'
    )
    twitter = forms.URLField(
        required=False,
        help_text='Twitter profile URL (optional).'
    )
    facebook = forms.URLField(
        required=False,
        help_text='Facebook page URL (optional).'
    )

    # Custom domain
    custom_domain = forms.CharField(
        max_length=255,
        required=False,
        help_text='Custom domain for your company page (optional).'
    )

    # Industry and categories fields
    industry = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control industry-dropdown'}),
        help_text="Select your company's industry."
    )
    size = forms.CharField(
        max_length=50,
        required=False,
        help_text='Company size (e.g., 1-10, 11-50, etc.).'
    )
    categories = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control category-dropdown'}),
        help_text="Select categories for your company."
    )
    # Removed is_active field - only settable by superusers

    class Meta:
        model = Company
        fields = ['name', 'entity_type'] # is_active and list_in_directory are handled in save

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        self.fields['name'].widget.attrs.update({
            'placeholder': 'Enter name',
            'autofocus': True
        })
        # Add required attribute to contact_phone field
        self.fields['contact_phone'].widget.attrs.update({
            'required': 'required',
            'class': 'form-control'
        })
        # Add classes to other fields for consistent styling
        self.fields['mission'].widget.attrs.update({'class': 'form-control'})
        self.fields['description'].widget.attrs.update({'class': 'form-control'})
        self.fields['website'].widget.attrs.update({'class': 'form-control'})
        self.fields['contact_email'].widget.attrs.update({'class': 'form-control'})
        self.fields['entity_type'].widget.attrs.update({'class': 'form-select'})
        self.fields['name'].widget.attrs.update({'class': 'form-control'})
        self.fields['list_in_directory'].widget.attrs.update({'class': 'form-check-input'})
        self.fields['timezone'].widget.attrs.update({'class': 'form-select'})
        self.fields['language'].widget.attrs.update({'class': 'form-select'})
        self.fields['industry'].widget.attrs.update({'class': 'form-control'})
        self.fields['size'].widget.attrs.update({'class': 'form-control'})

        # Address fields
        self.fields['address_line1'].widget.attrs.update({'class': 'form-control'})
        self.fields['address_line2'].widget.attrs.update({'class': 'form-control'})
        self.fields['city'].widget.attrs.update({'class': 'form-control'})
        self.fields['postal_code'].widget.attrs.update({'class': 'form-control'})
        self.fields['country'].widget.attrs.update({'class': 'form-control'})

        # Company details
        self.fields['logo'].widget.attrs.update({'class': 'form-control', 'accept': 'image/*'})
        self.fields['founded'].widget.attrs.update({'class': 'form-control', 'min': '1800'})

        # Social media
        self.fields['linkedin'].widget.attrs.update({'class': 'form-control'})
        self.fields['twitter'].widget.attrs.update({'class': 'form-control'})
        self.fields['facebook'].widget.attrs.update({'class': 'form-control'})

        # Custom domain
        self.fields['custom_domain'].widget.attrs.update({'class': 'form-control'})

        # Categories field
        self.fields['categories'].widget.attrs.update({'class': 'form-control'})

    def clean_name(self):
        name = self.cleaned_data['name']
        sanitized_name = sanitize_company_name(name)
        if Company.objects.filter(name__iexact=sanitized_name).exists():
            raise ValidationError('A company with this name already exists.')
        return sanitized_name

    def clean_founded(self):
        founded = self.cleaned_data.get('founded')
        if founded:
            import datetime
            current_year = datetime.date.today().year
            if founded < 1800 or founded > current_year:
                raise ValidationError(f'Founded year must be between 1800 and {current_year}.')
        return founded

    def clean_custom_domain(self):
        domain = self.cleaned_data.get('custom_domain')
        if domain:
            # Basic domain validation
            import re
            domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
            if not re.match(domain_pattern, domain):
                raise ValidationError('Please enter a valid domain name.')
        return domain

    def clean(self):
        cleaned_data = super().clean()
        # Validate that contact_phone is provided
        contact_phone = cleaned_data.get('contact_phone')
        if not contact_phone:
            self.add_error('contact_phone', 'Contact phone number is required.')
        return cleaned_data

    def save(self, commit=True):
        company = super().save(commit=False)
        if self.user:
            company.owner = self.user

        # Set is_active to False by default - only settable by superusers
        company.is_active = False

        if commit:
            company.save()

            # The signal will create an empty CompanyInformation, so we need to get it and update it
            # Always get the CompanyInformation (created by signal) and update it with form data
            try:
                company_info = company.info
            except CompanyInformation.DoesNotExist:
                # Fallback: create it manually if signal didn't work
                company_info = CompanyInformation.objects.create(company=company)

            # Always update the CompanyInformation with form data (whether created by signal or manually)
            company_info.mission = self.cleaned_data.get('mission', '')
            company_info.description = self.cleaned_data.get('description', '')
            company_info.website = self.cleaned_data.get('website', '')
            company_info.contact_email = self.cleaned_data.get('contact_email', '')
            company_info.contact_phone = self.cleaned_data.get('contact_phone', '')
            company_info.list_in_directory = self.cleaned_data.get('list_in_directory', False)
            company_info.timezone = self.cleaned_data.get('timezone', 'UTC')
            company_info.language = self.cleaned_data.get('language', 'en')
            company_info.industry = self.cleaned_data.get('industry', '')
            company_info.size = self.cleaned_data.get('size', '')

            # Address fields
            company_info.address_line1 = self.cleaned_data.get('address_line1', '')
            company_info.address_line2 = self.cleaned_data.get('address_line2', '')
            company_info.city = self.cleaned_data.get('city', '')
            company_info.postal_code = self.cleaned_data.get('postal_code', '')
            company_info.country = self.cleaned_data.get('country', '')

            # Company details - handle None values properly
            logo_value = self.cleaned_data.get('logo')
            if logo_value is not None:
                company_info.logo = logo_value

            founded_value = self.cleaned_data.get('founded')
            if founded_value is not None:
                company_info.founded = founded_value

            # Social media
            company_info.linkedin = self.cleaned_data.get('linkedin', '')
            company_info.twitter = self.cleaned_data.get('twitter', '')
            company_info.facebook = self.cleaned_data.get('facebook', '')

            # Custom domain
            company_info.custom_domain = self.cleaned_data.get('custom_domain', '')

            company_info.save()

            # Handle categories - create a CompanyListing if it doesn't exist
            from directory.models import CompanyListing, CompanyCategory
            categories_text = self.cleaned_data.get('categories')
            if categories_text:
                listing, created = CompanyListing.objects.get_or_create(
                    company=company,
                    defaults={'is_listed': self.cleaned_data.get('list_in_directory', False)}
                )

                # Clear existing categories if any
                if not created:
                    listing.categories.clear()

                # Create or get category objects and add them to the listing
                category_names = [name.strip() for name in categories_text.split(',') if name.strip()]
                for name in category_names:
                    category, _ = CompanyCategory.objects.get_or_create(name=name)
                    listing.categories.add(category)

        return company

class CompanySettingsForm(forms.ModelForm):
    # Override contact_phone to make it required
    contact_phone = forms.CharField(
        max_length=50,
        required=True,
        help_text='Primary contact phone number.'
    )

    class Meta:
        model = CompanyInformation
        # Explicitly list all fields instead of using exclude
        fields = [
            'mission', 'description', 'website', 'contact_email', 'contact_phone',
            'timezone', 'language', 'address_line1', 'address_line2', 'city',
            'postal_code', 'country', 'logo', 'industry', 'size', 'founded',
            'linkedin', 'twitter', 'facebook', 'custom_domain', 'list_in_directory'
        ]
        widgets = {
            'logo': forms.ClearableFileInput(attrs={'accept': 'image/*', 'class': 'form-control'}),
            'mission': forms.Textarea(attrs={'rows': 3, 'placeholder': "Brief description of your company's mission", 'class': 'form-control'}),
            'website': forms.URLInput(attrs={'class': 'form-control'}), # Added widget + form-control
            'industry': forms.TextInput(attrs={'class': 'form-control industry-dropdown'}), # Updated for dropdown
            'categories': forms.TextInput(attrs={'class': 'form-control category-dropdown'}), # Added for dropdown
            'size': forms.TextInput(attrs={'class': 'form-control'}), # Added widget + form-control
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}), # Added form-control
            'contact_email': forms.EmailInput(attrs={'class': 'form-control'}),
            'contact_phone': forms.TextInput(attrs={'class': 'form-control'}),
            # Removed incorrect 'contact_address' widget
            'address_line1': forms.TextInput(attrs={'placeholder': 'Street Address', 'class': 'form-control'}),
            'address_line2': forms.TextInput(attrs={'placeholder': 'Apartment, suite, etc. (optional)', 'class': 'form-control'}),
            'city': forms.TextInput(attrs={'placeholder': 'City', 'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'placeholder': 'Postal Code', 'class': 'form-control'}),
            'country': forms.TextInput(attrs={'placeholder': 'Country', 'class': 'form-control'}),
            'founded': forms.NumberInput(attrs={'min': '1800', 'class': 'form-control'}),
            'linkedin': forms.URLInput(attrs={'class': 'form-control'}),
            'twitter': forms.URLInput(attrs={'class': 'form-control'}),
            'facebook': forms.URLInput(attrs={'class': 'form-control'}), # Added widget + form-control
            # Timezone and Language need choices defined in the form now
            'timezone': forms.Select(attrs={'class': 'form-select'}),
            'language': forms.Select(attrs={'class': 'form-select'}),
            'custom_domain': forms.TextInput(attrs={'class': 'form-control'}),
            'list_in_directory': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    # Add categories field - relates to CompanyListing, not CompanyInformation directly
    categories = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control category-dropdown'}),
        label="Directory Categories",
        help_text="Select categories for the public directory listing."
    )

    # Define choices for timezone and language directly in the form
    timezone = forms.ChoiceField(
        choices=[(tz, tz) for tz in pytz.common_timezones],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    language = forms.ChoiceField(
        choices=settings.LANGUAGES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        current_year = kwargs.pop('current_year', None)
        super().__init__(*args, **kwargs)

        if self.instance and self.instance.pk:
            # For ModelForm, we need to ensure the form fields get the instance values
            # The issue is that empty strings are not being displayed properly
            # Let's explicitly set the field values
            for field_name in self.fields:
                if hasattr(self.instance, field_name):
                    field_value = getattr(self.instance, field_name)
                    # For ModelForm, we need to set both initial and the field's value
                    if field_value is not None:
                        self.initial[field_name] = field_value
                        # Also set the field's value directly for empty strings
                        if field_name in self.fields:
                            self.fields[field_name].initial = field_value

            # Set default values ONLY for fields that are None (not set at all)
            # Don't override empty strings as they are valid user input
            if getattr(self.instance, 'mission', None) is None:
                self.initial['mission'] = "We want to help people and businesses do more with less effort by using smart AI assistants that learn and adapt to your needs."
                self.fields['mission'].initial = self.initial['mission']

            if getattr(self.instance, 'website', None) is None:
                self.initial['website'] = "https://24seven.com"
                self.fields['website'].initial = self.initial['website']

            if getattr(self.instance, 'industry', None) is None:
                self.initial['industry'] = "Technology"
                self.fields['industry'].initial = self.initial['industry']

            if getattr(self.instance, 'size', None) is None:
                self.initial['size'] = "10-50"
                self.fields['size'].initial = self.initial['size']

            if getattr(self.instance, 'founded', None) is None:
                self.initial['founded'] = 2020
                self.fields['founded'].initial = self.initial['founded']

        # Safely get the 'founded' field and check if it exists before modifying widget
        founded_field = self.fields.get('founded')
        if current_year and founded_field:
            founded_field.widget.attrs['max'] = current_year

        # Initialize categories field with the listing's current categories as a comma-separated string
        # If the instance has a related 'listing', try to set initial value.
        if self.instance and hasattr(self.instance, 'company') and hasattr(self.instance.company, 'listing'):
            categories = self.instance.company.listing.categories.all()
            categories_text = ', '.join([category.name for category in categories])
            self.fields['categories'].initial = categories_text


    def clean_custom_domain(self):
        domain = self.cleaned_data.get('custom_domain') # Use .get() for safety
        if domain and not validate_domain(domain):
            raise ValidationError('Invalid domain format.')
        return domain

# New form for Company Tier and Featured status requests
class CompanyDirectorySettingsForm(forms.ModelForm):
    # Restore class-level definitions
    request_new_tier = forms.ChoiceField(
        choices=[('', '--- No Change ---')] + Company.TIER_REQUEST_CHOICES,
        required=False,
        label="Request Tier Upgrade",
        help_text="Select a paid tier (Gold, Silver, Bronze) to request an upgrade (requires superadmin approval)."
    )
    request_featured_status = forms.BooleanField(
        required=False,
        label="Request Featured Status",
        help_text="Check this box to request this company be featured (requires superadmin approval)."
    )
    # Removed is_active field - only settable by superusers

    class Meta:
        model = Company # This form will update the Company model directly
        fields = [
            # Removed 'is_active' - only settable by superusers
            'requested_tier_duration',
            'requested_featured_duration'
        ]
        widgets = {
            'requested_tier_duration': forms.Select(attrs={'class': 'form-select'}),
            'requested_featured_duration': forms.Select(attrs={'class': 'form-select'}),
        }
        labels = {
            'requested_tier_duration': 'Requested Tier Duration',
            'requested_featured_duration': 'Requested Featured Duration',
        }
        help_texts = {
            'requested_tier_duration': 'Select duration if requesting a paid tier upgrade.',
            'requested_featured_duration': 'Select duration if requesting featured status.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Apply widgets to the fields (which should now exist from class definition)
        self.fields['request_new_tier'].widget = forms.Select(attrs={'class': 'form-select'})
        self.fields['request_featured_status'].widget = forms.CheckboxInput(attrs={'class': 'form-check-input'})

        # Make duration fields not required by default
        self.fields['requested_tier_duration'].required = False
        self.fields['requested_featured_duration'].required = False
        # Set choices for duration fields
        self.fields['requested_tier_duration'].choices = [('', '--- Select Duration ---')] + Company.DURATION_CHOICES
        self.fields['requested_featured_duration'].choices = [('', '--- Select Duration ---')] + Company.DURATION_CHOICES

        # Set initial values for the request fields based on current status
        if self.instance and self.instance.pk:
            # Use self.fields[] to access the fields after they are added
            # Do NOT set initial for request_new_tier, let it default to 'No Change'
            self.fields['request_featured_status'].initial = self.instance.is_featured
            # Removed is_active initialization - only settable by superusers

class TeamInvitationForm(forms.ModelForm):
    emails = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 3,
            'placeholder': 'Enter email addresses (one per line)'
        }),
        help_text='Enter one email address per line.'
    )
    # Removed role ModelChoiceField - Role/permission assignment handled in the view
    # We might add a ChoiceField here later if admins should select intended permission level
    message = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False,
        help_text='Optional message to include in the invitation email.'
    )

    class Meta:
        model = TeamInvitation
        # Removed 'role' from fields
        fields = ['emails', 'message']

    def clean_emails(self):
        emails_raw = self.cleaned_data.get('emails', '')
        emails = emails_raw.split('\n')
        cleaned_emails = []
        for email in emails:
            email = email.strip()
            if email:
                try:
                    forms.EmailField().clean(email) # Validate each email
                    cleaned_emails.append(email)
                except ValidationError:
                     raise ValidationError(f'Invalid email address found: {email}')
        if not cleaned_emails:
            raise ValidationError("Please enter at least one valid email address.")
        return cleaned_emails

    def save(self, company, invited_by, commit=True):
        invitations = []
        cleaned_emails = self.cleaned_data.get('emails', [])
        message = self.cleaned_data.get('message', '')

        # Create metadata dictionary for the invitation
        metadata = {}
        if message:
            metadata['message'] = message

        for email in cleaned_emails:
            # Skip if user is already a member (using Membership model)
            try:
                user = User.objects.get(email=email)
                if Membership.objects.filter(company=company, user=user).exists():
                    continue
            except User.DoesNotExist:
                pass # User doesn't exist yet, can be invited

            # Skip if pending invitation exists for this company and email
            if TeamInvitation.objects.filter(
                company=company,
                email=email,
                status='pending'
            ).exists():
                continue

            # Generate unique token
            token = generate_invite_token() # Use the utility function

            invitation = TeamInvitation(
                company=company,
                email=email,
                invited_by=invited_by,
                token=token, # Assign generated token
                metadata=metadata # Add metadata with optional message
            )

            if commit:
                invitation.save()

                # Send invitation email
                try:
                    # Get site configuration for email templates
                    from site_settings.models import SiteConfiguration
                    site_config = SiteConfiguration.objects.first()

                    # Send the invitation email
                    send_team_invitation_email(invitation, site_config)
                except Exception as e:
                    # Log the error but continue with the process
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"Error sending invitation email to {email}: {e}")

            invitations.append(invitation)

        # Send bulk invitation emails if needed for additional notifications
        if invitations and commit:
            try:
                # This could be used for admin notifications or other bulk operations
                # send_bulk_invitation_emails(invitations)
                pass
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error sending bulk invitation emails: {e}")

        return invitations

# Removed old UserSettingsForm

# New form combining User and UserProfile fields
class UserProfileForm(forms.ModelForm):
    # Fields from User model
    first_name = forms.CharField(max_length=30, required=False)
    last_name = forms.CharField(max_length=30, required=False)
    email = forms.EmailField(required=True)

    # Fields from UserProfile model
    avatar = forms.ImageField(
        required=False,
        # Use FileInput for cleaner rendering, hide with CSS/JS in template
        widget=forms.FileInput(attrs={'accept': 'image/*'})
    )
    bio = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False
    )

    class Meta:
        model = User # Specify the primary model
        # Fields handled by this form (combining User and UserProfile)
        fields = ['first_name', 'last_name', 'email', 'avatar', 'bio']

    def __init__(self, *args, **kwargs):
        # Expecting 'instance' to be the User instance
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            # Populate User fields
            self.fields['first_name'].initial = self.instance.first_name
            self.fields['last_name'].initial = self.instance.last_name
            self.fields['email'].initial = self.instance.email
            # Populate UserProfile fields (handle potential DoesNotExist)
            try:
                profile = self.instance.profile # Access profile via related_name
                self.fields['avatar'].initial = profile.avatar
                self.fields['bio'].initial = profile.bio
            except ObjectDoesNotExist:
                # If profile doesn't exist, create it silently or handle as needed
                # For now, fields will just be empty
                pass

    def clean_email(self):
        # Validate email uniqueness, excluding the current user
        email = self.cleaned_data['email']
        if User.objects.exclude(pk=self.instance.pk).filter(email=email).exists():
            raise ValidationError('A user with that email already exists.')
        return email

    def save(self, commit=True):
        user = self.instance # User instance passed to the form

        # Update User fields
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.email = self.cleaned_data['email']

        # Get or create UserProfile
        profile, created = UserProfile.objects.get_or_create(user=user)

        # Update UserProfile fields
        profile.bio = self.cleaned_data['bio']
        # Handle avatar upload - only update if a new file was provided
        if 'avatar' in self.files:
            # Check if a file was actually uploaded (self.files['avatar'] is not None)
            uploaded_file = self.files.get('avatar')
            if uploaded_file:
                profile.avatar = uploaded_file
            else:
                # No file uploaded, but 'avatar' might be in self.files if the field exists.
                # If we want to allow clearing, we need a separate mechanism
                # since FileInput doesn't have a 'clear' checkbox.
                # For now, if no file is uploaded, we don't change the existing avatar.
                pass
        # Removed the check for `self.cleaned_data.get('avatar') is False` as FileInput has no clear checkbox

        if commit:
            user.save()
            profile.save()

        return user # Return the user instance


class RegistrationLinkForm(forms.ModelForm):
    """Form for creating and managing registration links."""
    intended_group = forms.ModelChoiceField(
        queryset=Group.objects.all(), # Queryset for roles (groups)
        required=True, # Make role selection mandatory
        label="Assign Role",
        help_text="Select the role the user will be assigned upon joining.",
        widget=forms.Select(attrs={'class': 'form-select'}) # Use standard select widget
    )
    expires_at = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'form-control'}), # Added form-control
        help_text='Optional: Link will expire after this date/time.'
    )
    max_uses = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'}), # Added form-control
        help_text='Optional: Limit the number of times this link can be used.'
    )
    accessible_folders = forms.ModelMultipleChoiceField(
        queryset=AssistantFolder.objects.none(), # Start with an empty queryset
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label="Grant Access to Existing Folders", # Changed label
        help_text="Select existing folders the user joining via this link should be able to view." # Changed help text
    )
    new_folder_name = forms.CharField(
        required=False,
        max_length=100,
        label="Or Create New Folder",
        help_text="If you want to grant access to a new folder, enter its name here. It will be created if it doesn't exist.",
        widget=forms.TextInput(attrs={'placeholder': 'Enter new folder name (optional)', 'class': 'form-control mb-2'}) # Added class
    )

    class Meta:
        model = RegistrationLink
        # Added 'intended_group', reordered fields for better flow
        fields = ['notes', 'intended_group', 'new_folder_name', 'accessible_folders', 'expires_at', 'max_uses', 'is_active']
        widgets = {
            'notes': forms.TextInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        # Exclude company, created_by, token, uses_count (set automatically)

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        if self.company:
            # Filter accessible_folders queryset
            self.fields['accessible_folders'].queryset = AssistantFolder.objects.filter(company=self.company).order_by('name')
            # TODO: Filter 'intended_group' queryset based on company-specific groups if needed.
            # For now, it shows all groups. Example filter (if using naming convention):
            # self.fields['intended_group'].queryset = Group.objects.filter(name__startswith=f"company_{self.company.id}_") | Group.objects.filter(name__in=['Admin', 'Member']) # Example standard roles

    def clean_expires_at(self):
        expires_at = self.cleaned_data.get('expires_at')
        if expires_at and expires_at < timezone.now():
            raise ValidationError("Expiration date cannot be in the past.")
        return expires_at

    def clean(self):
        cleaned_data = super().clean()
        new_folder_name = cleaned_data.get('new_folder_name')
        accessible_folders = cleaned_data.get('accessible_folders', AssistantFolder.objects.none()) # Default to empty queryset

        if new_folder_name:
            if not self.company:
                # This should ideally not happen if the form is instantiated correctly in the view
                raise ValidationError("Company information is missing. Cannot create folder.")

            # Try to get or create the folder
            folder_name_stripped = new_folder_name.strip()
            if folder_name_stripped: # Ensure non-empty name after stripping
                folder, created = AssistantFolder.objects.get_or_create(
                    company=self.company,
                    name=folder_name_stripped
                )
                # Add the new/found folder to the list of selected folders
                # Convert QuerySet to list, add new folder, then assign back
                folder_list = list(accessible_folders)
                if folder not in folder_list:
                    folder_list.append(folder)
                cleaned_data['accessible_folders'] = AssistantFolder.objects.filter(pk__in=[f.pk for f in folder_list])
                # Optionally add a non-field error to inform the user, especially if created
                # if created:
                #     self.add_error(None, f"Created and selected new folder: '{folder_name_stripped}'")

        return cleaned_data

    def save(self, company, created_by, commit=True):
        link = super().save(commit=False)
        link.company = company
        link.created_by = created_by
        # Token is generated automatically by model default
        if commit:
            link.save()
            # Need to save M2M fields after the main instance is saved
            self.save_m2m() # Ensure accessible_folders (potentially updated in clean) are saved
        return link
