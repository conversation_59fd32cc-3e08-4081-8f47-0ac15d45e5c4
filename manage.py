#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
# Load .env file as early as possible
from dotenv import load_dotenv
load_dotenv()

import os
import sys

# Ensure UTF-8 encoding for management commands
if hasattr(sys.stdout, 'encoding') and sys.stdout.encoding != 'utf-8':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
if hasattr(sys.stderr, 'encoding') and sys.stderr.encoding != 'utf-8':
    import codecs
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# Set environment variables for UTF-8
os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
os.environ.setdefault('LC_ALL', 'en_US.UTF-8')
os.environ.setdefault('LANG', 'en_US.UTF-8')


def main():
    """Run administrative tasks."""
    # load_dotenv() # Moved to top
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
