{% extends 'base/layout.html' %}
{% load static account_tags assistant_tags rating_tags %} {# Load rating_tags for render_stars #}

{% block title %}{{ company.name }} - Company Overview{% endblock %}

{% block head_extra %}
{# Load company detail mobile CSS #}
<link rel="stylesheet" href="{% static 'css/company-detail-mobile.css' %}">
{# Load company detail contact colors CSS #}
<link rel="stylesheet" href="{% static 'css/company-detail-contact-colors.css' %}">
{# Add styles if needed, e.g., for the like button positioning #}
<style>
.like-button { line-height: 1; } /* Align icon better */

/* Style for folder filter buttons (copied from my_favorites.html) */
.folder-filter-buttons .btn {
    margin-right: 0.25rem; /* Keep spacing for btn-link */
    margin-bottom: 0.25rem;
    font-weight: normal; /* Default state */
    color: var(--bs-link-color); /* Default link color */
}
.folder-filter-buttons .btn.active {
    font-weight: bold; /* Make active link bold */
    color: var(--bs-primary); /* Use primary color for active link */
    text-decoration: underline; /* Underline active link */
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    {% if not company.is_active %}
        {% include 'accounts/tags/simple_pending_approval.html' with entity_type='company' entity_name=company.name %}
    {% endif %}
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <!-- Company Info -->
                    <div class="text-center mb-4">
                        {# Use company.info.logo and apply consistent styling #}
                        {% if company.info.logo and company.info.logo.url %}
                            <div class="company-logo-container-sidebar mb-3" style="height: 160px; width: 160px; max-width: 160px; max-height: 160px; overflow: hidden; margin: 0 auto;">
                                <img src="{{ company.info.logo.url }}"
                                     alt="{{ company.name }} Logo"
                                     onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'bg-light rounded d-flex align-items-center justify-content-center\' style=\'height: 160px; width: 160px; max-width: 160px; max-height: 160px; margin: 0 auto;\'><i class=\'bi bi-building text-muted\' style=\'font-size: 3rem;\'></i></div>';"
                                     class="img-thumbnail rounded-circle company-sidebar-logo"
                                     style="width: 160px; height: 160px; max-width: 160px; max-height: 160px; object-fit: contain;">
                            </div>
                        {% else %}
                            {# Fallback icon #}
                            <div class="mb-3" style="height: 160px; width: 160px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                <i class="bi bi-building display-3 text-muted"></i>
                            </div>
                        {% endif %}
                        <div class="d-flex justify-content-center align-items-center mb-2"> {# Wrap title and button #}
                            <h1 class="h4 me-2 mb-0">{{ company.name }}</h1>
                            {# Favorite Button - Added #}
                            {% if user.is_authenticated %}
                                <button
                                    class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %}"
                                    data-item-id="{{ company.id }}"
                                    data-item-type="company"
                                    title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                                    style="background: none; border: none;">
                                    <i class="bi bi-heart{% if is_favorited %}-fill{% endif %} me-1"></i>
                                </button>
                            {% endif %}
                            {# End Favorite Button #}
                        </div>

                        {# Rating Display #}
                        <div class="text-center mb-2">
                            <div id="rating-display-{{ company.id }}">
                                {% if company_listing %}
                                    {% render_stars company_listing.avg_rating company_listing.total_ratings %}
                                {% else %}
                                    {% render_stars 0 0 %}
                                {% endif %}
                            </div>
                            {% if user.is_authenticated %}
                                <button type="button" class="btn btn-sm btn-link text-decoration-none p-0 mt-1"
                                        data-bs-toggle="modal" data-bs-target="#ratingModal"
                                        data-company-id="{{ company.id }}"
                                        data-company-name="{{ company.name }}">
                                    <i class="bi bi-star me-1"></i>Rate this company
                                </button>
                            {% endif %}
                        </div>
                        <p class="text-muted small mb-0">
                            {{ company.industry }}
                        </p>
                    </div>

                    <!-- Quick Stats -->
                    <div class="row g-2 text-center mb-4">
                        <div class="col-4">
                            <div class="p-2 border rounded bg-light">
                                <div class="h5 mb-0">{{ company.members.count }}</div>
                                <small class="text-muted">Members</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2 border rounded bg-light">
                                <div class="h5 mb-0">{{ active_conversations }}</div>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="p-2 border rounded bg-light">
                                <div class="h5 mb-0">{{ total_content }}</div>
                                <small class="text-muted">Content</small>
                            </div>
                        </div>
                    </div>

                    <!-- Company Details -->
                    <div class="list-group list-group-flush mb-4 contact-info">
                        {% if company.website %}
                            <a href="{{ company.website }}" target="_blank"
                               class="list-group-item list-group-item-action d-flex align-items-center">
                                <i class="bi bi-globe me-3"></i>
                                <span class="contact-text website-url">{{ company.website }}</span>
                                <i class="bi bi-box-arrow-up-right ms-auto"></i>
                            </a>
                        {% endif %}
                        {% if company.info.contact_email %}
                            <a href="mailto:{{ company.info.contact_email }}"
                               class="list-group-item list-group-item-action d-flex align-items-center">
                                <i class="bi bi-envelope me-3"></i>
                                <span class="contact-text email-text">{{ company.info.contact_email }}</span>
                            </a>
                        {% endif %}
                        {% if company.info.contact_phone %}
                            <div class="list-group-item d-flex align-items-center">
                                <i class="bi bi-telephone me-3"></i>
                                <span class="contact-text">{{ company.info.contact_phone|format_phone }}</span>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Actions -->
                    {% if user == company.owner %}
                        <div class="d-grid gap-2">
                            <a href="{% url 'accounts:company_settings' company.id %}" class="btn btn-light">
                                <i class="bi bi-gear me-2"></i>
                                Company Settings
                            </a>
                            <a href="{% url 'accounts:company_team' company.id %}" class="btn btn-light">
                                <i class="bi bi-people me-2"></i>
                                Manage Team
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Social Links -->
            {% if company.info.linkedin or company.info.twitter or company.info.facebook %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">Social Media</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            {% if company.info.linkedin %}
                                <a href="{{ company.info.linkedin }}" target="_blank"
                                   class="list-group-item list-group-item-action">
                                    <i class="bi bi-linkedin text-primary me-2"></i>
                                    LinkedIn
                                </a>
                            {% endif %}
                            {% if company.info.twitter %}
                                <a href="{{ company.info.twitter }}" target="_blank"
                                   class="list-group-item list-group-item-action">
                                    <i class="bi bi-twitter text-info me-2"></i>
                                    Twitter
                                </a>
                            {% endif %}
                            {% if company.info.facebook %}
                                <a href="{{ company.info.facebook }}" target="_blank"
                                   class="list-group-item list-group-item-action">
                                    <i class="bi bi-facebook text-primary me-2"></i>
                                    Facebook
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Mission Statement -->
            {% if company.info.mission %}
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-3">Our Mission</h5>
                        <p class="card-text">{{ company.info.mission }}</p>
                    </div>
                </div>
            {% endif %}

            <!-- Recent Activity -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Activity</h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-light active" data-filter="all">
                            All
                        </button>
                        <button type="button" class="btn btn-light" data-filter="content">
                            Content
                        </button>
                        <button type="button" class="btn btn-light" data-filter="team">
                            Team
                        </button>
                        <button type="button" class="btn btn-light" data-filter="system">
                            System
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush activity-list">
                        {% for activity in activities %}
                            <div class="activity-item" data-type="{{ activity.type }}">
                                {% activity_item activity %}
                            </div>
                        {% empty %}
                            <div class="text-center py-5">
                                <div class="text-muted">
                                    <i class="bi bi-clock h3 mb-2"></i>
                                    <p>No activity recorded yet</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
                {% if activities %}
                    <div class="card-footer bg-transparent text-center">
                        <button type="button" class="btn btn-link text-muted load-more">
                            Load More
                        </button>
                    </div>
                {% endif %}
            </div>

            <!-- Assistants Section -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-transparent">
                    <h5 class="card-title mb-0">Assistants</h5>
                </div>
                <div class="card-body">
                    {# Folder Filter Links (Horizontal - Style copied from my_favorites.html) #}
                    <div class="folder-filter-buttons mb-3 d-flex flex-wrap align-items-center border-bottom pb-2" role="group" aria-label="Filter by folder">
                        {% with current_params=request.GET.copy %}
                            {% query_string current_params folder_id="" as all_url %}
                            {# Use request.path to keep the base URL correct #}
                            <a href="{{ request.path }}{{ all_url }}" class="btn btn-link text-decoration-none p-1 me-3 {% if not selected_folder_id or selected_folder_id == "" %}active{% endif %}">
                                <i class="bi bi-grid-fill me-1"></i>Show All
                            </a>
                            {% query_string current_params folder_id="unassigned" as unassigned_url %}
                            <a href="{{ request.path }}{{ unassigned_url }}" class="btn btn-link text-decoration-none p-1 me-3 {% if selected_folder_id == 'unassigned' %}active{% endif %}">
                                <i class="bi bi-box me-1"></i>Unassigned
                            </a>
                            {% for folder in folders %} {# Loop handles empty list correctly #}
                                {% query_string current_params folder_id=folder.id as folder_url %}
                                <div class="d-inline-block position-relative me-3"> {# Wrapper for dropdown #}
                                    {# Link for filtering - Added request.path #}
                                    <a href="{{ request.path }}{{ folder_url }}" class="btn btn-link text-decoration-none p-1 folder-filter-link {% if selected_folder_id == folder.id|stringformat:"s" %}active{% endif %}" data-folder-id="{{ folder.id }}">
                                        <i class="bi bi-folder me-1"></i>{{ folder.name }}
                                    </a>
                                    {# Dropdown Trigger for Edit/Delete #}
                                    <button class="btn btn-sm btn-link text-secondary p-0 ms-1 position-absolute top-0 end-0" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="line-height: 1;">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item edit-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-pencil-square me-2"></i>Edit</a></li>
                                        <li><a class="dropdown-item delete-folder-btn" href="#" data-folder-id="{{ folder.id }}" data-folder-name="{{ folder.name|escapejs }}"><i class="bi bi-trash me-2"></i>Delete</a></li>
                                    </ul>
                                </div>
                            {% endfor %}
                        {% endwith %}
                         {# Add Folder Button (Triggers Modal) #}
                        <button type="button" class="btn btn-sm btn-outline-secondary ms-auto" data-bs-toggle="modal" data-bs-target="#createFolderModal">
                            <i class="bi bi-plus-circle me-1"></i> Add Folder
                        </button>
                    </div>
                    {# End Folder Filter Links #}

                    {# Assistant List (No grouping needed now) #}
                    {% if assistants %}
                        <div class="list-group list-group-flush">
                            {% for assistant in assistants %}
                                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}"
                                   class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span>
                                        <i class="bi bi-robot me-2 text-muted"></i>
                                        {{ assistant.name }}
                                        {% if not assistant.is_active %}
                                            <span class="badge bg-secondary ms-2">Inactive</span>
                                        {% endif %}
                                        {% if not assistant.is_public %}
                                            <span class="badge bg-light text-dark border ms-1">Private</span>
                                        {% endif %}
                                    </span>
                                    <small class="text-muted">{{ assistant.get_assistant_type_display }}</small>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted text-center mt-3">
                            {% if selected_folder_id %}
                                No assistants found in this folder.
                            {% else %}
                                This company has no assistants yet.
                            {% endif %}
                        </p>
                    {% endif %}
                </div>
            </div>
            <!-- End Assistants Section -->

        </div>
    </div>
</div>

{# Folder Options Modal (Copied from list view) #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Add to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName">this item</strong> to favorites:</p>

        {# Option 1: Save without folder #}
        <div class="d-grid mb-3">
            <button type="button" class="btn btn-outline-primary text-start" id="saveNoFolderBtn">
                <i class="bi bi-bookmark-heart me-2"></i>Save (Uncategorized)
            </button>
        </div>

        <hr>

        {# Option 2: Add to existing folder #}
        <div id="existingFoldersSection" class="mb-3" style="display: none;">
            <label for="selectFolder" class="form-label small mb-1">Add to existing folder:</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                <select class="form-select" id="selectFolder">
                    <option selected disabled value="">Choose folder...</option>
                    {# Options will be populated by JS #}
                </select>
                <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                    <i class="bi bi-plus-lg"></i> Add
                </button>
            </div>
        </div>

        {# Option 3: Create new folder #}
        <div>
            <label for="newFolderName" class="form-label small mb-1">Or create a new folder:</label>
            <div class="input-group">
                 <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                <button class="btn btn-success" type="button" id="createFolderAndSaveBtn" disabled>
                   <i class="bi bi-check-lg"></i> Create & Save
                </button>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="text-danger small mt-3" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}

{# --- Modals for Folder Actions (Copied from assistant_list.html) --- #}
{# Create Folder Modal #}
<div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createFolderModalLabel">Create New Folder</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="createFolderForm">
        {% csrf_token %} {# Add CSRF token #}
        <div class="modal-body">
          <input type="hidden" name="folder_id" value="new"> {# Indicate new folder creation #}
          <div class="mb-3">
            <label for="createFolderNameInput" class="form-label">Folder Name</label>
            <input type="text" class="form-control" id="createFolderNameInput" name="name" required>
          </div>
          <div id="createFolderErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary" id="createFolderSubmitBtn">Create Folder</button>
        </div>
      </form>
    </div>
  </div>
</div>

{# Edit Folder Modal #}
<div class="modal fade" id="editFolderModal" tabindex="-1" aria-labelledby="editFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editFolderModalLabel">Edit Folder Name</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="editFolderForm">
        {% csrf_token %} {# Add CSRF token #}
        <input type="hidden" id="editFolderIdInput" name="folder_id">
        <div class="modal-body">
          <div class="mb-3">
            <label for="editFolderNameInput" class="form-label">New Folder Name</label>
            <input type="text" class="form-control" id="editFolderNameInput" name="name" required>
          </div>
          <div id="editFolderErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary" id="saveFolderEditBtn">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
</div>

{# Delete Folder Confirmation Modal #}
<div class="modal fade" id="deleteFolderModal" tabindex="-1" aria-labelledby="deleteFolderModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteFolderModalLabel">Confirm Deletion</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <input type="hidden" id="deleteFolderIdInput">
        <p>Are you sure you want to delete the folder "<strong id="deleteFolderNameSpan"></strong>"?</p>
        <p class="small text-muted">Assistants in this folder will become uncategorized.</p>
        <div id="deleteFolderErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteFolderBtn">Delete Folder</button>
      </div>
    </div>
  </div>
</div>
{# --- End Modals --- #}

{# Rating Modal #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate <span id="modalCompanyName">this company</span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="ratingForm">
          {% csrf_token %}
          <p class="mb-3">How would you rate your experience with <strong id="modalCompanyNameInner">this company</strong>?</p>

          <div class="modal-stars text-center mb-4">
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="1">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="2">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="3">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="4">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="5">
              <i class="bi bi-star text-secondary"></i>
            </button>
          </div>

          <div id="modalErrorMsg" class="alert alert-danger" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{% endblock %}

{% block extra_js %}
<script>
// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    // Fallback for meta tag if needed, though form input is standard in Django
    if (!csrfInput) {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) return csrfMeta.getAttribute('content');
    }
    return csrfInput ? csrfInput.value : null;
}


// --- Favorite Button & Folder Modal Logic (Adapted for Company Detail Page) ---
function handleFavoriteButtons() {
    // Target the specific button on the detail page
    const likeButton = document.querySelector('.like-button[data-item-type="company"]'); // Target company button
    const folderModalElement = document.getElementById('folderOptionsModal');
    let folderModal = null;
    if (folderModalElement) {
        folderModal = new bootstrap.Modal(folderModalElement);
    } else {
        console.error("Folder options modal element not found.");
    }

    if (!likeButton) {
        console.log("Like button not found on this page (or commented out).");
        return; // Exit if button doesn't exist (e.g., commented out)
    }

    // Store current item details for modal actions
    let currentModalItemId = likeButton.dataset.itemId; // Get ID directly
    let currentModalItemType = likeButton.dataset.itemType; // Get type directly

    // --- Main Click Handler (Like Button) ---
    likeButton.addEventListener('click', async (event) => {
        event.preventDefault();
        event.stopPropagation();

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            console.error("CSRF token not found!");
            alert("Action failed. Please refresh.");
            return;
        }

        // Use the stored item ID and type
        const itemId = currentModalItemId;
        const itemType = currentModalItemType;
        const url = "{% url 'directory:toggle_saved_item' %}"; // Use the same toggle URL

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
            });

            const data = await response.json();

            if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

            if (data.status === 'success' && data.action === 'unfavorited') {
                // Item was unfavorited successfully
                updateHeartIcon(likeButton, false);
            } else if (data.status === 'options') {
                // Item is not saved, show folder options modal
                if (folderModal) { // Check if modal exists
                    populateAndShowFolderModal(data);
                } else {
                    alert("Could not display folder options."); // Fallback if modal failed to init
                }
            } else {
                // Unexpected success response
                console.error('Unexpected response from toggle_saved_item:', data);
                alert('An unexpected error occurred.');
            }

        } catch (error) {
            console.error('Error handling favorite click:', error);
            alert(`An error occurred: ${error.message}`);
        }
    });

    // --- Helper: Update Heart Icon ---
    function updateHeartIcon(button, isSaved) {
        if (!button) return;
        if (isSaved) {
            button.classList.remove('text-secondary');
            button.classList.add('text-danger');
            button.title = 'Unlike';
        } else {
            button.classList.remove('text-danger');
            button.classList.add('text-secondary');
            button.title = 'Like';
        }
    }

    // --- Helper: Populate and Show Folder Modal ---
    function populateAndShowFolderModal(data) {
        if (!folderModalElement || !folderModal) return;

        // Set item name
        const itemNameElement = folderModalElement.querySelector('#modalFolderNameItemName');
        if (itemNameElement) itemNameElement.textContent = data.item_name || 'this item';

        // Populate existing folders dropdown
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const existingFoldersSection = folderModalElement.querySelector('#existingFoldersSection');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');

        selectFolder.innerHTML = '<option selected disabled value="">Choose folder...</option>'; // Reset options
        if (data.folders && data.folders.length > 0) {
            data.folders.forEach(folder => {
                const option = document.createElement('option');
                option.value = folder.id;
                option.textContent = folder.name;
                selectFolder.appendChild(option);
            });
            existingFoldersSection.style.display = 'block';
            addToFolderBtn.disabled = true; // Disable until a folder is selected
        } else {
            existingFoldersSection.style.display = 'none';
        }

        // Reset other fields
        folderModalElement.querySelector('#newFolderName').value = '';
        folderModalElement.querySelector('#createFolderAndSaveBtn').disabled = true;
        folderModalElement.querySelector('#folderModalErrorMsg').style.display = 'none';
        folderModalElement.querySelector('#folderModalErrorMsg').textContent = '';

        // Show the modal
        folderModal.show();
    }

    // --- Folder Modal Event Listeners ---
    if (folderModalElement) {
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');
        const newFolderNameInput = folderModalElement.querySelector('#newFolderName');
        const createFolderBtn = folderModalElement.querySelector('#createFolderAndSaveBtn');
        const saveNoFolderBtn = folderModalElement.querySelector('#saveNoFolderBtn');
        const errorMsgElement = folderModalElement.querySelector('#folderModalErrorMsg');

        // Enable/disable "Add" button based on selection
        selectFolder.addEventListener('change', () => {
            addToFolderBtn.disabled = !selectFolder.value;
        });

        // Enable/disable "Create & Save" button based on input
        newFolderNameInput.addEventListener('input', () => {
            createFolderBtn.disabled = !newFolderNameInput.value.trim();
        });

        // Action: Save without folder
        saveNoFolderBtn.addEventListener('click', async () => {
            await handleSaveFolderAction("{% url 'directory:save_item_no_folder' %}", {});
        });

        // Action: Add to existing folder
        addToFolderBtn.addEventListener('click', async () => {
            const folderId = selectFolder.value;
            if (!folderId) return;
            await handleSaveFolderAction("{% url 'directory:add_item_to_folder' %}", { folder_id: folderId });
        });

        // Action: Create folder and save
        createFolderBtn.addEventListener('click', async () => {
            const folderName = newFolderNameInput.value.trim();
            if (!folderName) return;
            await handleSaveFolderAction("{% url 'directory:create_folder_and_save' %}", { folder_name: folderName });
        });

        // Generic function to handle the save actions
        async function handleSaveFolderAction(url, additionalParams) {
            if (!currentModalItemId || !currentModalItemType) {
                showFolderModalError("Item details missing. Please close and retry.");
                return;
            }
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                 showFolderModalError("CSRF token missing. Please refresh.");
                 return;
            }

            showFolderModalError(''); // Clear previous errors

            const bodyParams = new URLSearchParams({
                'item_id': currentModalItemId,
                'item_type': currentModalItemType,
                ...additionalParams
            });

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });
                const data = await response.json();

                if (!response.ok || data.status !== 'success') {
                    throw new Error(data.message || `HTTP error ${response.status}`);
                }

                // Success! Update the heart icon on the page and close modal
                // Find the button again in case the DOM changed slightly
                const originalButton = document.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                updateHeartIcon(originalButton, true);
                if (folderModal) folderModal.hide();

            } catch (error) {
                console.error(`Error saving favorite via modal (${url}):`, error);
                showFolderModalError(`Error: ${error.message}`);
            }
        }

        function showFolderModalError(message) {
            errorMsgElement.textContent = message;
            errorMsgElement.style.display = message ? 'block' : 'none';
        }
    }
}
// --- End Favorite Button & Folder Modal Logic ---


// --- Company Rating Modal Logic ---
function handleCompanyRatingModal() {
    const ratingModalElement = document.getElementById('ratingModal');
    if (!ratingModalElement) {
        console.log("Rating modal not found on this page.");
        return;
    }

    // Initialize Bootstrap modal if needed
    let ratingModal = bootstrap.Modal.getInstance(ratingModalElement);
    if (!ratingModal) {
        try {
            ratingModal = new bootstrap.Modal(ratingModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal for rating:", e);
        }
    }

    const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
    const submitRatingBtn = ratingModalElement.querySelector('#submitRatingBtn');
    const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
    let ratingCompanyId = null;
    let selectedRating = 0;

    // Set up modal when it's shown
    ratingModalElement.addEventListener('show.bs.modal', function (event) {
        try {
            const button = event.relatedTarget;
            if (!button) {
                console.warn('Modal opened without a relatedTarget button');
                return;
            }

            ratingCompanyId = button.getAttribute('data-company-id');
            const companyName = button.getAttribute('data-company-name');
            const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
            const modalCompanyName = ratingModalElement.querySelector('#modalCompanyName');
            const modalCompanyNameInner = ratingModalElement.querySelector('#modalCompanyNameInner');

            console.log('Rating modal opened for company:', companyName, 'ID:', ratingCompanyId);

            if (modalTitle) modalTitle.innerHTML = `Rate <span id="modalCompanyName">${companyName}</span>`;
            if (modalCompanyName) modalCompanyName.textContent = companyName;
            if (modalCompanyNameInner) modalCompanyNameInner.textContent = companyName;

            selectedRating = 0;
            if (submitRatingBtn) submitRatingBtn.disabled = true;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';
            if (modalStarsContainer) {
                const starButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                if (starButtons && starButtons.length > 0) {
                    starButtons.forEach(btn => {
                        btn.classList.remove('active');
                        const starIcon = btn.querySelector('i');
                        if (starIcon) {
                            starIcon.classList.remove('bi-star-fill', 'text-warning');
                            starIcon.classList.add('bi-star', 'text-secondary');
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Error in modal show event:', error);
        }
    });

    // Handle star clicks
    if (modalStarsContainer) {
        modalStarsContainer.addEventListener('click', function (event) {
            try {
                const starButton = event.target.closest('.modal-star-btn');
                if (!starButton) return;

                const ratingValue = starButton.getAttribute('data-rating-value');
                if (!ratingValue) {
                    console.warn('Star button clicked but no rating value found');
                    return;
                }

                selectedRating = parseInt(ratingValue);
                console.log('Selected rating:', selectedRating);

                if (submitRatingBtn) submitRatingBtn.disabled = false;
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                // Update star appearance
                const allStarButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                if (allStarButtons && allStarButtons.length > 0) {
                    allStarButtons.forEach(btn => {
                        const starIcon = btn.querySelector('i');
                        if (!starIcon) return;

                        const btnValueAttr = btn.getAttribute('data-rating-value');
                        if (!btnValueAttr) return;

                        const btnValue = parseInt(btnValueAttr);

                        if (btnValue <= selectedRating) {
                            starIcon.classList.remove('bi-star', 'text-secondary');
                            starIcon.classList.add('bi-star-fill', 'text-warning');
                            btn.classList.add('active');
                        } else {
                            starIcon.classList.remove('bi-star-fill', 'text-warning');
                            starIcon.classList.add('bi-star', 'text-secondary');
                            btn.classList.remove('active');
                        }
                    });
                }
            } catch (error) {
                console.error('Error handling star click:', error);
            }
        });
    }

    // Handle submit button click
    if (submitRatingBtn) {
        submitRatingBtn.addEventListener('click', function() {
            try {
                if (!selectedRating || !ratingCompanyId) {
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = 'Please select a rating.';
                        modalErrorMsg.style.display = 'block';
                    }
                    return;
                }

                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    console.error('CSRF token not found!');
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = 'CSRF token not found. Please refresh the page.';
                        modalErrorMsg.style.display = 'block';
                    }
                    return;
                }

                // Save original button text for restoration
                const originalButtonText = submitRatingBtn.innerHTML;
                submitRatingBtn.disabled = true;
                submitRatingBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

                // Use the URL for rating companies
                const rateUrl = "{% url 'directory:rate_company' %}";

                console.log('Submitting company rating to:', rateUrl, 'Company ID:', ratingCompanyId, 'Rating:', selectedRating);

                const formData = new URLSearchParams();
                formData.append('company_id', ratingCompanyId);
                formData.append('rating', selectedRating);

                // Use fetch API to submit the rating
                fetch(rateUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Rating response:', data);

                    if (data.status === 'success') {
                        // Update the rating display
                        const ratingDisplay = document.getElementById(`rating-display-${ratingCompanyId}`);
                        if (ratingDisplay && data.rendered_stars_html) {
                            ratingDisplay.innerHTML = data.rendered_stars_html;
                            console.log('Updated rating display for company ID:', ratingCompanyId);
                        }

                        // Close the modal
                        if (ratingModal) {
                            ratingModal.hide();
                        }

                        // Show success message using toast notification
                        if (typeof showToast === 'function') {
                            showToast('Rating submitted successfully!', 'success');
                        } else {
                            console.log('Rating submitted successfully!');
                        }
                    } else {
                        if (modalErrorMsg) {
                            modalErrorMsg.textContent = data.message || 'Failed to submit rating.';
                            modalErrorMsg.style.display = 'block';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error submitting rating:', error);
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = error.message || 'An unexpected error occurred.';
                        modalErrorMsg.style.display = 'block';
                    }
                })
                .finally(() => {
                    // Always re-enable the button and restore its text
                    if (submitRatingBtn) {
                        submitRatingBtn.disabled = false;
                        submitRatingBtn.innerHTML = originalButtonText || 'Submit Rating';
                    }
                });
            } catch (error) {
                console.error('Error in submit button click handler:', error);
                if (modalErrorMsg) {
                    modalErrorMsg.textContent = error.message || 'An unexpected error occurred.';
                    modalErrorMsg.style.display = 'block';
                }
            }
        });
    }
}
// --- End Company Rating Modal Logic ---

document.addEventListener('DOMContentLoaded', function() {
    // Initialize Favorite Button Logic
    handleFavoriteButtons();

    // Initialize Rating Modal Logic
    handleCompanyRatingModal();

    // Activity Filters (Existing logic)
    const filterButtons = document.querySelectorAll('[data-filter]');
    const activityItems = document.querySelectorAll('.activity-item');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.dataset.filter;

            // Update active state
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filter items
            activityItems.forEach(item => {
                if (filter === 'all' || item.dataset.type === filter) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // Load More
    const loadMore = document.querySelector('.load-more');
    if (loadMore) {
        let page = 1;
        loadMore.addEventListener('click', function() {
            page++;
            fetch(`?page=${page}`)
                .then(response => response.json())
                .then(data => {
                    if (data.activities.length > 0) {
                        const activityList = document.querySelector('.activity-list');
                        data.activities.forEach(activity => {
                            const div = document.createElement('div');
                            div.className = 'activity-item';
                            div.dataset.type = activity.type;
                            div.innerHTML = activity.html;
                            activityList.appendChild(div);
                        });
                    }
                    if (!data.has_more) {
                        loadMore.style.display = 'none';
                    }
                });
        });
    }
});

// --- Folder Management JS (Copied from assistant_list.html) ---
document.addEventListener('DOMContentLoaded', function() {

    // Helper function (already defined above, ensure it's accessible or redefine if needed)
    // function getCsrfToken() { ... }

    // --- Folder Management JS ---
    const createFolderModalElement = document.getElementById('createFolderModal'); // Assumes modal exists in base or this template
    const editFolderModalElement = document.getElementById('editFolderModal');
    const deleteFolderModalElement = document.getElementById('deleteFolderModal');

    // Create Folder
    if (createFolderModalElement) {
        const createFolderForm = createFolderModalElement.querySelector('#createFolderForm');
        const createErrorMsg = createFolderModalElement.querySelector('#createFolderErrorMsg');
        const createSubmitBtn = createFolderModalElement.querySelector('#createFolderSubmitBtn');

        createFolderForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            const csrfToken = getCsrfToken();
            const folderNameInput = createFolderModalElement.querySelector('#createFolderNameInput');
            const folderName = folderNameInput.value.trim();

            if (!folderName) {
                createErrorMsg.textContent = "Folder name cannot be empty.";
                createErrorMsg.style.display = 'block';
                return;
            }
            createErrorMsg.style.display = 'none';
            createSubmitBtn.disabled = true;
            createSubmitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Creating...';

            try {
                // Use the correct URL for creating folders within this company context
                const response = await fetch("{% url 'assistants:folder_create' company_id=company.id %}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'name': folderName })
                });
                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    window.location.reload(); // Reload to show the new folder
                } else {
                    createErrorMsg.textContent = data.errors?.name?.[0] || data.message || 'Error creating folder.';
                    createErrorMsg.style.display = 'block';
                }
            } catch (error) {
                console.error('Error creating folder:', error);
                createErrorMsg.textContent = 'An unexpected error occurred.';
                createErrorMsg.style.display = 'block';
            } finally {
                 createSubmitBtn.disabled = false;
                 createSubmitBtn.innerHTML = 'Create Folder';
            }
        });
    }

    // Edit Folder
    if (editFolderModalElement) {
        const editFolderForm = editFolderModalElement.querySelector('#editFolderForm');
        const editFolderIdInput = editFolderModalElement.querySelector('#editFolderIdInput');
        const editFolderNameInput = editFolderModalElement.querySelector('#editFolderNameInput');
        const editErrorMsg = editFolderModalElement.querySelector('#editFolderErrorMsg');
        const saveFolderEditBtn = editFolderModalElement.querySelector('#saveFolderEditBtn');

        // Use event delegation on a stable parent if the buttons are dynamically added/removed
        document.body.addEventListener('click', (event) => {
            const editBtn = event.target.closest('.edit-folder-btn');
            if (editBtn) {
                event.preventDefault();
                const folderId = editBtn.dataset.folderId;
                const folderName = editBtn.dataset.folderName;
                editFolderIdInput.value = folderId;
                editFolderNameInput.value = folderName;
                editErrorMsg.style.display = 'none';
                const editModal = bootstrap.Modal.getInstance(editFolderModalElement) || new bootstrap.Modal(editFolderModalElement);
                editModal.show();
            }
        });

        editFolderForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            const folderId = editFolderIdInput.value;
            const newName = editFolderNameInput.value.trim();
            const csrfToken = getCsrfToken();
            const url = `/assistants/folder/${folderId}/edit/`; // Use dynamic URL

            if (!newName) {
                editErrorMsg.textContent = "Folder name cannot be empty.";
                editErrorMsg.style.display = 'block';
                return;
            }
            editErrorMsg.style.display = 'none';
            saveFolderEditBtn.disabled = true;
            saveFolderEditBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'name': newName })
                });
                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    window.location.reload(); // Reload to reflect changes
                } else {
                    editErrorMsg.textContent = data.errors?.name?.[0] || data.message || 'Error updating folder.';
                    editErrorMsg.style.display = 'block';
                }
            } catch (error) {
                console.error('Error updating folder:', error);
                editErrorMsg.textContent = 'An unexpected error occurred.';
                editErrorMsg.style.display = 'block';
            } finally {
                saveFolderEditBtn.disabled = false;
                saveFolderEditBtn.innerHTML = 'Save Changes';
            }
        });
    }

    // Delete Folder
    if (deleteFolderModalElement) {
        const deleteFolderIdInput = deleteFolderModalElement.querySelector('#deleteFolderIdInput');
        const deleteFolderNameSpan = deleteFolderModalElement.querySelector('#deleteFolderNameSpan');
        const confirmDeleteBtn = deleteFolderModalElement.querySelector('#confirmDeleteFolderBtn');
        const deleteErrorMsg = deleteFolderModalElement.querySelector('#deleteFolderErrorMsg');

        document.body.addEventListener('click', (event) => {
            const deleteBtn = event.target.closest('.delete-folder-btn');
            if (deleteBtn) {
                event.preventDefault();
                const folderId = deleteBtn.dataset.folderId;
                const folderName = deleteBtn.dataset.folderName;
                deleteFolderIdInput.value = folderId;
                deleteFolderNameSpan.textContent = folderName; // Use textContent for security
                deleteErrorMsg.style.display = 'none';
                const deleteModal = bootstrap.Modal.getInstance(deleteFolderModalElement) || new bootstrap.Modal(deleteFolderModalElement);
                deleteModal.show();
            }
        });

        confirmDeleteBtn.addEventListener('click', async () => {
            const folderId = deleteFolderIdInput.value;
            const csrfToken = getCsrfToken();
            const url = `/assistants/folder/${folderId}/delete/`; // Use dynamic URL

            confirmDeleteBtn.disabled = true;
            confirmDeleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...';
            deleteErrorMsg.style.display = 'none';

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                const data = await response.json();

                if (response.ok && data.status === 'success') {
                    window.location.reload(); // Reload to reflect deletion
                } else {
                    deleteErrorMsg.textContent = data.message || 'Error deleting folder.';
                    deleteErrorMsg.style.display = 'block';
                }
            } catch (error) {
                console.error('Error deleting folder:', error);
                deleteErrorMsg.textContent = 'An unexpected error occurred.';
                deleteErrorMsg.style.display = 'block';
            } finally {
                confirmDeleteBtn.disabled = false;
                confirmDeleteBtn.innerHTML = 'Delete Folder';
            }
        });
    }

    // --- Assign Folder Action (Not typically needed on company detail, but include if desired) ---
    // document.body.addEventListener('click', async (event) => { ... }); // Add if needed

});
</script>
{% endblock %}
