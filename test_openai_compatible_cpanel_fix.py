#!/usr/bin/env python
"""
Test script to verify OpenAI compatible models work with cPanel optimized system.
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from assistants.models import Assistant
from accounts.models import Company, User
from django.conf import settings

def test_cpanel_openai_compatible():
    """Test OpenAI compatible models with cPanel optimized system."""
    print("🔧 Testing OpenAI Compatible Models with cPanel Optimization")
    print("=" * 60)
    
    # Check if we're in cPanel mode
    in_cpanel = getattr(settings, 'IN_CPANEL', False)
    print(f"IN_CPANEL setting: {in_cpanel}")
    
    # Test importing the cPanel optimized manager
    try:
        from assistants.llm_cpanel_optimized import generate_cpanel_optimized_response, cpanel_llm_manager
        print("✅ Successfully imported cPanel optimized LLM manager")
    except ImportError as e:
        print(f"❌ Failed to import cPanel optimized manager: {e}")
        return False
    
    # Test the OpenAI compatible method exists
    if hasattr(cpanel_llm_manager, '_call_openai_compatible'):
        print("✅ cPanel LLM manager has OpenAI compatible method")
    else:
        print("❌ cPanel LLM manager missing OpenAI compatible method")
        return False
    
    # Create a mock assistant for testing
    class MockAssistant:
        def __init__(self):
            self.model = 'openai-compatible'
            self.api_key = 'test-key'
            self.base_url = 'https://api.example.com/v1'
            self.custom_model_name = 'test-model'
            self.temperature = 0.7
            self.max_tokens = 100
    
    assistant = MockAssistant()
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"}
    ]
    
    # Test the cPanel optimized response generation
    print("\n📋 Testing cPanel optimized response generation...")
    try:
        response = generate_cpanel_optimized_response(assistant, messages, "Hello, how are you?")
        print(f"✅ cPanel response generated: {type(response)}")
        print(f"Response keys: {list(response.keys())}")
        
        # Check if it's handling OpenAI compatible models
        if 'content' in response:
            if 'high load' in response['content'] or 'technical difficulties' in response['content']:
                print("⚠️  Got fallback response - this is expected since we don't have a real API")
            else:
                print(f"Response content: {response['content'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in cPanel response generation: {e}")
        return False

def test_view_conditional_logic():
    """Test that views use the correct LLM system based on IN_CPANEL setting."""
    print("\n📋 Testing view conditional logic...")
    
    # Test the conditional import logic
    from django.conf import settings
    
    if getattr(settings, 'IN_CPANEL', False):
        print("✅ IN_CPANEL is True - views should use cPanel optimized manager")
        
        # Test importing what the views would import
        try:
            from assistants.llm_cpanel_optimized import generate_cpanel_optimized_response
            from assistants.llm_utils_optimized import _get_system_context_cached
            print("✅ Successfully imported cPanel dependencies used by views")
        except ImportError as e:
            print(f"❌ Failed to import cPanel dependencies: {e}")
            return False
    else:
        print("✅ IN_CPANEL is False - views should use regular optimized manager")
        
        # Test importing what the views would import
        try:
            from assistants.llm_utils_optimized import generate_assistant_response_optimized
            print("✅ Successfully imported regular optimized dependencies")
        except ImportError as e:
            print(f"❌ Failed to import regular dependencies: {e}")
            return False
    
    return True

def test_openai_compatible_validation():
    """Test OpenAI compatible validation in cPanel manager."""
    print("\n📋 Testing OpenAI compatible validation...")
    
    from assistants.llm_cpanel_optimized import cpanel_llm_manager
    
    # Test with missing fields
    class MockAssistantMissingFields:
        def __init__(self):
            self.model = 'openai-compatible'
            self.api_key = ''  # Missing
            self.base_url = ''  # Missing
            self.custom_model_name = ''  # Missing
            self.temperature = 0.7
            self.max_tokens = 100
    
    assistant = MockAssistantMissingFields()
    messages = [{"role": "user", "content": "test"}]
    
    try:
        cpanel_llm_manager._call_openai_compatible(messages, 'openai-compatible', assistant)
        print("❌ Validation should have failed for missing fields")
        return False
    except Exception as e:
        if "required" in str(e).lower():
            print("✅ Validation correctly failed for missing fields")
        else:
            print(f"❌ Unexpected error: {e}")
            return False
    
    return True

def main():
    """Run all tests."""
    print("🚀 Testing OpenAI Compatible Models with cPanel Fix")
    print("=" * 60)
    
    tests = [
        ("cPanel OpenAI Compatible", test_cpanel_openai_compatible),
        ("View Conditional Logic", test_view_conditional_logic),
        ("OpenAI Compatible Validation", test_openai_compatible_validation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        try:
            result = test_func()
            results.append(result)
            if result:
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            import traceback
            traceback.print_exc()
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if all(results):
        print("✅ All tests passed! OpenAI compatible models should work with cPanel optimization.")
        print("\n🎯 Summary of fixes applied:")
        print("1. ✅ Added OpenAI compatible support to cPanel LLM manager")
        print("2. ✅ Added conditional logic in views to use cPanel manager when IN_CPANEL=True")
        print("3. ✅ Added proper validation and error handling")
        print("4. ✅ Maintained backward compatibility with regular optimized system")
        return True
    else:
        print("❌ Some tests failed. Please check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
