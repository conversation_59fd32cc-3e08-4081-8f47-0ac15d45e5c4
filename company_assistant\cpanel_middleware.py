"""
Ultra-lightweight middleware for cPanel environments.
Designed to minimize resource usage and prevent process buildup.
"""

import gc
import time
import logging
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.conf import settings
from django.http import HttpResponse

logger = logging.getLogger(__name__)


class CPanelResourceMiddleware(MiddlewareMixin):
    """
    Aggressive resource management middleware for cPanel.
    Prevents memory leaks and reduces process overhead.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.request_count = 0
        super().__init__(get_response)
    
    def process_request(self, request):
        """Optimize request processing for cPanel."""
        # Skip processing for static files completely
        if self._is_static_request(request):
            return None
        
        # Track request start time for timeout protection
        request._start_time = time.time()
        
        # Aggressive memory cleanup every 10 requests
        self.request_count += 1
        if self.request_count % 10 == 0:
            gc.collect()
            self.request_count = 0
        
        # Prevent long-running requests
        if hasattr(request, 'META'):
            # Set aggressive timeout for cPanel
            request.META['HTTP_TIMEOUT'] = '15'  # 15 seconds max
        
        return None
    
    def process_response(self, request, response):
        """Optimize response processing for cPanel."""
        # Skip for static files
        if self._is_static_request(request):
            return response
        
        # Check for long-running requests
        if hasattr(request, '_start_time'):
            duration = time.time() - request._start_time
            if duration > 15:  # 15 seconds
                logger.warning(f"Long request detected: {request.path} took {duration:.2f}s")
        
        # Force garbage collection for memory-intensive responses
        if hasattr(response, 'content') and len(getattr(response, 'content', b'')) > 100000:  # 100KB
            gc.collect()
        
        # Add cache headers for static-like content
        if self._should_cache_response(request, response):
            response['Cache-Control'] = 'public, max-age=300'  # 5 minutes
        
        return response
    
    def process_exception(self, request, exception):
        """Handle exceptions efficiently."""
        # Log error but don't let it crash the process
        logger.error(f"Exception in {request.path}: {exception}")
        
        # Force garbage collection after exceptions
        gc.collect()
        
        # Return a simple error response to prevent process hanging
        if getattr(settings, 'IN_CPANEL', False):
            return HttpResponse(
                "Service temporarily unavailable. Please try again.",
                status=503,
                content_type='text/plain'
            )
        
        return None
    
    def _is_static_request(self, request):
        """Check if request is for static files."""
        path = request.path
        return (
            path.startswith('/static/') or
            path.startswith('/media/') or
            path.endswith('.css') or
            path.endswith('.js') or
            path.endswith('.png') or
            path.endswith('.jpg') or
            path.endswith('.jpeg') or
            path.endswith('.gif') or
            path.endswith('.ico') or
            path.endswith('.svg')
        )
    
    def _should_cache_response(self, request, response):
        """Determine if response should be cached."""
        return (
            request.method == 'GET' and
            response.status_code == 200 and
            not request.path.startswith('/admin/') and
            not request.path.startswith('/api/')
        )


class CPanelMemoryMiddleware(MiddlewareMixin):
    """
    Memory management middleware specifically for cPanel.
    Prevents memory leaks and optimizes garbage collection.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.memory_threshold = 50 * 1024 * 1024  # 50MB threshold
        super().__init__(get_response)
    
    def process_request(self, request):
        """Monitor and manage memory usage."""
        # Check memory usage periodically
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            # If memory usage is high, force garbage collection
            if memory_mb > 50:  # 50MB threshold for cPanel
                gc.collect()
                
                # If still high after GC, log warning
                memory_mb_after = process.memory_info().rss / 1024 / 1024
                if memory_mb_after > 60:  # 60MB critical threshold
                    logger.warning(f"High memory usage: {memory_mb_after:.1f}MB")
                    
        except ImportError:
            # psutil not available, use basic GC
            gc.collect()
        
        return None
    
    def process_response(self, request, response):
        """Clean up after response."""
        # Force garbage collection for large responses
        if hasattr(response, 'content'):
            content_size = len(getattr(response, 'content', b''))
            if content_size > 50000:  # 50KB
                gc.collect()
        
        return response


class CPanelTimeoutMiddleware(MiddlewareMixin):
    """
    Timeout middleware to prevent hanging processes in cPanel.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.timeout_seconds = 20  # 20 second timeout for cPanel
        super().__init__(get_response)
    
    def process_request(self, request):
        """Set request timeout."""
        request._timeout_start = time.time()
        return None
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        """Check timeout before view processing."""
        if hasattr(request, '_timeout_start'):
            elapsed = time.time() - request._timeout_start
            if elapsed > self.timeout_seconds:
                logger.warning(f"Request timeout: {request.path} exceeded {self.timeout_seconds}s")
                return HttpResponse(
                    "Request timeout. Please try again.",
                    status=408,
                    content_type='text/plain'
                )
        return None
    
    def process_response(self, request, response):
        """Log slow responses."""
        if hasattr(request, '_timeout_start'):
            elapsed = time.time() - request._timeout_start
            if elapsed > 10:  # Log requests over 10 seconds
                logger.warning(f"Slow response: {request.path} took {elapsed:.2f}s")
        
        return response


class CPanelCacheMiddleware(MiddlewareMixin):
    """
    Aggressive caching middleware for cPanel to reduce database load.
    """
    
    def process_request(self, request):
        """Check cache for GET requests."""
        if request.method != 'GET':
            return None
        
        # Skip caching for admin and API
        if request.path.startswith('/admin/') or request.path.startswith('/api/'):
            return None
        
        # Generate cache key
        cache_key = f"cpanel_page:{request.path}:{request.GET.urlencode()}"
        
        # Try to get cached response
        cached_response = cache.get(cache_key)
        if cached_response:
            return HttpResponse(cached_response, content_type='text/html')
        
        # Store cache key for later use
        request._cache_key = cache_key
        return None
    
    def process_response(self, request, response):
        """Cache successful responses."""
        if (hasattr(request, '_cache_key') and 
            response.status_code == 200 and
            hasattr(response, 'content')):
            
            # Cache for 5 minutes
            cache.set(request._cache_key, response.content, 300)
        
        return response
