/**
 * Advanced Service Worker for Aggressive Caching and Offline Support
 */

const CACHE_VERSION = 'v2.0.0';
const CACHE_NAMES = {
    static: `static-${CACHE_VERSION}`,
    dynamic: `dynamic-${CACHE_VERSION}`,
    api: `api-${CACHE_VERSION}`,
    images: `images-${CACHE_VERSION}`
};

const STATIC_ASSETS = [
    '/',
    '/static/css/critical.css',
    '/static/js/core-performance.js',
    '/static/js/performance/core-performance.js',
    '/static/fonts/main.woff2',
    '/static/images/logo.webp',
    '/static/images/placeholder.webp',
    '/offline.html'
];

const API_CACHE_PATTERNS = [
    /\/api\/assistants\/\d+\/$/,
    /\/api\/companies\/\d+\/$/,
    /\/api\/interactions\/recent\/$/
];

const IMAGE_EXTENSIONS = /\.(jpg|jpeg|png|gif|webp|avif|svg)$/i;
const FONT_EXTENSIONS = /\.(woff|woff2|ttf|otf)$/i;

class AdvancedServiceWorker {
    constructor() {
        this.strategies = {
            'cache-first': this.cacheFirst.bind(this),
            'network-first': this.networkFirst.bind(this),
            'stale-while-revalidate': this.staleWhileRevalidate.bind(this),
            'network-only': this.networkOnly.bind(this),
            'cache-only': this.cacheOnly.bind(this)
        };
        
        this.init();
    }
    
    init() {
        self.addEventListener('install', this.handleInstall.bind(this));
        self.addEventListener('activate', this.handleActivate.bind(this));
        self.addEventListener('fetch', this.handleFetch.bind(this));
        self.addEventListener('message', this.handleMessage.bind(this));
        
        console.log('🔧 Advanced Service Worker initialized');
    }
    
    async handleInstall(event) {
        console.log('📦 Service Worker installing...');
        
        event.waitUntil(
            this.preCache()
        );
        
        // Skip waiting to activate immediately
        self.skipWaiting();
    }
    
    async preCache() {
        try {
            const cache = await caches.open(CACHE_NAMES.static);
            await cache.addAll(STATIC_ASSETS);
            console.log('✅ Static assets pre-cached');
        } catch (error) {
            console.error('❌ Pre-cache failed:', error);
        }
    }
    
    async handleActivate(event) {
        console.log('🚀 Service Worker activating...');
        
        event.waitUntil(
            this.cleanupOldCaches()
        );
        
        // Take control of all clients immediately
        self.clients.claim();
    }
    
    async cleanupOldCaches() {
        const cacheNames = await caches.keys();
        const currentCaches = Object.values(CACHE_NAMES);
        
        const deletePromises = cacheNames
            .filter(cacheName => !currentCaches.includes(cacheName))
            .map(cacheName => {
                console.log(`🗑️ Deleting old cache: ${cacheName}`);
                return caches.delete(cacheName);
            });
        
        await Promise.all(deletePromises);
    }
    
    handleFetch(event) {
        const { request } = event;
        const url = new URL(request.url);
        
        // Skip non-GET requests
        if (request.method !== 'GET') {
            return;
        }
        
        // Skip chrome-extension and other non-http requests
        if (!url.protocol.startsWith('http')) {
            return;
        }
        
        event.respondWith(
            this.routeRequest(request)
        );
    }
    
    async routeRequest(request) {
        const url = new URL(request.url);
        const pathname = url.pathname;
        
        try {
            // Route based on request type
            if (this.isStaticAsset(pathname)) {
                return await this.strategies['cache-first'](request, CACHE_NAMES.static);
            }
            
            if (this.isImage(pathname)) {
                return await this.strategies['cache-first'](request, CACHE_NAMES.images);
            }
            
            if (this.isFont(pathname)) {
                return await this.strategies['cache-first'](request, CACHE_NAMES.static);
            }
            
            if (this.isAPIRequest(pathname)) {
                return await this.strategies['stale-while-revalidate'](request, CACHE_NAMES.api);
            }
            
            if (this.isHTMLPage(request)) {
                return await this.strategies['network-first'](request, CACHE_NAMES.dynamic);
            }
            
            // Default to network-first for everything else
            return await this.strategies['network-first'](request, CACHE_NAMES.dynamic);
            
        } catch (error) {
            console.error('❌ Request routing failed:', error);
            return await this.handleOffline(request);
        }
    }
    
    // Caching Strategies
    async cacheFirst(request, cacheName) {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            // Update cache in background if stale
            this.updateCacheInBackground(request, cache);
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    }
    
    async networkFirst(request, cacheName) {
        const cache = await caches.open(cacheName);
        
        try {
            const networkResponse = await fetch(request);
            
            if (networkResponse.ok) {
                cache.put(request, networkResponse.clone());
            }
            
            return networkResponse;
        } catch (error) {
            const cachedResponse = await cache.match(request);
            
            if (cachedResponse) {
                return cachedResponse;
            }
            
            throw error;
        }
    }
    
    async staleWhileRevalidate(request, cacheName) {
        const cache = await caches.open(cacheName);
        const cachedResponse = await cache.match(request);
        
        // Always try to update cache in background
        const networkPromise = fetch(request).then(response => {
            if (response.ok) {
                cache.put(request, response.clone());
            }
            return response;
        }).catch(() => {
            // Ignore network errors for background updates
        });
        
        // Return cached response immediately if available
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Wait for network if no cache
        return await networkPromise;
    }
    
    async networkOnly(request) {
        return await fetch(request);
    }
    
    async cacheOnly(request, cacheName) {
        const cache = await caches.open(cacheName);
        return await cache.match(request);
    }
    
    // Helper Methods
    isStaticAsset(pathname) {
        return pathname.startsWith('/static/') || 
               pathname.endsWith('.css') || 
               pathname.endsWith('.js') ||
               pathname.endsWith('.json');
    }
    
    isImage(pathname) {
        return IMAGE_EXTENSIONS.test(pathname);
    }
    
    isFont(pathname) {
        return FONT_EXTENSIONS.test(pathname);
    }
    
    isAPIRequest(pathname) {
        return pathname.startsWith('/api/') || 
               API_CACHE_PATTERNS.some(pattern => pattern.test(pathname));
    }
    
    isHTMLPage(request) {
        return request.headers.get('accept')?.includes('text/html');
    }
    
    async updateCacheInBackground(request, cache) {
        try {
            const response = await fetch(request);
            if (response.ok) {
                await cache.put(request, response);
            }
        } catch (error) {
            // Ignore background update errors
        }
    }
    
    async handleOffline(request) {
        const url = new URL(request.url);
        
        // Return offline page for HTML requests
        if (this.isHTMLPage(request)) {
            const cache = await caches.open(CACHE_NAMES.static);
            return await cache.match('/offline.html');
        }
        
        // Return placeholder for images
        if (this.isImage(url.pathname)) {
            const cache = await caches.open(CACHE_NAMES.static);
            return await cache.match('/static/images/placeholder.webp');
        }
        
        // Return generic offline response
        return new Response('Offline', {
            status: 503,
            statusText: 'Service Unavailable',
            headers: {
                'Content-Type': 'text/plain'
            }
        });
    }
    
    handleMessage(event) {
        const { data } = event;
        
        switch (data.type) {
            case 'SKIP_WAITING':
                self.skipWaiting();
                break;
                
            case 'GET_CACHE_STATS':
                this.getCacheStats().then(stats => {
                    event.ports[0].postMessage(stats);
                });
                break;
                
            case 'CLEAR_CACHE':
                this.clearCache(data.cacheName).then(success => {
                    event.ports[0].postMessage({ success });
                });
                break;
                
            case 'PREFETCH_URLS':
                this.prefetchUrls(data.urls);
                break;
        }
    }
    
    async getCacheStats() {
        const stats = {};
        
        for (const [name, cacheName] of Object.entries(CACHE_NAMES)) {
            try {
                const cache = await caches.open(cacheName);
                const keys = await cache.keys();
                stats[name] = {
                    name: cacheName,
                    size: keys.length,
                    urls: keys.map(request => request.url)
                };
            } catch (error) {
                stats[name] = { error: error.message };
            }
        }
        
        return stats;
    }
    
    async clearCache(cacheName) {
        try {
            if (cacheName) {
                return await caches.delete(cacheName);
            } else {
                // Clear all caches
                const cacheNames = await caches.keys();
                const deletePromises = cacheNames.map(name => caches.delete(name));
                await Promise.all(deletePromises);
                return true;
            }
        } catch (error) {
            console.error('❌ Cache clear failed:', error);
            return false;
        }
    }
    
    async prefetchUrls(urls) {
        const cache = await caches.open(CACHE_NAMES.dynamic);
        
        const prefetchPromises = urls.map(async (url) => {
            try {
                const response = await fetch(url);
                if (response.ok) {
                    await cache.put(url, response);
                }
            } catch (error) {
                console.warn(`❌ Prefetch failed for ${url}:`, error);
            }
        });
        
        await Promise.all(prefetchPromises);
        console.log(`✅ Prefetched ${urls.length} URLs`);
    }
}

// Initialize the service worker
new AdvancedServiceWorker();

// Performance monitoring
self.addEventListener('fetch', (event) => {
    const start = performance.now();
    
    event.respondWith(
        event.response || fetch(event.request).then(response => {
            const duration = performance.now() - start;
            
            // Log slow requests
            if (duration > 1000) {
                console.warn(`🐌 Slow request: ${event.request.url} took ${duration.toFixed(2)}ms`);
            }
            
            return response;
        })
    );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        event.waitUntil(
            // Handle background sync tasks
            console.log('🔄 Background sync triggered')
        );
    }
});

// Push notifications support
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json();
        
        event.waitUntil(
            self.registration.showNotification(data.title, {
                body: data.body,
                icon: '/static/images/icon-192.png',
                badge: '/static/images/badge-72.png',
                data: data.url
            })
        );
    }
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    if (event.notification.data) {
        event.waitUntil(
            clients.openWindow(event.notification.data)
        );
    }
});
