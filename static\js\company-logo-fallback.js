/**
 * Company Logo Fallback System
 * Specifically handles company logo fallbacks for carousels and directory listings
 */

class CompanyLogoFallback {
    constructor() {
        this.fallbackImages = [
            '/static/img/default-company-logo.svg',
            '/static/img/logos/company-1.svg',
            '/static/img/logos/company-2.svg',
            '/static/img/logos/company-3.svg',
            '/static/img/logos/company-4.svg',
            '/static/img/logos/company-5.svg'
        ];
        
        this.placeholderTemplate = `
            <div class="company-logo-placeholder d-flex flex-column align-items-center justify-content-center h-100 w-100">
                <i class="bi bi-building-fill text-muted" style="font-size: 2.5rem;"></i>
                <span class="d-block mt-2 small text-muted text-center">{{COMPANY_NAME}}</span>
            </div>
        `;
        
        this.init();
    }

    init() {
        console.log('[CompanyLogoFallback] Initializing company logo fallback system');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupFallbacks());
        } else {
            this.setupFallbacks();
        }
        
        // Preload fallback images
        this.preloadFallbackImages();
    }

    /**
     * Preload fallback images to ensure they're available
     */
    preloadFallbackImages() {
        this.fallbackImages.forEach((src, index) => {
            const img = new Image();
            img.onload = () => console.log(`[CompanyLogoFallback] Preloaded fallback ${index + 1}:`, src);
            img.onerror = () => console.warn(`[CompanyLogoFallback] Failed to preload fallback ${index + 1}:`, src);
            img.src = src;
        });
    }

    /**
     * Setup fallbacks for all company logos on the page
     */
    setupFallbacks() {
        // Handle carousel logos
        this.setupCarouselLogos();
        
        // Handle directory card logos
        this.setupDirectoryLogos();
        
        // Setup mutation observer for dynamic content
        this.setupMutationObserver();
    }

    /**
     * Setup fallbacks for carousel company logos
     */
    setupCarouselLogos() {
        const carouselLogos = document.querySelectorAll(`
            .company-logo-carousel img,
            .featured-carousel-items img,
            .company-logo-item img,
            .featured-carousel-item img,
            .home-carousel-logo img,
            .featured-company-logo-img
        `);
        
        console.log(`[CompanyLogoFallback] Found ${carouselLogos.length} carousel logos`);
        
        carouselLogos.forEach((img, index) => {
            this.setupImageFallback(img, `carousel-${index}`);
        });
    }

    /**
     * Setup fallbacks for directory card logos
     */
    setupDirectoryLogos() {
        const directoryLogos = document.querySelectorAll(`
            .directory-card img,
            .company-card img,
            .card img[src*="company_logos"],
            img[alt*="Logo"]
        `);
        
        console.log(`[CompanyLogoFallback] Found ${directoryLogos.length} directory logos`);
        
        directoryLogos.forEach((img, index) => {
            this.setupImageFallback(img, `directory-${index}`);
        });
    }

    /**
     * Setup fallback for a specific image
     */
    setupImageFallback(img, identifier) {
        if (img.dataset.fallbackSetup) {
            return; // Already setup
        }

        img.dataset.fallbackSetup = 'true';
        img.dataset.fallbackId = identifier;
        img.dataset.originalSrc = img.src;
        img.dataset.fallbackAttempt = '0';

        // Store company name from alt text or nearby elements
        const companyName = this.extractCompanyName(img);
        if (companyName) {
            img.dataset.companyName = companyName;
        }

        // Remove existing error handlers
        img.onerror = null;
        img.onload = null;

        // Setup new handlers
        img.addEventListener('error', (e) => this.handleImageError(e.target));
        img.addEventListener('load', (e) => this.handleImageLoad(e.target));

        // Check if image is already broken
        if (img.complete && img.naturalWidth === 0) {
            console.log(`[CompanyLogoFallback] Image already broken: ${identifier}`);
            this.handleImageError(img);
        }
    }

    /**
     * Handle successful image load
     */
    handleImageLoad(img) {
        const identifier = img.dataset.fallbackId;
        console.log(`[CompanyLogoFallback] Image loaded successfully: ${identifier}`);
        
        // Ensure image is visible
        img.style.display = '';
        
        // Remove any existing placeholder
        this.removePlaceholder(img);
    }

    /**
     * Handle image load error
     */
    handleImageError(img) {
        const identifier = img.dataset.fallbackId;
        const originalSrc = img.dataset.originalSrc;
        const attemptNumber = parseInt(img.dataset.fallbackAttempt) || 0;
        
        console.error(`[CompanyLogoFallback] Image failed (attempt ${attemptNumber}): ${identifier} - ${originalSrc}`);

        // Try next fallback image
        if (attemptNumber < this.fallbackImages.length) {
            const fallbackSrc = this.fallbackImages[attemptNumber];
            img.dataset.fallbackAttempt = (attemptNumber + 1).toString();
            
            console.log(`[CompanyLogoFallback] Trying fallback ${attemptNumber + 1}: ${fallbackSrc}`);
            img.src = fallbackSrc;
            return;
        }

        // All fallbacks failed, show placeholder
        console.warn(`[CompanyLogoFallback] All fallbacks failed for: ${identifier}`);
        this.showPlaceholder(img);
    }

    /**
     * Show placeholder when all fallbacks fail
     */
    showPlaceholder(img) {
        const container = img.parentNode;
        const companyName = img.dataset.companyName || this.extractCompanyName(img) || 'Company';
        
        // Hide the broken image
        img.style.display = 'none';
        
        // Remove existing placeholder
        this.removePlaceholder(img);
        
        // Create new placeholder
        const placeholder = document.createElement('div');
        placeholder.className = 'logo-fallback-placeholder';
        placeholder.innerHTML = this.placeholderTemplate.replace('{{COMPANY_NAME}}', companyName);
        
        // Match container styling
        const containerClasses = container.className;
        if (containerClasses.includes('home-carousel-logo') || containerClasses.includes('logo-container')) {
            placeholder.style.height = '100%';
            placeholder.style.width = '100%';
            placeholder.style.minHeight = '120px';
        }
        
        container.appendChild(placeholder);
    }

    /**
     * Remove existing placeholder
     */
    removePlaceholder(img) {
        const container = img.parentNode;
        const existingPlaceholder = container.querySelector('.logo-fallback-placeholder');
        if (existingPlaceholder) {
            existingPlaceholder.remove();
        }
    }

    /**
     * Extract company name from image or nearby elements
     */
    extractCompanyName(img) {
        // Try alt text first
        if (img.alt && img.alt.includes('Logo')) {
            return img.alt.replace(/\s*Logo\s*/i, '').trim();
        }

        // Try data attributes
        if (img.dataset.companyName) {
            return img.dataset.companyName;
        }

        // Try to find company name in nearby elements
        const card = img.closest('.directory-card, .featured-carousel-item, .company-logo-item, .card');
        if (card) {
            const nameElement = card.querySelector('h5, h6, .company-name, .item-info h5, .company-info h5');
            if (nameElement) {
                return nameElement.textContent.trim();
            }
        }

        // Try link title
        const link = img.closest('a');
        if (link && link.title) {
            return link.title;
        }

        return null;
    }

    /**
     * Setup mutation observer for dynamic content
     */
    setupMutationObserver() {
        if (!document.body) {
            setTimeout(() => this.setupMutationObserver(), 100);
            return;
        }

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the added node is an image
                        if (node.tagName === 'IMG') {
                            this.setupImageFallback(node, `dynamic-${Date.now()}`);
                        }

                        // Check for images within the added node
                        if (node.querySelectorAll) {
                            const images = node.querySelectorAll('img');
                            images.forEach((img, index) => {
                                this.setupImageFallback(img, `dynamic-${Date.now()}-${index}`);
                            });
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Manually refresh fallbacks for a specific container
     */
    refreshContainer(container) {
        const images = container.querySelectorAll('img');
        images.forEach((img, index) => {
            // Reset fallback data
            delete img.dataset.fallbackSetup;
            delete img.dataset.fallbackAttempt;
            this.setupImageFallback(img, `refresh-${Date.now()}-${index}`);
        });
    }
}

// Initialize the fallback system
window.companyLogoFallback = new CompanyLogoFallback();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CompanyLogoFallback;
}
