#!/usr/bin/env python
"""
Simple verification script to check if OpenAI compatible fixes are working.
This script doesn't require Django setup and tests the core functionality.
"""

import sys
import traceback

def test_imports():
    """Test if we can import the required modules."""
    print("Testing imports...")
    
    try:
        import openai
        print("✅ openai module imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import openai: {e}")
        return False
    
    try:
        from openai import APIConnectionError, AuthenticationError, RateLimitError, APIError
        print("✅ OpenAI exception classes imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import OpenAI exceptions: {e}")
        return False
    
    return True

def test_validation_logic():
    """Test the validation logic without Django."""
    print("\nTesting validation logic...")
    
    # Mock assistant class for testing
    class MockAssistant:
        def __init__(self, api_key="", base_url="", custom_model_name=""):
            self.api_key = api_key
            self.base_url = base_url
            self.custom_model_name = custom_model_name
    
    # Test validation function (simplified version of what we added)
    def validate_openai_config(assistant):
        errors = []
        
        if not assistant.api_key or not assistant.api_key.strip():
            errors.append("API key is required for OpenAI Compatible model")
        
        if not assistant.base_url or not assistant.base_url.strip():
            errors.append("Base URL is required for OpenAI Compatible model")
        
        if not assistant.custom_model_name or not assistant.custom_model_name.strip():
            errors.append("Model name is required for OpenAI Compatible model")
        
        # Simple URL validation
        if assistant.base_url and assistant.base_url.strip():
            if not (assistant.base_url.startswith('http://') or assistant.base_url.startswith('https://')):
                errors.append("Base URL must be a valid URL")
        
        return errors
    
    # Test 1: Empty fields
    assistant = MockAssistant()
    errors = validate_openai_config(assistant)
    if len(errors) == 3:
        print("✅ Validation correctly identifies missing fields")
    else:
        print(f"❌ Expected 3 errors, got {len(errors)}: {errors}")
        return False
    
    # Test 2: Invalid URL
    assistant = MockAssistant("key", "invalid-url", "model")
    errors = validate_openai_config(assistant)
    if "Base URL must be a valid URL" in errors:
        print("✅ Validation correctly identifies invalid URL")
    else:
        print(f"❌ Expected URL validation error, got: {errors}")
        return False
    
    # Test 3: Valid configuration
    assistant = MockAssistant("key", "https://api.example.com/v1", "model")
    errors = validate_openai_config(assistant)
    if len(errors) == 0:
        print("✅ Validation passes for valid configuration")
    else:
        print(f"❌ Expected no errors for valid config, got: {errors}")
        return False
    
    return True

def test_error_handling():
    """Test error handling logic."""
    print("\nTesting error handling logic...")
    
    # Mock function to test error handling patterns
    def mock_openai_call():
        """Simulate different types of OpenAI errors."""
        import openai
        
        # This will fail since we don't have a real API key/endpoint
        try:
            client = openai.OpenAI(
                api_key="fake-key",
                base_url="https://fake-api.example.com/v1"
            )
            
            response = client.chat.completions.create(
                model="fake-model",
                messages=[{"role": "user", "content": "test"}],
                temperature=0.7,
                max_tokens=100
            )
            
            return response.choices[0].message.content
            
        except openai.APIConnectionError as e:
            return f"Connection error handled: {type(e).__name__}"
        except openai.AuthenticationError as e:
            return f"Authentication error handled: {type(e).__name__}"
        except openai.RateLimitError as e:
            return f"Rate limit error handled: {type(e).__name__}"
        except openai.APIError as e:
            return f"API error handled: {type(e).__name__}"
        except Exception as e:
            return f"General error handled: {type(e).__name__}"
    
    try:
        result = mock_openai_call()
        if "error handled" in result:
            print(f"✅ Error handling working: {result}")
            return True
        else:
            print(f"❌ Unexpected result: {result}")
            return False
    except Exception as e:
        print(f"❌ Error handling failed: {e}")
        traceback.print_exc()
        return False

def test_input_sanitization():
    """Test input sanitization logic."""
    print("\nTesting input sanitization...")
    
    # Test string sanitization
    test_cases = [
        ("  api-key  ", "api-key"),
        ("\tapi-key\n", "api-key"),
        ("", ""),
        ("   ", ""),
        ("normal-key", "normal-key"),
    ]
    
    for input_val, expected in test_cases:
        result = input_val.strip() if input_val else ""
        if result == expected:
            print(f"✅ Sanitization correct: '{input_val}' -> '{result}'")
        else:
            print(f"❌ Sanitization failed: '{input_val}' -> '{result}', expected '{expected}'")
            return False
    
    return True

def main():
    """Run all verification tests."""
    print("🔧 Verifying OpenAI Compatible Models Fixes")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Validation Logic", test_validation_logic),
        ("Error Handling", test_error_handling),
        ("Input Sanitization", test_input_sanitization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            result = test_func()
            results.append(result)
            if result:
                print(f"✅ {test_name} passed")
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            traceback.print_exc()
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 Verification Results:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if all(results):
        print("✅ All verification tests passed! OpenAI compatible fixes are working.")
        return True
    else:
        print("❌ Some verification tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
