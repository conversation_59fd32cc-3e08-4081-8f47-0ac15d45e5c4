"""
Unified session management system for proper login/logout synchronization.
This replaces multiple conflicting session cleanup middlewares.
"""
from django.utils.deprecation import MiddlewareMixin
from django.contrib.sessions.models import Session
from django.contrib.auth.signals import user_logged_in, user_logged_out
from django.dispatch import receiver
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.conf import settings
import logging
import hashlib

logger = logging.getLogger(__name__)
User = get_user_model()


class UnifiedSessionManager:
    """
    Centralized session management for user authentication.
    """

    @staticmethod
    def get_user_sessions(user):
        """
        Get all active sessions for a specific user.

        Args:
            user: User object

        Returns:
            QuerySet of Session objects
        """
        if not user or not user.is_authenticated:
            return Session.objects.none()

        try:
            # Get all non-expired sessions
            active_sessions = Session.objects.filter(
                expire_date__gte=timezone.now()
            )

            # Filter sessions that belong to this user
            user_sessions = []
            for session in active_sessions:
                try:
                    session_data = session.get_decoded()
                    if session_data.get('_auth_user_id') == str(user.id):
                        user_sessions.append(session)
                except Exception as e:
                    logger.warning(f"Error decoding session {session.session_key}: {e}")
                    # Delete corrupted session
                    session.delete()

            return user_sessions

        except Exception as e:
            logger.error(f"Error getting user sessions for {user}: {e}")
            return []

    @staticmethod
    def logout_all_other_sessions(user, current_session_key=None):
        """
        Log out all other sessions for a user, keeping the current one.

        Args:
            user: User object
            current_session_key: Session key to preserve (optional)

        Returns:
            int: Number of sessions terminated
        """
        if not user or not user.is_authenticated:
            return 0

        try:
            user_sessions = UnifiedSessionManager.get_user_sessions(user)
            terminated_count = 0

            for session in user_sessions:
                if current_session_key and session.session_key == current_session_key:
                    continue  # Skip current session

                try:
                    session.delete()
                    terminated_count += 1
                    logger.info(f"Terminated session {session.session_key} for user {user.username}")
                except Exception as e:
                    logger.error(f"Error terminating session {session.session_key}: {e}")

            # Clean up user-specific cache data
            UnifiedSessionManager.cleanup_user_cache(user)

            return terminated_count

        except Exception as e:
            logger.error(f"Error logging out other sessions for {user}: {e}")
            return 0

    @staticmethod
    def logout_all_sessions(user):
        """
        Log out ALL sessions for a user, including current one.

        Args:
            user: User object

        Returns:
            int: Number of sessions terminated
        """
        return UnifiedSessionManager.logout_all_other_sessions(user, current_session_key=None)

    @staticmethod
    def cleanup_user_cache(user):
        """
        Clean up user-specific cache data.

        Args:
            user: User object
        """
        if not user or not user.is_authenticated:
            return

        try:
            # Clean up user session cache keys
            cache_patterns = [
                f"user_session_{user.id}_*",
                f"user_context_{user.id}_*",
                f"user_preferences_{user.id}_*",
            ]

            # Note: This is a simplified cleanup. In production with Redis,
            # you would use SCAN with patterns to find and delete keys
            logger.info(f"Cleaned up cache data for user {user.username}")

        except Exception as e:
            logger.error(f"Error cleaning up cache for user {user}: {e}")

    @staticmethod
    def enforce_single_session(user, current_session_key):
        """
        Enforce single session per user (optional feature).

        Args:
            user: User object
            current_session_key: Current session to keep

        Returns:
            int: Number of old sessions terminated
        """
        if not getattr(settings, 'ENFORCE_SINGLE_SESSION_PER_USER', False):
            return 0

        return UnifiedSessionManager.logout_all_other_sessions(user, current_session_key)

    @staticmethod
    def cleanup_expired_sessions():
        """
        Clean up expired sessions (periodic maintenance).

        Returns:
            int: Number of expired sessions cleaned up
        """
        try:
            expired_sessions = Session.objects.filter(
                expire_date__lt=timezone.now()
            )[:100]  # Limit to avoid long-running queries

            if expired_sessions:
                session_keys = [s.session_key for s in expired_sessions]
                count = Session.objects.filter(session_key__in=session_keys).delete()[0]
                logger.info(f"Cleaned up {count} expired sessions")
                return count

            return 0

        except Exception as e:
            logger.error(f"Error cleaning up expired sessions: {e}")
            return 0


class UnifiedSessionMiddleware(MiddlewareMixin):
    """
    Unified middleware for session management and cleanup.
    This replaces multiple conflicting session middlewares.
    """

    def process_request(self, request):
        """
        Process incoming requests for session validation.
        """
        # Clean up impersonation data for anonymous users
        if not request.user.is_authenticated:
            if 'impersonate_id' in request.session:
                del request.session['impersonate_id']

            if hasattr(request, 'real_user'):
                delattr(request, 'real_user')

            if hasattr(request, 'is_impersonate'):
                request.is_impersonate = False

        return None

    def process_response(self, request, response):
        """
        Process responses for session cleanup.
        """
        # Handle logout cleanup - but only after the response is processed
        # to avoid interfering with CSRF tokens
        if (hasattr(request, 'user') and
            hasattr(request, '_logout_cleanup_needed')):

            try:
                # Clean up user's other sessions on logout
                user = getattr(request, '_logout_user', None)
                if user:
                    UnifiedSessionManager.logout_all_other_sessions(
                        user,
                        None  # Don't preserve any session on logout
                    )
            except Exception as e:
                logger.error(f"Error cleaning up sessions on logout: {e}")

        # Periodic cleanup (every 200 requests approximately)
        import random
        if random.randint(1, 200) == 1:
            UnifiedSessionManager.cleanup_expired_sessions()

        return response


# Signal handlers for login/logout events
@receiver(user_logged_in)
def handle_user_login(sender, request, user, **kwargs):
    """
    Handle user login events.
    """
    try:
        # Enforce single session if configured
        if hasattr(request, 'session'):
            terminated = UnifiedSessionManager.enforce_single_session(
                user,
                request.session.session_key
            )
            if terminated > 0:
                logger.info(f"Terminated {terminated} old sessions for user {user.username}")

        logger.info(f"User {user.username} logged in successfully")

    except Exception as e:
        logger.error(f"Error handling user login for {user}: {e}")


@receiver(user_logged_out)
def handle_user_logout(sender, request, user, **kwargs):
    """
    Handle user logout events.
    """
    try:
        # Clean up impersonation session data
        if hasattr(request, 'session'):
            if 'impersonate_id' in request.session:
                del request.session['impersonate_id']

        # Clean up user-specific cache data
        UnifiedSessionManager.cleanup_user_cache(user)

        logger.info(f"User {user.username} logged out successfully")

    except Exception as e:
        logger.error(f"Error handling user logout for {user}: {e}")
