{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Contact Us - NUP{% endblock %}

{% block meta_description %}
Get in touch with the National Unity Platform. We're here to answer your questions and help you connect with our representatives.
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="nup-section-red">
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">Contact Us</h1>
                <p class="lead mb-0">We'd love to hear from you. Get in touch with our team.</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="nup-section-white">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Information -->
            <div class="col-lg-4">
                <h2 class="h3 fw-bold mb-4">Get In Touch</h2>
                <p class="text-muted mb-4">
                    Have questions about NUP? Want to learn more about how to get involved or connect with your representatives? Our team is here to help.
                </p>

                <div class="d-flex mb-4">
                    <div class="flex-shrink-0">
                        <i class="bi bi-envelope text-nup-red fs-4"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-1">Email Us</h3>
                        <p class="text-muted mb-0"><a href="mailto:{{ site_config.support_email|default:'<EMAIL>' }}">{{ site_config.support_email|default:'<EMAIL>' }}</a></p>
                    </div>
                </div>

                <div class="d-flex mb-4">
                    <div class="flex-shrink-0">
                        <i class="bi bi-telephone text-nup-red fs-4"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-1">Call Us</h3>
                        <p class="text-muted mb-0">+256 (0) 123-456-789</p>
                    </div>
                </div>

                <div class="d-flex mb-4">
                    <div class="flex-shrink-0">
                        <i class="bi bi-geo-alt text-primary fs-4"></i>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-1">Visit Us</h3>
                        <p class="text-muted mb-0">
                            123 AI Avenue<br>
                            Tech District<br>
                            San Francisco, CA 94105
                        </p>
                    </div>
                </div>

                <h3 class="h5 mb-3">Connect With Us</h3>
                <div class="social-icons">
                    <a href="#" aria-label="Twitter" class="me-2"><i class="bi bi-twitter"></i></a>
                    <a href="#" aria-label="LinkedIn" class="me-2"><i class="bi bi-linkedin"></i></a>
                    <a href="#" aria-label="Facebook" class="me-2"><i class="bi bi-facebook"></i></a>
                    <a href="#" aria-label="Instagram"><i class="bi bi-instagram"></i></a>
                </div>
            </div>

            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-4 p-lg-5">
                        <h2 class="h3 fw-bold mb-4">Send Us a Message</h2>
                        <form id="contactForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">Your Name</label>
                                    <input type="text" class="form-control" id="name" placeholder="Enter your name" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" placeholder="Enter your email" required>
                                </div>
                                <div class="col-12">
                                    <label for="subject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="subject" placeholder="Enter subject">
                                </div>
                                <div class="col-12">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" rows="5" placeholder="Enter your message" required></textarea>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="privacyPolicy" required>
                                        <label class="form-check-label" for="privacyPolicy">
                                            I agree to the <a href="{% url 'privacy' %}">Privacy Policy</a>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg">Send Message</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="fw-bold mb-4">Frequently Asked Questions</h2>
                <p class="lead">
                    Find quick answers to common questions about 24seven.
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="accordion" id="faqAccordion">
                    <!-- FAQ Item 1 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                What is 24seven?
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                24seven is an AI-powered virtual assistant platform designed to enhance productivity and streamline workflows for businesses of all sizes. Our platform allows you to create customized virtual assistants tailored to your specific business needs.
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                How do I get started with 24seven?
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Getting started is easy! Simply sign up for an account, create your company profile, and start configuring your first virtual assistant. Our intuitive interface makes it simple to set up and customize assistants to meet your specific needs.
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                Is my data secure with 24seven?
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Absolutely. We take data security and privacy very seriously. All data is encrypted both in transit and at rest, and we follow industry best practices for security. We never share your data with third parties without your explicit consent.
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingFour">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                Can I customize my virtual assistants?
                            </button>
                        </h2>
                        <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes! Customization is one of our core features. You can configure your assistants with specific knowledge, behaviors, and capabilities to match your business requirements. You can also customize the appearance and branding of your assistants.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const contactForm = document.getElementById('contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                // In a real implementation, you would send the form data to your backend
                alert('Thank you for your message! We will get back to you soon.');
                contactForm.reset();
            });
        }
    });
</script>
{% endblock %}
