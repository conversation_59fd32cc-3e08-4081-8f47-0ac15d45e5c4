{% extends "base/layout.html" %}
{% load static account_tags rating_tags %} {# Load new rating_tags #}

{% block title %}NUP Candidates Directory{% endblock %}

{% block body_class %}nup-section-white company-directory-page{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/directory.css' %}">
<link rel="stylesheet" href="{% static 'css/featured-carousel.css' %}">
<link rel="stylesheet" href="{% static 'css/green-to-nup-override.css' %}">
<link rel="stylesheet" href="{% static 'css/blue-to-nup-override.css' %}">
<link rel="stylesheet" href="{% static 'css/improved-candidate-cards.css' %}">
<link rel="stylesheet" href="{% static 'css/assistant-card-fixes.css' %}">
<link rel="stylesheet" href="{% static 'css/clean-card-hover-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/individual-card-hover-fix.css' %}">
<!-- <link rel="stylesheet" href="{% static 'css/assistant-list-dark-mode.css' %}"> -->
<style>
    /* Override like button styling - no circles, transparent background */
    .like-button,
    .like-button:hover,
    .like-button:active,
    .like-button:focus,
    .like-button.text-danger,
    .like-button.text-secondary,
    .featured-carousel-item .like-button,
    .btn-like,
    .btn-favorite,
    .favorite-button {
        background: transparent !important;
        background-color: transparent !important;
        border: none !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        width: auto !important;
        height: auto !important;
        padding: 4px !important;
    }

    /* Ensure animation is applied to carousel */
    .featured-carousel-items {
        display: flex;
        animation: scroll 60s linear infinite;
        width: max-content;
        min-height: 300px;
        animation-play-state: running;
        overflow: visible;
        transition: animation-play-state 0.3s ease;
    }

    /* Make carousel items visible */
    .featured-carousel-item {
        flex: 0 0 auto;
        margin: 0 30px;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        min-width: 300px;
        min-height: 300px;
        position: relative;
        z-index: 1;
        transition: transform 0.3s ease, filter 0.3s ease;
    }

    .featured-carousel-item:hover {
        transform: scale(1.05);
        z-index: 10; /* Bring hovered item to front */
        filter: brightness(1.05);
    }

    /* Style the featured item wrapper */
    .featured-item-wrapper {
        position: relative !important;
        width: 355px !important;
        min-height: 300px !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        background-color: rgba(255, 255, 255, 0.95) !important; /* More opaque white */
        border-radius: 10px !important;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15) !important; /* Stronger shadow */
        padding: 20px !important;
        transition: all 0.3s ease !important;
        border: 1px solid rgba(207, 46, 46, 0.1) !important; /* Subtle NUP red border */
    }

    .featured-item-wrapper:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15) !important;
    }

    /* Make logo container visible */
    .featured-carousel-item .logo-container {
        height: 180px !important;
        width: 180px !important;
        min-height: 180px !important;
        min-width: 180px !important;
        max-height: 180px !important;
        max-width: 180px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: white !important;
        border-radius: 0.25rem !important;
        overflow: hidden !important;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 8px 15px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        position: relative !important;
        margin: 0 auto 20px !important;
        z-index: 2 !important;
    }

    /* Make logo placeholder visible */
    .featured-carousel-item .logo-placeholder {
        color: #cf2e2e !important; /* NUP Red */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(255, 255, 255, 0.9) !important; /* White background for visibility */
    }

    .featured-carousel-item .logo-placeholder i {
        font-size: 140px !important;
        line-height: 1 !important;
        display: block !important;
        text-align: center !important;
        margin: 0 !important;
        padding: 0 !important;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    }

    /* Make item info visible */
    .featured-carousel-item .item-info {
        text-align: center !important;
        width: 100% !important;
        padding: 10px !important;
    }

    .featured-carousel-item .item-info h5 {
        color: #cf2e2e !important; /* NUP Red */
        font-size: 1.3rem !important;
        font-weight: 700 !important;
        line-height: 1.3 !important;
        margin-bottom: 0.5rem !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) !important;
    }

    .featured-carousel-item .item-info p {
        font-size: 0.9rem !important;
        line-height: 1.5 !important;
        color: #242424 !important; /* NUP Dark Charcoal */
        margin-bottom: 0.5rem !important;
    }

    /* Make like button visible */
    .featured-carousel-item .like-button {
        background-color: white !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        width: 36px !important;
        height: 36px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.2s ease !important;
        z-index: 10 !important;
        position: absolute !important;
        top: 10px !important;
        right: 10px !important;
    }

    /* Animation for continuous scrolling */
    @keyframes scroll {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
    }

    /* Apply directory theme styling */
    .featured-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
        position: relative !important;
        z-index: 100 !important;
        overflow: visible !important;
    }

    /* Make featured carousel container visible */
    .featured-carousel-container {
        width: 100% !important;
        overflow: hidden !important;
        position: relative !important;
        padding: 30px 0 70px 0 !important;
        margin-bottom: 30px !important;
        background-color: rgba(255, 255, 255, 0.5) !important;
        border-radius: 10px !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05) !important;
    }

    .tier-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .list-group-item {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
        overflow: hidden;
        height: auto;
        min-height: 160px;
        margin-bottom: 1rem;
    }

    .list-group-item:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .filter-form {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    /* Logo container styles are now in directory.css */

    .logo-placeholder {
        font-size: 2.5rem;
    }

    .tier-badge {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 0.7em;
        padding: 0.25em 0.5em;
        margin: 0.5rem;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .tier-gold {
        background-color: #ffc107;
        color: #212529;
    }

    .tier-silver {
        background-color: #adb5bd;
        color: #212529;
    }

    .tier-bronze {
        background-color: #cd7f32;
        color: #fff;
    }

    .featured-carousel .carousel-item {
        background-color: #fff;
        border-radius: 0.5rem;
        padding: 1.5rem;
    }

    .carousel-indicators {
        position: relative;
        margin-top: 1rem;
        margin-bottom: 0;
    }

    .carousel-indicators button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #dee2e6;
    }

    .carousel-indicators button.active {
        background-color: #0d6efd;
    }
</style>
{% endblock %}

{% block head_extra %}
<style>
    /* Fix for carousel hover pause */
    .featured-carousel-items:hover {
        animation-play-state: paused !important;
    }

    /* Rating stars styling */
    .star-rating .stars .bi-star-fill {
        color: gold;
    }
    .star-rating .stars .bi-star {
        color: #ccc;
    }
    .modal-stars .modal-star-btn.active i {
        color: gold !important;
    }

    /* Make stars more clickable */
    .modal-stars .modal-star-btn {
        cursor: pointer;
        padding: 10px !important;
        margin: 0 5px;
        transition: transform 0.2s;
    }
    .modal-stars .modal-star-btn:hover {
        transform: scale(1.2);
    }
    .modal-stars .modal-star-btn i {
        font-size: 1.5em;
    }

    /* Featured carousel rating styling */
    .featured-carousel-item .rating-display-container {
        margin-top: 5px;
        text-align: center;
    }
    .featured-carousel-item .star-rating {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    .featured-carousel-item .star-rating .stars {
        display: inline-flex;
    }
    .featured-carousel-item .star-rating .stars i {
        font-size: 1rem;
        margin: 0 1px;
    }

    /* Logo container styles are now in directory.css */

    /* Ensure consistent card sizing */
    .list-group-item {
        height: 220px;
        overflow: hidden;
    }

    /* Interactive Stars */
    .interactive-star-rating { cursor: default; }
    .interactive-star-rating .star-btn {
        background: none;
        border: none;
        padding: 0 0.1em;
        margin: 0;
        cursor: pointer;
        color: #ddd;
        transition: color 0.2s ease-in-out, transform 0.1s ease;
        font-size: 1.1em;
    }
    .interactive-star-rating .star-btn:hover {
        color: #f8d96c;
        transform: scale(1.1);
    }
    .interactive-star-rating .star-btn.filled {
        color: #f5c518;
    }
    .interactive-star-rating .star-btn.user-rated {
        color: #f5c518;
    }
    .interactive-star-rating.rating-submitted .star-btn {
        cursor: not-allowed;
        opacity: 0.7;
    }
</style>
{% csrf_token %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="nup-section-red">
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">NUP Candidates Directory</h1>
                <div class="nup-accent-bar"></div>
                <p class="lead mb-0">Connect with National Unity Platform candidates and representatives across Uganda</p>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="nup-section-white">
<div class="container mt-5 mb-5 company-directory-container"
     data-hide-standard-tier-assistants="{{ hide_standard_tier_assistants|yesno:'True,False' }}"
     data-hide-standard-tier-companies="{{ hide_standard_tier_companies|yesno:'True,False' }}"
     data-hide-standard-tier-community-assistants="{{ hide_standard_tier_community_assistants|yesno:'True,False' }}">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-5 mb-0 fw-bold">NUP Candidates Directory</h1>
    </div>

    {# Filter Form #}
    <form method="get" action="{% url 'directory:assistant_list' %}" class="filter-form">
        <h5 class="mb-3 fw-bold"><i class="bi bi-funnel-fill me-2 text-nup-red"></i>Find Candidates</h5>

        <div class="row g-3 mb-3">
            <div class="col-md-6"> {# Name/Description Filter #}
                <label for="q_name_dir" class="form-label">Search</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" name="q_name" id="q_name_dir" class="form-control" placeholder="Search name or description..." value="{{ q_name|default:'' }}">
                </div>
            </div>
            <div class="col-md-3"> {# Company Filter #}
                <label for="q_company_dir" class="form-label">Company</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-building"></i></span>
                    <input type="text" name="q_company" id="q_company_dir" class="form-control" placeholder="Company..." value="{{ q_company|default:'' }}">
                </div>
            </div>
            <div class="col-md-3"> {# Sort Dropdown #}
                <label for="sort_by" class="form-label">Sort By</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-sort-down"></i></span>
                    <select name="sort_by" id="sort_by" class="form-select">
                        <option value="tier" {% if sort_by == 'tier' or not sort_by %}selected{% endif %}>Sort by Tier</option>
                        <option value="latest" {% if sort_by == 'latest' %}selected{% endif %}>Sort by Latest</option>
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Sort by Name</option>
                        <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Sort by Rating</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-md-4"> {# Category Filter #}
                <label for="q_category_dir" class="form-label">Category</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-tag"></i></span>
                    <input type="text" name="q_category" id="q_category_dir" class="form-control" placeholder="Category..." value="{{ q_category|default:'' }}">
                </div>
            </div>
            <div class="col-md-4"> {# Tag Filter #}
                <label for="q_tag_dir" class="form-label">Tag</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-tags"></i></span>
                    <input type="text" name="q_tag" id="q_tag_dir" class="form-control" placeholder="Tag..." value="{{ q_tag|default:'' }}">
                </div>
            </div>
            <div class="col-md-4"> {# Buttons #}
                <label class="form-label">&nbsp;</label>
                <div class="d-flex">
                    <button type="submit" class="btn btn-primary me-2 flex-grow-1">
                        <i class="bi bi-filter me-1"></i> Apply Filters
                    </button>
                    <a href="{% url 'directory:assistant_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle"></i>
                    </a>
                </div>
            </div>
        </div>
    </form>
    {# End Filter Form #}

    {# Display Active Filters - Only show filters from search bar #}
    {% if q_name or q_company or q_category or q_tag %}
    <div class="alert alert-light border mb-4 mt-2">
        <div class="d-flex align-items-center">
            <i class="bi bi-funnel me-2 text-primary"></i>
            <span class="fw-medium">Filtering by:</span>
            <div class="ms-2">
                {% if q_name %}<span class="badge bg-primary me-1">Name/Desc: {{ q_name }}</span>{% endif %}
                {% if q_company %}<span class="badge bg-info text-dark me-1">Company: {{ q_company }}</span>{% endif %}
                {% if q_category %}<span class="badge bg-secondary me-1">Category: {{ q_category }}</span>{% endif %}
                {% if q_tag %}<span class="badge bg-light text-dark border me-1">Tag: {{ q_tag }}</span>{% endif %}
            </div>
        </div>
    </div>
    {% endif %}
    {# End Display Active Filters #}



    {# --- Featured Assistants Section (Continuous Scrolling Carousel) --- #}
    <div class="featured-section mt-5">
        <h2><i class="bi bi-star-fill me-2 text-nup-gold"></i>Featured Candidates</h2>

        {% with settings=directory_settings_dict %}
        <div class="featured-carousel-container"
             data-visible-count="{{ settings.featured_visible_count|default:1 }}"
             data-animation-delay="{{ settings.featured_autoplay_delay|default:5000 }}">
            <div class="featured-carousel-items">
                {% if featured_listings %}
                    {# Display real featured assistants #}
                    {% for listing in featured_listings %}
                        {% with assistant=listing.assistant %}
                        <div class="featured-carousel-item">
                            <div class="featured-item-wrapper">
                                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" title="{{ assistant.name }}" class="text-center">
                                    {% with logo_url=assistant.get_logo_url %}
                                        <div class="logo-container">
                                            {% if logo_url %}
                                                <img src="{{ logo_url }}" alt="{{ assistant.name }} Logo"
                                                     onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'logo-placeholder\'><i class=\'bi bi-robot\'></i></div>';">
                                            {% else %}
                                                <div class="logo-placeholder">
                                                    <i class="bi bi-robot"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endwith %}
                                    <div class="item-info">
                                        <h5>{{ assistant.name }}</h5>
                                        {% if listing.description %}
                                            <p>{{ listing.description|truncatechars:60 }}</p>
                                        {% endif %}
                                        <div class="rating-display-container" id="rating-display-{{ assistant.id }}">
                                            {% if listing.avg_rating and listing.avg_rating > 0 %}
                                                {% render_stars listing.avg_rating listing.total_ratings %}
                                            {% else %}
                                                <span class="text-muted fst-italic">(No ratings yet)</span>
                                            {% endif %}
                                        </div>
                                        {% if assistant.company.name %}
                                            <p class="assistant-count">
                                                <i class="bi bi-building me-1"></i>
                                                {{ assistant.company.name }}
                                            </p>
                                        {% endif %}
                                    </div>
                                </a>
                                {% if user.is_authenticated and assistant.id %}
                                    <button class="like-button btn btn-icon {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                            data-item-id="{{ assistant.id }}"
                                            data-item-type="assistant"
                                            title="{% if assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}">
                                        <i class="bi {% if assistant.id in saved_assistant_ids %}bi-heart-fill{% else %}bi-heart{% endif %}"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                        {% endwith %}
                    {% endfor %}

                    {# Duplicate the real featured assistants for continuous scrolling effect #}
                    {% if featured_listings|length >= 3 %}
                        {% for listing in featured_listings %}
                            {% with assistant=listing.assistant %}
                            <div class="featured-carousel-item">
                                <div class="featured-item-wrapper">
                                    <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" title="{{ assistant.name }}" class="text-center">
                                        {% with logo_url=assistant.get_logo_url %}
                                            <div class="logo-container">
                                                {% if logo_url %}
                                                    <img src="{{ logo_url }}" alt="{{ assistant.name }} Logo"
                                                         onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'logo-placeholder\'><i class=\'bi bi-robot\'></i></div>';">
                                                {% else %}
                                                    <div class="logo-placeholder">
                                                        <i class="bi bi-robot"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        {% endwith %}
                                        <div class="item-info">
                                            <h5>{{ assistant.name }}</h5>
                                            {% if listing.description %}
                                                <p>{{ listing.description|truncatechars:60 }}</p>
                                            {% endif %}
                                            <div class="rating-display-container" id="rating-display-dup-{{ assistant.id }}">
                                                {% if listing.avg_rating and listing.avg_rating > 0 %}
                                                    {% render_stars listing.avg_rating listing.total_ratings %}
                                                {% else %}
                                                    <span class="text-muted fst-italic">(No ratings yet)</span>
                                                {% endif %}
                                            </div>
                                            {% if assistant.company.name %}
                                                <p class="assistant-count">
                                                    <i class="bi bi-building me-1"></i>
                                                    {{ assistant.company.name }}
                                                </p>
                                            {% endif %}
                                        </div>
                                    </a>
                                    {% if user.is_authenticated and assistant.id %}
                                        <button class="like-button btn btn-icon {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                                data-item-id="{{ assistant.id }}"
                                                data-item-type="assistant"
                                                title="{% if assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}">
                                            <i class="bi {% if assistant.id in saved_assistant_ids %}bi-heart-fill{% else %}bi-heart{% endif %}"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                            {% endwith %}
                        {% endfor %}
                    {% endif %}
                {% else %}
                    {# No featured assistants - JavaScript will create example items #}
                    <!-- No featured assistants found. Example items will be created by JavaScript. -->
                {% endif %}
            </div>
        </div>
        {% endwith %}
    </div>
    {# --- End Featured Assistants Section --- #}

    {# --- Main Assistant List Section --- #}
    <div class="mt-5">
        <h2 class="h2 mb-4 fw-bold">
            {% if sort_by == 'tier' %}
                <i class="bi bi-layers-fill me-2 text-nup-red"></i>Candidates by Position
            {% else %}
                <i class="bi bi-people me-2 text-nup-red"></i>All Candidates
            {% endif %}
        </h2>

        {% if page_obj.object_list %}
            <div class="list-group" id="assistant-list-container"> {# Container for event delegation, added ID #}
                {% if sort_by == 'tier' %}
                    {# Logic to display tier headers dynamically #}
                    {% with TIER_GOLD='gold' TIER_SILVER='silver' TIER_BRONZE='bronze' TIER_STANDARD='standard' %} {# Define constants #}
                    {% regroup page_obj.object_list by assistant.get_tier_display as tier_groups %}

                    {% for group in tier_groups %}
                        {# Determine Tier Heading based on grouper (which is the display name) #}
                        {% with tier_display_name=group.grouper %}
                            {# Display all tiers, including Standard #}
                            <div class="tier-section {% if tier_display_name == 'Gold' %}gold{% elif tier_display_name == 'Silver' %}silver{% elif tier_display_name == 'Bronze' %}bronze{% elif tier_display_name == 'Standard' %}standard{% endif %}"
                                 {% if tier_display_name == 'Standard' and hide_standard_tier_assistants %}style="display: none;"{% endif %}>
                                <h3 class="d-flex align-items-center">
                                    {% if tier_display_name == 'Gold' %}
                                        <i class="bi bi-trophy-fill me-2 text-warning"></i>Presidential Candidates
                                        <span class="badge bg-warning text-dark ms-2 small">National</span>
                                    {% elif tier_display_name == 'Silver' %}
                                        <i class="bi bi-award-fill me-2 text-secondary"></i>Parliamentary Candidates
                                        <span class="badge bg-secondary text-white ms-2 small">Constituency</span>
                                    {% elif tier_display_name == 'Bronze' %}
                                        <i class="bi bi-award me-2" style="color: #cd7f32;"></i>Local Council Candidates
                                        <span class="badge" style="background-color: #cd7f32; color: white;" class="ms-2 small">District</span>
                                    {% elif tier_display_name == 'Standard' %}
                                        <i class="bi bi-people me-2 text-primary"></i>Community Representatives
                                    {% else %}
                                        <i class="bi bi-robot me-2"></i>Other Assistants {# Fallback #}
                                    {% endif %}
                                </h3>

                                {# Loop through items in this tier group #}
                                <div class="list-group company-cards-container">
                                    {% for listing in group.list %}
                                        {% include "directory/partials/assistant_card.html" with listing=listing saved_assistant_ids=saved_assistant_ids display_context='tier' %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endwith %}
                    {% endfor %}
                    {% endwith %}

                {% else %}
                    {# Default display if not sorting by tier #}
                    <div class="list-group company-cards-container">
                        {% for listing in page_obj.object_list %}
                            {% include "directory/partials/assistant_card.html" with listing=listing saved_assistant_ids=saved_assistant_ids display_context='tier' %}
                        {% endfor %}
                    </div>
                {% endif %}
            </div> {# /list-group #}

            <div class="mt-5">
                {% include "pagination_with_items_per_page.html" with page_obj=page_obj %} {# Use enhanced pagination with items per page #}
            </div>
            {% csrf_token %} {# Keep CSRF token if needed for other actions on the page #}

        {% else %}
            <div class="alert alert-info shadow-sm">
                <div class="d-flex align-items-center">
                    <i class="bi bi-info-circle-fill me-3 fs-4"></i>
                    <div>
                        <h5 class="mb-1">No Results Found</h5>
                        <p class="mb-0">No assistants found matching your criteria. Try adjusting your filters.</p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
    {# --- End Main Assistant List Section --- #}

    {# --- Community Assistants Section Removed --- #}
    {# Community assistants have their own dedicated page at /assistant/community/ #}

</div> {# /container #}

{# Rating Modal Structure (Enhanced) #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-4">How would you rate <strong id="modalAssistantName" class="text-primary">this assistant</strong>?</p>
        <div class="modal-stars text-center mb-4" style="font-size: 2.5rem;">
            {% for i_int in "12345" %}
            <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                <i class="bi bi-star"></i>
            </button>
            {% endfor %}
        </div>
        <div class="text-center text-muted small mb-3">Click on a star to select your rating</div>
        <div id="modalErrorMsg" class="alert alert-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>
            <i class="bi bi-check-circle me-1"></i> Submit Rating
        </button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{# Folder Options Modal (Enhanced) #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="folderOptionsModalLabel">
            <i class="bi bi-heart-fill text-danger me-2"></i>Add to Favorites
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName" class="text-primary">this item</strong> to your favorites:</p>

        {# Option 1: Save without folder #}
        <div class="card mb-3 border-primary">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-bookmark-heart-fill me-2 text-primary"></i>Quick Save</h6>
                <p class="card-text small text-muted mb-2">Save without organizing into a folder</p>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary save-without-folder-btn" id="saveWithoutFolderBtn">
                        <i class="bi bi-bookmark-heart me-2"></i>Save to Favorites
                    </button>
                </div>
            </div>
        </div>

        {# Option 2: Add to existing folder #}
        <div id="existingFoldersSection" class="card mb-3" style="display: none;">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-folder-fill me-2 text-warning"></i>Add to Folder</h6>
                <p class="card-text small text-muted mb-2">Add to one of your existing folders</p>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                    <select class="form-select" id="selectFolder">
                        <option selected disabled value="">Choose folder...</option>
                        {# Options will be populated by JS #}
                    </select>
                    <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                        <i class="bi bi-plus-lg"></i> Add
                    </button>
                </div>
            </div>
        </div>

        {# Option 3: Create new folder #}
        <div class="card">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-folder-plus me-2 text-success"></i>Create New Folder</h6>
                <p class="card-text small text-muted mb-2">Create a new folder and add this item to it</p>
                <button type="button" class="btn btn-outline-success w-100 mb-3 create-folder-toggle-btn" id="createFolderToggleBtn">
                    <i class="bi bi-folder-plus me-2"></i> Create New Folder
                </button>
                <div id="createFolderForm" style="display: none; margin-top: 15px;">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                        <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                        <button class="btn btn-success create-and-save-btn" type="button" id="createAndSaveBtn" disabled>
                            <i class="bi bi-check-lg"></i> Create
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="alert alert-danger small mt-3" style="display: none;">
          <!-- Error messages will be displayed here -->
        </div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}


{% endblock %}

{% block extra_js %}
<!-- Carousel functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hover pause for assistants list carousel
    const carousel = document.querySelector('.featured-carousel-items');
    const carouselContainer = document.querySelector('.featured-carousel-container');

    if (carousel && carouselContainer) {
        // Remove any inline animation-play-state
        carousel.style.removeProperty('animation-play-state');

        // Add hover handlers to container
        carouselContainer.addEventListener('mouseenter', function() {
            carousel.style.animationPlayState = 'paused';
        });

        carouselContainer.addEventListener('mouseleave', function() {
            carousel.style.animationPlayState = 'running';
        });

        // Add hover handlers to all items
        const items = carousel.querySelectorAll('.featured-carousel-item');
        items.forEach(function(item) {
            item.addEventListener('mouseenter', function() {
                carousel.style.animationPlayState = 'paused';
            });

            item.addEventListener('mouseleave', function() {
                if (!carouselContainer.matches(':hover')) {
                    carousel.style.animationPlayState = 'running';
                }
            });
        });
    }
});
</script>
<script src="{% static 'js/featured-carousel.js' %}"></script>


</script>

{# Removed Swiper.js JS #}
{# <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script> #}

{# Removed Swiper Initialization Script #}
{# {{ directory_settings_dict|json_script:"directory-settings-data" }} #}
{# Removed script tag #}

{# Removed duplicate Folder Options Modal #}

<!-- Dark mode script for assistant list - DISABLED -->
<!-- <script src="{% static 'js/assistant-list-dark-mode.js' %}"></script> -->
<script src="{% static 'js/individual-card-hover-fix.js' %}"></script>
<script src="{% static 'js/favorites-functionality.js' %}"></script>
<script src="{% static 'js/tier-filter.js' %}"></script>

{% endblock %}
</section>
