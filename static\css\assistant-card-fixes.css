/**
 * Assistant Card Layout Fixes
 * Comprehensive fixes for assistant card layout and styling issues
 */

/* ===== ASSISTANT CARD SPECIFIC FIXES ===== */
.directory-card[data-assistant-id] {
    /* Ensure proper card structure */
    min-height: 200px !important;
    max-height: none !important;
    height: auto !important;
    
    /* Better padding and spacing */
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    
    /* Clean background */
    background: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    
    /* Professional shadow */
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    
    /* Remove problematic transforms */
    transform: none !important;
    backdrop-filter: none !important;
}

.directory-card[data-assistant-id]:hover {
    transform: translateY(-2px) !important;
    box-shadow: 
        0 4px 16px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.06) !important;
    border-color: rgba(207, 46, 46, 0.3) !important;
}

/* ===== ROW AND COLUMN LAYOUT FIXES ===== */
.directory-card[data-assistant-id] .row {
    margin: 0 !important;
    align-items: center !important;
    min-height: 140px !important;
}

.directory-card[data-assistant-id] .row > * {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
}

/* ===== LOGO CONTAINER FIXES ===== */
.directory-card[data-assistant-id] .logo-container {
    width: 90px !important;
    height: 90px !important;
    min-width: 90px !important;
    min-height: 90px !important;
    max-width: 90px !important;
    max-height: 90px !important;
    
    /* Clean styling */
    background: linear-gradient(145deg, #f8f9fa 0%, #ffffff 100%) !important;
    border: 2px solid #e9ecef !important;
    border-radius: 12px !important;
    
    /* Perfect centering */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    
    /* Subtle shadow */
    box-shadow: 
        0 2px 6px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    
    /* Smooth transition */
    transition: all 0.3s ease !important;
    overflow: hidden !important;
    margin: 0 auto !important;
}

.directory-card[data-assistant-id]:hover .logo-container {
    border-color: rgba(207, 46, 46, 0.2) !important;
    box-shadow: 
        0 4px 10px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
}

/* ===== LOGO IMAGE FIXES ===== */
.directory-card[data-assistant-id] .logo-container img {
    width: 65px !important;
    height: 65px !important;
    max-width: 65px !important;
    max-height: 65px !important;
    object-fit: contain !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
}

.directory-card[data-assistant-id]:hover .logo-container img {
    transform: scale(1.05) !important;
}

/* ===== LOGO PLACEHOLDER FIXES ===== */
.directory-card[data-assistant-id] .logo-placeholder {
    font-size: 2.5rem !important;
    color: #cf2e2e !important;
    opacity: 0.7 !important;
    transition: all 0.3s ease !important;
}

.directory-card[data-assistant-id]:hover .logo-placeholder {
    opacity: 1 !important;
    transform: scale(1.1) !important;
}

/* ===== CONTENT COLUMN FIXES ===== */
.directory-card[data-assistant-id] .col-md-2:first-child {
    /* Logo column */
    flex: 0 0 16.666667% !important;
    max-width: 16.666667% !important;
    padding-right: 0.75rem !important;
}

.directory-card[data-assistant-id] .col-md-3:nth-child(2) {
    /* Name and company column */
    flex: 0 0 25% !important;
    max-width: 25% !important;
    padding-right: 1rem !important;
}

.directory-card[data-assistant-id] .col-md-5:nth-child(3) {
    /* Description column */
    flex: 0 0 41.666667% !important;
    max-width: 41.666667% !important;
    padding-right: 1rem !important;
}

.directory-card[data-assistant-id] .col-md-2:last-child {
    /* Actions column */
    flex: 0 0 16.666667% !important;
    max-width: 16.666667% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
    align-items: flex-end !important;
    min-height: 140px !important;
}

/* ===== TEXT AND TYPOGRAPHY FIXES ===== */
.directory-card[data-assistant-id] h6 {
    color: #242424 !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    margin-bottom: 0.5rem !important;
    line-height: 1.3 !important;
}

.directory-card[data-assistant-id] .text-muted {
    color: #6c757d !important;
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important;
}

.directory-card[data-assistant-id] .item-description {
    color: #495057 !important;
    line-height: 1.5 !important;
    font-size: 0.95rem !important;
    margin-bottom: 0 !important;
    
    /* Limit description height */
    max-height: 4.5em !important;
    overflow: hidden !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 3 !important;
    -webkit-box-orient: vertical !important;
}

/* ===== BADGE FIXES ===== */
.directory-card[data-assistant-id] .badge {
    font-size: 0.75rem !important;
    padding: 0.3rem 0.6rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    margin-right: 0.4rem !important;
    margin-bottom: 0.25rem !important;
    display: inline-block !important;
}

.directory-card[data-assistant-id] .badge.bg-primary {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
}

.directory-card[data-assistant-id] .badge.bg-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

.directory-card[data-assistant-id] .badge.bg-light {
    background-color: #f8f9fa !important;
    color: #495057 !important;
    border: 1px solid #dee2e6 !important;
}

.directory-card[data-assistant-id] .community-badge {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== CONTACT INFO FIXES ===== */
.directory-card[data-assistant-id] .contact-info {
    font-size: 0.8rem !important;
    color: #6c757d !important;
    margin-bottom: 1rem !important;
}

.directory-card[data-assistant-id] .contact-info li {
    margin-bottom: 0.25rem !important;
    display: flex !important;
    align-items: center !important;
}

.directory-card[data-assistant-id] .contact-info i {
    color: #cf2e2e !important;
    margin-right: 0.5rem !important;
    width: 14px !important;
    text-align: center !important;
    flex-shrink: 0 !important;
}

/* ===== RATING DISPLAY FIXES ===== */
.directory-card[data-assistant-id] .rating-display-container {
    margin-bottom: 1rem !important;
    text-align: right !important;
}

.directory-card[data-assistant-id] .star-rating .bi-star-fill {
    color: #f7bd00 !important;
}

.directory-card[data-assistant-id] .star-rating .bi-star {
    color: #dee2e6 !important;
}

/* ===== BUTTON FIXES ===== */
.directory-card[data-assistant-id] .btn {
    border-radius: 6px !important;
    font-weight: 500 !important;
    font-size: 0.85rem !important;
    padding: 0.4rem 0.8rem !important;
    transition: all 0.3s ease !important;
}

.directory-card[data-assistant-id] .rate-assistant-btn,
.directory-card[data-assistant-id] .action-btn {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.directory-card[data-assistant-id] .rate-assistant-btn:hover,
.directory-card[data-assistant-id] .action-btn:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 6px rgba(207, 46, 46, 0.3) !important;
}

/* ===== LIKE BUTTON FIXES ===== */
.directory-card[data-assistant-id] .like-button {
    background: transparent !important;
    border: none !important;
    padding: 0.5rem !important;
    transition: all 0.3s ease !important;
}

.directory-card[data-assistant-id] .like-button.text-danger i {
    color: #cf2e2e !important;
}

.directory-card[data-assistant-id] .like-button.text-secondary i {
    color: #6c757d !important;
}

.directory-card[data-assistant-id] .like-button:hover i {
    transform: scale(1.1) !important;
}

/* ===== RESPONSIVE FIXES ===== */
@media (max-width: 768px) {
    .directory-card[data-assistant-id] {
        padding: 1rem !important;
        margin-bottom: 1rem !important;
    }
    
    .directory-card[data-assistant-id] .logo-container {
        width: 80px !important;
        height: 80px !important;
        min-width: 80px !important;
        min-height: 80px !important;
        max-width: 80px !important;
        max-height: 80px !important;
    }
    
    .directory-card[data-assistant-id] .logo-container img {
        width: 60px !important;
        height: 60px !important;
        max-width: 60px !important;
        max-height: 60px !important;
    }
    
    .directory-card[data-assistant-id] .logo-placeholder {
        font-size: 2rem !important;
    }
    
    .directory-card[data-assistant-id] .row {
        min-height: auto !important;
    }
    
    .directory-card[data-assistant-id] .col-md-2:last-child {
        min-height: auto !important;
        margin-top: 1rem !important;
    }
}

/* ===== LINK WRAPPER FIXES ===== */
.directory-card[data-assistant-id] .directory-item-link-wrapper {
    text-decoration: none !important;
    color: inherit !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
}

.directory-card[data-assistant-id] .directory-item-link-wrapper:hover {
    text-decoration: none !important;
    color: inherit !important;
}

/* ===== TIER BADGE POSITIONING FIXES ===== */
.directory-card[data-assistant-id] .tier-badge {
    position: absolute !important;
    top: 1rem !important;
    left: 1rem !important;
    z-index: 10 !important;
    font-size: 0.7rem !important;
    padding: 0.3rem 0.6rem !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
}
