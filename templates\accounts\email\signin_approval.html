{% extends 'accounts/email/base_email.html' %}

{% block email_title %}Sign-in Verification Required - 24seven{% endblock %}

{% block email_icon %}🔐{% endblock %}

{% block email_heading %}Sign-in Verification Required{% endblock %}

{% block email_subtitle %}Secure your account access{% endblock %}

{% block email_content %}
    <p style="font-size: 18px; margin-bottom: 24px;">
        <strong>Hello {{ user.get_full_name|default:user.username }},</strong>
    </p>

    <p style="font-size: 16px; margin-bottom: 24px;">
        We detected a sign-in attempt to your 24seven account. For security reasons, we need to verify that it's really you.
    </p>

    <div class="info-box">
        <h4 style="margin: 0 0 16px 0; color: #2b6cb0; display: flex; align-items: center;">
            🔍 Sign-in Details
        </h4>
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Username:</strong> {{ user.username }}</li>
            <li><strong>Email:</strong> {{ user.email }}</li>
            <li><strong>Time:</strong> {{ now|date:"F j, Y, g:i a" }}</li>
            <li><strong>IP Address:</strong> {{ request.META.REMOTE_ADDR|default:"Unknown" }}</li>
        </ul>
    </div>

    <p style="font-size: 16px; text-align: center; margin: 32px 0 24px 0;">
        If this was you, click the button below to approve and automatically complete your sign-in:
    </p>

    <div class="button-container">
        <a href="{{ approval_url }}" class="button" style="font-size: 18px; padding: 18px 36px;">
            ✅ Approve & Sign In
        </a>
    </div>

    <div class="error-box">
        <h4 style="margin: 0 0 12px 0; color: #c53030; display: flex; align-items: center;">
            ⏰ Time-Sensitive Security Link
        </h4>
        <p style="margin: 0; font-size: 16px;">
            <strong>This verification link will expire in {{ expiry_hours }} hour{{ expiry_hours|pluralize }}.</strong><br>
            For your security, this link can only be used once.
        </p>
    </div>

    <div style="background-color: #f8fafc; padding: 20px; border-radius: 12px; margin: 24px 0;">
        <h4 style="margin: 0 0 12px 0; color: #4a5568;">Alternative Access</h4>
        <p style="margin: 0 0 8px 0; font-size: 14px; color: #718096;">
            If the button above doesn't work, copy and paste this link into your browser:
        </p>
        <p style="word-break: break-all; font-family: 'Courier New', monospace; background-color: #edf2f7; padding: 12px; border-radius: 6px; font-size: 13px; margin: 0;">
            {{ approval_url }}
        </p>
    </div>

    <div class="warning-box">
        <h4 style="margin: 0 0 16px 0; color: #c05621; display: flex; align-items: center;">
            ⚠️ Important Security Notice
        </h4>
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>One-time use:</strong> This verification link can only be used once and will automatically log you in</li>
            <li><strong>Automatic expiry:</strong> The link will expire automatically for security</li>
            <li><strong>Instant access:</strong> Clicking the link will immediately complete your sign-in - no password required</li>
            <li><strong>Didn't sign in?</strong> If you didn't attempt to sign in, you can safely ignore this email</li>
            <li><strong>Suspicious activity?</strong> Consider changing your password if you suspect unauthorized access</li>
        </ul>
    </div>

    <div class="security-info">
        <h4>🛡️ Account Security Tips</h4>
        <ul>
            <li>Always verify sign-in attempts from unknown devices or locations</li>
            <li>Use a strong, unique password for your 24seven account</li>
            <li>Enable two-factor authentication when available</li>
            <li>Keep your account information up to date</li>
            <li>Report suspicious activity immediately</li>
        </ul>
    </div>

    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 24px; border-radius: 12px; margin: 24px 0; text-align: center;">
        <h3 style="margin: 0 0 16px 0; font-size: 20px;">🔒 Why We Verify Sign-ins</h3>
        <p style="margin: 0; opacity: 0.9; font-size: 16px;">
            This extra security step helps protect your account and sensitive data from unauthorized access. We take your security seriously.
        </p>
    </div>

    <div style="text-align: center; margin-top: 32px; padding: 20px; background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-radius: 12px;">
        <p style="margin: 0; font-size: 14px; color: #718096;">
            Need help with account security?<br>
            <a href="mailto:{{ site_config.support_email|default:'<EMAIL>' }}" style="color: #3182ce; text-decoration: none; font-weight: 600;">{{ site_config.support_email|default:'<EMAIL>' }}</a>
            or visit our <a href="{{ site_config.support_url|default:site_config.contact_url|default:'/support/' }}" style="color: #3182ce; text-decoration: none; font-weight: 600;">support center</a>
        </p>
    </div>
{% endblock %}
