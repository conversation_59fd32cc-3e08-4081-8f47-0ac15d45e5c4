/**
 * Glass Notifications CSS
 * Adds a modern glass-like appearance to notification alerts
 */

/* Base glass notification style */
.glass-notification {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    color: #ffffff !important;
    padding: 1rem 1.25rem !important;
    margin-bottom: 1.5rem !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    /* Match container width */
    width: 100% !important;
    max-width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    transform: none !important;
}

/* Glass notification warning style (yellow/gold) */
.glass-notification.notification-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.25), rgba(255, 152, 0, 0.2)) !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
    box-shadow: 0 8px 32px rgba(255, 193, 7, 0.15), 0 0 0 1px rgba(255, 193, 7, 0.05) !important;
}

/* Glass notification warning style for dark mode */
[data-theme="dark"] .glass-notification.notification-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.15)) !important;
    border: 1px solid rgba(255, 193, 7, 0.25) !important;
    box-shadow: 0 8px 32px rgba(255, 193, 7, 0.1), 0 0 0 1px rgba(255, 193, 7, 0.1) !important;
    color: rgba(255, 255, 255, 0.95) !important;
}

/* Icon styling */
.glass-notification .notification-icon {
    font-size: 1.5rem !important;
    margin-right: 1rem !important;
    color: rgba(255, 193, 7, 0.9) !important;
    text-shadow: 0 0 10px rgba(255, 193, 7, 0.5) !important;
}

/* Text styling */
.glass-notification .notification-text {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 400 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    flex: 1 !important;
}

/* Strong text styling */
.glass-notification .notification-text strong {
    font-weight: 600 !important;
    color: #ffffff !important;
}

/* Hover effect */
.glass-notification:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 40px rgba(255, 193, 7, 0.2), 0 0 0 1px rgba(255, 193, 7, 0.1) !important;
}

/* Glow effect */
.glass-notification::before {
    content: "" !important;
    position: absolute !important;
    top: -10px !important;
    left: -10px !important;
    right: -10px !important;
    bottom: -10px !important;
    z-index: -1 !important;
    background: radial-gradient(circle at center, rgba(255, 193, 7, 0.2) 0%, rgba(255, 193, 7, 0) 70%) !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.glass-notification:hover::before {
    opacity: 1 !important;
}

/* Pending items list styling */
.glass-notification .pending-items-list {
    display: inline-block;
    margin-top: 0.25rem;
}

.glass-notification .pending-item {
    display: inline-block;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.95) !important;
}

/* Animation for new notifications */
@keyframes notificationPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

.glass-notification.new-notification {
    animation: notificationPulse 2s infinite;
}

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
    .glass-notification {
        width: 100% !important;
        max-width: 100% !important;
        transform: none !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

/* Responsive adjustments for tablet devices */
@media (max-width: 992px) and (min-width: 769px) {
    .glass-notification {
        width: 100% !important;
        max-width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        transform: none !important;
    }
}

/* Homepage specific notification styling */
#pending-notifications-container.container .glass-notification {
    /* Homepage notifications should span the full hero content width */
    width: 100% !important;
    max-width: 100% !important;
}

/* Progressive homepage notification width - match full hero content */
@media (min-width: 992px) {
    #pending-notifications-container.container {
        /* Make the container itself span full width */
        max-width: none !important;
        width: 100% !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
    }

    #pending-notifications-container.container .row {
        /* Remove row margins */
        margin-left: 0 !important;
        margin-right: 0 !important;
        max-width: 1140px !important;
        margin: 0 auto !important;
    }

    #pending-notifications-container.container .col-lg-6 {
        /* Make the column span full width */
        flex: 0 0 100% !important;
        max-width: 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    #pending-notifications-container.container .glass-notification {
        /* Notification spans the full row width */
        width: 100% !important;
        max-width: 100% !important;
    }
}

@media (min-width: 1200px) {
    #pending-notifications-container.container .row {
        max-width: 1140px !important;
    }
}

@media (min-width: 1400px) {
    #pending-notifications-container.container .row {
        max-width: 1320px !important;
    }
}

/* Dashboard specific notification styling */
div[id="pending-notifications-container"]:not(.container) .glass-notification {
    /* Dashboard notifications should match the dashboard container width exactly */
    width: 100% !important;
    max-width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}
