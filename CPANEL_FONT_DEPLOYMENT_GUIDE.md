# cPanel Font Deployment Guide

This guide provides comprehensive instructions for installing and managing fonts on Linux when deploying your Django application to cPanel.

## Overview

Font installation on cPanel Linux servers can be challenging due to limited system access. This guide provides multiple approaches to ensure your application has access to the fonts it needs.

## Quick Setup

### 1. Run the Font Setup Script

```bash
# Run the complete font setup
python setup_fonts_for_cpanel.py

# Or run individual components
python cpanel_font_manager.py check    # Check system fonts
python cpanel_font_manager.py install  # Install fonts
python cpanel_font_manager.py test     # Test font loading
```

### 2. Install Fonts System-Wide (if possible)

```bash
# Make the installation script executable and run it
chmod +x install_fonts.sh
./install_fonts.sh
```

### 3. Collect Static Files

```bash
python manage.py collectstatic --noinput
```

### 4. Test Font Loading

```bash
python test_fonts.py
```

## Font Installation Methods

### Method 1: Project-Level Fonts (Recommended)

This method includes fonts as part of your project's static files:

1. **Create font directories:**
   ```
   static/fonts/system/     # System fonts (Liberation, DejaVu)
   static/fonts/custom/     # Custom fonts
   fonts/                   # Alternative location
   ```

2. **Download and install free fonts:**
   - Liberation Fonts (Arial alternative)
   - DejaVu Fonts (comprehensive Unicode support)
   - FreeFonts (GNU fonts)

3. **Include fonts in CSS:**
   ```css
   @font-face {
       font-family: 'Liberation Sans';
       src: url('../fonts/system/LiberationSans-Regular.ttf') format('truetype');
       font-display: swap;
   }
   ```

### Method 2: User-Level Font Installation

Install fonts in user directories:

```bash
# Create user font directories
mkdir -p ~/.fonts
mkdir -p ~/fonts
mkdir -p ~/public_html/fonts

# Copy fonts to user directories
cp static/fonts/system/*.ttf ~/.fonts/
cp static/fonts/system/*.ttf ~/fonts/
cp static/fonts/system/*.ttf ~/public_html/fonts/

# Update font cache (if available)
fc-cache -f -v ~/.fonts
```

### Method 3: System Package Installation (if sudo available)

```bash
# Install font packages (requires sudo)
sudo apt-get update
sudo apt-get install -y fonts-liberation fonts-dejavu-core fonts-freefont-ttf

# Or on CentOS/RHEL
sudo yum install liberation-fonts dejavu-sans-fonts gnu-free-fonts
```

## Font Verification

### Check Available Fonts

```bash
# List available fonts (if fc-list is available)
fc-list | grep -E "(Liberation|DejaVu|Free)"

# Check font directories
ls -la ~/.fonts/
ls -la ~/fonts/
ls -la static/fonts/system/
```

### Test Font Loading with Python

```python
from PIL import Image, ImageDraw, ImageFont
import os

# Test font loading
font_paths = [
    "~/.fonts/LiberationSans-Regular.ttf",
    "~/fonts/DejaVuSans.ttf",
    "static/fonts/system/LiberationSans-Regular.ttf"
]

for font_path in font_paths:
    expanded_path = os.path.expanduser(font_path)
    if os.path.exists(expanded_path):
        try:
            font = ImageFont.truetype(expanded_path, 24)
            print(f"✓ Successfully loaded: {font_path}")
        except Exception as e:
            print(f"✗ Failed to load {font_path}: {e}")
    else:
        print(f"✗ Not found: {font_path}")
```

## Troubleshooting

### Common Issues

1. **Fonts not loading in QR codes:**
   - Check file permissions (644 for files, 755 for directories)
   - Verify font paths in QR generator
   - Test with the font test script

2. **Permission denied errors:**
   ```bash
   # Fix permissions
   find static/fonts/ -type d -exec chmod 755 {} \;
   find static/fonts/ -type f -exec chmod 644 {} \;
   find ~/.fonts/ -type f -exec chmod 644 {} \;
   ```

3. **Font cache issues:**
   ```bash
   # Clear and rebuild font cache
   rm -rf ~/.fontconfig/
   fc-cache -f -v ~/.fonts
   ```

### Debugging Font Issues

1. **Enable font debugging:**
   ```python
   # In your QR generator or font loading code
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **Check font loading paths:**
   ```bash
   python cpanel_font_manager.py check
   ```

3. **Test specific fonts:**
   ```bash
   python test_fonts.py
   ```

## Best Practices

### 1. Font Fallback Strategy

Always implement a font fallback strategy:

```python
font_names = [
    "Liberation Sans",
    "DejaVu Sans", 
    "FreeSans",
    "Arial",
    "sans-serif"
]
```

### 2. Font Optimization

- Use WOFF2 format for web fonts (smaller file size)
- Include only necessary font weights and styles
- Use `font-display: swap` for better performance

### 3. Caching Strategy

- Include fonts in static file collection
- Set appropriate cache headers for font files
- Use CDN for font delivery if possible

## File Permissions

Ensure correct permissions for all font files:

```bash
# Set directory permissions
chmod 755 static/fonts/
chmod 755 ~/.fonts/
chmod 755 ~/fonts/

# Set file permissions
chmod 644 static/fonts/system/*.ttf
chmod 644 ~/.fonts/*.ttf
chmod 644 ~/fonts/*.ttf
```

## Integration with Django

### Update Settings

```python
# In settings.py
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Font-specific settings
FONT_DIRS = [
    BASE_DIR / 'static' / 'fonts',
    BASE_DIR / 'fonts',
    os.path.expanduser('~/.fonts'),
    os.path.expanduser('~/fonts'),
]
```

### Template Integration

```html
<!-- In base template -->
<link href="{% static 'css/fonts.css' %}" rel="stylesheet">
```

## Monitoring and Maintenance

### Regular Checks

1. **Font availability check:**
   ```bash
   python cpanel_font_manager.py check
   ```

2. **QR code generation test:**
   ```bash
   python test_fonts.py
   ```

3. **Static file collection:**
   ```bash
   python manage.py collectstatic --noinput
   ```

### Automated Deployment

Include font setup in your deployment script:

```bash
#!/bin/bash
# deployment.sh

# Setup fonts
python setup_fonts_for_cpanel.py

# Collect static files
python manage.py collectstatic --noinput

# Test fonts
python test_fonts.py

# Restart application
touch passenger_wsgi.py
```

## Support and Resources

- **Liberation Fonts:** https://github.com/liberationfonts/liberation-fonts
- **DejaVu Fonts:** https://dejavu-fonts.github.io/
- **GNU FreeFont:** https://www.gnu.org/software/freefont/

For additional support, check the generated font test images and error logs to diagnose specific font loading issues.
