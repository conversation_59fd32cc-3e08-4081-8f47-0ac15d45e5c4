/*
 * NUP Company Detail Page Styling
 * Applies NUP party colors and completes the design
 * Overrides non-NUP colors and fixes incomplete styling
 */

/* ===== NUP OFFICIAL COLOR VARIABLES (COPIED FROM HOME PAGE) ===== */
/* Based on nupuganda.org design system with new dark accent */
:root {
    /* Core Official Colors - Updated with new dark accent */
    --nup-primary-red: #cf2e2e;        /* NUP Vivid Red - Primary accent */
    --nup-secondary-gold: #f7bd00;     /* NUP Strong Yellow/Gold - Secondary accent */
    --nup-pure-white: #ffffff;         /* Neutral White - Primary background */
    --nup-light-gray: #f7f7f7;         /* Light Gray - Secondary background */
    --nup-dark-charcoal: #242424;      /* Dark Charcoal - Main text */
    --nup-medium-gray: #797979;        /* Medium Gray - Secondary text */
    --nup-dark-accent: #252638;        /* New Dark Blue-Gray - Replaces all blues/purples */

    /* Interactive states */
    --nup-red-hover: #b82626;
    --nup-gold-hover: #e6aa00;
    --nup-dark-accent-hover: #1e1f2e;

    /* Utility colors */
    --nup-border-light: #e5e5e5;
    --nup-shadow: rgba(37, 38, 56, 0.1);
    --nup-shadow-strong: rgba(37, 38, 56, 0.15);
    --nup-light-red: rgba(207, 46, 46, 0.1);
    --nup-border-red: rgba(207, 46, 46, 0.3);
}

/* ===== NUP OFFICIAL BUTTONS (COPIED FROM HOME PAGE) ===== */

/* Primary CTA button - Using NUP dark accent (blue) */
.btn-primary,
button.btn-primary,
input[type="submit"].btn-primary,
.btn-nup-primary {
    background-color: var(--nup-dark-accent) !important;
    border: 2px solid var(--nup-dark-accent) !important;
    color: var(--nup-pure-white) !important;
    font-family: 'Merriweather Sans', 'Montserrat', sans-serif !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    padding: 12px 24px !important;
    border-radius: 4px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
    cursor: pointer !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-nup-primary:hover {
    background-color: var(--nup-dark-accent-hover) !important;
    border-color: var(--nup-dark-accent-hover) !important;
    color: var(--nup-pure-white) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

/* Secondary button - White with red border */
.btn-outline-secondary,
.btn-nup-secondary {
    background-color: var(--nup-pure-white) !important;
    border: 2px solid var(--nup-primary-red) !important;
    color: var(--nup-primary-red) !important;
    font-family: 'Merriweather Sans', 'Montserrat', sans-serif !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    padding: 12px 24px !important;
    border-radius: 4px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
    cursor: pointer !important;
}

.btn-outline-secondary:hover,
.btn-outline-secondary:focus,
.btn-outline-secondary:active,
.btn-nup-secondary:hover {
    background-color: var(--nup-primary-red) !important;
    border-color: var(--nup-primary-red) !important;
    color: var(--nup-pure-white) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== NUP OFFICIAL SECTION BACKGROUNDS (COPIED FROM HOME PAGE) ===== */

/* Primary white background - Dominant usage (60% of sections) */
.nup-section-white,
body,
.container,
.container-fluid {
    background-color: var(--nup-pure-white) !important;
    color: var(--nup-dark-charcoal) !important;
}

/* Light gray background - Secondary sections (20% usage) */
.nup-section-light,
.bg-light,
form.bg-light {
    background-color: var(--nup-light-gray) !important;
    color: var(--nup-dark-charcoal) !important;
}

/* Enhanced card styling with NUP theme */
.card {
    border: 1px solid var(--nup-border-light) !important;
    border-radius: 12px !important;
    box-shadow: var(--nup-shadow) !important;
    background-color: var(--nup-pure-white) !important;
}

/* Card header with NUP theme */
.card-header {
    background-color: var(--nup-light-gray) !important;
    border-bottom: 1px solid var(--nup-border-light) !important;
    border-radius: 12px 12px 0 0 !important;
    color: var(--nup-dark-charcoal) !important;
}

/* ===== COMPANY LOGO STYLING ===== */

.company-logo-container {
    width: 72px !important;
    height: 72px !important;
    min-width: 72px !important;
    min-height: 72px !important;
    max-width: 72px !important;
    max-height: 72px !important;
    margin-right: 0.75rem !important;
    padding: 2px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
    border-radius: 6px !important;
    background-color: var(--nup-light-gray) !important;
    flex-shrink: 0 !important;
    border: 1px solid var(--nup-border-light) !important;
}

.company-header-logo {
    width: 66px !important;
    height: 66px !important;
    max-width: 66px !important;
    max-height: 66px !important;
    object-fit: contain !important;
}

.company-logo-fallback {
    font-size: 2rem !important;
    color: var(--nup-primary-red) !important;
}

/* ===== FORM STYLING ===== */

/* Search/Filter form with NUP theme */
form.bg-light {
    background-color: var(--nup-light-gray) !important;
    border: 1px solid var(--nup-border-light) !important;
    border-radius: 8px !important;
}

/* Form controls with NUP accents */
.form-control:focus {
    border-color: var(--nup-primary-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
}

/* ===== FOLDER FILTER BUTTONS ===== */

.folder-filter-buttons .btn-link {
    color: var(--nup-primary-red) !important;
    text-decoration: none !important;
    font-weight: 600 !important;
}

.folder-filter-buttons .btn-link:hover {
    color: var(--nup-dark-charcoal) !important;
    background-color: var(--nup-light-gray) !important;
    border-radius: 4px !important;
}

.folder-filter-buttons .btn-link.active {
    color: var(--nup-pure-white) !important;
    background-color: var(--nup-primary-red) !important;
    border-radius: 4px !important;
    font-weight: bold !important;
}

/* ===== ASSISTANT LIST STYLING ===== */

/* Assistant list items with NUP theme */
.list-group-item {
    border: 1px solid var(--nup-border-light) !important;
    border-radius: 8px !important;
    margin-bottom: 0.5rem !important;
    background-color: var(--nup-pure-white) !important;
    transition: all 0.3s ease !important;
    color: var(--nup-dark-charcoal) !important;
}

.list-group-item:hover {
    background-color: var(--nup-light-gray) !important;
    border-color: var(--nup-primary-red) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--nup-shadow-strong) !important;
}

.list-group-item-action:hover {
    background-color: var(--nup-light-gray) !important;
    color: var(--nup-dark-charcoal) !important;
}

/* ===== CONTACT INFO STYLING ===== */

/* Contact info with NUP accents */
.contact-info i {
    color: var(--nup-primary-red) !important;
}

.contact-info a {
    color: var(--nup-dark-charcoal) !important;
    text-decoration: none !important;
}

.contact-info a:hover {
    color: var(--nup-primary-red) !important;
    text-decoration: underline !important;
}

/* ===== BADGE STYLING ===== */

/* Convert secondary badges to NUP theme */
.badge.bg-secondary {
    background-color: var(--nup-primary-red) !important;
    color: var(--nup-pure-white) !important;
}

.badge.bg-light {
    background-color: var(--nup-light-gray) !important;
    color: var(--nup-dark-charcoal) !important;
    border: 1px solid var(--nup-border-light) !important;
}

/* ===== RATING DISPLAY ===== */

/* Rating stars with NUP colors */
.rating-display-container .text-warning {
    color: var(--nup-secondary-gold) !important;
}

/* ===== LIKE BUTTON STYLING ===== */

/* Like button with NUP colors */
.like-button {
    background: transparent !important;
    border: none !important;
    padding: 4px !important;
}

.like-button.text-danger {
    color: var(--nup-primary-red) !important;
}

.like-button.text-secondary {
    color: var(--nup-medium-gray) !important;
}

.like-button:hover {
    color: var(--nup-primary-red) !important;
}

/* ===== HEADINGS WITH NUP THEME ===== */

h2.h4, h5 {
    color: var(--nup-dark-charcoal) !important;
    font-weight: bold !important;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */

@media (max-width: 768px) {
    .company-logo-container {
        width: 60px !important;
        height: 60px !important;
        min-width: 60px !important;
        min-height: 60px !important;
        max-width: 60px !important;
        max-height: 60px !important;
    }
    
    .company-header-logo {
        width: 54px !important;
        height: 54px !important;
        max-width: 54px !important;
        max-height: 54px !important;
    }
    
    .company-logo-fallback {
        font-size: 1.5rem !important;
    }
}

/* ===== ALERT STYLING ===== */

.alert-info {
    background-color: var(--nup-light-red) !important;
    border-color: var(--nup-border-red) !important;
    color: var(--nup-dark) !important;
}

/* ===== BORDER IMPROVEMENTS ===== */

.border-start {
    border-left: 2px solid var(--nup-border-red) !important;
}

.border-bottom {
    border-bottom: 1px solid var(--nup-border-red) !important;
}

.border {
    border: 1px solid var(--nup-border-red) !important;
}

/* ===== COMPLETE DESIGN POLISH ===== */

/* Ensure all text is readable */
.text-muted {
    color: #6c757d !important;
}

/* Enhanced shadows for depth */
.shadow-sm {
    box-shadow: 0 2px 8px rgba(207, 46, 46, 0.1) !important;
}

/* Smooth transitions for all interactive elements */
button, .btn, a, .list-group-item {
    transition: all 0.3s ease !important;
}

/* ===== SPECIFIC BUTTON FIXES ===== */

/* Fix any remaining red buttons that should use NUP colors */
.btn-danger,
button.btn-danger {
    background-color: var(--nup-primary-red) !important;
    border-color: var(--nup-primary-red) !important;
    color: var(--nup-pure-white) !important;
}

.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active {
    background-color: var(--nup-red-hover) !important;
    border-color: var(--nup-red-hover) !important;
    color: var(--nup-pure-white) !important;
}

/* Fix any success buttons to use NUP colors */
.btn-success {
    background-color: var(--nup-primary-red) !important;
    border-color: var(--nup-primary-red) !important;
    color: var(--nup-pure-white) !important;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active {
    background-color: var(--nup-red-hover) !important;
    border-color: var(--nup-red-hover) !important;
    color: var(--nup-pure-white) !important;
}

/* ===== COMPLETE PAGE BACKGROUND ===== */

/* Ensure page has proper NUP-themed background */
body.nup-section-white,
.container-fluid,
.container {
    background-color: var(--nup-pure-white) !important;
    background-image: none !important;
}

/* ===== FINAL POLISH ===== */

/* Remove any blue/purple colors that might remain */
.text-primary {
    color: var(--nup-primary-red) !important;
}

.bg-primary {
    background-color: var(--nup-primary-red) !important;
}

.border-primary {
    border-color: var(--nup-primary-red) !important;
}

/* ===== NUP TYPOGRAPHY (FROM HOME PAGE) ===== */

/* Set base font for the entire document */
html, body {
    font-family: 'Open Sans', 'Lato', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    line-height: 1.5 !important;
    color: var(--nup-dark-charcoal) !important;
}

/* Text color utilities */
.text-muted {
    color: var(--nup-medium-gray) !important;
}

.text-dark {
    color: var(--nup-dark-charcoal) !important;
}

/* ===== BORDER IMPROVEMENTS ===== */

.border-start {
    border-left: 2px solid var(--nup-border-light) !important;
}

.border-bottom {
    border-bottom: 1px solid var(--nup-border-light) !important;
}

.border {
    border: 1px solid var(--nup-border-light) !important;
}

/* ===== ALERT STYLING ===== */

.alert-info {
    background-color: var(--nup-light-gray) !important;
    border-color: var(--nup-border-light) !important;
    color: var(--nup-dark-charcoal) !important;
}

/* Enhanced shadows for depth */
.shadow-sm {
    box-shadow: var(--nup-shadow) !important;
}
