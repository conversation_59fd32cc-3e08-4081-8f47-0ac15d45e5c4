/*
 * NUP Company Detail Page Styling
 * Applies NUP party colors and completes the design
 * Overrides non-NUP colors and fixes incomplete styling
 */

/* ===== NUP COLOR VARIABLES ===== */
:root {
    --nup-red: #cf2e2e;
    --nup-yellow: #f7bd00;
    --nup-white: #ffffff;
    --nup-dark: #242424;
    --nup-light-red: rgba(207, 46, 46, 0.1);
    --nup-border-red: rgba(207, 46, 46, 0.3);
}

/* ===== OVERRIDE NON-NUP BUTTON COLORS ===== */

/* Convert all primary buttons to NUP red */
.btn-primary,
button.btn-primary,
input[type="submit"].btn-primary {
    background-color: var(--nup-red) !important;
    border-color: var(--nup-red) !important;
    color: var(--nup-white) !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: var(--nup-dark) !important;
    border-color: var(--nup-dark) !important;
    color: var(--nup-white) !important;
}

/* Convert outline-secondary buttons to NUP theme */
.btn-outline-secondary {
    border-color: var(--nup-border-red) !important;
    color: var(--nup-red) !important;
    background-color: transparent !important;
}

.btn-outline-secondary:hover,
.btn-outline-secondary:focus,
.btn-outline-secondary:active {
    background-color: var(--nup-red) !important;
    border-color: var(--nup-red) !important;
    color: var(--nup-white) !important;
}

/* ===== COMPLETE THE CARD DESIGN ===== */

/* Enhanced card styling with NUP theme */
.card {
    border: 1px solid var(--nup-border-red) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.1) !important;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.98) 0%, 
        rgba(248, 249, 250, 0.95) 100%) !important;
}

/* Card header with NUP theme */
.card-header {
    background: linear-gradient(135deg, 
        var(--nup-light-red) 0%, 
        rgba(255, 255, 255, 0.95) 100%) !important;
    border-bottom: 2px solid var(--nup-border-red) !important;
    border-radius: 12px 12px 0 0 !important;
}

/* ===== COMPANY LOGO STYLING ===== */

.company-logo-container {
    width: 72px !important;
    height: 72px !important;
    min-width: 72px !important;
    min-height: 72px !important;
    max-width: 72px !important;
    max-height: 72px !important;
    margin-right: 0.75rem !important;
    padding: 2px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
    border-radius: 6px !important;
    background-color: #f8f9fa !important;
    flex-shrink: 0 !important;
    border: 1px solid var(--nup-border-red) !important;
}

.company-header-logo {
    width: 66px !important;
    height: 66px !important;
    max-width: 66px !important;
    max-height: 66px !important;
    object-fit: contain !important;
}

.company-logo-fallback {
    font-size: 2rem !important;
    color: var(--nup-red) !important;
}

/* ===== FORM STYLING ===== */

/* Search/Filter form with NUP theme */
form.bg-light {
    background: linear-gradient(135deg, 
        var(--nup-light-red) 0%, 
        rgba(255, 255, 255, 0.95) 100%) !important;
    border: 1px solid var(--nup-border-red) !important;
    border-radius: 8px !important;
}

/* Form controls with NUP accents */
.form-control:focus {
    border-color: var(--nup-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
}

/* ===== FOLDER FILTER BUTTONS ===== */

.folder-filter-buttons .btn-link {
    color: var(--nup-red) !important;
    text-decoration: none !important;
}

.folder-filter-buttons .btn-link:hover {
    color: var(--nup-dark) !important;
    background-color: var(--nup-light-red) !important;
    border-radius: 4px !important;
}

.folder-filter-buttons .btn-link.active {
    color: var(--nup-white) !important;
    background-color: var(--nup-red) !important;
    border-radius: 4px !important;
    font-weight: bold !important;
}

/* ===== ASSISTANT LIST STYLING ===== */

/* Assistant list items with NUP theme */
.list-group-item {
    border: 1px solid var(--nup-border-red) !important;
    border-radius: 8px !important;
    margin-bottom: 0.5rem !important;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.98) 0%, 
        rgba(248, 249, 250, 0.95) 100%) !important;
    transition: all 0.3s ease !important;
}

.list-group-item:hover {
    background: linear-gradient(135deg, 
        var(--nup-light-red) 0%, 
        rgba(255, 255, 255, 0.98) 100%) !important;
    border-color: var(--nup-red) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.15) !important;
}

.list-group-item-action:hover {
    background: linear-gradient(135deg, 
        var(--nup-light-red) 0%, 
        rgba(255, 255, 255, 0.98) 100%) !important;
}

/* ===== CONTACT INFO STYLING ===== */

/* Contact info with NUP accents */
.contact-info i {
    color: var(--nup-red) !important;
}

.contact-info a {
    color: var(--nup-dark) !important;
    text-decoration: none !important;
}

.contact-info a:hover {
    color: var(--nup-red) !important;
    text-decoration: underline !important;
}

/* ===== BADGE STYLING ===== */

/* Convert secondary badges to NUP theme */
.badge.bg-secondary {
    background-color: var(--nup-red) !important;
    color: var(--nup-white) !important;
}

.badge.bg-light {
    background-color: var(--nup-light-red) !important;
    color: var(--nup-dark) !important;
    border: 1px solid var(--nup-border-red) !important;
}

/* ===== RATING DISPLAY ===== */

/* Rating stars with NUP colors */
.rating-display-container .text-warning {
    color: var(--nup-yellow) !important;
}

/* ===== LIKE BUTTON STYLING ===== */

/* Like button with NUP colors */
.like-button {
    background: transparent !important;
    border: none !important;
    padding: 4px !important;
}

.like-button.text-danger {
    color: var(--nup-red) !important;
}

.like-button.text-secondary {
    color: #6c757d !important;
}

.like-button:hover {
    color: var(--nup-red) !important;
}

/* ===== HEADINGS WITH NUP THEME ===== */

h2.h4, h5 {
    color: var(--nup-dark) !important;
    font-weight: bold !important;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */

@media (max-width: 768px) {
    .company-logo-container {
        width: 60px !important;
        height: 60px !important;
        min-width: 60px !important;
        min-height: 60px !important;
        max-width: 60px !important;
        max-height: 60px !important;
    }
    
    .company-header-logo {
        width: 54px !important;
        height: 54px !important;
        max-width: 54px !important;
        max-height: 54px !important;
    }
    
    .company-logo-fallback {
        font-size: 1.5rem !important;
    }
}

/* ===== ALERT STYLING ===== */

.alert-info {
    background-color: var(--nup-light-red) !important;
    border-color: var(--nup-border-red) !important;
    color: var(--nup-dark) !important;
}

/* ===== BORDER IMPROVEMENTS ===== */

.border-start {
    border-left: 2px solid var(--nup-border-red) !important;
}

.border-bottom {
    border-bottom: 1px solid var(--nup-border-red) !important;
}

.border {
    border: 1px solid var(--nup-border-red) !important;
}

/* ===== COMPLETE DESIGN POLISH ===== */

/* Ensure all text is readable */
.text-muted {
    color: #6c757d !important;
}

/* Enhanced shadows for depth */
.shadow-sm {
    box-shadow: 0 2px 8px rgba(207, 46, 46, 0.1) !important;
}

/* Smooth transitions for all interactive elements */
button, .btn, a, .list-group-item {
    transition: all 0.3s ease !important;
}

/* ===== SPECIFIC BUTTON FIXES ===== */

/* Fix any remaining red buttons that should use NUP colors */
.btn-danger,
button.btn-danger {
    background-color: var(--nup-red) !important;
    border-color: var(--nup-red) !important;
    color: var(--nup-white) !important;
}

.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active {
    background-color: var(--nup-dark) !important;
    border-color: var(--nup-dark) !important;
    color: var(--nup-white) !important;
}

/* Fix any success buttons to use NUP colors */
.btn-success {
    background-color: var(--nup-red) !important;
    border-color: var(--nup-red) !important;
    color: var(--nup-white) !important;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active {
    background-color: var(--nup-dark) !important;
    border-color: var(--nup-dark) !important;
    color: var(--nup-white) !important;
}

/* ===== COMPLETE PAGE BACKGROUND ===== */

/* Ensure page has proper NUP-themed background */
body.nup-section-white,
.container-fluid,
.container {
    background-color: var(--nup-white) !important;
    background-image: none !important;
}

/* ===== FINAL POLISH ===== */

/* Remove any blue/purple colors that might remain */
.text-primary {
    color: var(--nup-red) !important;
}

.bg-primary {
    background-color: var(--nup-red) !important;
}

.border-primary {
    border-color: var(--nup-red) !important;
}
