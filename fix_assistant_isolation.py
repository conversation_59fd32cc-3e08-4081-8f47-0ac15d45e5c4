#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to fix the assistant data isolation issue by running migrations
and testing that changes to one assistant don't affect others.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.core.management import call_command
from assistants.models import Assistant
from directory.models import AssistantListing, CompanyListing


def run_migrations():
    """Run the migrations to fix mutable defaults."""
    print("Running migrations to fix mutable default values...")
    
    try:
        # Run assistants app migration
        call_command('migrate', 'assistants', '0002_fix_mutable_defaults', verbosity=2)
        print("✓ Assistants migration completed successfully")
        
        # Run directory app migration
        call_command('migrate', 'directory', '0002_fix_mutable_defaults', verbosity=2)
        print("✓ Directory migration completed successfully")
        
    except Exception as e:
        print(f"✗ Migration failed: {e}")
        return False
    
    return True


def test_assistant_isolation():
    """Test that changes to one assistant don't affect others."""
    print("\nTesting assistant data isolation...")
    
    # Get or create test assistants
    assistants = list(Assistant.objects.all()[:3])
    
    if len(assistants) < 2:
        print("Need at least 2 assistants to test isolation. Creating test assistants...")
        from accounts.models import Company
        
        # Get or create a test company
        company = Company.objects.first()
        if not company:
            print("No company found. Please create a company first.")
            return False
        
        # Create test assistants
        for i in range(2):
            assistant = Assistant.objects.create(
                name=f"Test Assistant {i+1}",
                company=company,
                assistant_type=Assistant.TYPE_GENERAL,
                model=Assistant.MODEL_GPT35,
                greeting_message=f"Hello from Assistant {i+1}",
                system_prompt=f"You are test assistant {i+1}",
            )
            assistants.append(assistant)
    
    # Test 1: Modify greeting_message of first assistant
    assistant1 = assistants[0]
    assistant2 = assistants[1]
    
    original_greeting1 = assistant1.greeting_message
    original_greeting2 = assistant2.greeting_message
    
    print(f"Assistant 1 original greeting: '{original_greeting1}'")
    print(f"Assistant 2 original greeting: '{original_greeting2}'")
    
    # Change assistant 1's greeting
    new_greeting = "This is a test greeting message for isolation testing"
    assistant1.greeting_message = new_greeting
    assistant1.save()
    
    # Refresh assistant 2 from database
    assistant2.refresh_from_db()
    
    print(f"Assistant 1 new greeting: '{assistant1.greeting_message}'")
    print(f"Assistant 2 greeting after change: '{assistant2.greeting_message}'")
    
    # Check if assistant 2's greeting changed (it shouldn't)
    if assistant2.greeting_message == new_greeting:
        print("✗ FAILED: Assistant 2's greeting was affected by changes to Assistant 1")
        return False
    elif assistant2.greeting_message == original_greeting2:
        print("✓ PASSED: Assistant 2's greeting remained unchanged")
    else:
        print(f"? UNEXPECTED: Assistant 2's greeting changed to something else: '{assistant2.greeting_message}'")
    
    # Test 2: Modify website_data of first assistant
    print("\nTesting website_data isolation...")
    
    original_data1 = assistant1.website_data.copy() if assistant1.website_data else {}
    original_data2 = assistant2.website_data.copy() if assistant2.website_data else {}
    
    # Add test data to assistant 1
    test_data = {"test_key": "test_value", "isolation_test": True}
    assistant1.website_data.update(test_data)
    assistant1.save()
    
    # Refresh assistant 2 from database
    assistant2.refresh_from_db()
    
    # Check if assistant 2's data was affected
    if "isolation_test" in assistant2.website_data:
        print("✗ FAILED: Assistant 2's website_data was affected by changes to Assistant 1")
        return False
    else:
        print("✓ PASSED: Assistant 2's website_data remained unchanged")
    
    # Test 3: Modify saved_suggestions of first assistant
    print("\nTesting saved_suggestions isolation...")
    
    original_suggestions1 = assistant1.saved_suggestions.copy() if assistant1.saved_suggestions else []
    original_suggestions2 = assistant2.saved_suggestions.copy() if assistant2.saved_suggestions else []
    
    # Add test suggestion to assistant 1
    test_suggestion = "What is the isolation test?"
    assistant1.saved_suggestions.append(test_suggestion)
    assistant1.save()
    
    # Refresh assistant 2 from database
    assistant2.refresh_from_db()
    
    # Check if assistant 2's suggestions were affected
    if test_suggestion in assistant2.saved_suggestions:
        print("✗ FAILED: Assistant 2's saved_suggestions was affected by changes to Assistant 1")
        return False
    else:
        print("✓ PASSED: Assistant 2's saved_suggestions remained unchanged")
    
    print("\n✓ All isolation tests passed!")
    return True


def main():
    """Main function to run the fix and tests."""
    print("=== Assistant Data Isolation Fix ===")
    print("This script fixes the issue where changing settings in one assistant affects all assistants.")
    print()
    
    # Run migrations
    if not run_migrations():
        print("Migration failed. Exiting.")
        return 1
    
    # Test isolation
    if not test_assistant_isolation():
        print("Isolation tests failed. The issue may not be fully resolved.")
        return 1
    
    print("\n=== Fix completed successfully! ===")
    print("Assistant data isolation has been restored.")
    print("Changes to one assistant will no longer affect other assistants.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
