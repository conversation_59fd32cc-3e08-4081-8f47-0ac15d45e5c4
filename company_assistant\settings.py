from pathlib import Path
import os
import sys
from django.utils.translation import gettext_lazy as _ # Import gettext_lazy

# Ensure UTF-8 encoding for Python environment
if sys.stdout.encoding != 'utf-8':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
if sys.stderr.encoding != 'utf-8':
    import codecs
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # dotenv not installed, skip loading .env file
    pass

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Environment Detection
IN_CPANEL = os.environ.get('CPANEL_ENV') == 'True' or 'PASSENGER_WSGI' in os.environ
IN_PRODUCTION = os.environ.get('PRODUCTION', 'False').lower() == 'true' or IN_CPANEL

# Security Settings
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-change-this-in-production')

# Debug Settings - False in production/cPanel, configurable in development
if IN_PRODUCTION or IN_CPANEL:
    DEBUG = False
else:
    DEBUG = os.environ.get('DEBUG', 'True').lower() != 'false'

# Allowed Hosts - optimized for cPanel
ALLOWED_HOSTS = ['*', '24seven.site', 'www.24seven.site', 'localhost', '127.0.0.1']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites', # Added sites framework
    # Third party apps
    'widget_tweaks',
    'crispy_forms', # Added for form rendering
    'crispy_bootstrap5', # Added template pack for crispy-forms
    'guardian', # Added django-guardian
    'tinymce', # Added django-tinymce
    # Local apps
    'accounts.apps.AccountsConfig',
    'assistants.apps.AssistantsConfig',
    'content.apps.ContentConfig',
    'directory.apps.DirectoryConfig',
    'site_settings', # Simplified app registration
    'superadmin', # Added superadmin app
]

# Add impersonate app conditionally (only in development, not in cPanel)
if not IN_CPANEL:
    INSTALLED_APPS.insert(-1, 'impersonate')  # Insert before superadmin

# Middleware Configuration - Optimized for cPanel
if IN_CPANEL or IN_PRODUCTION:
    # Optimized middleware for production/cPanel
    MIDDLEWARE = [
        'django.middleware.security.SecurityMiddleware',
        'whitenoise.middleware.WhiteNoiseMiddleware',
        'django.contrib.sessions.middleware.SessionMiddleware',
        'django.middleware.common.CommonMiddleware',
        'django.middleware.csrf.CsrfViewMiddleware',
        'django.contrib.auth.middleware.AuthenticationMiddleware',
        'django.contrib.messages.middleware.MessageMiddleware',
        'django.middleware.clickjacking.XFrameOptionsMiddleware',
    ]
    # Add impersonate middleware only if not in cPanel
    if not IN_CPANEL:
        MIDDLEWARE.insert(-2, 'accounts.impersonate_debug.ImpersonateDebugMiddleware')
        MIDDLEWARE.insert(-2, 'impersonate.middleware.ImpersonateMiddleware')
        MIDDLEWARE.insert(-2, 'accounts.impersonate_fix.ImpersonateFixMiddleware')
        MIDDLEWARE.insert(-2, 'accounts.impersonate_session_fix.ImpersonateSessionFixMiddleware')
        MIDDLEWARE.insert(-2, 'accounts.middleware.ImpersonatePermissionsMiddleware')
else:
    # Full middleware for development
    MIDDLEWARE = [
        'django.middleware.security.SecurityMiddleware',
        'whitenoise.middleware.WhiteNoiseMiddleware',
        'django.contrib.sessions.middleware.SessionMiddleware',
        'django.middleware.common.CommonMiddleware',
        'django.middleware.csrf.CsrfViewMiddleware',
        'django.contrib.auth.middleware.AuthenticationMiddleware',
        'accounts.dashboard_sync_middleware.DashboardSyncMiddleware',  # Dashboard sync
        'accounts.impersonate_debug.ImpersonateDebugMiddleware',
        'impersonate.middleware.ImpersonateMiddleware',
        'accounts.impersonate_fix.ImpersonateFixMiddleware',
        'accounts.impersonate_session_fix.ImpersonateSessionFixMiddleware',
        # 'accounts.unified_session_manager.UnifiedSessionMiddleware',  # Temporarily disabled for impersonation testing
        'accounts.middleware.ImpersonatePermissionsMiddleware',
        'django.contrib.messages.middleware.MessageMiddleware',
        'django.middleware.clickjacking.XFrameOptionsMiddleware',
    ]

# Add performance middleware for production
if not DEBUG:
    MIDDLEWARE.insert(1, 'django.middleware.gzip.GZipMiddleware')

ROOT_URLCONF = 'company_assistant.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates', # Corrected typo
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'accounts.context_processors.company_context', # Added company context processor
                'accounts.context_processors.impersonation_debug_context', # Added impersonation debug context processor
                'assistants.context_processors.community_assistants_context', # Added community assistants context processor
                'site_settings.context_processors.site_configuration', # Added site configuration context processor
                'directory.context_processors.directory_settings_context', # Added directory settings context processor
                'superadmin.context_processors.superadmin_notifications', # Added superadmin notifications context processor
            ],
        },
    },
]

WSGI_APPLICATION = 'company_assistant.wsgi.application'

# Database Configuration - Optimized for cPanel
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'postgres4'),
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'M@kerere1'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
        # Connection optimization for cPanel
        'CONN_MAX_AGE': 300 if IN_CPANEL else 600,  # 5 minutes for cPanel, 10 for dev
        'CONN_HEALTH_CHECKS': True,
        'OPTIONS': {
            'connect_timeout': 5 if IN_CPANEL else 10,
            'client_encoding': 'UTF8',
            'isolation_level': 1,  # READ_COMMITTED
        },
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
LANGUAGES = [
    ('en', _('English')),
    # Add other languages here if needed, e.g.:
    # ('es', _('Spanish')),
    # ('fr', _('French')),
]
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']
# Using Whitenoise for static file serving
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Authentication settings
LOGIN_URL = 'accounts:login'
LOGIN_REDIRECT_URL = 'home' # Changed from 'accounts:dashboard'
LOGOUT_REDIRECT_URL = 'home'

# Email settings (configured for 24seven.site - cPanel settings)
EMAIL_BACKEND = os.getenv('EMAIL_BACKEND', 'django.core.mail.backends.smtp.EmailBackend')
EMAIL_HOST = os.getenv('EMAIL_HOST', 'mail.24seven.site')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', 465))  # SMTP SSL Port
EMAIL_USE_SSL = os.getenv('EMAIL_USE_SSL', 'True').lower() == 'true'  # SSL/TLS enabled
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'False').lower() == 'true'  # TLS disabled (using SSL)
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '24seven <<EMAIL>>')

# Additional email settings for cPanel compatibility
EMAIL_TIMEOUT = 30  # 30 second timeout for email operations
SERVER_EMAIL = DEFAULT_FROM_EMAIL  # Server error emails

# Crispy Forms Settings
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Site ID (Required for django.contrib.sites)
SITE_ID = 1

# Debug toolbar settings removed

# Custom User Model
# AUTH_USER_MODEL = 'accounts.User' # Removed - Using default Django User model

# Authentication Backends
AUTHENTICATION_BACKENDS = (
    'accounts.backends.EmailOrUsernameModelBackend', # Custom backend for email/username login
    'guardian.backends.ObjectPermissionBackend', # Guardian backend for object permissions
)

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'debug.log'),
            'formatter': 'verbose',
        },
        'directory_file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'directory_debug.log'),
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'WARNING',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.utils.autoreload': {
            'handlers': ['file'],
            'level': 'WARNING',
            'propagate': False,
        },
        'directory': {
            'handlers': ['console', 'directory_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
CONTENT_TYPES = ['application/pdf', 'text/plain', 'application/msword',
                 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
MAX_UPLOAD_SIZE = 5242880  # 5MB

# ============================================================================
# LLM PROVIDER CONFIGURATIONS
# ============================================================================
# All LLM configurations are now environment-configurable

# OpenAI settings
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
OPENAI_DEFAULT_MODEL = os.getenv('OPENAI_DEFAULT_MODEL', 'gpt-4')

# Groq settings (for Llama models)
GROQ_API_KEY = os.getenv('GROQ_API_KEY', '')
GROQ_BASE_URL = os.getenv('GROQ_BASE_URL', 'https://api.groq.com/openai/v1')
GROQ_DEFAULT_MODEL = os.getenv('GROQ_DEFAULT_MODEL', 'llama-3.3-70b-versatile')

# Anthropic settings
ANTHROPIC_API_KEY = os.getenv('ANTHROPIC_API_KEY', '')
ANTHROPIC_BASE_URL = os.getenv('ANTHROPIC_BASE_URL', 'https://api.anthropic.com')
ANTHROPIC_DEFAULT_MODEL = os.getenv('ANTHROPIC_DEFAULT_MODEL', 'claude-3-sonnet-20240229')

# Gemini settings
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', '')
GEMINI_BASE_URL = os.getenv('GEMINI_BASE_URL', 'https://generativelanguage.googleapis.com/v1beta/openai/')
GEMINI_DEFAULT_MODEL = os.getenv('GEMINI_DEFAULT_MODEL', 'gemini-2.0-flash')

# Model name configurations (environment-configurable)
MODEL_GPT35 = os.getenv('MODEL_GPT35', 'gpt-3.5-turbo')
MODEL_GPT4 = os.getenv('MODEL_GPT4', 'gpt-4')
MODEL_GPT4_TURBO = os.getenv('MODEL_GPT4_TURBO', 'gpt-4-turbo')
MODEL_CLAUDE = os.getenv('MODEL_CLAUDE', 'claude-3-sonnet-20240229')
MODEL_CLAUDE_INSTANT = os.getenv('MODEL_CLAUDE_INSTANT', 'claude-3-haiku-20240307')
MODEL_GEMINI_FLASH = os.getenv('MODEL_GEMINI_FLASH', 'gemini-2.0-flash')
MODEL_LLAMA_70B = os.getenv('MODEL_LLAMA_70B', 'llama-3.3-70b-versatile')

# Legacy compatibility (deprecated - use specific model environment variables above)
OPENAI_MODEL = GROQ_DEFAULT_MODEL  # For backward compatibility

# QR Code settings
QRCODE_DEFAULTS = {
    'VERSION': 1,
    'ERROR_CORRECTION': 'H',
    'BOX_SIZE': 10,
    'BORDER': 4,
}

# Security settings (explicitly disabled in development)
SECURE_SSL_REDIRECT = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_PRELOAD = False

# CSRF settings - Development friendly
CSRF_COOKIE_HTTPONLY = False  # Allow JavaScript to access the CSRF cookie
CSRF_USE_SESSIONS = False  # Store CSRF token in cookie, not session
CSRF_COOKIE_SAMESITE = 'Lax'  # Allow CSRF cookie to be sent in same-site requests
CSRF_COOKIE_SECURE = False  # Allow CSRF cookie over HTTP in development
CSRF_TRUSTED_ORIGINS = ['http://localhost:8000', 'http://127.0.0.1:8000', 'https://24seven.site', 'http://24seven.site', 'https://www.24seven.site', 'http://www.24seven.site']  # Updated with production domain

# Additional CSRF settings for better compatibility
CSRF_FAILURE_VIEW = 'django.views.csrf.csrf_failure'
CSRF_COOKIE_AGE = 31449600  # 1 year
CSRF_COOKIE_DOMAIN = None  # Use default domain
CSRF_COOKIE_PATH = '/'

# Enhanced CSRF settings for session management compatibility
CSRF_COOKIE_NAME = 'csrftoken'  # Default name
CSRF_HEADER_NAME = 'HTTP_X_CSRFTOKEN'  # Default header
CSRF_TOKEN_ROTATION = True  # Rotate CSRF tokens on login for security

# Cache Configuration - Optimized for cPanel
CACHE_TIMEOUT = 3600 if IN_CPANEL else 86400  # 1 hour for cPanel, 24 hours for dev

# Database cache for cPanel (more reliable than file cache)
DB_CACHE_CONFIG = {
    'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
    'LOCATION': 'django_cache_table',
    'TIMEOUT': CACHE_TIMEOUT,
    'OPTIONS': {
        'MAX_ENTRIES': 500 if IN_CPANEL else 1000,
        'CULL_FREQUENCY': 2 if IN_CPANEL else 3,
    },
}

# File-based cache configuration
FILE_CACHE_CONFIG = {
    'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
    'LOCATION': os.path.join(BASE_DIR, 'cache'),
    'TIMEOUT': CACHE_TIMEOUT,
    'OPTIONS': {
        'MAX_ENTRIES': 500 if IN_CPANEL else 1000,
        'CULL_FREQUENCY': 2 if IN_CPANEL else 3,
    },
}

# Local memory cache configuration for development
LOCMEM_CACHE_CONFIG = {
    'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    'LOCATION': 'company-assistant-cache',
    'TIMEOUT': CACHE_TIMEOUT,
}

# Cache selection based on environment
if IN_CPANEL:
    # Use file cache for cPanel (more reliable than database cache)
    # Database cache can cause issues if table doesn't exist
    DEFAULT_CACHE = FILE_CACHE_CONFIG
elif IN_PRODUCTION:
    # Use file cache for production
    DEFAULT_CACHE = FILE_CACHE_CONFIG
else:
    # Use memory cache for development
    DEFAULT_CACHE = LOCMEM_CACHE_CONFIG

CACHES = {
    'default': DEFAULT_CACHE,
    'session_tokens': {
        'BACKEND': DEFAULT_CACHE['BACKEND'],
        'LOCATION': (DEFAULT_CACHE.get('LOCATION', '') + '_tokens') if 'LOCATION' in DEFAULT_CACHE else 'session_tokens',
        'TIMEOUT': CACHE_TIMEOUT,
        'OPTIONS': DEFAULT_CACHE.get('OPTIONS', {}),
    },
}

# Add performance caches for production
if not DEBUG:
    CACHES.update({
        'llm_responses': {
            'BACKEND': DEFAULT_CACHE['BACKEND'],
            'LOCATION': (DEFAULT_CACHE.get('LOCATION', '') + '_llm') if 'LOCATION' in DEFAULT_CACHE else 'llm_responses',
            'TIMEOUT': 3600,  # 1 hour
            'OPTIONS': {
                'MAX_ENTRIES': 200 if IN_CPANEL else 500,
                'CULL_FREQUENCY': 2,
            },
        },
        'query_cache': {
            'BACKEND': DEFAULT_CACHE['BACKEND'],
            'LOCATION': (DEFAULT_CACHE.get('LOCATION', '') + '_queries') if 'LOCATION' in DEFAULT_CACHE else 'query_cache',
            'TIMEOUT': 1800,  # 30 minutes
            'OPTIONS': {
                'MAX_ENTRIES': 100 if IN_CPANEL else 300,
                'CULL_FREQUENCY': 2,
            },
        },
    })

# Session Configuration - Optimized for cPanel
if IN_CPANEL:
    # Use cached database sessions for cPanel (best performance)
    SESSION_ENGINE = 'django.contrib.sessions.backends.cached_db'
    SESSION_CACHE_ALIAS = 'default'
    SESSION_COOKIE_AGE = 86400  # 1 day for cPanel
    SESSION_SAVE_EVERY_REQUEST = False  # Don't save on every request
    SESSION_EXPIRE_AT_BROWSER_CLOSE = True
elif IN_PRODUCTION:
    # Use file-based sessions for production
    SESSION_ENGINE = 'django.contrib.sessions.backends.file'
    SESSION_FILE_PATH = os.path.join(BASE_DIR, 'session')
    os.makedirs(SESSION_FILE_PATH, exist_ok=True)
    SESSION_COOKIE_AGE = 60 * 60 * 24 * 7  # 1 week
    SESSION_SAVE_EVERY_REQUEST = False
else:
    # Use database sessions for development
    SESSION_ENGINE = 'django.contrib.sessions.backends.db'
    SESSION_COOKIE_AGE = 60 * 60 * 24 * 14  # 14 days
    SESSION_SAVE_EVERY_REQUEST = True

# Enhanced Session Security Settings
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access to session cookie
SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
SESSION_SERIALIZER = 'django.contrib.sessions.serializers.JSONSerializer'  # Use JSON serializer

# Optional: Enforce single session per user (set to True to enable)
ENFORCE_SINGLE_SESSION_PER_USER = False

# Session cleanup settings
SESSION_CLEANUP_INTERVAL = 200  # Clean up expired sessions every N requests

# Site ID (Required for django.contrib.sites)
SITE_ID = 1

# REMOVED: TinyMCE Configuration

# TinyMCE Configuration
TINYMCE_DEFAULT_CONFIG = {
    'height': 360,
    'width': 'auto',
    'menubar': True,
    'plugins': 'advlist autolink lists link image charmap print preview anchor '
               'searchreplace visualblocks code fullscreen '
               'insertdatetime media table paste code help wordcount imagetools '
               'emoticons hr pagebreak nonbreaking toc textpattern codesample tabfocus', # Enhanced plugins
    'toolbar1': 'formatselect fontselect fontsizeselect | '
                'bold italic underline strikethrough | forecolor backcolor | '
                'alignleft aligncenter alignright alignjustify | '
                'bullist numlist outdent indent',
    'toolbar2': 'undo redo | cut copy paste | searchreplace | '
                'link image media table tableprops tablecellprops tablerowprops tabledelete | '
                'emoticons charmap | hr pagebreak nonbreaking | removeformat code | help', # Enhanced toolbar
    'content_style': '''
        body {
            font-family: Helvetica, Arial, sans-serif;
            font-size: 14px;
            max-width: 100%;
            word-wrap: break-word;
            overflow-wrap: break-word;
            padding: 10px;
            box-sizing: border-box;
        }
        img {
            max-width: 100%;
            height: auto !important;
            display: block;
            margin: 0.5em auto;
            border-radius: 4px;
        }
        p {
            margin: 0 0 1em 0;
            line-height: 1.5;
        }
        /* Table wrapper for horizontal scrolling on small screens */
        .table-wrapper {
            width: 100%;
            overflow-x: auto;
            margin: 0.75rem 0;
            border-radius: 0.5rem;
            position: relative;
            -webkit-overflow-scrolling: touch;
        }
        /* Improved table styling */
        table {
            width: 100% !important;
            max-width: 100% !important;
            margin-bottom: 1rem;
            border-collapse: collapse !important;
            border-spacing: 0 !important;
            overflow: hidden !important;
            table-layout: auto !important; /* Changed from fixed to auto for better content fitting */
        }
        /* Ensure tables don't overflow their containers */
        table td, table th {
            padding: 0.75rem !important;
            vertical-align: top !important;
            border: 1px solid #dee2e6 !important;
            word-break: normal !important;
            overflow-wrap: break-word !important;
            min-width: 50px !important;
        }
        /* Responsive media queries */
        @media (max-width: 992px) {
            body { font-size: 15px; }
            table { font-size: 14px; }
            table td, table th { padding: 0.6rem !important; }
        }
        @media (max-width: 768px) {
            body { font-size: 16px; }
            table { font-size: 14px; border: none !important; }
            table td, table th {
                padding: 0.5rem !important;
                min-width: 80px !important;
            }
            /* Auto-wrap tables in a scrollable container on mobile */
            table:not(.table-wrapper table) {
                display: block;
                width: 100%;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
        }
        @media (max-width: 576px) {
            body { font-size: 16px; }
            table { font-size: 13px; }
            table td, table th {
                padding: 0.4rem !important;
                min-width: 70px !important;
            }
        }
    ''',
    'image_advtab': True,
    'image_title': True,
    'automatic_uploads': True,
    'file_picker_types': 'image',
    'image_class_list': [
        {'title': 'Responsive', 'value': 'img-fluid'},
        {'title': 'Left Aligned', 'value': 'img-fluid float-start me-3'},
        {'title': 'Right Aligned', 'value': 'img-fluid float-end ms-3'},
        {'title': 'Centered', 'value': 'img-fluid mx-auto d-block'},
    ],
    'image_dimensions': True,
    'image_caption': True,
    'resize_img_proportional': True,
    'object_resizing': 'img,table',
    'resize': True,
    'mobile_friendly': True,
    'paste_data_images': True,
    'table_responsive_width': True,
    'table_default_attributes': {
        'border': '1',
        'cellpadding': '5',
        'cellspacing': '0',
        'class': 'table table-bordered',
    },
    'table_default_styles': {
        'width': '100%',
        'border-collapse': 'collapse',
    },
    'table_appearance_options': False,
    'table_advtab': True,
    'table_cell_advtab': True,
    'table_row_advtab': True,
    'table_class_list': [
        {'title': 'None', 'value': ''},
        {'title': 'Responsive Table', 'value': 'table-responsive'},
        {'title': 'Bordered Table', 'value': 'table-bordered'},
        {'title': 'Striped Table', 'value': 'table-striped'},
        {'title': 'Small Table', 'value': 'table-sm'},
        {'title': 'Hover Effect', 'value': 'table-hover'},
    ],
    'table_grid': True,
    'table_toolbar': 'tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
    'images_upload_url': '/assistants/tinymce/upload/',
    'file_picker_callback': """
        function (callback, value, meta) {
            if (meta.filetype == 'image') {
                var input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.onchange = function () {
                    var file = this.files[0];
                    var reader = new FileReader();
                    reader.onload = function () {
                        callback(reader.result, {
                            alt: file.name,
                            style: 'max-width: 100%; height: auto;'
                        });
                    };
                    reader.readAsDataURL(file);
                };
                input.click();
            }
        }
    """,
    'setup': """
        function (editor) {
            // Auto-wrap tables in a responsive container
            editor.on('SetContent', function (e) {
                if (e.content && e.content.indexOf('<table') !== -1) {
                    var content = editor.getContent();
                    var updatedContent = content.replace(/<table(?![^>]*class="[^"]*table-responsive[^"]*")[^>]*>/g,
                        '<div class="table-wrapper"><table $1>');
                    updatedContent = updatedContent.replace(/<\\/table>/g, '</table></div>');
                    if (content !== updatedContent) {
                        editor.setContent(updatedContent);
                    }
                }
            });

            // Handle paste events to ensure images are responsive
            editor.on('PastePostProcess', function (e) {
                var imgs = e.node.getElementsByTagName('img');
                for (var i = 0; i < imgs.length; i++) {
                    if (!imgs[i].hasAttribute('class') || imgs[i].getAttribute('class').indexOf('img-fluid') === -1) {
                        imgs[i].setAttribute('class', (imgs[i].getAttribute('class') || '') + ' img-fluid');
                    }
                    imgs[i].setAttribute('style', 'max-width: 100%; height: auto;');
                }

                // Handle tables in pasted content
                var tables = e.node.getElementsByTagName('table');
                for (var j = 0; j < tables.length; j++) {
                    if (!tables[j].parentNode.classList || !tables[j].parentNode.classList.contains('table-wrapper')) {
                        var wrapper = e.node.ownerDocument.createElement('div');
                        wrapper.className = 'table-wrapper';
                        tables[j].parentNode.insertBefore(wrapper, tables[j]);
                        wrapper.appendChild(tables[j]);
                    }

                    if (!tables[j].hasAttribute('class') || tables[j].getAttribute('class').indexOf('table') === -1) {
                        tables[j].setAttribute('class', (tables[j].getAttribute('class') || '') + ' table table-bordered');
                    }
                }
            });
        }
    """,
}

# Ensure TinyMCE URLs are included for upload handling
TINYMCE_EXTRA_MEDIA = {
    'css': {
        'all': [],
    },
    'js': [],
}


# Django-impersonate settings - Optimized for cPanel
IMPERSONATE_ALLOW_SUPERUSER = True
IMPERSONATE_REQUIRE_SUPERUSER = True
IMPERSONATE_REDIRECT_URL = '/accounts/dashboard/'  # Redirect to user dashboard after impersonation
IMPERSONATE_USE_HTTP_REFERER = False  # Don't use HTTP referer to avoid redirect loops
IMPERSONATE_PAGINATE_COUNT = 20
IMPERSONATE_LOOKUP_TYPE = 'icontains'
IMPERSONATE_SEARCH_FIELDS = ['username', 'first_name', 'last_name', 'email']
IMPERSONATE_URI_EXCLUSIONS = [r'^/admin/', r'^/superadmin/']  # Exclude superadmin URLs
IMPERSONATE_CUSTOM_USER_QUERYSET = None  # Use default queryset
IMPERSONATE_CUSTOM_ALLOW = None  # Use default permission check
if not DEBUG:
    # Disable logging in production for performance
    IMPERSONATE_DISABLE_LOGGING = True
else:
    IMPERSONATE_DISABLE_LOGGING = False

# ============================================================================
# PERFORMANCE OPTIMIZATION SETTINGS
# ============================================================================

# Performance optimization flags
ENABLE_LLM_CACHING = True
ENABLE_QUERY_OPTIMIZATION = True
ENABLE_MEMORY_OPTIMIZATION = True
ENABLE_COMPRESSION = True

# Advanced optimization flags
ENABLE_ADVANCED_DATA_STRUCTURES = True
ENABLE_ENHANCED_LLM_PROCESSING = True
ENABLE_ADVANCED_CACHING = True
ENABLE_ASYNC_PROCESSING = True
ENABLE_MEMORY_MANAGEMENT = True
ENABLE_ADVANCED_QUERIES = True

# LLM Caching Configuration (environment-configurable)
LLM_CACHE_TTL = int(os.getenv('LLM_CACHE_TTL', '3600'))  # 1 hour
LLM_CACHE_MAX_SIZE = int(os.getenv('LLM_CACHE_MAX_SIZE', '500' if IN_CPANEL else '1000'))
LLM_CACHE_SIMILARITY_THRESHOLD = float(os.getenv('LLM_CACHE_SIMILARITY_THRESHOLD', '0.85'))

# Additional LLM Provider Settings
OLLAMA_BASE_URL = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
OLLAMA_DEFAULT_MODEL = os.getenv('OLLAMA_DEFAULT_MODEL', 'llama2')

# Custom LLM Provider Settings (for OpenAI-compatible APIs)
CUSTOM_LLM_BASE_URL = os.getenv('CUSTOM_LLM_BASE_URL', 'https://api.openai.com/v1')
CUSTOM_LLM_API_KEY = os.getenv('CUSTOM_LLM_API_KEY', '')
CUSTOM_LLM_DEFAULT_MODEL = os.getenv('CUSTOM_LLM_DEFAULT_MODEL', 'gpt-3.5-turbo')

# LLM Timeout and Retry Settings
LLM_REQUEST_TIMEOUT = int(os.getenv('LLM_REQUEST_TIMEOUT', '30'))
LLM_MAX_RETRIES = int(os.getenv('LLM_MAX_RETRIES', '3'))
LLM_RETRY_DELAY = float(os.getenv('LLM_RETRY_DELAY', '1.0'))

# Context Preloading Configuration
ENABLE_CONTEXT_PRELOADING = True
CONTEXT_PRELOAD_TTL = 1800  # 30 minutes
CONTEXT_PRELOAD_MAX_WORKERS = 2 if IN_CPANEL else 3
CONTEXT_PRELOAD_HOVER_DELAY = 500  # milliseconds
CONTEXT_PRELOAD_TIMEOUT = 30  # seconds

# Memory Optimization Settings - Aggressive for cPanel
if IN_CPANEL:
    DATA_UPLOAD_MAX_MEMORY_SIZE = 1048576  # 1MB for cPanel (very conservative)
    FILE_UPLOAD_MAX_MEMORY_SIZE = 1048576  # 1MB for cPanel
    DATA_UPLOAD_MAX_NUMBER_FIELDS = 50     # Very limited for cPanel
    MAX_UPLOAD_SIZE = 1048576              # 1MB max upload
else:
    DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB for dev
    FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB for dev
    DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000   # Normal for dev
    MAX_UPLOAD_SIZE = 5242880              # 5MB for dev

# Pagination Settings
PAGINATE_BY = 20
MAX_PAGE_SIZE = 50 if IN_CPANEL else 100

# Query Optimization Settings
SELECT_RELATED_DEPTH = 2
PREFETCH_RELATED_DEPTH = 1

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING = not IN_CPANEL  # Disable in cPanel for performance
PERFORMANCE_LOG_SLOW_QUERIES = not IN_CPANEL
SLOW_QUERY_THRESHOLD = 1.0

# HTTP Client Settings for LLM APIs
HTTP_CLIENT_SETTINGS = {
    'timeout': 20.0 if IN_CPANEL else 30.0,
    'connect_timeout': 5.0 if IN_CPANEL else 10.0,
    'max_keepalive_connections': 10 if IN_CPANEL else 20,
    'max_connections': 50 if IN_CPANEL else 100,
    'retries': 2 if IN_CPANEL else 3,
    'backoff_factor': 0.3,
}

# Static Files Optimization
if not DEBUG:
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
    WHITENOISE_USE_FINDERS = True
    WHITENOISE_AUTOREFRESH = False
    WHITENOISE_MAX_AGE = 31536000  # 1 year

# Template Optimization for production
if not DEBUG:
    # Remove APP_DIRS when using custom loaders
    TEMPLATES[0]['APP_DIRS'] = False
    TEMPLATES[0]['OPTIONS']['loaders'] = [
        ('django.template.loaders.cached.Loader', [
            'django.template.loaders.filesystem.Loader',
            'django.template.loaders.app_directories.Loader',
        ]),
    ]

# Security Settings - Enhanced for production
if IN_PRODUCTION or IN_CPANEL:
    SECURE_SSL_REDIRECT = True
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'

    # Optimize CSRF for production
    # CSRF_COOKIE_HTTPONLY = True  # Commented out to prevent conflicts
    # CSRF_USE_SESSIONS = True     # Commented out to prevent conflicts

# Internationalization Optimization
if IN_CPANEL:
    USE_I18N = False  # Disable if not needed for performance
    USE_L10N = False  # Disable if not needed for performance

# Performance Settings Dictionary
PERFORMANCE_SETTINGS = {
    'enable_llm_caching': ENABLE_LLM_CACHING,
    'enable_query_optimization': ENABLE_QUERY_OPTIMIZATION,
    'enable_memory_optimization': ENABLE_MEMORY_OPTIMIZATION,
    'enable_compression': ENABLE_COMPRESSION,
    'llm_cache_ttl': LLM_CACHE_TTL,
    'llm_cache_max_size': LLM_CACHE_MAX_SIZE,
    'paginate_by': PAGINATE_BY,
    'max_page_size': MAX_PAGE_SIZE,
    'slow_query_threshold': SLOW_QUERY_THRESHOLD,
    'enable_advanced_data_structures': ENABLE_ADVANCED_DATA_STRUCTURES,
    'enable_enhanced_llm_processing': ENABLE_ENHANCED_LLM_PROCESSING,
    'enable_advanced_caching': ENABLE_ADVANCED_CACHING,
    'enable_async_processing': ENABLE_ASYNC_PROCESSING,
    'enable_memory_management': ENABLE_MEMORY_MANAGEMENT,
    'enable_advanced_queries': ENABLE_ADVANCED_QUERIES,
    'enable_context_preloading': ENABLE_CONTEXT_PRELOADING,
    'context_preload_ttl': CONTEXT_PRELOAD_TTL,
    'context_preload_max_workers': CONTEXT_PRELOAD_MAX_WORKERS,
    'context_preload_hover_delay': CONTEXT_PRELOAD_HOVER_DELAY,
    'context_preload_timeout': CONTEXT_PRELOAD_TIMEOUT,
}

# ============================================================================
# AGGRESSIVE CPANEL OPTIMIZATIONS
# ============================================================================

if IN_CPANEL:
    # Aggressive memory management
    import gc
    gc.set_threshold(100, 5, 5)  # Very aggressive garbage collection

    # Reduce Django's internal caches
    TEMPLATE_CACHE_SIZE = 50  # Reduce template cache

    # Limit concurrent operations
    DATABASE_POOL_SIZE = 2  # Very small connection pool

    # Disable expensive features
    USE_I18N = False
    USE_L10N = False

    # Aggressive session settings
    SESSION_COOKIE_AGE = 3600  # 1 hour only
    SESSION_SAVE_EVERY_REQUEST = False
    SESSION_EXPIRE_AT_BROWSER_CLOSE = True

    # Ultra-lightweight middleware for cPanel
    MIDDLEWARE = [
        'company_assistant.cpanel_middleware.CPanelResourceMiddleware',
        'company_assistant.cpanel_middleware.CPanelMemoryMiddleware',
        'company_assistant.cpanel_middleware.CPanelTimeoutMiddleware',
        'django.middleware.security.SecurityMiddleware',
        'whitenoise.middleware.WhiteNoiseMiddleware',
        'django.contrib.sessions.middleware.SessionMiddleware',
        'django.middleware.common.CommonMiddleware',
        'django.middleware.csrf.CsrfViewMiddleware',
        'django.contrib.auth.middleware.AuthenticationMiddleware',
        'django.contrib.messages.middleware.MessageMiddleware',
        'company_assistant.cpanel_middleware.CPanelCacheMiddleware',
    ]

    # Aggressive cache settings
    CACHE_TIMEOUT = 1800  # 30 minutes only
    CACHES['default']['OPTIONS']['MAX_ENTRIES'] = 100  # Very small cache

    # Disable debug features completely
    DEBUG = False
    TEMPLATE_DEBUG = False

    # Reduce logging to minimum
    LOGGING['root']['level'] = 'ERROR'
    for logger in LOGGING['loggers'].values():
        logger['level'] = 'ERROR'

    # Aggressive static file settings
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'
    WHITENOISE_MAX_AGE = 86400  # 1 day only for cPanel

    # Limit installed apps to essentials
    INSTALLED_APPS = [
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'django.contrib.sites',
        # Essential third party only
        'widget_tweaks',
        'crispy_forms',  # Added back for cPanel compatibility - used in templates
        'crispy_bootstrap5',  # Added back for cPanel compatibility - template pack
        'guardian',
        'tinymce',  # Added back for cPanel compatibility - URLs included
        # Local apps
        'accounts.apps.AccountsConfig',
        'assistants.apps.AssistantsConfig',
        'content.apps.ContentConfig',
        'directory.apps.DirectoryConfig',
        'site_settings',
        'superadmin',
    ]

    # Aggressive database settings
    DATABASES['default'].update({
        'CONN_MAX_AGE': 60,  # Very short connection age
        'OPTIONS': {
            'connect_timeout': 3,  # Very short timeout
            'client_encoding': 'UTF8',
            'isolation_level': 1,
        },
    })

# Create necessary directories
if IN_CPANEL or IN_PRODUCTION:
    required_dirs = ['logs', 'cache', 'session', 'media', 'staticfiles', 'tmp']
    for directory in required_dirs:
        dir_path = os.path.join(BASE_DIR, directory)
        os.makedirs(dir_path, exist_ok=True)

# Context processor for performance data
def performance_context(request):
    """Add performance-related context variables."""
    return {
        'performance_settings': PERFORMANCE_SETTINGS,
        'enable_caching': ENABLE_LLM_CACHING,
        'in_cpanel': IN_CPANEL,
        'in_production': IN_PRODUCTION,
        'cache_stats': {
            'llm_cache_enabled': ENABLE_LLM_CACHING,
            'query_optimization_enabled': ENABLE_QUERY_OPTIMIZATION,
            'memory_optimization_enabled': ENABLE_MEMORY_OPTIMIZATION,
        }
    }


