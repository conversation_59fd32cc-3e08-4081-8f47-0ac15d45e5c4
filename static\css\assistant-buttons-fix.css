/**
 * Assistant <PERSON><PERSON> Fix CSS
 * Ensures assistant buttons are properly styled and clickable
 */

/* General button fixes */
.rate-btn, 
.favorite-btn,
.share-url-btn,
.reset-conversation-btn,
#send-button,
button[data-action="rate"],
button[data-action="favorite"],
button[data-action="share"],
button[data-action="reset"],
button.rate-assistant-btn,
button.favorite-assistant-btn,
button.share-assistant-btn,
button.reset-conversation,
button.send-message-btn,
a.rate-assistant-btn,
a.favorite-assistant-btn,
a.share-assistant-btn,
a.reset-conversation,
a.send-message-btn {
    cursor: pointer !important;
    pointer-events: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: inline-block !important;
    position: relative !important;
    z-index: 1050 !important;
}

/* Rate button */
.rate-btn, 
button[data-action="rate"],
button.rate-assistant-btn,
a.rate-assistant-btn {
    background-color: transparent !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    color: #212529 !important;
}

/* Favorite button */
.favorite-btn,
button[data-action="favorite"],
button.favorite-assistant-btn,
a.favorite-assistant-btn {
    background-color: transparent !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    color: #212529 !important;
}

/* Share URL button */
.share-url-btn,
button[data-action="share"],
button.share-assistant-btn,
a.share-assistant-btn {
    background-color: transparent !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    color: #212529 !important;
}

/* Reset conversation button */
.reset-conversation-btn,
button[data-action="reset"],
button.reset-conversation,
a.reset-conversation {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* Send button */
#send-button,
button.send-message-btn,
a.send-message-btn {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}

/* Dark mode styles */
[data-theme="dark"] .rate-btn, 
[data-theme="dark"] button[data-action="rate"],
[data-theme="dark"] button.rate-assistant-btn,
[data-theme="dark"] a.rate-assistant-btn,
[data-theme="dark"] .favorite-btn,
[data-theme="dark"] button[data-action="favorite"],
[data-theme="dark"] button.favorite-assistant-btn,
[data-theme="dark"] a.favorite-assistant-btn,
[data-theme="dark"] .share-url-btn,
[data-theme="dark"] button[data-action="share"],
[data-theme="dark"] button.share-assistant-btn,
[data-theme="dark"] a.share-assistant-btn {
    background-color: #1e1e1e !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

/* Reset conversation button in dark mode */
[data-theme="dark"] .reset-conversation-btn,
[data-theme="dark"] button[data-action="reset"],
[data-theme="dark"] button.reset-conversation,
[data-theme="dark"] a.reset-conversation {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* Send button in dark mode */
[data-theme="dark"] #send-button,
[data-theme="dark"] button.send-message-btn,
[data-theme="dark"] a.send-message-btn {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}

/* Hover effects */
.rate-btn:hover, 
button[data-action="rate"]:hover,
button.rate-assistant-btn:hover,
a.rate-assistant-btn:hover,
.favorite-btn:hover,
button[data-action="favorite"]:hover,
button.favorite-assistant-btn:hover,
a.favorite-assistant-btn:hover,
.share-url-btn:hover,
button[data-action="share"]:hover,
button.share-assistant-btn:hover,
a.share-assistant-btn:hover {
    background-color: rgba(0, 0, 0, 0.05) !important;
}

[data-theme="dark"] .rate-btn:hover, 
[data-theme="dark"] button[data-action="rate"]:hover,
[data-theme="dark"] button.rate-assistant-btn:hover,
[data-theme="dark"] a.rate-assistant-btn:hover,
[data-theme="dark"] .favorite-btn:hover,
[data-theme="dark"] button[data-action="favorite"]:hover,
[data-theme="dark"] button.favorite-assistant-btn:hover,
[data-theme="dark"] a.favorite-assistant-btn:hover,
[data-theme="dark"] .share-url-btn:hover,
[data-theme="dark"] button[data-action="share"]:hover,
[data-theme="dark"] button.share-assistant-btn:hover,
[data-theme="dark"] a.share-assistant-btn:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

.reset-conversation-btn:hover,
button[data-action="reset"]:hover,
button.reset-conversation:hover,
a.reset-conversation:hover {
    background-color: #b82626 !important;
    border-color: #b82626 !important;
}

#send-button:hover,
button.send-message-btn:hover,
a.send-message-btn:hover {
    background-color: #0b5ed7 !important;
    border-color: #0a58ca !important;
}
