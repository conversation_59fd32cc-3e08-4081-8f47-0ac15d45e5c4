#!/usr/bin/env python3
"""
Test QR Code Generation with New Fonts
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from utils.qr_generator import generate_qr_with_a

def test_qr_generation():
    """Test QR code generation with the new fonts."""
    print("=== Testing QR Code Generation with New Fonts ===")
    
    # Test data
    test_url = "https://example.com/test"
    
    try:
        # Generate QR code
        print("Generating QR code...")
        qr_img = generate_qr_with_a(test_url, letter="A")
        
        # Save the QR code
        qr_filename = "test_qr_with_fonts.png"
        qr_img.save(qr_filename)
        
        print(f"✅ QR code generated successfully: {qr_filename}")
        print("Check the generated QR code to see if the letter 'A' is properly sized.")
        
        return True
        
    except Exception as e:
        print(f"❌ QR code generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_qr_generation()
