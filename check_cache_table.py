#!/usr/bin/env python
"""
Quick script to check if django_cache_table exists in the database.
"""

import os
import django
from django.db import connection

def setup_django():
    """Setup Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
    django.setup()

def check_cache_table():
    """Check if the django_cache_table exists and show its status."""
    print("🔍 Checking cache table status...")
    print("=" * 40)
    
    try:
        with connection.cursor() as cursor:
            # Check if the table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'django_cache_table'
                );
            """)
            exists = cursor.fetchone()[0]
            
            if exists:
                print("✅ Table Status: django_cache_table EXISTS")
                
                # Get table info
                cursor.execute("SELECT COUNT(*) FROM django_cache_table;")
                count = cursor.fetchone()[0]
                print(f"📊 Cache Entries: {count}")
                
                # Check table structure
                cursor.execute("""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_name = 'django_cache_table'
                    ORDER BY ordinal_position;
                """)
                columns = cursor.fetchall()
                print("🏗️  Table Structure:")
                for col_name, col_type in columns:
                    print(f"   - {col_name}: {col_type}")
                
                print("\n✅ RESULT: Cache table is properly installed!")
                print("   Your Django cache should work correctly.")
                
            else:
                print("❌ Table Status: django_cache_table DOES NOT EXIST")
                print("\n🔧 SOLUTION: Run one of these commands:")
                print("   python manage.py createcachetable")
                print("   python fix_cache_table.py")
                
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        print("\n🔧 POSSIBLE ISSUES:")
        print("   - Database connection problem")
        print("   - Wrong database credentials")
        print("   - Database server not running")

if __name__ == '__main__':
    setup_django()
    check_cache_table()
