/**
 * Mobile Chat Interface Improvements
 * Enhanced mobile experience for the chat interface
 */

/* Base chat improvements for all screen sizes */
.chat-container {
  max-width: 1200px !important;
  margin: 0 auto !important;
  border-radius: 1rem !important;
  overflow: hidden !important;
}

/* Mobile viewport optimizations for fixed input layout */
@media (max-width: 768px) {
  /* Ensure body and html take full height with no scroll */
  html, body {
    height: 100vh !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    position: fixed !important;
    width: 100% !important;
  }

  /* Remove any top margin/padding from main content */
  main {
    margin-top: 0 !important;
    padding-top: 0 !important;
    height: 100vh !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Optimize main container for mobile with fixed header */
  .container-fluid {
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-top: 70px !important; /* Space for fixed header */
    overflow: hidden !important;
  }

  /* Ensure row takes full height */
  .container-fluid > .row {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    margin: 0 !important;
    overflow: hidden !important;
  }

  /* Make main column take full height */
  .container-fluid > .row > .col-12 {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    padding: 0 !important;
    overflow: hidden !important;
  }

  /* Fixed unified header at top */
  .unified-chat-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1050 !important;
    margin: 0 !important;
    padding: 0.5rem 0.75rem !important;
    flex-shrink: 0 !important;
    width: 100% !important;
  }

  /* Fixed chat input at bottom */
  #fixed-chat-input {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1040 !important;
    background-color: #ffffff !important;
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
    padding: 0.75rem !important;
  }

  /* Ensure body has padding for fixed header and input */
  body {
    padding-top: 70px !important; /* Space for fixed header */
    padding-bottom: 90px !important; /* Space for fixed input */
  }
}

.chat-box {
  min-height: 400px !important;
  padding-bottom: 80px !important; /* Space for the fixed input form */
}

#chat-form {
  border-radius: 1.5rem !important;
  transition: all 0.2s ease !important;
}

#message-input {
  border-radius: 1.5rem !important;
  padding: 0.75rem 1rem !important;
  font-size: 1rem !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  background-color: #ffffff !important;
}

#send-button {
  border-radius: 1.5rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
}

/* Mobile optimizations - Enhanced */
@media (max-width: 768px) {
  /* Remove top margin on mobile to maximize chat space */
  .container-fluid {
    margin-top: 0 !important;
    padding-top: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  /* Ensure tab content takes full height */
  .tab-content {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .tab-pane {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Full-width chat container on mobile with fixed header and input */
  .chat-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    border-left: none !important;
    border-right: none !important;
    box-shadow: none !important;
    /* Chat container takes remaining height between fixed header and fixed input */
    height: calc(100vh - 160px) !important; /* 70px header + 90px input */
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }

  /* Scrollable chat box for messages */
  .chat-box {
    padding: 0.75rem !important;
    padding-bottom: 0.75rem !important; /* No extra bottom padding needed */
    /* Chat box takes full available height and scrolls */
    height: 100% !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    flex-grow: 1 !important;
    -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
  }

  /* Optimize initial display area for mobile */
  #initial-display-area {
    flex-shrink: 0 !important;
  }

  /* Make chat messages area flexible */
  .chat-messages-area {
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* Fixed chat form at the bottom */
  #chat-form {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    padding: 0.75rem 1rem !important;
    background-color: #ffffff !important;
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 0 !important;
    margin: 0 !important;
    z-index: 1000 !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
  }

  /* Optimize input field */
  #message-input {
    flex-grow: 1 !important;
    font-size: 16px !important; /* Prevents iOS zoom on focus */
    padding: 0.5rem 0.75rem !important;
    margin-right: 0.5rem !important;
    border-radius: 1.25rem !important;
  }

  /* Optimize send button */
  #send-button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.9rem !important;
    border-radius: 1.25rem !important;
    min-width: 60px !important;
  }

  /* Optimize chat messages */
  .message {
    margin: 0.5rem 0 !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  .message-content {
    max-width: 100% !important;
    width: 100% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.95rem !important;
    border-radius: 1.25rem !important;
    line-height: 1.5 !important;
  }

  /* Optimize user message */
  .user-message {
    justify-content: flex-end !important;
    padding-left: 15% !important;
  }

  /* Optimize assistant message */
  .assistant-message {
    justify-content: flex-start !important;
    padding-right: 15% !important;
  }

  /* Optimize chat avatars - hide only in messages, not in welcome area */
  .message .chat-avatar-container {
    display: none !important; /* Hide avatars in messages on mobile to save space */
  }

  /* Keep the welcome avatar visible and styled */
  #initial-display-area .assistant-profile-pic,
  #initial-display-area .assistant-profile-pic-placeholder {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Optimize initial display area */
  #initial-display-area {
    padding: 1rem !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .initial-greeting {
    font-size: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  /* Optimize sidebar */
  #chat-sidebar {
    width: 85% !important;
    max-width: 300px !important;
    z-index: 1080 !important;
  }

  /* Optimize sidebar toggle button */
  #test-sidebar-toggle-mobile {
    padding: 0.4rem 0.75rem !important;
    font-size: 0.9rem !important;
  }

  /* Optimize sidebar overlay */
  #sidebar-overlay {
    background-color: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  /* Unified header mobile optimizations */
  .unified-chat-header .dropdown-menu {
    font-size: 0.9rem !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #444 !important;
  }

  .unified-chat-header .dropdown-item {
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
  }

  .unified-chat-header .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  /* Ensure rating stars are properly sized in unified header */
  .unified-chat-header .star-rating {
    font-size: 0.75rem !important;
  }

  .unified-chat-header .star-rating .bi-star-fill,
  .unified-chat-header .star-rating .bi-star {
    font-size: 0.75rem !important;
    color: #ffc107 !important; /* Ensure stars are visible on dark background */
  }

  .unified-chat-header .rating-count {
    display: none !important; /* Hide rating count text on mobile to save space */
  }

  /* Completely hide footer on mobile */
  footer.footer,
  footer,
  .footer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute !important;
    left: -9999px !important;
  }
}

/* Dark mode optimizations for mobile */
@media (max-width: 768px) {
  [data-theme="dark"] #chat-form {
    background-color: #1a1a1a !important;
    border-top: 1px solid #333333 !important;
  }

  [data-theme="dark"] #message-input {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
  }

  [data-theme="dark"] #chat-sidebar {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }
}
