/**
 * Force Dark Mode Script
 * This script ensures dark mode is applied to all elements immediately on page load
 */

// Apply dark mode immediately before DOM content loaded
(function() {
    // Set dark mode on html and body
    document.documentElement.setAttribute('data-theme', 'dark');
    document.documentElement.classList.add('dark-mode');

    // Only set body attributes if body exists
    if (document.body) {
        document.body.setAttribute('data-theme', 'dark');
    }

    // Store theme preference
    try {
        localStorage.setItem('theme', 'dark');
    } catch (e) {
        // Ignore localStorage errors
    }

    // Set dark background color immediately
    document.documentElement.style.backgroundColor = '#121212';
    document.documentElement.style.color = '#ffffff';

    // Create and inject a style element for immediate dark mode
    const style = document.createElement('style');
    style.textContent = `
        html, body, main {
            background-color: #121212 !important;
            color: #ffffff !important;
        }

        [data-theme="light"] {
            data-theme: dark !important;
        }

        .theme-toggle-btn {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }
    `;
    document.head.appendChild(style);
})();

// Apply dark mode on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // Always use dark mode
    const theme = 'dark';

    // Apply dark mode to all elements
    document.documentElement.setAttribute('data-theme', theme);
    document.body.setAttribute('data-theme', theme);
    document.documentElement.classList.add('dark-mode');
    document.body.classList.add('dark-mode');

    // Apply to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('dark-mode');
        mainContent.setAttribute('data-theme', theme);
        mainContent.style.backgroundColor = '#121212';
        mainContent.style.color = '#ffffff';
    }

    // Apply to all cards
    document.querySelectorAll('.card').forEach(card => {
        card.classList.add('dark-mode');
        card.setAttribute('data-theme', theme);
    });

    // Apply to all modals
    document.querySelectorAll('.modal-content').forEach(modal => {
        modal.classList.add('dark-mode');
        modal.setAttribute('data-theme', theme);
    });

    // Apply to navbar
    document.querySelectorAll('.navbar').forEach(navbar => {
        navbar.classList.add('dark-mode');
        navbar.setAttribute('data-theme', theme);
    });

    // Apply to footer
    document.querySelectorAll('footer').forEach(footer => {
        footer.classList.add('dark-mode');
        footer.setAttribute('data-theme', theme);
    });

    // Remove any theme toggle buttons
    document.querySelectorAll('.theme-toggle-btn').forEach(btn => {
        btn.style.display = 'none';
        btn.style.visibility = 'hidden';
        btn.style.opacity = '0';
        btn.style.pointerEvents = 'none';
    });

    // Dispatch theme changed event
    document.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));

    // Force dark mode on TinyMCE if it exists
    if (window.tinymce) {
        tinymce.editors.forEach(editor => {
            try {
                if (editor.initialized) {
                    const editorContainer = editor.getContainer();
                    if (editorContainer) {
                        editorContainer.classList.add('tox-tinymce--dark');
                    }
                }
            } catch (e) {
                // Silently handle errors
            }
        });
    }
});
