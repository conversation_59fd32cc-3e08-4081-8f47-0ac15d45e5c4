"""
Optimized database queries for better performance.
Eliminates N+1 queries and implements efficient data retrieval patterns.
"""

from django.db.models import Prefetch, Count, Q, F, Avg, Sum
from django.core.cache import cache
from django.core.paginator import Paginator
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class OptimizedAssistantQueries:
    """Optimized queries for Assistant model operations."""

    @staticmethod
    def get_company_assistants_optimized(company, user=None, page_size=20):
        """
        Get company assistants with optimized queries and caching.
        Eliminates N+1 queries using select_related and prefetch_related.
        """
        from .models import Assistant
        from accounts.models import Company

        # Build base queryset with optimized joins
        queryset = Assistant.objects.filter(company=company).select_related(
            'company',
            'company__info',
            'folder'
        ).prefetch_related(
            'interactions',
            Prefetch('interactions__user')
        )

        # Apply user-specific filtering if provided
        if user and user.is_authenticated:
            # Use guardian for permission checking if available
            try:
                from guardian.shortcuts import get_objects_for_user
                viewable_assistants = get_objects_for_user(
                    user, 'assistants.view_assistant', queryset, accept_global_perms=False
                )
                public_assistants = queryset.filter(is_public=True)
                queryset = (viewable_assistants | public_assistants).distinct()
            except ImportError:
                # Fallback if guardian not available
                queryset = queryset.filter(is_public=True)
        else:
            queryset = queryset.filter(is_public=True, is_active=True)

        # Add annotations for statistics
        queryset = queryset.annotate(
            interaction_count=Count('interactions'),
            avg_rating=Avg('interactions__rating'),
            total_tokens=Sum('interactions__token_count')
        )

        return queryset.order_by('-created_at')

    @staticmethod
    def get_assistant_with_stats(assistant_id: int, user=None):
        """Get single assistant with comprehensive stats and related data."""
        from .models import Assistant, Interaction

        cache_key = f"assistant_stats:{assistant_id}:{user.id if user else 'anon'}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        try:
            assistant = Assistant.objects.select_related(
                'company',
                'company__info',
                'folder'
            ).prefetch_related(
                Prefetch(
                    'interactions',
                    queryset=Interaction.objects.select_related('user').order_by('-created_at')[:10]
                )
            ).get(id=assistant_id)

            # Calculate stats efficiently
            stats = Interaction.objects.filter(assistant=assistant).aggregate(
                total_interactions=Count('id'),
                avg_rating=Avg('rating'),
                total_tokens=Sum('token_count'),
                avg_duration=Avg('duration')
            )

            result = {
                'assistant': assistant,
                'stats': stats,
                'recent_interactions': list(assistant.interactions.all()[:10])
            }

            # Cache for 5 minutes
            cache.set(cache_key, result, 300)
            return result

        except Assistant.DoesNotExist:
            return None

    @staticmethod
    def get_community_assistants_optimized(user=None, search_query=None, page=1, page_size=20):
        """Get community assistants with optimized queries and pagination."""
        from .models import Assistant

        # Build base queryset
        queryset = Assistant.objects.filter(
            assistant_type=Assistant.TYPE_COMMUNITY,
            is_active=True
        ).select_related(
            'company',
            'company__info'
        ).prefetch_related(
            Prefetch('company__info')
        )

        # Apply user-specific filtering
        if user and user.is_authenticated:
            queryset = queryset.filter(
                Q(is_public=True) |
                Q(company__owner=user) |
                Q(company__memberships__user=user)
            ).distinct()
        else:
            queryset = queryset.filter(is_public=True)

        # Apply search filter
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(company__name__icontains=search_query)
            )

        # Add interaction counts
        queryset = queryset.annotate(
            interaction_count=Count('interactions'),
            avg_rating=Avg('interactions__rating')
        )

        # Paginate efficiently
        paginator = Paginator(queryset.order_by('-created_at'), page_size)
        page_obj = paginator.get_page(page)

        return {
            'assistants': page_obj.object_list,
            'page_obj': page_obj,
            'total_count': paginator.count
        }


class OptimizedInteractionQueries:
    """Optimized queries for Interaction model operations."""

    @staticmethod
    def get_user_interactions_optimized(user, assistant=None, limit=50):
        """Get user interactions with optimized queries and proper user isolation."""
        from .models import Interaction

        # Ensure user isolation - only return interactions for the specific user
        if not user or not user.is_authenticated:
            return Interaction.objects.none()

        queryset = Interaction.objects.filter(user=user).select_related(
            'assistant',
            'assistant__company'
        )

        if assistant:
            queryset = queryset.filter(assistant=assistant)

        # Add additional security check to ensure user owns these interactions
        queryset = queryset.filter(user_id=user.id)

        return queryset.order_by('-created_at')[:limit]

    @staticmethod
    def get_assistant_analytics_optimized(assistant, period_days=30):
        """Get assistant analytics with efficient aggregation."""
        from .models import Interaction
        from django.utils import timezone
        from datetime import timedelta

        cache_key = f"assistant_analytics:{assistant.id}:{period_days}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        # Calculate date range
        end_date = timezone.now()
        start_date = end_date - timedelta(days=period_days)

        # Get interactions in period
        interactions = Interaction.objects.filter(
            assistant=assistant,
            created_at__gte=start_date
        )

        # Calculate analytics
        analytics = interactions.aggregate(
            total_interactions=Count('id'),
            avg_rating=Avg('rating'),
            total_tokens=Sum('token_count'),
            avg_duration=Avg('duration'),
            unique_users=Count('user', distinct=True)
        )

        # Get daily breakdown
        daily_stats = []
        for i in range(period_days):
            day_start = start_date + timedelta(days=i)
            day_end = day_start + timedelta(days=1)

            day_interactions = interactions.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).aggregate(
                count=Count('id'),
                avg_rating=Avg('rating')
            )

            daily_stats.append({
                'date': day_start.date(),
                'interactions': day_interactions['count'] or 0,
                'avg_rating': day_interactions['avg_rating'] or 0
            })

        result = {
            'period_stats': analytics,
            'daily_stats': daily_stats
        }

        # Cache for 1 hour
        cache.set(cache_key, result, 3600)
        return result


class OptimizedContentQueries:
    """Optimized queries for Content model operations."""

    @staticmethod
    def get_company_content_optimized(company, content_type=None, search_query=None):
        """Get company content with optimized queries."""
        from content.models import Content

        queryset = Content.objects.filter(company=company).select_related(
            'company',
            'author'
        )

        if content_type:
            queryset = queryset.filter(content_type=content_type)

        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(body__icontains=search_query) |
                Q(summary__icontains=search_query)
            )

        return queryset.order_by('-created_at')


class OptimizedDirectoryQueries:
    """Optimized queries for Directory operations."""

    @staticmethod
    def get_featured_companies_optimized(limit=6):
        """Get featured companies with assistant counts."""
        from accounts.models import Company

        cache_key = f"featured_companies:{limit}"
        cached_data = cache.get(cache_key)

        if cached_data:
            return cached_data

        companies = Company.objects.filter(
            info__list_in_directory=True,
            is_featured=True,
            is_active=True
        ).select_related('info').annotate(
            public_assistants_count=Count(
                'assistants',
                filter=Q(assistants__is_public=True, assistants__is_active=True)
            )
        ).order_by('-created_at')[:limit]

        result = list(companies)
        # Cache for 30 minutes
        cache.set(cache_key, result, 1800)
        return result

    @staticmethod
    def search_directory_optimized(query, search_type='both', page=1, page_size=10):
        """Optimized directory search with pagination."""
        from accounts.models import Company
        from assistants.models import Assistant

        results = {'companies': [], 'assistants': [], 'total_companies': 0, 'total_assistants': 0}

        if search_type in ['companies', 'both']:
            company_qs = Company.objects.filter(
                Q(name__icontains=query) | Q(info__description__icontains=query),
                info__list_in_directory=True,
                is_active=True
            ).select_related('info').distinct()

            company_paginator = Paginator(company_qs, page_size)
            company_page = company_paginator.get_page(page)

            results['companies'] = company_page.object_list
            results['total_companies'] = company_paginator.count
            results['company_page_obj'] = company_page

        if search_type in ['assistants', 'both']:
            assistant_qs = Assistant.objects.filter(
                Q(name__icontains=query) | Q(description__icontains=query),
                is_public=True,
                is_active=True
            ).select_related('company').distinct()

            assistant_paginator = Paginator(assistant_qs, page_size)
            assistant_page = assistant_paginator.get_page(page)

            results['assistants'] = assistant_page.object_list
            results['total_assistants'] = assistant_paginator.count
            results['assistant_page_obj'] = assistant_page

        return results


# Convenience functions for easy integration
def get_optimized_company_assistants(company, user=None, page_size=20):
    """Convenience function for getting company assistants."""
    return OptimizedAssistantQueries.get_company_assistants_optimized(company, user, page_size)


def get_optimized_assistant_stats(assistant_id, user=None):
    """Convenience function for getting assistant with stats."""
    return OptimizedAssistantQueries.get_assistant_with_stats(assistant_id, user)


def get_optimized_community_assistants(user=None, search_query=None, page=1, page_size=20):
    """Convenience function for getting community assistants."""
    return OptimizedAssistantQueries.get_community_assistants_optimized(user, search_query, page, page_size)


def get_optimized_assistant_analytics(assistant, period_days=30):
    """Convenience function for getting assistant analytics."""
    return OptimizedInteractionQueries.get_assistant_analytics_optimized(assistant, period_days)
