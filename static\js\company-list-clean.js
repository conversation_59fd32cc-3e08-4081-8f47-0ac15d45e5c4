/*
 * Clean Company/Constituency List JavaScript
 * Consolidated and conflict-free JS for directory pages
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== CSRF TOKEN HELPER =====
    function getCsrfToken() {
        const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (csrfInput) {
            return csrfInput.value;
        }
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        return csrfMeta ? csrfMeta.getAttribute('content') : '';
    }

    // ===== IMAGE FALLBACK HANDLING =====
    function handleImageError(img) {
        console.log('Image failed to load:', img.src);
        
        // Prevent infinite loop
        if (img.dataset.fallbackAttempted) {
            return;
        }
        img.dataset.fallbackAttempted = 'true';

        // Get company name for fallback
        const companyName = img.dataset.companyName || img.alt || 'Company';
        
        // Create fallback HTML
        const fallbackHtml = `
            <div class="company-logo-placeholder" style="width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; background: #f7f7f7; border-radius: 8px; color: #797979;">
                <i class="bi bi-building" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <span style="font-size: 0.8rem; text-align: center; max-width: 100px; word-wrap: break-word;">${companyName}</span>
            </div>
        `;
        
        // Replace image with fallback
        img.outerHTML = fallbackHtml;
    }

    function handleImageLoad(img) {
        // Image loaded successfully
        img.style.opacity = '1';
    }

    // Apply image handlers to all company logos
    const companyImages = document.querySelectorAll('.company-logo-img, .company-logo');
    companyImages.forEach(img => {
        img.addEventListener('error', () => handleImageError(img));
        img.addEventListener('load', () => handleImageLoad(img));
        
        // Set initial opacity for fade-in effect
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease';
    });

    // ===== CAROUSEL FUNCTIONALITY =====
    function initializeCarousel() {
        const carousel = document.querySelector('.company-logo-carousel');
        if (!carousel) return;

        // Pause animation on hover
        carousel.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
        });

        carousel.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
        });

        // Ensure smooth scrolling
        const items = carousel.querySelectorAll('.company-logo-item');
        if (items.length > 0) {
            // Clone items for seamless loop if needed
            const containerWidth = carousel.parentElement.offsetWidth;
            const totalItemsWidth = items.length * 220; // Approximate item width + gap
            
            if (totalItemsWidth < containerWidth * 2) {
                // Clone items to ensure smooth scrolling
                items.forEach(item => {
                    const clone = item.cloneNode(true);
                    carousel.appendChild(clone);
                });
            }
        }
    }

    // ===== LIKE BUTTON FUNCTIONALITY =====
    function initializeLikeButtons() {
        const likeButtons = document.querySelectorAll('.like-button');
        
        likeButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const itemId = this.dataset.itemId;
                const itemType = this.dataset.itemType;
                
                if (!itemId || !itemType) {
                    console.error('Missing item ID or type for like button');
                    return;
                }
                
                // Toggle like status
                const isLiked = this.classList.contains('text-danger');
                const action = isLiked ? 'unlike' : 'like';
                
                // Send AJAX request
                fetch(`/api/${itemType}/${itemId}/${action}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken(),
                    },
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update button appearance
                        if (action === 'like') {
                            this.classList.remove('text-secondary');
                            this.classList.add('text-danger');
                            this.title = 'Unlike';
                        } else {
                            this.classList.remove('text-danger');
                            this.classList.add('text-secondary');
                            this.title = 'Like';
                        }
                    } else {
                        console.error('Failed to update like status:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error updating like status:', error);
                });
            });
        });
    }

    // ===== RATING FUNCTIONALITY =====
    function initializeRatingModal() {
        const ratingModal = document.getElementById('ratingModal');
        if (!ratingModal) return;

        const modalStars = ratingModal.querySelectorAll('.modal-star-btn');
        const submitBtn = ratingModal.querySelector('#submitRatingBtn');
        let selectedRating = 0;
        let currentItemId = null;
        let currentItemType = null;

        // Handle rating modal show
        ratingModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            currentItemId = button.dataset.companyId || button.dataset.itemId;
            currentItemType = button.dataset.itemType || 'company';
            
            const itemName = button.dataset.companyName || button.dataset.itemName || 'this item';
            document.getElementById('modalItemName').textContent = itemName;
            
            // Reset stars and button
            selectedRating = 0;
            modalStars.forEach(star => {
                star.classList.remove('text-warning');
                star.classList.add('text-secondary');
                star.querySelector('i').classList.remove('bi-star-fill');
                star.querySelector('i').classList.add('bi-star');
            });
            submitBtn.disabled = true;
        });

        // Handle star clicks
        modalStars.forEach((star, index) => {
            star.addEventListener('click', function() {
                selectedRating = index + 1;
                
                // Update star display
                modalStars.forEach((s, i) => {
                    if (i < selectedRating) {
                        s.classList.remove('text-secondary');
                        s.classList.add('text-warning');
                        s.querySelector('i').classList.remove('bi-star');
                        s.querySelector('i').classList.add('bi-star-fill');
                    } else {
                        s.classList.remove('text-warning');
                        s.classList.add('text-secondary');
                        s.querySelector('i').classList.remove('bi-star-fill');
                        s.querySelector('i').classList.add('bi-star');
                    }
                });
                
                submitBtn.disabled = false;
            });
        });

        // Handle rating submission
        if (submitBtn) {
            submitBtn.addEventListener('click', function() {
                if (!selectedRating || !currentItemId) return;
                
                this.disabled = true;
                this.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Submitting...';
                
                fetch(`/api/${currentItemType}/${currentItemId}/rate/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCsrfToken(),
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify({ rating: selectedRating })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update rating display on page
                        const ratingContainer = document.getElementById(`rating-display-${currentItemType}-${currentItemId}`);
                        if (ratingContainer && data.html) {
                            ratingContainer.innerHTML = data.html;
                        }
                        
                        // Show success message
                        const messageContainer = document.getElementById(`rating-msg-${currentItemType}-${currentItemId}`);
                        if (messageContainer) {
                            messageContainer.textContent = 'Rating submitted successfully!';
                            messageContainer.style.display = 'block';
                            setTimeout(() => {
                                messageContainer.style.display = 'none';
                            }, 3000);
                        }
                        
                        // Close modal
                        const modal = bootstrap.Modal.getInstance(ratingModal);
                        modal.hide();
                    } else {
                        console.error('Failed to submit rating:', data.error);
                        alert('Failed to submit rating. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error submitting rating:', error);
                    alert('An error occurred. Please try again.');
                })
                .finally(() => {
                    this.disabled = false;
                    this.innerHTML = 'Submit Rating';
                });
            });
        }
    }

    // ===== CARD HOVER EFFECTS =====
    function initializeCardHoverEffects() {
        const directoryCards = document.querySelectorAll('.directory-card');
        
        directoryCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }

    // ===== INITIALIZE ALL FUNCTIONALITY =====
    initializeCarousel();
    initializeLikeButtons();
    initializeRatingModal();
    initializeCardHoverEffects();

    // Make functions globally available for inline handlers
    window.handleImageError = handleImageError;
    window.handleImageLoad = handleImageLoad;
    
    console.log('Company list functionality initialized');
});
