/**
 * Homepage Preloader CSS
 * Simple, fast-loading preloader for the homepage
 */

/* Preloader overlay */
#homepage-preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #121212;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

#homepage-preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

/* Simple spinner */
.preloader-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 102, 255, 0.3);
    border-top: 3px solid #0066ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Preloader text */
.preloader-text {
    color: #ffffff;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-size: 14px;
    margin-top: 20px;
    opacity: 0.8;
}

/* Logo animation */
.preloader-logo {
    width: 60px;
    height: 60px;
    margin-bottom: 20px;
    opacity: 0.9;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.9; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

/* Mobile optimizations */
@media (max-width: 767.98px) {
    .preloader-spinner {
        width: 30px;
        height: 30px;
        border-width: 2px;
    }
    
    .preloader-logo {
        width: 50px;
        height: 50px;
    }
    
    .preloader-text {
        font-size: 12px;
    }
}
