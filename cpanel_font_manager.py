#!/usr/bin/env python3
"""
cPanel Font Manager
Comprehensive font management for Django applications on cPanel Linux servers.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

class CPanelFontManager:
    def __init__(self):
        self.home_dir = os.path.expanduser("~")
        self.project_dir = os.getcwd()
        self.font_dirs = {
            'user_fonts': os.path.join(self.home_dir, '.fonts'),
            'home_fonts': os.path.join(self.home_dir, 'fonts'),
            'public_fonts': os.path.join(self.home_dir, 'public_html', 'fonts'),
            'static_fonts': os.path.join(self.project_dir, 'static', 'fonts'),
            'project_fonts': os.path.join(self.project_dir, 'fonts')
        }
        
    def create_font_directories(self):
        """Create all necessary font directories."""
        print("Creating font directories...")
        for name, path in self.font_dirs.items():
            os.makedirs(path, exist_ok=True)
            print(f"✓ Created {name}: {path}")
    
    def check_system_fonts(self):
        """Check what fonts are available on the system."""
        print("\n=== System Font Check ===")
        
        # Check common Linux font directories
        system_font_dirs = [
            "/usr/share/fonts",
            "/usr/share/fonts/truetype",
            "/usr/share/fonts/opentype",
            "/usr/local/share/fonts",
            "/usr/share/fonts/truetype/dejavu",
            "/usr/share/fonts/truetype/liberation",
            "/usr/share/fonts/truetype/freefont"
        ]
        
        available_fonts = []
        for font_dir in system_font_dirs:
            if os.path.exists(font_dir):
                print(f"✓ Found font directory: {font_dir}")
                try:
                    fonts = [f for f in os.listdir(font_dir) if f.endswith(('.ttf', '.otf'))]
                    if fonts:
                        available_fonts.extend(fonts)
                        print(f"  - Contains {len(fonts)} font files")
                except PermissionError:
                    print(f"  - Permission denied")
            else:
                print(f"✗ Not found: {font_dir}")
        
        print(f"\nTotal system fonts found: {len(available_fonts)}")
        return available_fonts
    
    def install_essential_fonts(self):
        """Install essential fonts for the project."""
        print("\n=== Installing Essential Fonts ===")
        
        # Essential font packages to try installing
        font_packages = [
            'fonts-liberation',
            'fonts-dejavu-core',
            'fonts-freefont-ttf',
            'fonts-noto-core'
        ]
        
        for package in font_packages:
            try:
                print(f"Attempting to install {package}...")
                result = subprocess.run(['apt-get', 'install', '-y', package], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✓ Successfully installed {package}")
                else:
                    print(f"✗ Failed to install {package} (may need sudo)")
            except FileNotFoundError:
                print(f"✗ apt-get not available for {package}")
    
    def copy_system_fonts_to_project(self):
        """Copy available system fonts to project directories."""
        print("\n=== Copying System Fonts to Project ===")
        
        system_font_dirs = [
            "/usr/share/fonts/truetype/dejavu",
            "/usr/share/fonts/truetype/liberation",
            "/usr/share/fonts/truetype/freefont"
        ]
        
        copied_count = 0
        for system_dir in system_font_dirs:
            if os.path.exists(system_dir):
                try:
                    fonts = [f for f in os.listdir(system_dir) if f.endswith(('.ttf', '.otf'))]
                    for font in fonts:
                        src = os.path.join(system_dir, font)
                        
                        # Copy to multiple locations for redundancy
                        for dest_name, dest_dir in self.font_dirs.items():
                            dest_path = os.path.join(dest_dir, font)
                            try:
                                if not os.path.exists(dest_path):
                                    subprocess.run(['cp', src, dest_path], check=True)
                                    copied_count += 1
                                    print(f"✓ Copied {font} to {dest_name}")
                            except subprocess.CalledProcessError:
                                print(f"✗ Failed to copy {font} to {dest_name}")
                except PermissionError:
                    print(f"✗ Permission denied accessing {system_dir}")
        
        print(f"Total fonts copied: {copied_count}")
    
    def set_font_permissions(self):
        """Set proper permissions for font files."""
        print("\n=== Setting Font Permissions ===")
        
        for name, font_dir in self.font_dirs.items():
            if os.path.exists(font_dir):
                try:
                    # Set directory permissions
                    os.chmod(font_dir, 0o755)
                    
                    # Set file permissions
                    for root, dirs, files in os.walk(font_dir):
                        for file in files:
                            if file.endswith(('.ttf', '.otf', '.woff', '.woff2')):
                                file_path = os.path.join(root, file)
                                os.chmod(file_path, 0o644)
                    
                    print(f"✓ Set permissions for {name}")
                except Exception as e:
                    print(f"✗ Failed to set permissions for {name}: {e}")
    
    def update_font_cache(self):
        """Update the font cache if fc-cache is available."""
        print("\n=== Updating Font Cache ===")
        
        try:
            # Try to update font cache for user fonts
            result = subprocess.run(['fc-cache', '-f', '-v', self.font_dirs['user_fonts']], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ Font cache updated successfully")
                print(result.stdout)
            else:
                print("✗ Failed to update font cache")
                print(result.stderr)
        except FileNotFoundError:
            print("✗ fc-cache not available")
    
    def test_font_loading(self):
        """Test font loading with PIL/Pillow."""
        print("\n=== Testing Font Loading ===")
        
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # Test fonts to try
            test_fonts = [
                'DejaVuSans.ttf',
                'DejaVuSans-Bold.ttf',
                'LiberationSans-Regular.ttf',
                'LiberationSans-Bold.ttf',
                'FreeSans.ttf'
            ]
            
            working_fonts = []
            
            for font_name in test_fonts:
                for dir_name, font_dir in self.font_dirs.items():
                    font_path = os.path.join(font_dir, font_name)
                    if os.path.exists(font_path):
                        try:
                            font = ImageFont.truetype(font_path, 20)
                            # Test if font actually works
                            img = Image.new('RGB', (100, 50), 'white')
                            draw = ImageDraw.Draw(img)
                            draw.text((10, 10), "Test", font=font, fill='black')
                            working_fonts.append((font_name, font_path))
                            print(f"✓ {font_name} works from {dir_name}")
                            break
                        except Exception as e:
                            print(f"✗ {font_name} failed: {e}")
            
            print(f"\nWorking fonts found: {len(working_fonts)}")
            return working_fonts
            
        except ImportError:
            print("✗ PIL/Pillow not available for testing")
            return []
    
    def create_font_test_script(self):
        """Create a script to test font loading."""
        script_content = '''#!/usr/bin/env python3
"""
Font Test Script
Test font loading for QR code generation and other PIL operations.
"""

import os
from PIL import Image, ImageDraw, ImageFont

def test_font_loading():
    """Test loading fonts from various locations."""
    font_dirs = [
        os.path.expanduser("~/.fonts"),
        os.path.expanduser("~/fonts"),
        os.path.expanduser("~/public_html/fonts"),
        "static/fonts",
        "fonts"
    ]
    
    test_fonts = [
        'DejaVuSans.ttf',
        'LiberationSans-Regular.ttf',
        'FreeSans.ttf',
        'Arial.ttf',
        'arial.ttf'
    ]
    
    print("Testing font loading...")
    working_fonts = []
    
    for font_name in test_fonts:
        for font_dir in font_dirs:
            font_path = os.path.join(font_dir, font_name)
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 24)
                    # Create test image
                    img = Image.new('RGB', (200, 100), 'white')
                    draw = ImageDraw.Draw(img)
                    draw.text((10, 30), "Font Test: A", font=font, fill='black')
                    
                    # Save test image
                    test_filename = f"font_test_{font_name.replace('.ttf', '')}.png"
                    img.save(test_filename)
                    
                    working_fonts.append((font_name, font_path))
                    print(f"✓ {font_name} works - saved {test_filename}")
                    break
                except Exception as e:
                    print(f"✗ {font_name} failed: {e}")
    
    print(f"\\nTotal working fonts: {len(working_fonts)}")
    return working_fonts

if __name__ == "__main__":
    test_font_loading()
'''
        
        with open('test_fonts.py', 'w') as f:
            f.write(script_content)
        os.chmod('test_fonts.py', 0o755)
        print("✓ Created font test script: test_fonts.py")
    
    def run_complete_setup(self):
        """Run the complete font setup process."""
        print("=== cPanel Font Manager - Complete Setup ===")
        
        self.create_font_directories()
        self.check_system_fonts()
        self.install_essential_fonts()
        self.copy_system_fonts_to_project()
        self.set_font_permissions()
        self.update_font_cache()
        working_fonts = self.test_font_loading()
        self.create_font_test_script()
        
        print("\n=== Setup Complete ===")
        print("Next steps:")
        print("1. Run: python test_fonts.py")
        print("2. Run: python manage.py collectstatic")
        print("3. Test your QR code generation")
        print("4. Check the generated font test images")
        
        if working_fonts:
            print(f"\n✓ {len(working_fonts)} fonts are ready to use!")
        else:
            print("\n⚠ No fonts were successfully loaded. You may need to:")
            print("  - Install fonts manually")
            print("  - Check file permissions")
            print("  - Contact your hosting provider")

def main():
    """Main function."""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        manager = CPanelFontManager()
        
        if command == "check":
            manager.check_system_fonts()
        elif command == "install":
            manager.install_essential_fonts()
        elif command == "copy":
            manager.copy_system_fonts_to_project()
        elif command == "permissions":
            manager.set_font_permissions()
        elif command == "test":
            manager.test_font_loading()
        elif command == "cache":
            manager.update_font_cache()
        else:
            print("Usage: python cpanel_font_manager.py [check|install|copy|permissions|test|cache]")
    else:
        # Run complete setup
        manager = CPanelFontManager()
        manager.run_complete_setup()

if __name__ == "__main__":
    main()
