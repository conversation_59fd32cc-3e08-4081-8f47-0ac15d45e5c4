{% extends 'base/layout.html' %}
{% load static %}
{% load account_tags %}

{% block title %}Switch Company - 24seven{% endblock %}



{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <!-- Header -->
            <div class="text-center mb-4">
                <img src="{% static 'img/24seven-logo.svg' %}" alt="24seven" class="mb-4" height="48">
                <h1 class="h3 mb-3">Switch Company</h1>
                <p class="text-muted">
                    Select the company you want to work with
                </p>
            </div>

            <!-- Company List -->
            <div class="card border-0 shadow-sm">
                <div class="list-group list-group-flush">
                    {% for company in companies %}
                        <div class="list-group-item list-group-item-action p-4">
                            <form method="post" class="d-flex align-items-center">
                                {% csrf_token %}
                                <input type="hidden" name="company_id" value="{{ company.id }}">

                                <!-- Company Info -->
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center">
                                        <!-- Logo/Icon -->
                                        <div class="flex-shrink-0 me-3">
                                            {% if company.logo %}
                                                <img src="{{ company.logo.url }}"
                                                     alt="{{ company.name }}"
                                                     class="rounded"
                                                     width="48" height="48">
                                            {% else %}
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                     style="width: 48px; height: 48px;">
                                                    <i class="bi bi-building text-muted"></i>
                                                </div>
                                            {% endif %}
                                        </div>

                                        <!-- Details -->
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                {{ company.name }}
                                                {% if company == active_company %}
                                                    <span class="badge bg-success ms-2">Active</span>
                                                {% endif %}
                                            </h6>
                                            <div class="text-muted small d-flex align-items-center">
                                                <span class="me-3">
                                                    <i class="bi bi-people me-1"></i>
                                                    {{ company.members.count }} member{{ company.members.count|pluralize }}
                                                </span>
                                                {% if user == company.owner %}
                                                    <span class="badge bg-primary">Owner</span>
                                                {% else %}
                                                    {{ user|company_role:company|role_badge }}
                                                {% endif %}
                                            </div>
                                        </div>

                                        <!-- Action -->
                                        <div class="flex-shrink-0 ms-3">
                                            {% if company == active_company %}
                                                <button type="submit" class="btn btn-light" disabled>
                                                    <i class="bi bi-check2-circle"></i>
                                                </button>
                                            {% else %}
                                                <button type="submit" class="btn btn-primary">
                                                    Switch
                                                </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    {% empty %}
                        <div class="list-group-item text-center py-5">
                            <div class="text-muted">
                                <i class="bi bi-building h3 mb-2"></i>
                                <p>You don't belong to any companies yet</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Actions -->
                <div class="card-footer bg-transparent p-4">
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:company_create' %}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            Create New Company
                        </a>
                        {# Always link back to home page #}
                        <a href="{% url 'home' %}" class="btn btn-light">
                            <i class="bi bi-house me-2"></i>
                            Back to Home Page
                        </a>
                    </div>
                </div>
            </div>

            <!-- Help Text -->
            <div class="text-center mt-4">
                <p class="text-muted small mb-0">
                    Need to join another company? Ask for an invitation from your team admin.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Switch Preview -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Switch to {{ company.name }}?</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>You are about to switch to working with <strong>{{ company.name }}</strong>.</p>
                <p class="mb-0">This will:</p>
                <ul class="mb-0">
                    <li>Change your active workspace</li>
                    <li>Update your available tools and content</li>
                    <li>Switch your team context</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="switchForm" class="btn btn-primary">Switch Company</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.list-group-item-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
    z-index: 1;
}

.company-card {
    transition: all 0.2s;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });

    // Form submissions
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const button = form.querySelector('button[type="submit"]:not([disabled])');
            if (button) {
                button.disabled = true;
                button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Switching...';
            }
        });
    });
});
</script>
{% endblock %}
