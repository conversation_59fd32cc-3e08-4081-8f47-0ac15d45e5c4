"""
Advanced database query optimization with bulk operations, materialized views, and query pooling.
"""

import time
import threading
from typing import Dict, Any, List, Optional, Union, Tuple
from collections import defaultdict, deque
from django.db import models, connection, transaction
from django.db.models import Q, F, Count, Sum, Avg, <PERSON>, <PERSON>, Prefetch
from django.core.cache import cache
from django.core.paginator import Paginator
import logging

logger = logging.getLogger(__name__)


class QueryPool:
    """
    Pool of prepared queries for reuse and better performance.
    """
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.queries = {}
        self.usage_count = defaultdict(int)
        self.last_used = {}
        self.lock = threading.RLock()
    
    def get_query(self, query_key: str, query_builder: callable) -> models.QuerySet:
        """Get a query from pool or create new one."""
        with self.lock:
            current_time = time.time()
            
            if query_key in self.queries:
                self.usage_count[query_key] += 1
                self.last_used[query_key] = current_time
                return self.queries[query_key].all()  # Clone the queryset
            
            # Create new query
            query = query_builder()
            
            # Add to pool if not full
            if len(self.queries) < self.max_size:
                self.queries[query_key] = query
                self.usage_count[query_key] = 1
                self.last_used[query_key] = current_time
            else:
                # Evict least recently used
                lru_key = min(self.last_used.keys(), key=lambda k: self.last_used[k])
                self._evict_query(lru_key)
                
                self.queries[query_key] = query
                self.usage_count[query_key] = 1
                self.last_used[query_key] = current_time
            
            return query.all()
    
    def _evict_query(self, query_key: str) -> None:
        """Evict a query from the pool."""
        self.queries.pop(query_key, None)
        self.usage_count.pop(query_key, None)
        self.last_used.pop(query_key, None)
    
    def clear(self) -> None:
        """Clear the query pool."""
        with self.lock:
            self.queries.clear()
            self.usage_count.clear()
            self.last_used.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pool statistics."""
        with self.lock:
            return {
                'pool_size': len(self.queries),
                'max_size': self.max_size,
                'total_usage': sum(self.usage_count.values()),
                'most_used': max(self.usage_count.items(), key=lambda x: x[1]) if self.usage_count else None
            }


class BulkOperationManager:
    """
    Manages bulk database operations for improved performance.
    """
    
    def __init__(self, batch_size: int = 1000):
        self.batch_size = batch_size
        self.pending_creates = defaultdict(list)
        self.pending_updates = defaultdict(list)
        self.pending_deletes = defaultdict(list)
        self.lock = threading.Lock()
    
    def bulk_create(self, model_class: models.Model, objects: List[models.Model], 
                   execute_immediately: bool = False) -> None:
        """Add objects for bulk creation."""
        with self.lock:
            model_name = model_class._meta.label
            self.pending_creates[model_name].extend(objects)
            
            if execute_immediately or len(self.pending_creates[model_name]) >= self.batch_size:
                self._execute_bulk_create(model_class, model_name)
    
    def bulk_update(self, model_class: models.Model, objects: List[models.Model], 
                   fields: List[str], execute_immediately: bool = False) -> None:
        """Add objects for bulk update."""
        with self.lock:
            model_name = model_class._meta.label
            self.pending_updates[model_name].extend([(obj, fields) for obj in objects])
            
            if execute_immediately or len(self.pending_updates[model_name]) >= self.batch_size:
                self._execute_bulk_update(model_class, model_name)
    
    def bulk_delete(self, model_class: models.Model, object_ids: List[int], 
                   execute_immediately: bool = False) -> None:
        """Add objects for bulk deletion."""
        with self.lock:
            model_name = model_class._meta.label
            self.pending_deletes[model_name].extend(object_ids)
            
            if execute_immediately or len(self.pending_deletes[model_name]) >= self.batch_size:
                self._execute_bulk_delete(model_class, model_name)
    
    def flush_all(self) -> Dict[str, int]:
        """Execute all pending bulk operations."""
        with self.lock:
            results = {}
            
            # Execute creates
            for model_name in list(self.pending_creates.keys()):
                model_class = self._get_model_class(model_name)
                if model_class:
                    results[f"{model_name}_created"] = self._execute_bulk_create(model_class, model_name)
            
            # Execute updates
            for model_name in list(self.pending_updates.keys()):
                model_class = self._get_model_class(model_name)
                if model_class:
                    results[f"{model_name}_updated"] = self._execute_bulk_update(model_class, model_name)
            
            # Execute deletes
            for model_name in list(self.pending_deletes.keys()):
                model_class = self._get_model_class(model_name)
                if model_class:
                    results[f"{model_name}_deleted"] = self._execute_bulk_delete(model_class, model_name)
            
            return results
    
    def _execute_bulk_create(self, model_class: models.Model, model_name: str) -> int:
        """Execute bulk create operation."""
        objects = self.pending_creates[model_name]
        if not objects:
            return 0
        
        try:
            created_objects = model_class.objects.bulk_create(objects, batch_size=self.batch_size)
            count = len(created_objects)
            self.pending_creates[model_name].clear()
            logger.info(f"Bulk created {count} {model_name} objects")
            return count
        except Exception as e:
            logger.error(f"Bulk create error for {model_name}: {e}")
            return 0
    
    def _execute_bulk_update(self, model_class: models.Model, model_name: str) -> int:
        """Execute bulk update operation."""
        updates = self.pending_updates[model_name]
        if not updates:
            return 0
        
        try:
            # Group by fields to update
            field_groups = defaultdict(list)
            for obj, fields in updates:
                field_key = tuple(sorted(fields))
                field_groups[field_key].append(obj)
            
            total_updated = 0
            for fields, objects in field_groups.items():
                updated_count = model_class.objects.bulk_update(objects, fields, batch_size=self.batch_size)
                total_updated += updated_count
            
            self.pending_updates[model_name].clear()
            logger.info(f"Bulk updated {total_updated} {model_name} objects")
            return total_updated
        except Exception as e:
            logger.error(f"Bulk update error for {model_name}: {e}")
            return 0
    
    def _execute_bulk_delete(self, model_class: models.Model, model_name: str) -> int:
        """Execute bulk delete operation."""
        object_ids = self.pending_deletes[model_name]
        if not object_ids:
            return 0
        
        try:
            deleted_count, _ = model_class.objects.filter(id__in=object_ids).delete()
            self.pending_deletes[model_name].clear()
            logger.info(f"Bulk deleted {deleted_count} {model_name} objects")
            return deleted_count
        except Exception as e:
            logger.error(f"Bulk delete error for {model_name}: {e}")
            return 0
    
    def _get_model_class(self, model_name: str) -> Optional[models.Model]:
        """Get model class from model name."""
        try:
            from django.apps import apps
            return apps.get_model(model_name)
        except Exception:
            return None


class MaterializedViewManager:
    """
    Manages materialized views for complex aggregations.
    """
    
    def __init__(self):
        self.views = {}
        self.refresh_intervals = {}
        self.last_refresh = {}
        self.lock = threading.Lock()
    
    def create_view(self, view_name: str, query: str, refresh_interval: int = 3600) -> None:
        """Create a materialized view."""
        with self.lock:
            try:
                with connection.cursor() as cursor:
                    # Drop existing view if it exists
                    cursor.execute(f"DROP MATERIALIZED VIEW IF EXISTS {view_name}")
                    
                    # Create new materialized view
                    cursor.execute(f"CREATE MATERIALIZED VIEW {view_name} AS {query}")
                    
                    self.views[view_name] = query
                    self.refresh_intervals[view_name] = refresh_interval
                    self.last_refresh[view_name] = time.time()
                    
                    logger.info(f"Created materialized view: {view_name}")
            except Exception as e:
                logger.error(f"Error creating materialized view {view_name}: {e}")
    
    def refresh_view(self, view_name: str) -> bool:
        """Refresh a materialized view."""
        if view_name not in self.views:
            return False
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"REFRESH MATERIALIZED VIEW {view_name}")
                self.last_refresh[view_name] = time.time()
                logger.info(f"Refreshed materialized view: {view_name}")
                return True
        except Exception as e:
            logger.error(f"Error refreshing materialized view {view_name}: {e}")
            return False
    
    def refresh_if_needed(self, view_name: str) -> bool:
        """Refresh view if refresh interval has passed."""
        if view_name not in self.views:
            return False
        
        current_time = time.time()
        last_refresh = self.last_refresh.get(view_name, 0)
        refresh_interval = self.refresh_intervals.get(view_name, 3600)
        
        if current_time - last_refresh > refresh_interval:
            return self.refresh_view(view_name)
        
        return False
    
    def query_view(self, view_name: str, additional_filters: str = "") -> List[Dict]:
        """Query a materialized view."""
        if view_name not in self.views:
            return []
        
        # Refresh if needed
        self.refresh_if_needed(view_name)
        
        try:
            with connection.cursor() as cursor:
                query = f"SELECT * FROM {view_name}"
                if additional_filters:
                    query += f" WHERE {additional_filters}"
                
                cursor.execute(query)
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Error querying materialized view {view_name}: {e}")
            return []


class QueryOptimizer:
    """
    Optimizes queries with hints and analysis.
    """
    
    def __init__(self):
        self.query_stats = defaultdict(list)
        self.slow_queries = deque(maxlen=100)
        self.optimization_rules = []
    
    def analyze_query(self, queryset: models.QuerySet) -> Dict[str, Any]:
        """Analyze query performance."""
        start_time = time.time()
        
        # Get query plan
        query_plan = self._get_query_plan(queryset)
        
        # Execute query
        list(queryset)  # Force evaluation
        
        execution_time = time.time() - start_time
        
        # Store stats
        query_hash = hash(str(queryset.query))
        self.query_stats[query_hash].append({
            'execution_time': execution_time,
            'timestamp': start_time,
            'query_plan': query_plan
        })
        
        # Track slow queries
        if execution_time > 1.0:  # Queries taking more than 1 second
            self.slow_queries.append({
                'query': str(queryset.query),
                'execution_time': execution_time,
                'timestamp': start_time
            })
        
        return {
            'execution_time': execution_time,
            'query_plan': query_plan,
            'is_slow': execution_time > 1.0
        }
    
    def _get_query_plan(self, queryset: models.QuerySet) -> Optional[str]:
        """Get query execution plan."""
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"EXPLAIN {queryset.query}")
                return '\n'.join([row[0] for row in cursor.fetchall()])
        except Exception as e:
            logger.warning(f"Could not get query plan: {e}")
            return None
    
    def get_slow_queries(self, limit: int = 10) -> List[Dict]:
        """Get slowest queries."""
        sorted_queries = sorted(
            self.slow_queries,
            key=lambda x: x['execution_time'],
            reverse=True
        )
        return sorted_queries[:limit]
    
    def optimize_queryset(self, queryset: models.QuerySet) -> models.QuerySet:
        """Apply optimization rules to queryset."""
        optimized = queryset
        
        for rule in self.optimization_rules:
            optimized = rule(optimized)
        
        return optimized
    
    def add_optimization_rule(self, rule: callable) -> None:
        """Add a query optimization rule."""
        self.optimization_rules.append(rule)


# Global instances
query_pool = QueryPool()
bulk_manager = BulkOperationManager()
view_manager = MaterializedViewManager()
query_optimizer = QueryOptimizer()


# Optimization rules
def add_select_related_rule(queryset: models.QuerySet) -> models.QuerySet:
    """Add select_related for foreign key fields."""
    model = queryset.model
    foreign_keys = [field.name for field in model._meta.fields if field.many_to_one]
    
    if foreign_keys and not queryset.query.select_related:
        return queryset.select_related(*foreign_keys[:3])  # Limit to avoid over-fetching
    
    return queryset


def add_prefetch_related_rule(queryset: models.QuerySet) -> models.QuerySet:
    """Add prefetch_related for reverse foreign key fields."""
    model = queryset.model
    reverse_fks = [field.get_accessor_name() for field in model._meta.get_fields() 
                   if field.one_to_many or field.many_to_many]
    
    if reverse_fks and not queryset._prefetch_related_lookups:
        return queryset.prefetch_related(*reverse_fks[:2])  # Limit to avoid over-fetching
    
    return queryset


# Register optimization rules
query_optimizer.add_optimization_rule(add_select_related_rule)
query_optimizer.add_optimization_rule(add_prefetch_related_rule)


# Convenience functions
def get_optimized_query(query_key: str, query_builder: callable) -> models.QuerySet:
    """Get an optimized query from pool."""
    queryset = query_pool.get_query(query_key, query_builder)
    return query_optimizer.optimize_queryset(queryset)


def bulk_create_objects(model_class: models.Model, objects: List[models.Model]) -> None:
    """Bulk create objects."""
    bulk_manager.bulk_create(model_class, objects)


def bulk_update_objects(model_class: models.Model, objects: List[models.Model], fields: List[str]) -> None:
    """Bulk update objects."""
    bulk_manager.bulk_update(model_class, objects, fields)


def create_assistant_stats_view() -> None:
    """Create materialized view for assistant statistics."""
    query = """
    SELECT 
        a.id,
        a.name,
        a.company_id,
        COUNT(i.id) as interaction_count,
        AVG(i.rating) as avg_rating,
        SUM(i.token_count) as total_tokens,
        AVG(i.duration) as avg_duration,
        MAX(i.created_at) as last_interaction
    FROM assistants_assistant a
    LEFT JOIN assistants_interaction i ON a.id = i.assistant_id
    WHERE a.is_active = true
    GROUP BY a.id, a.name, a.company_id
    """
    view_manager.create_view('assistant_stats_mv', query, refresh_interval=1800)


def get_assistant_stats_from_view(assistant_id: int = None) -> List[Dict]:
    """Get assistant statistics from materialized view."""
    filters = f"id = {assistant_id}" if assistant_id else ""
    return view_manager.query_view('assistant_stats_mv', filters)


def analyze_query_performance(queryset: models.QuerySet) -> Dict[str, Any]:
    """Analyze query performance."""
    return query_optimizer.analyze_query(queryset)


def get_performance_stats() -> Dict[str, Any]:
    """Get comprehensive performance statistics."""
    return {
        'query_pool': query_pool.get_stats(),
        'slow_queries': query_optimizer.get_slow_queries(),
        'materialized_views': list(view_manager.views.keys())
    }
