/**
 * Individual Card Hover Fix - Simplified
 * Ensures only individual directory cards respond to hover
 */

/* ===== PREVENT PARENT CONTAINER HOVER EFFECTS ===== */

/* Disable hover effects on parent containers */
.tier-section:hover,
.list-group:hover,
.company-cards-container:hover {
    background: transparent !important;
    background-color: transparent !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== INDIVIDUAL CARD ISOLATION ===== */

/* Ensure each directory card is isolated */
.directory-card {
    isolation: isolate !important;
    position: relative !important;
    z-index: 1 !important;
}

/* ===== CLEAN DIRECTORY CARD STYLES ===== */

/* Ensure directory cards have clean, consistent styling */
.directory-card.list-group-item {
    background: #ffffff !important;
    background-color: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    transform: none !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    /* Smooth transition for all hover effects */
    transition: all 0.2s ease !important;
    /* Ensure the entire card area is covered */
    overflow: hidden !important;
}

/* Ensure ALL child elements are transparent so card background shows through */
.directory-card.list-group-item:hover *,
.directory-card.list-group-item:hover .row,
.directory-card.list-group-item:hover .directory-item-link-wrapper,
.directory-card.list-group-item:hover .col-md-2,
.directory-card.list-group-item:hover .col-md-3,
.directory-card.list-group-item:hover .col-md-5,
.directory-card.list-group-item:hover .col-md-10,
.directory-card.list-group-item:hover .logo-container,
.directory-card.list-group-item:hover .contact-info,
.directory-card.list-group-item:hover .rating-display-container {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

/* Ensure buttons and badges maintain their own colors */
.directory-card.list-group-item:hover .btn,
.directory-card.list-group-item:hover .badge,
.directory-card.list-group-item:hover .like-button {
    background: revert !important;
    background-color: revert !important;
}

/* ===== INDIVIDUAL CARD HOVER EFFECT ===== */

/* Complete card hover effect - entire card changes color */
.directory-card.list-group-item:hover {
    /* Enhanced shadow that extends beyond the card */
    box-shadow:
        0 6px 20px rgba(207, 46, 46, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;

    /* Strong NUP red border that covers the entire card */
    border: 3px solid rgba(207, 46, 46, 0.4) !important;

    /* More noticeable background color change - light NUP red tint */
    background: linear-gradient(135deg, #ffe8e8 0%, #ffebeb 50%, #ffe8e8 100%) !important;
    background-color: #ffe8e8 !important;

    /* Higher z-index */
    z-index: 2 !important;

    /* No transform effects */
    transform: none !important;

    /* Smooth transition */
    transition: all 0.2s ease !important;

    /* Ensure the entire card area is affected */
    outline: none !important;

    /* Force the background to cover the entire card including padding */
    background-clip: border-box !important;
}

/* ===== OVERRIDE PROBLEMATIC BROAD SELECTORS ===== */

/* Override enhanced-card-visibility.css broad selectors */
.tier-section .card:hover,
.tier-section [class*="card"]:hover,
.company-cards-container .card:hover,
.company-cards-container [class*="card"]:hover {
    border-color: #e0e0e0 !important;
    transform: none !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    background: #ffffff !important;
    background-color: #ffffff !important;
}

/* Override Bootstrap list-group hover effects */
.list-group-item:hover:not(.directory-card) {
    background-color: #ffffff !important;
    border-color: #e0e0e0 !important;
}

/* ===== FORCE COMPLETE CARD HOVER COVERAGE ===== */

/* Very specific override to ensure the entire card changes color */
.directory-card.list-group-item:hover,
.directory-card.list-group-item:hover::before,
.directory-card.list-group-item:hover::after {
    /* Force the background to cover everything */
    background: linear-gradient(135deg, #ffe8e8 0%, #ffebeb 50%, #ffe8e8 100%) !important;
    background-color: #ffe8e8 !important;
    background-image: linear-gradient(135deg, #ffe8e8 0%, #ffebeb 50%, #ffe8e8 100%) !important;

    /* Ensure border covers the entire perimeter */
    border: 3px solid rgba(207, 46, 46, 0.4) !important;
    border-radius: 12px !important;

    /* Force the styling to apply to the entire element */
    box-sizing: border-box !important;

    /* Enhanced shadow with NUP red tint */
    box-shadow:
        0 0 0 1px rgba(207, 46, 46, 0.1),
        0 6px 20px rgba(207, 46, 46, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08) !important;
}
