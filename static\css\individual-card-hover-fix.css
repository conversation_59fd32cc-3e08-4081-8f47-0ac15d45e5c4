/**
 * Individual Card Hover Fix - Simplified
 * Ensures only individual directory cards respond to hover
 */

/* ===== PREVENT PARENT CONTAINER HOVER EFFECTS ===== */

/* COMPLETELY disable hover effects on ALL parent containers and group cards */
.tier-section:hover,
.list-group:hover,
.company-cards-container:hover,
.card-group:hover,
.tier-cards:hover,
.constituency-cards:hover,
.featured-section:hover,
.directory-section:hover,
.list-group-item:hover:not(.directory-card),
.card:hover:not(.directory-card),
[class*="tier"]:hover:not(.directory-card),
[class*="group"]:hover:not(.directory-card) {
    background: transparent !important;
    background-color: transparent !important;
    transform: none !important;
    box-shadow: none !important;
    border-color: inherit !important;
    scale: none !important;
    filter: none !important;
}

/* Force group containers to maintain their original styling */
.tier-section,
.list-group,
.company-cards-container,
.card-group,
.tier-cards,
.constituency-cards {
    background: inherit !important;
    background-color: inherit !important;
    transition: none !important;
}

/* ===== INDIVIDUAL CARD ISOLATION ===== */

/* Ensure each directory card is isolated */
.directory-card {
    isolation: isolate !important;
    position: relative !important;
    z-index: 1 !important;
}

/* ===== CLEAN DIRECTORY CARD STYLES ===== */

/* Ensure directory cards have clean, consistent styling with strong contrast */
.directory-card.list-group-item {
    background: #ffffff !important;
    background-color: #ffffff !important;
    border: 2px solid #d0d0d0 !important;
    border-radius: 12px !important;
    transform: none !important;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06) !important;
    /* Smooth transition for all hover effects */
    transition: all 0.3s ease !important;
    /* Ensure the entire card area is covered */
    overflow: hidden !important;
    /* Strong contrast against gray background */
    margin: 8px 0 !important;
}

/* Ensure ALL child elements are transparent so STRONG card background shows through */
.directory-card.list-group-item:hover *,
.directory-card.list-group-item:hover .row,
.directory-card.list-group-item:hover .directory-item-link-wrapper,
.directory-card.list-group-item:hover .col-md-2,
.directory-card.list-group-item:hover .col-md-3,
.directory-card.list-group-item:hover .col-md-5,
.directory-card.list-group-item:hover .col-md-10,
.directory-card.list-group-item:hover .logo-container,
.directory-card.list-group-item:hover .contact-info,
.directory-card.list-group-item:hover .rating-display-container {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

/* Force text to remain readable on the new background */
.directory-card.list-group-item:hover h6,
.directory-card.list-group-item:hover p,
.directory-card.list-group-item:hover span:not(.badge),
.directory-card.list-group-item:hover .text-muted {
    color: #242424 !important;
}

/* Ensure buttons and badges maintain their own colors */
.directory-card.list-group-item:hover .btn,
.directory-card.list-group-item:hover .badge,
.directory-card.list-group-item:hover .like-button {
    background: revert !important;
    background-color: revert !important;
}

/* ===== INDIVIDUAL CARD HOVER EFFECT ===== */

/* DRAMATIC card hover effect - entire card changes to strong NUP colors */
.directory-card.list-group-item:hover {
    /* Dramatic shadow with strong NUP red glow */
    box-shadow:
        0 0 0 3px rgba(207, 46, 46, 0.3),
        0 8px 25px rgba(207, 46, 46, 0.25),
        0 6px 15px rgba(0, 0, 0, 0.15) !important;

    /* Strong NUP red border */
    border: 3px solid #cf2e2e !important;

    /* STRONG background color change - noticeable NUP red tint */
    background: linear-gradient(135deg, #ffd6d6 0%, #ffcccc 50%, #ffd6d6 100%) !important;
    background-color: #ffd6d6 !important;

    /* Higher z-index to lift above other cards */
    z-index: 10 !important;

    /* Slight scale for emphasis */
    transform: scale(1.02) !important;

    /* Smooth transition */
    transition: all 0.3s ease !important;

    /* Ensure the entire card area is affected */
    outline: none !important;

    /* Force the background to cover the entire card including padding */
    background-clip: border-box !important;
}

/* ===== OVERRIDE PROBLEMATIC BROAD SELECTORS ===== */

/* Override enhanced-card-visibility.css broad selectors - DISABLE group card hover */
.tier-section .card:hover:not(.directory-card),
.tier-section [class*="card"]:hover:not(.directory-card),
.company-cards-container .card:hover:not(.directory-card),
.company-cards-container [class*="card"]:hover:not(.directory-card),
.list-group .card:hover:not(.directory-card),
.card-group .card:hover:not(.directory-card),
.tier-cards:hover,
.constituency-cards:hover,
.featured-section .card:hover:not(.directory-card) {
    border-color: inherit !important;
    transform: none !important;
    box-shadow: inherit !important;
    background: inherit !important;
    background-color: inherit !important;
    scale: none !important;
    filter: none !important;
    transition: none !important;
}

/* Specifically target group containers to prevent ANY hover styling */
.tier-section,
.company-cards-container,
.list-group,
.card-group,
.tier-cards,
.constituency-cards,
.featured-section {
    pointer-events: none !important;
}

/* Re-enable pointer events ONLY for individual directory cards */
.directory-card.list-group-item {
    pointer-events: auto !important;
}

/* Override Bootstrap list-group hover effects */
.list-group-item:hover:not(.directory-card) {
    background-color: #ffffff !important;
    border-color: #e0e0e0 !important;
}

/* ===== FORCE COMPLETE CARD HOVER COVERAGE ===== */

/* Very specific override to ensure the entire card changes color DRAMATICALLY */
.directory-card.list-group-item:hover,
.directory-card.list-group-item:hover::before,
.directory-card.list-group-item:hover::after {
    /* Force STRONG background to cover everything */
    background: linear-gradient(135deg, #ffd6d6 0%, #ffcccc 50%, #ffd6d6 100%) !important;
    background-color: #ffd6d6 !important;
    background-image: linear-gradient(135deg, #ffd6d6 0%, #ffcccc 50%, #ffd6d6 100%) !important;

    /* Strong NUP red border covers the entire perimeter */
    border: 3px solid #cf2e2e !important;
    border-radius: 12px !important;

    /* Force the styling to apply to the entire element */
    box-sizing: border-box !important;

    /* DRAMATIC shadow with strong NUP red glow */
    box-shadow:
        0 0 0 3px rgba(207, 46, 46, 0.3),
        0 8px 25px rgba(207, 46, 46, 0.25),
        0 6px 15px rgba(0, 0, 0, 0.15) !important;

    /* Slight scale for emphasis */
    transform: scale(1.02) !important;
}
