/**
 * Individual Card Hover Fix
 * Ensures only individual cards respond to hover, not entire sections or all cards in a tier
 */

/* ===== PREVENT PARENT CONTAINER HOVER EFFECTS ===== */

/* Completely disable hover effects on parent containers that might affect all child cards */
.tier-section,
.tier-section:hover,
.tier-section:focus,
.tier-section:active,
.list-group,
.list-group:hover,
.list-group:focus,
.list-group:active,
.company-cards-container,
.company-cards-container:hover,
.company-cards-container:focus,
.company-cards-container:active {
    /* No hover effects on containers */
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    transform: none !important;
    box-shadow: none !important;
    border: none !important;
    /* Prevent any CSS transitions on containers */
    transition: none !important;
    /* Prevent hover bleeding */
    isolation: isolate !important;
}

/* ===== PREVENT HOVER BLEEDING BETWEEN CARDS ===== */

/* Ensure each card is completely isolated from siblings */
.directory-card {
    /* Complete isolation */
    isolation: isolate !important;
    position: relative !important;
    z-index: 1 !important;
    /* Prevent layout/style bleeding */
    contain: layout style !important;
}

/* ===== RESET ALL PROBLEMATIC SELECTORS ===== */

/* Override any CSS that might cause multiple cards to highlight */
.tier-section .directory-card,
.tier-section .list-group-item,
.company-cards-container .directory-card,
.company-cards-container .list-group-item,
.list-group .directory-card,
.list-group .list-group-item {
    /* Force clean state - no inherited hover effects */
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    transform: none !important;
    perspective: none !important;
    backdrop-filter: none !important;
    /* Clean shadow */
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    /* Only allow individual card transitions */
    transition: box-shadow 0.2s ease, border-color 0.2s ease !important;
}

/* ===== INDIVIDUAL CARD HOVER - VERY SPECIFIC TARGETING ===== */

/* Only target individual cards, not when parent containers are hovered */
.directory-card:hover:not(.tier-section:hover .directory-card):not(.list-group:hover .directory-card):not(.company-cards-container:hover .directory-card),
.list-group-item.directory-card:hover:not(.tier-section:hover .list-group-item):not(.list-group:hover .list-group-item):not(.company-cards-container:hover .list-group-item) {
    /* Very subtle individual hover effect */
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    /* Slightly higher z-index for the hovered card */
    z-index: 2 !important;
    /* Keep clean white background */
    background: #ffffff !important;
    background-color: #ffffff !important;
    /* No transform effects */
    transform: none !important;
}

/* ===== PREVENT CASCADING HOVER EFFECTS ===== */

/* Ensure parent hover doesn't affect children */
.tier-section:hover .directory-card,
.tier-section:hover .list-group-item,
.company-cards-container:hover .directory-card,
.company-cards-container:hover .list-group-item,
.list-group:hover .directory-card,
.list-group:hover .list-group-item {
    /* Force clean state when parent is hovered */
    background: #ffffff !important;
    background-color: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    transform: none !important;
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
}

/* ===== OVERRIDE ANY REMAINING PROBLEMATIC STYLES ===== */

/* Final override to ensure no other CSS interferes */
.directory-card,
.directory-card:focus,
.directory-card:active,
.directory-card.list-group-item,
.directory-card.list-group-item:focus,
.directory-card.list-group-item:active,
.list-group-item.directory-card,
.list-group-item.directory-card:focus,
.list-group-item.directory-card:active {
    /* Clean, consistent appearance */
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    transform: none !important;
    perspective: none !important;
    backdrop-filter: none !important;
    transition: box-shadow 0.2s ease, border-color 0.2s ease !important;
    /* Isolation */
    isolation: isolate !important;
    position: relative !important;
}

/* ===== SPECIFIC HOVER STATE FOR INDIVIDUAL CARDS ONLY ===== */

/* Very specific selector to only target individual card hover */
.directory-card:hover,
.list-group-item.directory-card:hover {
    /* Only apply when the specific card is hovered, not its container */
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    z-index: 2 !important;
    /* Keep everything else the same */
    background: #ffffff !important;
    background-color: #ffffff !important;
    transform: none !important;
}

/* ===== DISABLE PROBLEMATIC BOOTSTRAP/FRAMEWORK HOVER EFFECTS ===== */

/* Override Bootstrap list-group hover effects */
.list-group-item:hover,
.list-group-item:focus,
.list-group-item:active,
.list-group-item.active {
    background-color: #ffffff !important;
    border-color: #e0e0e0 !important;
    color: inherit !important;
}

/* Override any framework hover effects on containers */
.list-group:hover,
.card:hover,
.card-body:hover {
    background: transparent !important;
    background-color: transparent !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== OVERRIDE ENHANCED-CARD-VISIBILITY.CSS PROBLEMATIC RULES ===== */

/* Override the broad card hover selectors that affect all cards */
.tier-section .card:hover,
.tier-section [class*="card"]:hover,
.company-cards-container .card:hover,
.company-cards-container [class*="card"]:hover,
.list-group .card:hover,
.list-group [class*="card"]:hover {
    /* Prevent enhanced-card-visibility.css from affecting directory cards */
    border-color: #e0e0e0 !important;
    transform: none !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
}

/* ===== VERY SPECIFIC DIRECTORY CARD TARGETING ===== */

/* Target only directory cards specifically, not other card types */
.directory-card.list-group-item:hover,
.list-group-item.directory-card:hover,
div[class*="directory-card"]:hover {
    /* Only these specific directory cards get hover effects */
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    z-index: 2 !important;
    background: #ffffff !important;
    background-color: #ffffff !important;
    transform: none !important;
}

/* ===== PREVENT JAVASCRIPT HOVER EFFECTS ===== */

/* Override any JavaScript-added classes that might cause issues */
.directory-card.shadow,
.directory-card.shadow-sm,
.directory-card.shadow-lg {
    /* Reset any JavaScript-added shadow classes */
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
}

.directory-card:hover.shadow,
.directory-card:hover.shadow-sm,
.directory-card:hover.shadow-lg {
    /* Individual card hover with shadow classes */
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
}
