/**
 * Individual Card Hover Fix - Simplified
 * Ensures only individual directory cards respond to hover
 */

/* ===== PREVENT PARENT CONTAINER HOVER EFFECTS ===== */

/* Disable hover effects on parent containers */
.tier-section:hover,
.list-group:hover,
.company-cards-container:hover {
    background: transparent !important;
    background-color: transparent !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== INDIVIDUAL CARD ISOLATION ===== */

/* Ensure each directory card is isolated */
.directory-card {
    isolation: isolate !important;
    position: relative !important;
    z-index: 1 !important;
}

/* ===== CLEAN DIRECTORY CARD STYLES ===== */

/* Ensure directory cards have clean, consistent styling */
.directory-card.list-group-item {
    background: #ffffff !important;
    background-color: #ffffff !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    transform: none !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    transition: box-shadow 0.2s ease, border-color 0.2s ease !important;
}

/* ===== INDIVIDUAL CARD HOVER EFFECT ===== */

/* Simple hover effect for individual directory cards only */
.directory-card.list-group-item:hover {
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    z-index: 2 !important;
    background: #ffffff !important;
    background-color: #ffffff !important;
    transform: none !important;
}

/* ===== OVERRIDE PROBLEMATIC BROAD SELECTORS ===== */

/* Override enhanced-card-visibility.css broad selectors */
.tier-section .card:hover,
.tier-section [class*="card"]:hover,
.company-cards-container .card:hover,
.company-cards-container [class*="card"]:hover {
    border-color: #e0e0e0 !important;
    transform: none !important;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 4px rgba(0, 0, 0, 0.04) !important;
    background: #ffffff !important;
    background-color: #ffffff !important;
}

/* Override Bootstrap list-group hover effects */
.list-group-item:hover:not(.directory-card) {
    background-color: #ffffff !important;
    border-color: #e0e0e0 !important;
}
