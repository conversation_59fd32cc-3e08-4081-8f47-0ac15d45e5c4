/**
 * Individual Card Hover Fix - Simplified
 * Ensures only individual directory cards respond to hover
 */

/* ===== PREVENT PARENT CONTAINER HOVER EFFECTS ===== */

/* COMPLETELY disable hover effects on ALL parent containers and group cards */
.tier-section:hover,
.list-group:hover,
.company-cards-container:hover,
.card-group:hover,
.tier-cards:hover,
.constituency-cards:hover,
.featured-section:hover,
.directory-section:hover,
.list-group-item:hover:not(.directory-card),
.card:hover:not(.directory-card),
[class*="tier"]:hover:not(.directory-card),
[class*="group"]:hover:not(.directory-card),
/* CRITICAL: Prevent the main mt-5 container from changing colors - USELESS HOVER DISABLED */
.mt-5:hover,
.mt-5:has(.directory-card:hover),
.container:has(.directory-card:hover),
.company-directory-container:hover,
.company-directory-container:has(.directory-card:hover) {
    background: transparent !important;
    background-color: transparent !important;
    transform: none !important;
    box-shadow: none !important;
    border-color: inherit !important;
    scale: none !important;
    filter: none !important;
}

/* Force group containers to maintain their original styling */
.tier-section,
.list-group,
.company-cards-container,
.card-group,
.tier-cards,
.constituency-cards,
/* CRITICAL: Force mt-5 and main containers to maintain original styling */
.mt-5,
.container,
.company-directory-container,
div:has(.directory-card) {
    background: inherit !important;
    background-color: inherit !important;
    transition: none !important;
}

/* FORCE mt-5 container to NEVER change background regardless of child hover states */
.mt-5,
.mt-5:hover,
.mt-5:has(.directory-card:hover),
.mt-5 *:hover {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    filter: none !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== INDIVIDUAL CARD ISOLATION ===== */

/* Ensure each directory card is isolated */
.directory-card {
    isolation: isolate !important;
    position: relative !important;
    z-index: 1 !important;
}

/* ===== CLEAN DIRECTORY CARD STYLES ===== */

/* Ensure directory cards have clean, consistent styling with strong contrast */
.directory-card.list-group-item {
    background: #ffffff !important;
    background-color: #ffffff !important;
    border: 2px solid #d0d0d0 !important;
    border-radius: 12px !important;
    transform: none !important;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06) !important;
    /* Smooth transition for all hover effects */
    transition: all 0.3s ease !important;
    /* Ensure the entire card area is covered */
    overflow: hidden !important;
    /* Strong contrast against gray background */
    margin: 8px 0 !important;
}

/* Ensure ALL child elements are transparent so STRONG card background shows through */
.directory-card.list-group-item:hover *,
.directory-card.list-group-item:hover .row,
.directory-card.list-group-item:hover .directory-item-link-wrapper,
.directory-card.list-group-item:hover .col-md-2,
.directory-card.list-group-item:hover .col-md-3,
.directory-card.list-group-item:hover .col-md-5,
.directory-card.list-group-item:hover .col-md-10,
.directory-card.list-group-item:hover .logo-container,
.directory-card.list-group-item:hover .contact-info,
.directory-card.list-group-item:hover .rating-display-container {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

/* Enhanced text readability with official NUP colors */
.directory-card.list-group-item:hover h6,
.directory-card.list-group-item:hover h5,
.directory-card.list-group-item:hover h4,
.directory-card.list-group-item:hover p,
.directory-card.list-group-item:hover span:not(.badge),
.directory-card.list-group-item:hover .text-muted,
.directory-card.list-group-item:hover .company-name,
.directory-card.list-group-item:hover .company-description {
    color: #242424 !important;  /* Official NUP Dark Charcoal for excellent readability */
    font-weight: 600 !important; /* Slightly bolder for enhanced visibility */
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important; /* Subtle white shadow for depth */
}

/* Ensure buttons and badges maintain their own colors */
.directory-card.list-group-item:hover .btn,
.directory-card.list-group-item:hover .badge,
.directory-card.list-group-item:hover .like-button {
    background: revert !important;
    background-color: revert !important;
}

/* ===== INDIVIDUAL CARD HOVER EFFECT ===== */
/* Copy Manage Assistants hover animation with NUP party colors */

/* Base card styling - matching Manage Assistants structure */
.directory-card.list-group-item {
    border: 1px solid #e0e0e0 !important;
    border-radius: 12px !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.98) 100%) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    margin-bottom: 1rem !important;
    overflow: hidden !important;
    position: relative !important;
    cursor: pointer !important;
    pointer-events: auto !important;
}

/* Hover effect - copied from Manage Assistants with NUP colors */
/* OVERRIDE INLINE TEMPLATE STYLES - Multiple selectors for maximum specificity */
.directory-card.list-group-item:hover,
.list-group-item.directory-card:hover,
.company-cards-container .list-group-item.directory-card:hover,
.list-group .directory-card.list-group-item:hover {
    /* Same transform as Manage Assistants */
    transform: translateY(-2px) !important;

    /* Enhanced shadow with NUP red accent - matching Manage Assistants intensity */
    box-shadow: 0 8px 25px rgba(207, 46, 46, 0.15) !important;

    /* NUP red border instead of blue */
    border-color: rgba(207, 46, 46, 0.3) !important;

    /* Enhanced NUP-themed background gradient */
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 1) 0%,
        rgba(255, 248, 248, 1) 100%) !important;

    /* Higher z-index for layering */
    z-index: 10 !important;

    /* Smooth transition using same timing as Manage Assistants */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* ===== OVERRIDE INLINE TEMPLATE STYLES ===== */

/* Override the inline .list-group-item:hover styles from company_list.html template */
.list-group .list-group-item:hover:not(.directory-card) {
    /* Keep the basic template hover for non-directory cards */
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px) !important;
}

/* ===== OVERRIDE PROBLEMATIC BROAD SELECTORS ===== */

/* Override enhanced-card-visibility.css broad selectors - DISABLE group card hover */
.tier-section .card:hover:not(.directory-card),
.tier-section [class*="card"]:hover:not(.directory-card),
.company-cards-container .card:hover:not(.directory-card),
.company-cards-container [class*="card"]:hover:not(.directory-card),
.list-group .card:hover:not(.directory-card),
.card-group .card:hover:not(.directory-card),
.tier-cards:hover,
.constituency-cards:hover,
.featured-section .card:hover:not(.directory-card) {
    border-color: inherit !important;
    transform: none !important;
    box-shadow: inherit !important;
    background: inherit !important;
    background-color: inherit !important;
    scale: none !important;
    filter: none !important;
    transition: none !important;
}

/* REMOVE pointer-events: none from containers to allow card hover */
/* Only disable hover STYLING, not pointer events */

/* Re-enable pointer events for ALL elements - let CSS handle hover styling */
.tier-section,
.company-cards-container,
.list-group,
.card-group,
.tier-cards,
.constituency-cards,
.featured-section,
.mt-5,
.directory-card.list-group-item,
.directory-card.list-group-item *,
.directory-card.list-group-item a,
.directory-card.list-group-item button,
.directory-card.list-group-item .btn,
.directory-card.list-group-item .like-button {
    pointer-events: auto !important;
}

/* Override Bootstrap list-group hover effects */
.list-group-item:hover:not(.directory-card) {
    background-color: #ffffff !important;
    border-color: #e0e0e0 !important;
}



/* ===== FORCE COMPLETE CARD HOVER COVERAGE ===== */

/* Ensure consistent NUP theme application across entire card */
.directory-card.list-group-item:hover,
.directory-card.list-group-item:hover::before,
.directory-card.list-group-item:hover::after {
    /* Official NUP-themed background gradient */
    background: linear-gradient(135deg,
        #ffffff 0%,           /* Pure white start */
        #fff8f8 25%,          /* Very light red tint */
        #ffeeee 50%,          /* Light red tint */
        #fff8f8 75%,          /* Very light red tint */
        #ffffff 100%          /* Pure white end */
    ) !important;
    background-color: #fff8f8 !important;

    /* Official NUP red border */
    border: 3px solid #cf2e2e !important;
    border-radius: 12px !important;

    /* Proper box model */
    box-sizing: border-box !important;

    /* Professional NUP-themed shadow */
    box-shadow:
        0 0 0 2px rgba(207, 46, 46, 0.2),        /* NUP red outline */
        0 8px 20px rgba(207, 46, 46, 0.15),      /* NUP red shadow */
        0 4px 12px rgba(0, 0, 0, 0.1),           /* Depth shadow */
        0 2px 6px rgba(0, 0, 0, 0.05) !important; /* Subtle base shadow */

    /* Elegant elevation effect */
    transform: translateY(-3px) scale(1.01) !important;
}


