"""
Management command to optimize Django application for cPanel hosting.
This command sets up database cache tables and performs other optimizations.
"""

from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.core.cache import cache
from django.db import connection
from django.conf import settings
import os


class Command(BaseCommand):
    help = 'Optimize Django application for cPanel hosting environment'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-cache-tables',
            action='store_true',
            help='Skip creating cache tables',
        )
        parser.add_argument(
            '--skip-static',
            action='store_true',
            help='Skip collecting static files',
        )
        parser.add_argument(
            '--skip-migrations',
            action='store_true',
            help='Skip running migrations',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting cPanel optimization...')
        )

        # Run migrations if not skipped
        if not options['skip_migrations']:
            self.stdout.write('Running database migrations...')
            try:
                call_command('migrate', verbosity=0)
                self.stdout.write(
                    self.style.SUCCESS('✓ Database migrations completed')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Migration failed: {e}')
                )

        # Create cache tables if not skipped
        if not options['skip_cache_tables']:
            self.create_cache_tables()

        # Collect static files if not skipped
        if not options['skip_static']:
            self.stdout.write('Collecting static files...')
            try:
                call_command('collectstatic', '--noinput', verbosity=0)
                self.stdout.write(
                    self.style.SUCCESS('✓ Static files collected')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Static files collection failed: {e}')
                )

        # Create necessary directories
        self.create_directories()

        # Optimize database
        self.optimize_database()

        # Clear cache
        self.clear_cache()

        # Create log directories
        self.create_log_directories()

        self.stdout.write(
            self.style.SUCCESS('cPanel optimization completed!')
        )

    def create_cache_tables(self):
        """Create database cache tables."""
        self.stdout.write('Creating cache tables...')
        
        cache_tables = [
            'django_cache_table',
            'django_session_tokens_cache'
        ]
        
        for table_name in cache_tables:
            try:
                # Check if table exists
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = %s
                        );
                    """, [table_name])
                    
                    table_exists = cursor.fetchone()[0]
                    
                    if not table_exists:
                        # Create cache table
                        call_command('createcachetable', table_name, verbosity=0)
                        self.stdout.write(
                            self.style.SUCCESS(f'✓ Created cache table: {table_name}')
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(f'⚠ Cache table already exists: {table_name}')
                        )
                        
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Failed to create cache table {table_name}: {e}')
                )

    def create_directories(self):
        """Create necessary directories for cPanel deployment."""
        self.stdout.write('Creating necessary directories...')
        
        directories = [
            'cache',
            'session',
            'logs',
            'media',
            'staticfiles',
            'tmp',
        ]
        
        for directory in directories:
            dir_path = os.path.join(settings.BASE_DIR, directory)
            try:
                os.makedirs(dir_path, exist_ok=True)
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Created directory: {directory}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ Failed to create directory {directory}: {e}')
                )

    def optimize_database(self):
        """Optimize database for better performance."""
        self.stdout.write('Optimizing database...')
        
        try:
            with connection.cursor() as cursor:
                # Analyze tables for better query planning
                cursor.execute("ANALYZE;")
                
                # Clean up expired sessions
                cursor.execute("""
                    DELETE FROM django_session 
                    WHERE expire_date < NOW();
                """)
                
                deleted_sessions = cursor.rowcount
                if deleted_sessions > 0:
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ Cleaned up {deleted_sessions} expired sessions')
                    )
                
                # Vacuum database (PostgreSQL specific)
                if 'postgresql' in settings.DATABASES['default']['ENGINE']:
                    cursor.execute("VACUUM;")
                    self.stdout.write(
                        self.style.SUCCESS('✓ Database vacuum completed')
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Database optimization failed: {e}')
            )

    def clear_cache(self):
        """Clear all caches."""
        self.stdout.write('Clearing cache...')
        
        try:
            cache.clear()
            self.stdout.write(
                self.style.SUCCESS('✓ Cache cleared')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Cache clearing failed: {e}')
            )

    def create_log_directories(self):
        """Create log directories and files."""
        self.stdout.write('Setting up logging...')
        
        log_dir = os.path.join(settings.BASE_DIR, 'logs')
        log_files = [
            'django.log',
            'error.log',
            'access.log',
        ]
        
        try:
            os.makedirs(log_dir, exist_ok=True)
            
            for log_file in log_files:
                log_path = os.path.join(log_dir, log_file)
                if not os.path.exists(log_path):
                    with open(log_path, 'w') as f:
                        f.write('')  # Create empty log file
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ Created log file: {log_file}')
                    )
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Log setup failed: {e}')
            )

    def check_cpanel_environment(self):
        """Check if running in cPanel environment."""
        cpanel_indicators = [
            'CPANEL_ENV',
            'PASSENGER_WSGI',
            'DOCUMENT_ROOT',
        ]
        
        for indicator in cpanel_indicators:
            if indicator in os.environ:
                return True
        
        return False
