#!/usr/bin/env python
"""
Debug script to understand database connection and table creation issues.
"""

import os
import django
from django.db import connection
from django.conf import settings

def setup_django():
    """Setup Django environment."""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
    django.setup()

def debug_database_connection():
    """Debug database connection and settings."""
    print("🔍 Database Connection Debug")
    print("=" * 50)
    
    # Show database settings
    db_config = settings.DATABASES['default']
    print("📋 Database Configuration:")
    print(f"   Engine: {db_config['ENGINE']}")
    print(f"   Name: {db_config['NAME']}")
    print(f"   User: {db_config['USER']}")
    print(f"   Host: {db_config['HOST']}")
    print(f"   Port: {db_config['PORT']}")
    
    # Test connection
    try:
        with connection.cursor() as cursor:
            # Get database name
            cursor.execute("SELECT current_database();")
            current_db = cursor.fetchone()[0]
            print(f"   Connected to: {current_db}")
            
            # Get current user
            cursor.execute("SELECT current_user;")
            current_user = cursor.fetchone()[0]
            print(f"   Connected as: {current_user}")
            
            # Get current schema
            cursor.execute("SELECT current_schema();")
            current_schema = cursor.fetchone()[0]
            print(f"   Current schema: {current_schema}")
            
            print("✅ Database connection successful!")
            
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    
    return True

def list_all_tables():
    """List all tables in the database."""
    print("\n📋 All Tables in Database:")
    print("-" * 30)
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                ORDER BY table_name;
            """)
            tables = cursor.fetchall()
            
            if tables:
                for table in tables:
                    table_name = table[0]
                    # Check if it's the cache table
                    if 'cache' in table_name.lower():
                        print(f"   🎯 {table_name} (CACHE TABLE!)")
                    else:
                        print(f"   📄 {table_name}")
            else:
                print("   ❌ No tables found!")
                
    except Exception as e:
        print(f"❌ Error listing tables: {e}")

def search_cache_tables():
    """Search for any cache-related tables."""
    print("\n🔍 Searching for Cache Tables:")
    print("-" * 35)
    
    try:
        with connection.cursor() as cursor:
            # Search for any table with 'cache' in the name
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE '%cache%'
                ORDER BY table_name;
            """)
            cache_tables = cursor.fetchall()
            
            if cache_tables:
                print("   Found cache-related tables:")
                for table in cache_tables:
                    print(f"   ✅ {table[0]}")
            else:
                print("   ❌ No cache tables found")
                
            # Also search in all schemas
            cursor.execute("""
                SELECT table_schema, table_name 
                FROM information_schema.tables 
                WHERE table_name = 'django_cache_table'
                ORDER BY table_schema;
            """)
            all_cache_tables = cursor.fetchall()
            
            if all_cache_tables:
                print("\n   django_cache_table found in schemas:")
                for schema, table in all_cache_tables:
                    print(f"   📍 {schema}.{table}")
            else:
                print("\n   ❌ django_cache_table not found in any schema")
                
    except Exception as e:
        print(f"❌ Error searching cache tables: {e}")

def test_cache_creation():
    """Test creating the cache table manually."""
    print("\n🔧 Testing Manual Cache Table Creation:")
    print("-" * 40)
    
    try:
        with connection.cursor() as cursor:
            # Try to create the table manually
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS django_cache_table (
                    cache_key VARCHAR(255) NOT NULL PRIMARY KEY,
                    value TEXT NOT NULL,
                    expires TIMESTAMP WITH TIME ZONE NOT NULL
                );
            """)
            
            # Create index
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS django_cache_table_expires 
                ON django_cache_table (expires);
            """)
            
            print("✅ Manual cache table creation successful!")
            
            # Verify it exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'django_cache_table'
                );
            """)
            exists = cursor.fetchone()[0]
            
            if exists:
                print("✅ Table verification successful!")
                return True
            else:
                print("❌ Table verification failed!")
                return False
                
    except Exception as e:
        print(f"❌ Manual table creation failed: {e}")
        return False

def main():
    """Main debug function."""
    print("🐛 Django Cache Table Debug Tool")
    print("=" * 50)
    
    # Setup Django
    setup_django()
    
    # Debug database connection
    if not debug_database_connection():
        return
    
    # List all tables
    list_all_tables()
    
    # Search for cache tables
    search_cache_tables()
    
    # Test manual creation
    if test_cache_creation():
        print("\n🎉 Cache table issue resolved!")
        print("Try running your Django application now.")
    else:
        print("\n❌ Cache table issue persists.")
        print("Check database permissions and connection settings.")

if __name__ == '__main__':
    main()
