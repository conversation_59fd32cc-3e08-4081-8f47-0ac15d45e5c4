/**
 * Enhanced Card Boundaries CSS
 * Makes card boundaries more pronounced with darker borders, 3D effects, and shadows
 */

/* ===== ENHANCED CARD STYLING ===== */
.card, .card-body, .card-header, .card-footer {
    border: 2px solid #333333 !important;
    border-radius: 12px !important;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    background-color: #ffffff !important;
    position: relative !important;
}

.card {
    border: 3px solid #333333 !important;
    box-shadow: 
        0 8px 16px rgba(0, 0, 0, 0.2),
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
}

.card:hover {
    border-color: #cf2e2e !important;
    box-shadow: 
        0 12px 24px rgba(0, 0, 0, 0.25),
        0 6px 12px rgba(0, 0, 0, 0.2),
        0 3px 6px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
}

/* ===== CARD HEADERS ===== */
.card-header {
    border-bottom: 2px solid #333333 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    border-radius: 10px 10px 0 0 !important;
    position: relative !important;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #333333, transparent) !important;
}

/* ===== CARD FOOTERS ===== */
.card-footer {
    border-top: 2px solid #333333 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    box-shadow: 
        0 -2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    border-radius: 0 0 10px 10px !important;
    position: relative !important;
}

.card-footer::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #333333, transparent) !important;
}

/* ===== CARD BODY ENHANCEMENTS ===== */
.card-body {
    border: none !important;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
    position: relative !important;
}

/* ===== FORM CARDS ===== */
.search-form-card, .form-card {
    border: 3px solid #333333 !important;
    border-radius: 15px !important;
    box-shadow: 
        0 10px 20px rgba(0, 0, 0, 0.2),
        0 5px 10px rgba(0, 0, 0, 0.15),
        0 2px 5px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    position: relative !important;
}

.search-form-card::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #cf2e2e, #252638, #cf2e2e) !important;
    border-radius: 18px !important;
    z-index: -1 !important;
    opacity: 0.1 !important;
}

/* ===== INPUT FIELD ENHANCEMENTS ===== */
.form-control, .form-select, input, textarea, select {
    border: 2px solid #666666 !important;
    border-radius: 8px !important;
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.05) !important;
    background-color: #ffffff !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus, input:focus, textarea:focus, select:focus {
    border-color: #cf2e2e !important;
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 0 0 3px rgba(207, 46, 46, 0.2),
        0 2px 8px rgba(207, 46, 46, 0.15) !important;
    outline: none !important;
}

/* ===== BUTTON ENHANCEMENTS ===== */
.btn {
    border: 2px solid transparent !important;
    border-radius: 8px !important;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
    overflow: hidden !important;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
    transition: left 0.5s ease !important;
}

.btn:hover::before {
    left: 100% !important;
}

.btn-primary {
    border-color: #b82626 !important;
    box-shadow: 
        0 4px 8px rgba(207, 46, 46, 0.3),
        0 2px 4px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.btn-secondary {
    border-color: #1e1f2e !important;
    box-shadow: 
        0 4px 8px rgba(37, 38, 56, 0.3),
        0 2px 4px rgba(37, 38, 56, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* ===== LIST GROUP ENHANCEMENTS ===== */
.list-group-item {
    border: 2px solid #dee2e6 !important;
    border-radius: 8px !important;
    margin-bottom: 4px !important;
    box-shadow: 
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    transition: all 0.3s ease !important;
}

.list-group-item:hover {
    border-color: #cf2e2e !important;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
    transform: translateX(4px) !important;
}

/* ===== TABLE ENHANCEMENTS ===== */
.table {
    border: 2px solid #333333 !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.table th, .table td {
    border: 1px solid #666666 !important;
    position: relative !important;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
}

.table tbody tr:hover {
    background-color: rgba(207, 46, 46, 0.05) !important;
    box-shadow: inset 0 0 0 2px rgba(207, 46, 46, 0.1) !important;
}

/* ===== MODAL ENHANCEMENTS ===== */
.modal-content {
    border: 3px solid #333333 !important;
    border-radius: 15px !important;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 10px 20px rgba(0, 0, 0, 0.2),
        0 5px 10px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.modal-header {
    border-bottom: 2px solid #333333 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.modal-footer {
    border-top: 2px solid #333333 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* ===== ACCORDION ENHANCEMENTS ===== */
.accordion-item {
    border: 2px solid #333333 !important;
    border-radius: 10px !important;
    margin-bottom: 8px !important;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.accordion-button {
    border: none !important;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
    color: #ffffff !important;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 2px 4px rgba(207, 46, 46, 0.3) !important;
}

/* ===== DROPDOWN ENHANCEMENTS ===== */
.dropdown-menu {
    border: 2px solid #333333 !important;
    border-radius: 10px !important;
    box-shadow: 
        0 8px 16px rgba(0, 0, 0, 0.2),
        0 4px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.dropdown-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
    color: #ffffff !important;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

/* ===== ALERT ENHANCEMENTS ===== */
.alert {
    border: 2px solid #333333 !important;
    border-radius: 10px !important;
    box-shadow: 
        0 4px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    position: relative !important;
}

.alert::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 12px !important;
    background: linear-gradient(135deg, rgba(207, 46, 46, 0.1), rgba(37, 38, 56, 0.1)) !important;
    z-index: -1 !important;
}

/* ===== SEARCH FORM SPECIFIC ENHANCEMENTS ===== */
.search-form-card {
    border: 4px solid #333333 !important;
    border-radius: 20px !important;
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.25),
        0 8px 15px rgba(0, 0, 0, 0.2),
        0 4px 8px rgba(0, 0, 0, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.4) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%) !important;
    position: relative !important;
    overflow: hidden !important;
    text-align: center !important;
    padding: 1.5rem !important;
}

.search-form-card::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(45deg, #cf2e2e, #252638, #cf2e2e, #252638) !important;
    background-size: 400% 400% !important;
    border-radius: 24px !important;
    z-index: -1 !important;
    animation: gradientShift 6s ease infinite !important;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.search-form-card:hover {
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 12px 20px rgba(0, 0, 0, 0.25),
        0 6px 12px rgba(0, 0, 0, 0.2),
        inset 0 2px 0 rgba(255, 255, 255, 0.5) !important;
}

/* ===== FORM GROUP ENHANCEMENTS ===== */
.form-group, .mb-3, .mb-4 {
    position: relative !important;
    margin-bottom: 1.5rem !important;
}

.form-group::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, rgba(207, 46, 46, 0.1), rgba(37, 38, 56, 0.1)) !important;
    border-radius: 12px !important;
    z-index: -1 !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.form-group:hover::before {
    opacity: 1 !important;
}

/* ===== LABEL ENHANCEMENTS ===== */
.form-label, label {
    font-weight: 600 !important;
    color: #333333 !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
    margin-bottom: 8px !important;
    display: block !important;
    position: relative !important;
}

.form-label::after, label::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #cf2e2e, #252638) !important;
    transition: width 0.3s ease !important;
}

.form-group:hover .form-label::after,
.form-group:hover label::after {
    width: 100% !important;
}

/* ===== RADIO BUTTON AND CHECKBOX ENHANCEMENTS ===== */
.form-check:not(.search-form-card .form-check) {
    position: relative !important;
    padding: 12px !important;
    border: 2px solid #dee2e6 !important;
    border-radius: 8px !important;
    margin-bottom: 8px !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    transition: all 0.3s ease !important;
}

.form-check:not(.search-form-card .form-check):hover {
    border-color: #cf2e2e !important;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
    transform: translateX(4px) !important;
}

.form-check-input:checked {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    box-shadow: 0 0 0 3px rgba(207, 46, 46, 0.2) !important;
}

/* ===== SEARCH FORM RADIO BUTTON CONTAINER ===== */
.search-form-card .d-flex.flex-wrap {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 1rem !important;
    margin: 0.75rem 0 !important;
    width: 100% !important;
}

.search-form-card .form-check-inline {
    margin: 0 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ===== COMPACT SEARCH FORM RADIO BUTTONS ===== */
.search-form-card .form-check {
    position: relative !important;
    padding: 8px 12px !important;
    border: 3px solid #666666 !important;
    border-radius: 8px !important;
    margin: 0 !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%) !important;
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6) !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    min-width: auto !important;
    flex: 0 0 auto !important;
    cursor: pointer !important;
}

.search-form-card .form-check:hover {
    border-color: #cf2e2e !important;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(207, 46, 46, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    transform: translateY(-2px) scale(1.02) !important;
    background: linear-gradient(135deg, #ffffff 0%, #fff5f5 50%, #ffffff 100%) !important;
}

.search-form-card .form-check-input {
    margin-top: 0 !important;
    margin-right: 0.5rem !important;
    width: 1.1rem !important;
    height: 1.1rem !important;
    flex-shrink: 0 !important;
    border: 2px solid #666666 !important;
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.search-form-card .form-check-input:checked {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    box-shadow:
        0 0 0 3px rgba(207, 46, 46, 0.3),
        0 2px 4px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.search-form-card .form-check-input:focus {
    border-color: #cf2e2e !important;
    box-shadow:
        0 0 0 3px rgba(207, 46, 46, 0.25),
        0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.search-form-card .form-check-label {
    font-size: 0.85rem !important;
    font-weight: 700 !important;
    color: #333333 !important;
    margin-bottom: 0 !important;
    line-height: 1.3 !important;
    cursor: pointer !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;
}

/* ===== SEARCH BUTTON SPECIAL STYLING ===== */
.search-form-card .btn {
    border: 3px solid #cf2e2e !important;
    border-radius: 12px !important;
    box-shadow:
        0 6px 12px rgba(207, 46, 46, 0.3),
        0 3px 6px rgba(207, 46, 46, 0.2),
        inset 0 2px 0 rgba(255, 255, 255, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    font-weight: 700 !important;
}

.search-form-card .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent) !important;
    transition: left 0.6s ease !important;
}

.search-form-card .btn:hover::before {
    left: 100% !important;
}

.search-form-card .btn:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow:
        0 10px 20px rgba(207, 46, 46, 0.4),
        0 5px 10px rgba(207, 46, 46, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.4) !important;
}

/* ===== RESPONSIVE CENTERING ADJUSTMENTS ===== */
@media (min-width: 576px) {
    .search-form-card .d-flex.flex-wrap {
        justify-content: flex-start !important;
        margin: 0 !important;
    }

    .search-form-card .col-12.d-flex {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .search-form-card .ms-auto {
        margin-left: auto !important;
        margin-top: 0 !important;
        width: auto !important;
        justify-content: flex-end !important;
    }
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .card {
        border-width: 2px !important;
        box-shadow:
            0 4px 8px rgba(0, 0, 0, 0.15),
            0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    .search-form-card {
        border-width: 3px !important;
        border-radius: 15px !important;
        padding: 1.25rem !important;
    }

    .search-form-card .d-flex.flex-wrap {
        gap: 0.75rem !important;
        margin: 0.5rem 0 !important;
    }

    .btn {
        box-shadow:
            0 2px 4px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    }

    .search-form-card .btn {
        border-width: 2px !important;
        border-radius: 8px !important;
    }
}

@media (max-width: 576px) {
    .search-form-card {
        padding: 1rem !important;
    }

    .search-form-card .d-flex.flex-wrap {
        gap: 0.5rem !important;
        margin: 0.5rem 0 !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .search-form-card .form-check {
        margin-bottom: 0.25rem !important;
    }

    .search-form-card .ms-auto {
        width: 100% !important;
        margin-top: 0.75rem !important;
        justify-content: center !important;
    }
}
