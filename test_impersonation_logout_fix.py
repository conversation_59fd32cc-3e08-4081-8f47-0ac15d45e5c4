#!/usr/bin/env python
"""
Test script for the impersonation logout fix.
This script validates that the impersonation session preservation is working correctly.
"""

import os
import sys
import django

# Setup Django environment FIRST
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

# Now import Django modules
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.contrib.sessions.models import Session
from django.urls import reverse

def test_impersonation_middleware():
    """Test that the impersonation middleware is properly configured."""
    print("🔍 Testing impersonation middleware configuration...")
    
    from django.conf import settings
    
    # Check if our custom middleware is in the middleware stack
    middleware_classes = settings.MIDDLEWARE
    
    impersonate_middleware_found = False
    session_fix_middleware_found = False
    
    for middleware in middleware_classes:
        if 'impersonate_fix' in middleware:
            impersonate_middleware_found = True
            print(f"✅ Found impersonate fix middleware: {middleware}")
        if 'impersonate_session_fix' in middleware:
            session_fix_middleware_found = True
            print(f"✅ Found session fix middleware: {middleware}")
    
    if not impersonate_middleware_found:
        print("❌ Impersonate fix middleware not found in MIDDLEWARE")
        return False
    
    if session_fix_middleware_found:
        print("✅ Session fix middleware found in MIDDLEWARE")
    else:
        print("⚠️  Session fix middleware not found (this is optional)")
    
    return True

def test_decorator_imports():
    """Test that the impersonation decorators can be imported."""
    print("🔍 Testing decorator imports...")
    
    try:
        from accounts.impersonate_decorators import preserve_impersonation, debug_impersonation
        print("✅ Successfully imported preserve_impersonation decorator")
        print("✅ Successfully imported debug_impersonation decorator")
        return True
    except ImportError as e:
        print(f"❌ Failed to import decorators: {e}")
        return False

def test_template_tag_imports():
    """Test that the enhanced template tags can be imported."""
    print("🔍 Testing template tag imports...")

    try:
        from accounts.templatetags.permission_tags import check_assistant_change
        print("✅ Successfully imported enhanced check_assistant_change template tag")
        return True
    except ImportError as e:
        print(f"❌ Failed to import template tags: {e}")
        return False

def test_assistant_views_decorators():
    """Test that the assistant views have the proper decorators applied."""
    print("🔍 Testing assistant views decorators...")
    
    try:
        from assistants.views import assistant_update, assistant_delete, assistant_analytics, assistant_usage
        
        # Check if the views have the decorators applied
        views_to_check = [
            ('assistant_update', assistant_update),
            ('assistant_delete', assistant_delete),
            ('assistant_analytics', assistant_analytics),
            ('assistant_usage', assistant_usage)
        ]
        
        for view_name, view_func in views_to_check:
            # Check if the view has been wrapped with our decorators
            # This is a simple check - in a real scenario, we'd need more sophisticated testing
            if hasattr(view_func, '__wrapped__'):
                print(f"✅ {view_name} appears to have decorators applied")
            else:
                print(f"⚠️  {view_name} may not have decorators applied")
        
        return True
    except ImportError as e:
        print(f"❌ Failed to import assistant views: {e}")
        return False

def test_session_preservation_logic():
    """Test the session preservation logic."""
    print("🔍 Testing session preservation logic...")

    try:
        from accounts.impersonate_decorators import preserve_impersonation

        # Create a mock request object with proper session interface
        class MockSession(dict):
            def save(self):
                pass

        class MockRequest:
            def __init__(self):
                self.session = MockSession({'_impersonate': 123})
                self.user = None
                self.is_impersonate = False

        # Create a mock view function
        def mock_view(request):
            return "view_response"

        # Apply the decorator
        decorated_view = preserve_impersonation(mock_view)

        # Test the decorated view
        mock_request = MockRequest()
        result = decorated_view(mock_request)

        print("✅ Session preservation decorator executed successfully")
        return True
    except Exception as e:
        print(f"❌ Session preservation test failed: {e}")
        return False

def test_permission_tag_logic():
    """Test the enhanced permission tag logic."""
    print("🔍 Testing permission tag logic...")

    try:
        from accounts.templatetags.permission_tags import check_assistant_change

        # Create mock objects
        class MockUser:
            def __init__(self, user_id, is_authenticated=True):
                self.id = user_id
                self.is_authenticated = is_authenticated
                self.username = f"user_{user_id}"

            def has_perm(self, perm, obj=None):
                # Mock permission check that fails initially
                return False

        class MockCompany:
            def __init__(self, owner_id):
                self.owner_id = owner_id

        class MockAssistant:
            def __init__(self, company):
                self.company = company

        # Test scenario: user is company owner
        user = MockUser(1)
        company = MockCompany(1)  # Same ID as user
        assistant = MockAssistant(company)

        # This should work even if the permission check fails initially
        result = check_assistant_change(user, assistant)
        print(f"✅ Permission tag executed successfully, result: {result}")
        return True
    except Exception as e:
        print(f"❌ Permission tag test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide a summary."""
    print("🧪 Running Comprehensive Impersonation Fix Tests")
    print("=" * 60)
    
    tests = [
        ("Middleware Configuration", test_impersonation_middleware),
        ("Decorator Imports", test_decorator_imports),
        ("Template Tag Imports", test_template_tag_imports),
        ("Assistant Views Decorators", test_assistant_views_decorators),
        ("Session Preservation Logic", test_session_preservation_logic),
        ("Permission Tag Logic", test_permission_tag_logic),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The impersonation fix appears to be working correctly.")
        print("\n📋 Next Steps:")
        print("1. Start the Django development server")
        print("2. Log in as a superuser")
        print("3. Start impersonating a company owner")
        print("4. Navigate to manage assistants")
        print("5. Click on settings buttons to verify impersonation is preserved")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == '__main__':
    run_comprehensive_test()
