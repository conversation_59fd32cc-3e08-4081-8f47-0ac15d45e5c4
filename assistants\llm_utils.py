"""Utilities for handling AI/LLM operations."""

import os
import json
import time
import openai
from openai import APIConnectionError, AuthenticationError, RateLimitError, APIError # Import the specific errors
import traceback # Import traceback
from typing import Dict, Any, Optional, List
import tiktoken
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from anthropic import Anthropic
from datetime import datetime # Added for date in prompt

# Configure API clients using environment-configurable base URLs
openai_client = openai.OpenAI(
    api_key=settings.OPENAI_API_KEY,
    base_url=settings.OPENAI_BASE_URL
)

# Configure Groq client for Llama models
groq_client = openai.OpenAI(
    base_url=settings.GROQ_BASE_URL,
    api_key=settings.GROQ_API_KEY
)

anthropic_client = Anthropic(api_key=settings.ANTHROPIC_API_KEY)

# Gemini specific configuration
GEMINI_API_KEY = settings.GEMINI_API_KEY
GEMINI_BASE_URL = settings.GEMINI_BASE_URL
gemini_client = None
if GEMINI_API_KEY:
    try:
        gemini_client = openai.OpenAI(api_key=GEMINI_API_KEY, base_url=GEMINI_BASE_URL)
        print("Gemini client initialized successfully.")
    except Exception as e:
        print(f"Error initializing Gemini client: {e}")
else:
    print("GEMINI_API_KEY not found in settings. Gemini models will not be available.")


def get_token_count(text: str, model: str = "gpt-3.5-turbo") -> int:
    """Calculate the number of tokens in a text string."""
    try:
        encoding = tiktoken.encoding_for_model(model)
        return len(encoding.encode(text))
    except Exception:
        # Fallback to approximate token count
         return len(text.split()) * 1.3

def generate_assistant_response(assistant, user_input: str, user,
                              history: Optional[list] = None,
                              current_context_id: Optional[str] = None) -> Dict[str, Any]:
                              # Added current_context_id parameter
    """Generate a response from an AI assistant, considering conversation history."""
    start_time = time.time()
    history = history or [] # Ensure history is a list

    # --- Construct detailed system prompt like Gradio script ---
    website_data = assistant.website_data or {}
    # Get navigation items from website_data, default to empty list
    navigation_items = website_data.get('navigation_items', [])
    extra_context = assistant.extra_context or ""
    # Use persona_name if available, otherwise fall back to the internal name
    display_name = assistant.persona_name or assistant.name
    company = assistant.company # Get the related company object
    company_name = company.name

    # --- Fetch CompanyInformation for mission and founded date ---
    company_info = getattr(company, 'info', None) # Safely access related 'info' object
    mission = getattr(company_info, 'mission', "No mission provided.") if company_info else "No mission provided."
    # Format founded year if available
    founded_year = getattr(company_info, 'founded', None) if company_info else None
    founded = str(founded_year) if founded_year else "N/A"
    # --- End Fetch CompanyInformation ---

    system_context = (
        f"You are {display_name}, a friendly assistant for {company_name}. " # Use display_name
        f"Your company mission is: '{mission}', established in {founded}. "
        f"Today's date is {datetime.now().strftime('%B %d, %Y')}. "
        f"Do not start your responses with greetings like 'Hello!'. " # Added instruction
    )
    # Conditionally add website data and nav config for support assistants
    # Import Assistant model locally only when needed for type comparison
    from .models import Assistant
    if assistant.assistant_type == Assistant.TYPE_SUPPORT: # Check assistant type
        system_context += "\n\nUse the following website data and navigation structure to answer questions accurately:\n"
        # Add Navigation Structure Summary
        if navigation_items:
            system_context += "Navigation Structure:\n"
            for item in navigation_items:
                # Ensure item is a dict and has expected keys before accessing
                if isinstance(item, dict) and 'label' in item and 'unique_id' in item:
                    system_context += f"- {item.get('label', 'Unknown Label')} (ID: {item.get('unique_id', 'N/A')}, Type: {item.get('section_type', 'N/A')})\n"
            system_context += "\n" # Add a newline after the structure

        # Add Website Data Content, keyed by the navitems-X index key
        system_context += "\n--- Website Content Sections ---\n" # Clearer section header
        # Iterate through navigation items to structure the content output
        for item in navigation_items:
            if isinstance(item, dict) and 'unique_id' in item and 'label' in item:
                unique_id = item['unique_id']
                label = item['label']
                item_id = item['id'] # Get the actual item ID
                # Construct the correct data key using the item ID
                data_key = f"item_{item_id}"
                # Get the data associated with this item ID key
                item_data = website_data.get(data_key)
                # Add more explicit instruction for the LLM
                system_context += f"\n## Content for Section: '{label}' (ID: {unique_id})\n"
                system_context += f"When asked specifically about '{label}', use the following information:\n"
                if item_data:
                    # Append the actual content (string or JSON representation of list/dict)
                    if isinstance(item_data, (str, int, float, bool)):
                         system_context += f"{item_data}\n" # Remove extra newline for compactness
                    else: # For lists/dicts, dump as JSON for clarity in the prompt
                         system_context += f"{json.dumps(item_data, indent=2)}\n" # Remove extra newline
                else:
                    system_context += "(No specific data provided for this section)\n"
        system_context += "--- End Website Content Sections ---\n" # Clearer section end

    elif website_data: # For non-support types, include website_data if it exists (excluding nav items and navitems-X keys)
         # Filter out navigation_items and navitems-X keys before dumping
         filtered_data = {k: v for k, v in website_data.items() if k != 'navigation_items' and not k.startswith('navitems-')}
         if filtered_data:
             system_context += (
                f"\n\nUse the following general website data if relevant:\n" # Clarified label
                f"{json.dumps(filtered_data, indent=2)}"
             )

    # Append extra_context if present (outside the conditional block, applies to all types)
    if extra_context:
        system_context += f"\n\nAdditional context:\n{extra_context}"
    # Append system_prompt if present
    if assistant.system_prompt:
        system_context += f"\n\nSystem Instructions:\n{assistant.system_prompt}"
    # --- End system prompt construction ---
    # Prepare the messages list for the API call
    messages = [{"role": "system", "content": system_context}]

    # --- Add conversation history ---
    # Ensure history items have 'role' and 'content'
    valid_history = [msg for msg in history if isinstance(msg, dict) and 'role' in msg and 'content' in msg]
    messages.extend(valid_history)
    # --- End adding history ---

    # Add the current user input
    messages.append({"role": "user", "content": user_input})

    # --- Log the final messages list and token count before API call ---
    print(f"--- [LLM DEBUG] Sending to LLM ({assistant.model}) ---")
    # Calculate total tokens in the prompt messages
    total_prompt_tokens = 0
    for msg in messages:
        if isinstance(msg, dict) and 'content' in msg and isinstance(msg['content'], str):
            total_prompt_tokens += get_token_count(msg['content'], assistant.model)
    print(f"[LLM DEBUG] Estimated PROMPT token count: {total_prompt_tokens}")
    # Log the messages themselves (handle potential errors)
    try:
        # Use json.dumps for pretty printing, handle potential non-serializable data defensively
        print(json.dumps(messages, indent=2, default=str))
    except Exception as log_e:
        print(f"[LLM DEBUG] Error logging messages: {log_e}")
        print(f"[LLM DEBUG] Raw messages list: {messages}") # Fallback to raw print
    print(f"--- [LLM DEBUG] End LLM Input ---")
    # --- End Logging ---

    try:
        # Determine model type and call appropriate API
        model_name = assistant.model
        response_content = ""

        if model_name.startswith('gpt') or model_name.startswith('llama'):
            # Both OpenAI and Llama models (via Groq) use the OpenAI client
            response = _generate_openai_response(
                messages=messages,
                model=model_name,
                temperature=assistant.temperature,
                max_tokens=assistant.max_tokens
            )
            response_content = response['content']
        elif model_name.startswith('claude'):
            response = _generate_anthropic_response(
                messages=messages,
                model=model_name,
                temperature=assistant.temperature,
                max_tokens=assistant.max_tokens
            )
            response_content = response['content']
        elif model_name.startswith('gemini'):
             if not gemini_client:
                 raise ValueError("Gemini API key not configured, cannot use Gemini models.")
             # Use the dedicated Gemini client
             response = _generate_gemini_response(
                 messages=messages,
                 model=model_name, # Pass the specific gemini model name
                 temperature=assistant.temperature,
                 max_tokens=assistant.max_tokens
             )
             response_content = response['content']
        elif model_name == 'openai-compatible':
            # Handle OpenAI Compatible model
            response = _generate_openai_compatible_response(
                messages=messages,
                assistant=assistant,
                temperature=assistant.temperature,
                max_tokens=assistant.max_tokens
            )
            response_content = response['content']
        else:
            raise ValueError(f"Unsupported model: {model_name}")
        # Calculate metrics
        duration = time.time() - start_time
        # Ensure response_content is a string before token calculation
        response_text = response_content if isinstance(response_content, str) else json.dumps(response_content)
        token_count = get_token_count(user_input + response_text, model_name)

        # Record interaction
        from .models import Interaction # Import locally to avoid circular dependency
        interaction_obj = Interaction.objects.create( # Assign to variable
            assistant=assistant,
            user=user,
            prompt=user_input, # The latest user message
            response=response_text, # Save the actual response content
            # context=context or '', # Removed context field
            duration=duration,
            token_count=token_count,
            # Optionally save the history used for this interaction?
            # Requires adding a field to the Interaction model (e.g., history_json = JSONField(null=True))
            # history_json=json.dumps(messages) # Save the full message list including history
        )

        # Generate suggested questions based on the response and available context
        suggested_questions = []
        if assistant.assistant_type == Assistant.TYPE_SUPPORT:
            suggested_questions = _generate_suggested_questions(
                assistant=assistant,
                response_text=response_text,
                user_input=user_input,
                website_data=website_data,
                navigation_items=navigation_items,
                extra_context=extra_context
            )

        # Prepare empty lists for images and gallery
        response_images = []
        response_gallery = []

        return {
            'content': response_text, # Return the actual response content
            'duration': duration,
            'token_count': token_count,
            'status': 'success',
            'interaction_id': interaction_obj.id, # Add the ID to the return dict
            'context_id': current_context_id, # Return the context_id if provided
            'images': response_images, # Add empty images list for compatibility
            'gallery': response_gallery, # Add empty gallery list for compatibility
            'suggestions': suggested_questions # Add suggested questions to the response
        }

    except APIConnectionError as e:
        # Handle connection errors specifically (e.g., no internet)
        print(f"Connection Error generating assistant response for {assistant.name}: {e}")
        traceback.print_exc() # Print the full traceback
        return {
            'content': "Sorry, I couldn't connect to the AI service. Please check your internet connection and try again.",
            'status': 'error',
            'images': [],
            'gallery': [],
            'context_id': current_context_id
        }
    except Exception as e:
        # Log other errors for debugging, including traceback
        print(f"Error generating assistant response for {assistant.name}: {e}")
        traceback.print_exc() # Print the full traceback
        return {
            'content': f"Sorry, an unexpected error occurred while processing your request. Error: {e}",
            'status': 'error',
            'images': [],
            'gallery': [],
            'context_id': current_context_id
        }




def _generate_openai_response(messages: list, model: str,
                            temperature: float, max_tokens: int) -> Dict[str, str]:
    """Generate a response using OpenAI's API or Groq API for Llama models."""
    # Select the appropriate client based on the model
    if model.startswith('llama'):
        # Use Groq client for Llama models
        print(f"[LLM DEBUG] Using Groq client for model: {model}")
        client = groq_client

        # Clean messages for Groq API - it doesn't support 'gallery' property
        cleaned_messages = []
        for msg in messages:
            # Create a clean copy of the message without unsupported properties
            cleaned_msg = {"role": msg["role"], "content": msg["content"]}
            # Add only supported properties
            if "name" in msg:
                cleaned_msg["name"] = msg["name"]
            cleaned_messages.append(cleaned_msg)

        # Use the cleaned messages for Groq API
        messages = cleaned_messages
    else:
        # Use OpenAI client for GPT models
        print(f"[LLM DEBUG] Using OpenAI client for model: {model}")
        client = openai_client

    # Make the API call with the selected client
    response = client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,
        max_tokens=max_tokens
    )

    return {
        'content': response.choices[0].message.content.strip()
    }

def _generate_anthropic_response(messages: list, model: str,
                               temperature: float, max_tokens: int) -> Dict[str, str]:
    """Generate a response using Anthropic's API."""
    # Find the system prompt
    system_prompt = ""
    user_messages = []
    for msg in messages:
        if msg['role'] == 'system':
            system_prompt = msg['content'] # Anthropic uses a dedicated system parameter
        else:
            user_messages.append(msg) # Keep user/assistant messages for the main list

    response = anthropic_client.messages.create(
        model=model,
        system=system_prompt, # Use the system parameter
        messages=user_messages, # Pass remaining messages
        temperature=temperature,
        max_tokens=max_tokens # Note: Anthropic uses max_tokens, not max_tokens_to_sample for messages API
    )
    # Assuming the response structure gives content in response.content[0].text
    # Adjust based on actual Anthropic API response structure if different
    response_text = response.content[0].text if response.content else ""
    return {
        'content': response_text.strip()
    }

# Added function for Gemini
def _generate_gemini_response(messages: list, model: str,
                            temperature: float, max_tokens: int) -> Dict[str, str]:
    """Generate a response using the Gemini API via OpenAI client structure."""
    if not gemini_client:
        raise ConnectionError("Gemini client is not initialized.")
    try:
        response = gemini_client.chat.completions.create(
            model=model, # Use the specific Gemini model name (e.g., 'gemini-1.5-flash-latest')
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )
        return {
            'content': response.choices[0].message.content.strip()
        }
    except Exception as e:
        print(f"Error calling Gemini API: {e}")
        traceback.print_exc() # Print the full traceback for Gemini errors
        # Re-raise or return an error structure
        raise e # Re-raising allows the outer handler to catch it

# Function for OpenAI Compatible models
def _generate_openai_compatible_response(messages: list, assistant,
                                      temperature: float, max_tokens: int) -> Dict[str, str]:
    """Generate a response using a custom OpenAI-compatible API."""
    # Validate required fields for OpenAI compatible models
    if not assistant.api_key or not assistant.api_key.strip():
        raise ValueError("API key is required for OpenAI Compatible model")

    if not assistant.base_url or not assistant.base_url.strip():
        raise ValueError("Base URL is required for OpenAI Compatible model")

    if not assistant.custom_model_name or not assistant.custom_model_name.strip():
        raise ValueError("Model name is required for OpenAI Compatible model")

    try:
        # Create a custom client with the provided credentials
        custom_client = openai.OpenAI(
            api_key=assistant.api_key.strip(),
            base_url=assistant.base_url.strip()
        )

        # Make the API call with the custom client
        response = custom_client.chat.completions.create(
            model=assistant.custom_model_name.strip(),
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )

        # Validate response
        if not response.choices or not response.choices[0].message.content:
            raise ValueError("Empty response received from OpenAI Compatible API")

        return {
            'content': response.choices[0].message.content.strip()
        }
    except openai.APIConnectionError as e:
        print(f"Connection error with OpenAI Compatible API: {e}")
        traceback.print_exc()
        raise ValueError(f"Failed to connect to OpenAI Compatible API: {e}")
    except openai.AuthenticationError as e:
        print(f"Authentication error with OpenAI Compatible API: {e}")
        traceback.print_exc()
        raise ValueError(f"Authentication failed with OpenAI Compatible API. Please check your API key: {e}")
    except openai.RateLimitError as e:
        print(f"Rate limit error with OpenAI Compatible API: {e}")
        traceback.print_exc()
        raise ValueError(f"Rate limit exceeded for OpenAI Compatible API: {e}")
    except openai.APIError as e:
        print(f"API error with OpenAI Compatible API: {e}")
        traceback.print_exc()
        raise ValueError(f"OpenAI Compatible API error: {e}")
    except Exception as e:
        print(f"Unexpected error with OpenAI Compatible API: {e}")
        traceback.print_exc()
        raise ValueError(f"Unexpected error calling OpenAI Compatible API: {e}")

def process_training_file(assistant, file) -> None:
    """Process and incorporate training data into an assistant."""
    content_type = file.content_type

    try:
        if content_type == 'application/json':
            data = json.load(file)
            # Process structured JSON data
            _process_json_training_data(assistant, data)

        elif content_type == 'text/plain':
            text = file.read().decode('utf-8')
            # Process plain text data
            _process_text_training_data(assistant, text)

        elif content_type == 'application/pdf':
            # Process PDF data
            # Ensure PyPDF2 is installed (it was in requirements.txt)
            from PyPDF2 import PdfReader
            reader = PdfReader(file)
            text = ""
            for page in reader.pages:
                extracted = page.extract_text()
                if extracted: # Check if text extraction was successful
                    text += extracted
            _process_text_training_data(assistant, text)

        else:
            raise ValueError(f"Unsupported file type: {content_type}")

        # Update assistant metadata
        assistant.last_trained = timezone.now()
        assistant.save(update_fields=['last_trained']) # Save only the changed field

    except Exception as e:
        raise ValueError(f"Error processing training data: {str(e)}")

def _process_json_training_data(assistant, data: Dict) -> None:
    """Process structured JSON training data."""
    # Implementation would depend on specific requirements
    # Example: Process Q&A pairs, rules, or other structured data
    print(f"Processing JSON training data for {assistant.name} (Placeholder)")
    pass

def _process_text_training_data(assistant, text: str) -> None:
    """Process unstructured text training data."""
    # Implementation would depend on specific requirements
    # Example: Extract key information, generate embeddings, etc.
    print(f"Processing Text training data for {assistant.name} (Placeholder)")
    pass

def _generate_suggested_questions(assistant, response_text: str, user_input: str,
                                website_data: dict, navigation_items: list,
                                extra_context: str) -> list:
    """
    Generate suggested follow-up questions based on the assistant's response and available context.

    Args:
        assistant: The Assistant model instance
        response_text: The text response just generated by the LLM
        user_input: The user's most recent question
        website_data: The website data dictionary from the assistant
        navigation_items: List of navigation items from website_data
        extra_context: Additional context from the assistant

    Returns:
        A list of suggested questions (strings)
    """
    # Prepare a prompt for generating suggested questions
    model_name = assistant.model

    # Collect all available context data to use for generating questions
    context_data = []

    # Add navigation item content to context data
    for item in navigation_items:
        if isinstance(item, dict) and 'unique_id' in item and 'label' in item and 'id' in item:
            item_id = item['id']
            label = item['label']
            data_key = f"item_{item_id}"
            item_data = website_data.get(data_key)

            if item_data:
                # Format the context data with the section label
                if isinstance(item_data, (str, int, float, bool)):
                    context_data.append(f"SECTION '{label}':\n{item_data}")
                else:
                    context_data.append(f"SECTION '{label}':\n{json.dumps(item_data, indent=2)}")

    # Add extra context if available
    if extra_context:
        context_data.append(f"ADDITIONAL CONTEXT:\n{extra_context}")

    # Combine all context data
    combined_context = "\n\n".join(context_data)

    # Create a prompt for generating suggested questions
    prompt = f"""
You are an expert at generating follow-up questions based on a conversation and available context.

RECENT CONVERSATION:
User: {user_input}
Assistant: {response_text}

AVAILABLE CONTEXT DATA:
{combined_context}

TASK:
Generate 5 follow-up questions that:
1. Are DIRECTLY based on SPECIFIC information, facts, details, and topics present in the AVAILABLE CONTEXT DATA
2. Follow naturally from the most recent assistant response
3. Ask about specific details mentioned in the context that weren't fully covered in the response
4. Would help the user learn more about topics mentioned in the context
5. Can be answered using ONLY the information in the available context

CRITICAL INSTRUCTIONS:
- Each question MUST reference actual content from the context
- Questions should be specific and reference actual details from the context
- Generate questions that cover different sections and topics in the context
- ONLY generate questions that can be answered using the information provided in the context
- Make questions sound natural and conversational
- Do NOT generate questions about information that isn't in the context data
- Return ONLY the questions, one per line, with no numbering or additional text

QUESTIONS:
"""

    # Prepare messages for the LLM call
    messages = [{"role": "user", "content": prompt}]

    try:
        # Call the appropriate LLM API based on the model type
        if model_name.startswith('gpt') or model_name.startswith('llama'):
            # Select the appropriate client based on the model
            if model_name.startswith('llama'):
                # Use Groq client for Llama models
                print(f"[LLM DEBUG] Using Groq client for suggested questions with model: {model_name}")
                client = groq_client

                # Clean messages for Groq API - it doesn't support 'gallery' property
                cleaned_messages = []
                for msg in messages:
                    # Create a clean copy of the message without unsupported properties
                    cleaned_msg = {"role": msg["role"], "content": msg["content"]}
                    # Add only supported properties
                    if "name" in msg:
                        cleaned_msg["name"] = msg["name"]
                    cleaned_messages.append(cleaned_msg)

                # Use the cleaned messages for Groq API
                messages = cleaned_messages
            else:
                # Use OpenAI client for GPT models
                print(f"[LLM DEBUG] Using OpenAI client for suggested questions with model: {model_name}")
                client = openai_client

            response = client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=0.7,
                max_tokens=256
            )
            response_content = response.choices[0].message.content
        elif model_name.startswith('claude'):
            # Claude API requires system prompt to be separate from messages
            system_prompt = prompt  # Use the entire prompt as system for this task
            response = anthropic_client.messages.create(
                model=model_name,
                system=system_prompt,
                messages=[],  # Empty list since we're using system prompt
                temperature=0.7,
                max_tokens=256
            )
            response_content = response.content[0].text if response.content else ""
        elif model_name.startswith('gemini'):
            if not gemini_client:
                raise ValueError("Gemini API key not configured, cannot use Gemini models.")
            response = gemini_client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=0.7,
                max_tokens=256
            )
            response_content = response.choices[0].message.content
        elif model_name == 'openai-compatible':
            # For OpenAI Compatible model, create a custom client
            if not assistant.api_key or not assistant.api_key.strip():
                raise ValueError("API key is required for OpenAI Compatible model")
            if not assistant.base_url or not assistant.base_url.strip():
                raise ValueError("Base URL is required for OpenAI Compatible model")
            if not assistant.custom_model_name or not assistant.custom_model_name.strip():
                raise ValueError("Model name is required for OpenAI Compatible model")

            # Create a custom client with the provided credentials
            custom_client = openai.OpenAI(
                api_key=assistant.api_key.strip(),
                base_url=assistant.base_url.strip()
            )

            response = custom_client.chat.completions.create(
                model=assistant.custom_model_name.strip(),
                messages=messages,
                temperature=0.7,
                max_tokens=256
            )
            response_content = response.choices[0].message.content
        else:
            # Fallback to a default model if the assistant's model is not supported
            response = openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=messages,
                temperature=0.7,
                max_tokens=256
            )
            response_content = response.choices[0].message.content

        # Parse the response to extract questions
        questions = [line.strip() for line in response_content.split('\n') if line.strip() and '?' in line]

        # Limit to 5 questions and ensure they're unique
        unique_questions = []
        for q in questions:
            # Clean up the question (remove numbering, etc.)
            clean_q = q.lstrip('0123456789.- ').strip()
            if clean_q not in unique_questions:
                unique_questions.append(clean_q)

        return unique_questions[:5]

    except Exception as e:
        print(f"Error generating suggested questions: {e}")
        traceback.print_exc()
        return []  # Return empty list on error

def get_model_context_window(model: str) -> int:
    """Get the maximum context window size for a model."""
    context_windows = {
        'gpt-3.5-turbo': 4096,
        'gpt-4': 8192,
        'gpt-4-turbo': 128000, # Often refers to gpt-4-1106-preview or similar
        'gpt-4-1106-preview': 128000,
        'gpt-4-vision-preview': 128000,
        'claude-2': 100000, # Older model, Claude 3 preferred
        'claude-2.1': 200000,
        'claude-3-opus-20240229': 200000,
        'claude-3-sonnet-20240229': 200000,
        'claude-3-haiku-20240307': 200000,
        'claude-instant-1.2': 100000,
        # Add Gemini models - check official docs for exact limits
        'gemini-2.0-flash': 8192, # Example context window based on Gradio script - VERIFY ACTUAL LIMIT
        'gemini-1.5-pro-latest': 1048576, # Example context window for pro
        # Llama models
        'llama-3.3-70b-versatile': 128000, # Llama 3.3 70B context window
        # OpenAI Compatible - use a reasonable default
        'openai-compatible': 8192, # Default for OpenAI Compatible models
    }
    # Basic check, might need refinement for specific model variations
    for key, value in context_windows.items():
        if model.startswith(key):
            return value
    return 4096 # Default fallback

def estimate_cost(token_count: int, model: str) -> float:
    """Estimate the cost of an API call based on token count."""
    # Prices in USD per 1M tokens (Input/Output) - UPDATE WITH CURRENT PRICING!
    pricing = {
        'gpt-3.5-turbo': (0.50 / 1_000_000, 1.50 / 1_000_000),
        'gpt-4': (30.00 / 1_000_000, 60.00 / 1_000_000),
        'gpt-4-turbo': (10.00 / 1_000_000, 30.00 / 1_000_000), # Example for 1106-preview
        'claude-3-opus': (15.00 / 1_000_000, 75.00 / 1_000_000),
        'claude-3-sonnet': (3.00 / 1_000_000, 15.00 / 1_000_000),
        'claude-3-haiku': (0.25 / 1_000_000, 1.25 / 1_000_000),
        # Add Gemini pricing - CHECK OFFICIAL GEMINI PRICING! These are examples.
        'gemini-2.0-flash': (0.35 / 1_000_000, 1.05 / 1_000_000), # Example pricing - assuming similar to 1.5 flash
        'gemini-1.5-pro-latest': (3.50 / 1_000_000, 10.50 / 1_000_000), # Example pricing for <= 128k context
        # Llama models via Groq
        'llama-3.3-70b-versatile': (0.70 / 1_000_000, 0.90 / 1_000_000), # Groq pricing for Llama 3.3 70B
        # OpenAI Compatible - use a reasonable default
        'openai-compatible': (1.00 / 1_000_000, 2.00 / 1_000_000), # Default for OpenAI Compatible models
        # Add other models as needed
    }

    # Simplified cost estimation assuming output tokens dominate or are roughly equal
    # A more accurate calculation would need input/output token counts separately.
    # Using the first tuple value (input rate) if model not found, else use output rate.
    default_input_rate, default_output_rate = pricing['gpt-3.5-turbo']
    _, output_rate = pricing.get(model, (default_input_rate, default_output_rate))

    # Using output rate as a general estimate for cost calculation
    estimated_cost = (token_count / 1000) * (output_rate * 1000) # Rate is per 1M, calc is per 1K

    return estimated_cost
