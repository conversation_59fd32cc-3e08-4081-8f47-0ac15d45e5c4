/**
 * Homepage Optimized CSS
 * Consolidated critical styles for faster loading
 */

/* ===== CRITICAL STYLES - LOAD FIRST ===== */

/* Prevent white flash and ensure immediate dark background */
body {
    background-color: #121212 !important;
    transition: none !important;
}

/* Hero section immediate styling */
.hero-section,
.bg-primary {
    background-color: #121212 !important;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 102, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 60, 180, 0.2) 0%, transparent 50%),
        linear-gradient(to bottom, #121212, #0a0a0a) !important;
    color: #ffffff !important;
    min-height: 100vh;
}

/* Cards immediate styling */
.card {
    background-color: #1e1e1e !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    opacity: 1 !important;
    transform: none !important;
    transition: box-shadow 0.3s ease !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 102, 255, 0.2) !important;
    transform: translateY(-2px) !important;
}

.card-body {
    color: #ffffff !important;
}

.card-title,
.card h3,
.card h5 {
    color: #ffffff !important;
}

.card .text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

.card .btn {
    margin-top: 1rem;
}

/* ===== OPTIMIZED ANIMATIONS ===== */

/* Simplified scroll animations */
.card-animate {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.card-animate.visible {
    opacity: 1;
    transform: translateY(0);
}

/* ===== COMPANY LOGO CAROUSEL OPTIMIZATION ===== */

/* Use existing carousel styles but enhance for dark mode */
.company-logo-carousel {
    will-change: transform;
}

/* Dark mode enhancements for carousel */
[data-theme="dark"] .company-logo-item a {
    color: #ffffff !important;
}

[data-theme="dark"] .company-logo-item a:hover {
    color: #0066ff !important;
}

/* Ensure carousel animation works */
@keyframes scroll {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

/* Ensure carousel container is visible and properly styled */
.company-logo-carousel-container {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure company logo items are properly displayed */
.company-logo-item {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure company info is visible */
.company-info {
    opacity: 1 !important;
    visibility: visible !important;
}

/* ===== SEARCH FORM OPTIMIZATION ===== */

.search-form-card {
    background-color: #1e1e1e !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    width: 100%;
    max-width: 600px;
}

.search-form-card .form-control {
    background-color: #2a2a2a !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
}

.search-form-card .form-control:focus {
    background-color: #2a2a2a !important;
    border-color: #0066ff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 255, 0.25) !important;
    color: #ffffff !important;
}

.search-form-card .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

.search-form-card .form-label {
    color: #ffffff !important;
}

.search-form-card .form-check-label {
    color: #ffffff !important;
}

.search-form-card .form-check-input:checked {
    background-color: #0066ff !important;
    border-color: #0066ff !important;
}

/* ===== BUTTON OPTIMIZATIONS ===== */

.btn-primary {
    background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%) !important;
    border: none !important;
    color: #ffffff !important;
}

.btn-outline-light {
    border-color: rgba(255, 255, 255, 0.5) !important;
    color: #ffffff !important;
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: #ffffff !important;
    color: #ffffff !important;
}

/* ===== FEATURE SECTION OPTIMIZATION ===== */

.features-section {
    background: linear-gradient(135deg, #121212 0%, #1a1a1a 100%) !important;
    padding: 4rem 0 !important;
    position: relative;
    color: #ffffff !important;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(0, 102, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 60, 180, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

/* Modern Compact Feature Cards */
.modern-feature-card {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 20px !important;
    padding: 2rem 1.5rem !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    height: auto !important;
    min-height: 320px !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
}

/* Force remove any blue accent borders */
.modern-feature-card::before,
.modern-feature-card::after {
    display: none !important;
    content: none !important;
}

.modern-feature-card.card-accent::before,
.modern-feature-card.card-accent::after {
    display: none !important;
    content: none !important;
}

.modern-feature-card:hover {
    transform: translateY(-5px) !important;
    background: rgba(255, 255, 255, 0.08) !important;
    border-color: rgba(0, 102, 255, 0.3) !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3) !important;
}

/* Feature Icon Circles */
.feature-icon-circle {
    width: 60px !important;
    height: 60px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 24px !important;
    color: white !important;
    margin: 0 auto 1.5rem auto !important;
    transition: all 0.3s ease !important;
}

.feature-icon-circle.bg-primary {
    background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%) !important;
    box-shadow: 0 6px 20px rgba(0, 102, 255, 0.3) !important;
}

.feature-icon-circle.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3) !important;
}

.feature-icon-circle.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%) !important;
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.3) !important;
}

.modern-feature-card:hover .feature-icon-circle {
    transform: scale(1.1) !important;
}

/* Feature Text */
.feature-card-title {
    color: #ffffff !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}

.feature-card-text {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.9rem !important;
    line-height: 1.5 !important;
    margin-bottom: 1.5rem !important;
    flex-grow: 1 !important;
}

/* Feature Links and Actions */
.feature-card-link {
    color: #0066ff !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    font-size: 0.9rem !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
}

.feature-card-link:hover {
    color: #ffffff !important;
    text-decoration: none !important;
    transform: translateX(5px) !important;
}

.feature-card-coming-soon {
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 0.85rem !important;
    font-style: italic !important;
    display: inline-flex !important;
    align-items: center !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modern-feature-card {
        padding: 1.5rem 1rem !important;
        min-height: 280px !important;
    }

    .feature-icon-circle {
        width: 50px !important;
        height: 50px !important;
        font-size: 20px !important;
        margin-bottom: 1rem !important;
    }

    .feature-card-title {
        font-size: 1.1rem !important;
    }

    .feature-card-text {
        font-size: 0.85rem !important;
    }
}

/* ===== SOCIAL PROOF SECTION OPTIMIZATION ===== */

/* Only apply dark mode to bg-white sections in dark theme */
[data-theme="dark"] .bg-white {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

[data-theme="dark"] .company-logo-carousel-container {
    background-color: #1e1e1e !important;
}

[data-theme="dark"] .company-name {
    color: #ffffff !important;
}

[data-theme="dark"] .assistant-count {
    color: rgba(255, 255, 255, 0.7) !important;
}

[data-theme="dark"] .company-logo-placeholder {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

[data-theme="dark"] .company-logo-placeholder i {
    color: rgba(255, 255, 255, 0.7) !important;
}

[data-theme="dark"] .company-logo-placeholder span {
    color: #ffffff !important;
}

/* ===== CTA SECTION OPTIMIZATION ===== */

[data-theme="dark"] .cta-section {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%) !important;
}

[data-theme="dark"] .cta-section .bg-white {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* ===== MOBILE OPTIMIZATIONS ===== */

@media (max-width: 767.98px) {
    .search-form-card {
        max-width: 100%;
    }

    .company-logo {
        height: 80px;
        max-width: 160px;
    }

    .company-logo-placeholder {
        height: 80px;
        width: 160px;
    }

    .company-logo-item {
        margin: 0 15px;
    }

    .hero-section {
        min-height: auto;
        padding: 2rem 0;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Reduce repaints and reflows */
* {
    box-sizing: border-box;
}

/* GPU acceleration for animations */
.company-logo-carousel,
.card-animate {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Optimize images */
img {
    image-rendering: optimizeQuality;
    image-rendering: -webkit-optimize-contrast;
}

/* Reduce layout thrashing */
.container,
.row,
.col-lg-6 {
    contain: layout;
}

/* ===== DARK MODE OVERRIDES ===== */

[data-theme="dark"] body,
[data-theme="dark"] .bg-light {
    background-color: #121212 !important;
}

[data-theme="dark"] .card {
    background-color: #1e1e1e !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

[data-theme="dark"] .text-dark {
    color: #ffffff !important;
}

[data-theme="dark"] .bg-white {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* ===== LOADING STATES ===== */

.loading-skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Ensure all elements are visible by default */
.company-logo-carousel-container {
    opacity: 1 !important;
}

.company-logo-carousel-container.loaded {
    opacity: 1 !important;
}
