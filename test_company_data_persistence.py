#!/usr/bin/env python
"""
Comprehensive test to verify company creation data persistence.
Tests the complete workflow from creation to settings retrieval.
"""

import os
import sys
import django
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import TestCase
from django.db import transaction
from accounts.forms import CompanyCreationForm, CompanySettingsForm
from accounts.models import Company, CompanyInformation
import uuid

def test_company_data_persistence():
    """
    Test complete company creation and data persistence workflow
    """
    print("🧪 COMPANY DATA PERSISTENCE TEST")
    print("=" * 60)

    # Create test user
    test_username = f"testuser_{uuid.uuid4().hex[:8]}"
    test_user, created = User.objects.get_or_create(
        username=test_username,
        defaults={
            'email': f'{test_username}@test.com',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )

    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Created test user: {test_user.username}")
    else:
        print(f"✅ Using existing test user: {test_user.username}")

    # Test data for company creation
    test_data = {
        'name': f'Test Persistence Company {uuid.uuid4().hex[:6]}',
        'entity_type': 'company',
        'mission': 'To test data persistence in our platform',
        'description': 'A comprehensive test of our company creation system',
        'website': 'https://testpersistence.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '******-TEST-123',
        'timezone': 'America/New_York',
        'language': 'en',
        'industry': 'Software Testing',
        'size': '1-10',
        'city': 'Test City',
        'country': 'Test Country',
        'founded': 2024,
        'address_line1': '123 Test Street',
        'address_line2': 'Suite 456',
        'postal_code': '12345',
        'linkedin': 'https://linkedin.com/company/testpersistence',
        'twitter': 'https://twitter.com/testpersistence',
        'facebook': 'https://facebook.com/testpersistence',
        'custom_domain': 'testpersistence.com',
        'list_in_directory': True
    }

    try:
        with transaction.atomic():
            print("\n📝 STEP 1: Testing Company Creation Form")
            print("-" * 40)

            # Create and validate form
            creation_form = CompanyCreationForm(data=test_data, user=test_user)

            if not creation_form.is_valid():
                print("❌ Company creation form is invalid!")
                print("Form errors:", creation_form.errors)
                return False

            print("✅ Company creation form is valid")

            # Save the company
            company = creation_form.save()
            print(f"✅ Company created: {company.name} (ID: {company.id})")
            print(f"   - Entity Type: {company.entity_type}")
            print(f"   - Owner: {company.owner.username}")
            print(f"   - Slug: {company.slug}")

            print("\n🔍 STEP 2: Verifying Data Persistence")
            print("-" * 40)

            # Refresh from database to ensure we're getting saved data
            company.refresh_from_db()

            # Get company information
            try:
                company_info = CompanyInformation.objects.get(company=company)
                print("✅ CompanyInformation object found")
            except CompanyInformation.DoesNotExist:
                print("❌ CompanyInformation object not found!")
                return False

            # Verify all fields were saved correctly
            print("\n📊 Checking saved field values:")
            field_checks = []

            # Check Company model fields
            company_fields = {
                'name': company.name,
                'entity_type': company.entity_type,
                'owner': company.owner.username
            }

            for field, value in company_fields.items():
                expected = test_data.get(field, test_user.username if field == 'owner' else None)
                if field == 'owner':
                    expected = test_user.username

                status = "✅" if str(value) == str(expected) else "❌"
                print(f"   {status} Company.{field}: {value} (expected: {expected})")
                field_checks.append(str(value) == str(expected))

            # Check CompanyInformation model fields
            info_fields = [
                'mission', 'description', 'website', 'contact_email', 'contact_phone',
                'timezone', 'language', 'industry', 'size', 'city', 'country',
                'founded', 'address_line1', 'address_line2', 'postal_code',
                'linkedin', 'twitter', 'facebook', 'custom_domain', 'list_in_directory'
            ]

            for field in info_fields:
                saved_value = getattr(company_info, field, None)
                expected_value = test_data.get(field, None)

                # Handle boolean fields
                if isinstance(expected_value, bool):
                    match = saved_value == expected_value
                else:
                    match = str(saved_value) == str(expected_value)

                status = "✅" if match else "❌"
                print(f"   {status} CompanyInfo.{field}: {saved_value} (expected: {expected_value})")
                field_checks.append(match)

            print(f"\n📈 Field Verification: {sum(field_checks)}/{len(field_checks)} fields correct")

            print("\n🔄 STEP 3: Testing Settings Form Data Retrieval")
            print("-" * 40)

            # Test company settings form initialization
            settings_form = CompanySettingsForm(instance=company_info, current_year=datetime.now().year)
            print("✅ Company settings form initialized successfully")

            # Check that settings form has the correct initial values
            print("\n📋 Checking settings form initial values:")
            form_checks = []

            for field_name in settings_form.fields:
                if field_name in test_data:
                    form_initial = settings_form.initial.get(field_name)
                    expected = test_data[field_name]

                    # Handle boolean fields
                    if isinstance(expected, bool):
                        match = form_initial == expected
                    else:
                        match = str(form_initial) == str(expected)

                    status = "✅" if match else "❌"
                    print(f"   {status} Form.{field_name}: {form_initial} (expected: {expected})")
                    form_checks.append(match)

            print(f"\n📈 Form Initialization: {sum(form_checks)}/{len(form_checks)} fields correct")

            print("\n🧪 STEP 4: Testing Settings Form Save")
            print("-" * 40)

            # Modify some data and save through settings form
            modified_data = test_data.copy()
            modified_data.update({
                'mission': 'Updated mission through settings form',
                'website': 'https://updated-testpersistence.com',
                'industry': 'Updated Software Testing'
            })

            settings_form = CompanySettingsForm(
                data=modified_data,
                instance=company_info,
                current_year=datetime.now().year
            )

            if settings_form.is_valid():
                updated_company_info = settings_form.save()
                print("✅ Settings form saved successfully")

                # Verify updates
                updated_company_info.refresh_from_db()
                print(f"   ✅ Mission updated: {updated_company_info.mission}")
                print(f"   ✅ Website updated: {updated_company_info.website}")
                print(f"   ✅ Industry updated: {updated_company_info.industry}")
            else:
                print("❌ Settings form validation failed!")
                print("Form errors:", settings_form.errors)
                return False

            print("\n" + "=" * 60)

            # Calculate overall success
            total_checks = len(field_checks) + len(form_checks)
            passed_checks = sum(field_checks) + sum(form_checks)
            success_rate = (passed_checks / total_checks) * 100 if total_checks > 0 else 0

            if success_rate >= 95:
                print("🎉 TEST PASSED! Company data persistence is working correctly.")
                print(f"   Success Rate: {success_rate:.1f}% ({passed_checks}/{total_checks} checks passed)")
                result = True
            else:
                print("⚠️  TEST PARTIALLY FAILED! Some data persistence issues detected.")
                print(f"   Success Rate: {success_rate:.1f}% ({passed_checks}/{total_checks} checks passed)")
                result = False

            # Clean up
            print(f"\n🧹 Cleaning up test data...")
            company.delete()
            if created:
                test_user.delete()
            print("✅ Test data cleaned up")

            return result

    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_company_data_persistence()
    sys.exit(0 if success else 1)
