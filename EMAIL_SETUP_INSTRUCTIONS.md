# 📧 Email Setup <NAME_EMAIL>

## 🚨 Current Status

The test email failed with **"Incorrect authentication data"** because:

✅ **Django Configuration**: Complete and correct  
❌ **Email Account**: Not created yet OR password not updated  
❌ **Authentication**: Failed (535 error)

## 🔧 Step-by-Step Setup Instructions

### **Step 1: Create Email Account in cPanel**

1. **Log into your cPanel account**
   - Go to your hosting provider's cPanel login
   - Enter your cPanel username and password

2. **Navigate to Email Accounts**
   - Look for "Email" section in cPanel
   - Click on "Email Accounts"

3. **Create the Email Account**
   - Click "Create" or "Add Email Account"
   - **Email**: `system`
   - **Domain**: `24seven.site` (should be pre-selected)
   - **Password**: Create a strong password (save this!)
   - **Mailbox Quota**: Set appropriate size (e.g., 1GB)
   - Click "Create Account"

4. **Verify Account Creation**
   - The account `<EMAIL>` should now appear in your email accounts list
   - Note down the password you created

### **Step 2: Update Django Configuration**

1. **Edit your `.env` file**
   ```bash
   # Current (placeholder):
   EMAIL_HOST_PASSWORD=your-actual-email-password
   
   # Update to (use the password you created):
   EMAIL_HOST_PASSWORD=your-real-password-here
   ```

2. **Save the file** and ensure no extra spaces around the password

### **Step 3: Test Email Configuration**

Run the test script again:
```bash
python send_test_email.py
```

Expected output if successful:
```
✅ Email sent successfully using Django send_mail!
   📧 Sent to: <EMAIL>
   📝 Subject: Django Email Configuration Test
   🕒 Time: [timestamp]
```

### **Step 4: Check Email Reception**

1. **<NAME_EMAIL>**
   - Use cPanel webmail or email client
   - Check inbox for the test email
   - Also check spam/junk folder

2. **Verify Email Content**
   - Subject: "Django Email Configuration Test"
   - From: "24seven <<EMAIL>>"
   - Content: Configuration details and test information

## 🔍 Troubleshooting Common Issues

### **Issue 1: "Incorrect authentication data" (535 error)**
**Causes:**
- Email account doesn't exist
- Wrong password in `.env` file
- Typo in email address

**Solutions:**
- Verify email account exists in cPanel
- Double-check password in `.env` file
- Ensure no extra spaces in password

### **Issue 2: "Connection timeout"**
**Causes:**
- DNS issues with mail.24seven.site
- Firewall blocking port 465
- Email server not running

**Solutions:**
- Test DNS: `nslookup mail.24seven.site`
- Check firewall settings
- Contact hosting provider

### **Issue 3: "SSL certificate verification failed"**
**Causes:**
- Invalid SSL certificate for mail.24seven.site
- Self-signed certificate

**Solutions:**
- Verify SSL certificate validity
- Contact hosting provider about SSL setup

## 📋 Quick Verification Checklist

Before running the test:

- [ ] Email account `<EMAIL>` created in cPanel
- [ ] Password saved and noted down
- [ ] `.env` file updated with real password
- [ ] No typos in email address or password
- [ ] Django application restarted (if running)

## 🎯 Alternative Testing Methods

### **Method 1: Manual Email Client Test**
Use the cPanel settings to configure an email client:
- **Server**: mail.24seven.site
- **Port**: 465 (SSL)
- **Username**: <EMAIL>
- **Password**: [your password]

### **Method 2: cPanel Webmail Test**
- Log into cPanel webmail
- Send a test <NAME_EMAIL> to another email
- Verify sending works

### **Method 3: Django Shell Test**
```python
python manage.py shell

>>> from django.core.mail import send_mail
>>> from django.conf import settings
>>> 
>>> # Check settings
>>> print(f"Host: {settings.EMAIL_HOST}")
>>> print(f"User: {settings.EMAIL_HOST_USER}")
>>> print(f"Password set: {bool(settings.EMAIL_HOST_PASSWORD)}")
>>> 
>>> # Send test email
>>> result = send_mail(
...     'Quick Test',
...     'This is a quick test from Django shell.',
...     '<EMAIL>',
...     ['<EMAIL>'],
...     fail_silently=False
... )
>>> print(f"Result: {result}")
```

## 📞 Support Information

If you continue to have issues:

1. **Check cPanel Email Logs**
   - Look for email delivery logs in cPanel
   - Check for any error messages

2. **Contact Hosting Provider**
   - Verify email service is active
   - Confirm mail.24seven.site DNS setup
   - Check SSL certificate status

3. **Test with Different Email**
   - Try sending to external email (Gmail, etc.)
   - Verify if issue is with receiving or sending

## 🎉 Success Indicators

You'll know it's working when:

✅ Test script shows: "Email sent successfully"  
✅ No authentication errors (535)  
✅ Email <NAME_EMAIL> inbox  
✅ Email content displays correctly  
✅ Django can send emails to external addresses  

Once working, your Django application will be able to send:
- User registration emails
- Password reset emails  
- Team invitation emails
- System notifications
- All other application emails

## 🔄 Next Steps After Success

1. **Test User Registration** - Create a test user account
2. **Test Password Reset** - Try password reset functionality  
3. **Test Team Invitations** - Send team invitation emails
4. **Monitor Email Logs** - Check for any delivery issues
5. **Set up Email Monitoring** - Monitor email delivery rates

Your email system will be fully operational once these steps are completed! 🚀
