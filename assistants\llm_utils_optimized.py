"""
Optimized LLM utilities with caching, connection pooling, and performance improvements.
"""

import os
import json
import time
import openai
import httpx
from openai import APIConnectionError, AuthenticationError, RateLimitError, APIError
import traceback
from typing import Dict, Any, Optional, List
import tiktoken
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from anthropic import Anthropic
from datetime import datetime
import logging

from .llm_cache import LLMCache, get_cached_llm_response, cache_llm_response

logger = logging.getLogger(__name__)

# Connection pool configuration for better performance
HTTP_TIMEOUT = httpx.Timeout(30.0, connect=10.0)
HTTP_LIMITS = httpx.Limits(max_keepalive_connections=20, max_connections=100)

# Configure optimized API clients with connection pooling using environment-configurable base URLs
openai_client = openai.OpenAI(
    api_key=settings.OPENAI_API_KEY,
    base_url=settings.OPENAI_BASE_URL,
    http_client=httpx.Client(timeout=HTTP_TIMEOUT, limits=HTTP_LIMITS)
)

groq_client = openai.OpenAI(
    base_url=settings.GROQ_BASE_URL,
    api_key=settings.GROQ_API_KEY,
    http_client=httpx.Client(timeout=HTTP_TIMEOUT, limits=HTTP_LIMITS)
)

anthropic_client = Anthropic(
    api_key=settings.ANTHROPIC_API_KEY,
    timeout=30.0
)

# Gemini client with connection pooling using environment-configurable base URL
GEMINI_API_KEY = settings.GEMINI_API_KEY
GEMINI_BASE_URL = settings.GEMINI_BASE_URL
gemini_client = None
if GEMINI_API_KEY:
    try:
        gemini_client = openai.OpenAI(
            api_key=GEMINI_API_KEY,
            base_url=GEMINI_BASE_URL,
            http_client=httpx.Client(timeout=HTTP_TIMEOUT, limits=HTTP_LIMITS)
        )
        logger.info("Optimized Gemini client initialized successfully.")
    except Exception as e:
        logger.error(f"Error initializing optimized Gemini client: {e}")


def get_token_count_cached(text: str, model: str = "gpt-3.5-turbo") -> int:
    """
    Calculate token count with caching for repeated calculations.
    """
    # Create cache key for token count
    cache_key = f"token_count:{hash(text + model)}"
    cached_count = cache.get(cache_key)

    if cached_count is not None:
        return cached_count

    try:
        encoding = tiktoken.encoding_for_model(model)
        token_count = len(encoding.encode(text))
    except Exception:
        # Fallback to approximate token count
        token_count = int(len(text.split()) * 1.3)

    # Cache for 1 hour
    cache.set(cache_key, token_count, 3600)
    return token_count


def generate_assistant_response_optimized(assistant, user_input: str, user,
                                        history: Optional[list] = None,
                                        current_context_id: Optional[str] = None,
                                        use_cache: bool = True) -> Dict[str, Any]:
    """
    Optimized version of generate_assistant_response with caching and performance improvements.
    """
    start_time = time.time()
    history = history or []

    # Try to use preloaded context for faster response
    preloaded_context = _get_preloaded_context(assistant.id, user)

    # Build messages list efficiently
    messages = _build_messages_optimized(assistant, user_input, history, preloaded_context)

    # Check cache first if enabled
    if use_cache:
        cached_response = get_cached_llm_response(
            assistant.id, messages, assistant.temperature, assistant.max_tokens
        )
        if cached_response:
            logger.info(f"Cache hit for assistant {assistant.id}")
            # Add interaction record for cached responses too
            _record_interaction_optimized(assistant, user, user_input, cached_response, start_time)
            return cached_response

    try:
        # Generate response using optimized API calls
        response_content = _generate_llm_response_optimized(
            assistant, messages, assistant.temperature, assistant.max_tokens
        )

        # Calculate metrics efficiently
        duration = time.time() - start_time
        token_count = get_token_count_cached(user_input + response_content, assistant.model)

        # Prepare response data
        response_data = {
            'content': response_content,
            'duration': duration,
            'token_count': token_count,
            'status': 'success',
            'context_id': current_context_id,
            'images': [],
            'gallery': [],
            'model': assistant.model,
            'cached': False
        }

        # Record interaction
        interaction_obj = _record_interaction_optimized(assistant, user, user_input, response_data, start_time)
        response_data['interaction_id'] = interaction_obj.id

        # Generate suggestions asynchronously if needed
        if assistant.assistant_type == assistant.TYPE_SUPPORT:
            response_data['suggestions'] = _generate_suggestions_optimized(assistant, response_content, user_input)
        else:
            response_data['suggestions'] = []

        # Cache the response for future use
        if use_cache:
            cache_llm_response(
                assistant.id, messages, response_data,
                assistant.temperature, assistant.max_tokens
            )

        return response_data

    except APIConnectionError as e:
        logger.error(f"Connection error for assistant {assistant.name}: {e}")
        return _create_error_response("Connection error. Please check your internet connection.", current_context_id)

    except Exception as e:
        logger.error(f"Error generating response for assistant {assistant.name}: {e}")
        traceback.print_exc()
        return _create_error_response(f"An unexpected error occurred: {e}", current_context_id)


def _build_messages_optimized(assistant, user_input: str, history: list, preloaded_context: Optional[Dict] = None) -> List[Dict]:
    """
    Efficiently build the messages list for LLM API calls.
    """
    # Use preloaded system context if available, otherwise use cached version
    if preloaded_context and 'system_context' in preloaded_context:
        system_context = preloaded_context['system_context']
        logger.info(f"Using preloaded system context for assistant {assistant.id}")
    else:
        system_context = _get_system_context_cached(assistant)

    messages = [{"role": "system", "content": system_context}]

    # Add valid history efficiently
    valid_history = [
        msg for msg in history
        if isinstance(msg, dict) and 'role' in msg and 'content' in msg
    ]
    messages.extend(valid_history)

    # Add current user input
    messages.append({"role": "user", "content": user_input})

    return messages


def _get_system_context_cached(assistant) -> str:
    """
    Get system context with caching to avoid rebuilding for each request.
    """
    cache_key = f"system_context:{assistant.id}:{assistant.updated_at.timestamp()}"
    cached_context = cache.get(cache_key)

    if cached_context is not None:
        return cached_context

    # Build system context (simplified version of original logic)
    website_data = assistant.website_data or {}
    navigation_items = website_data.get('navigation_items', [])
    extra_context = assistant.extra_context or ""
    display_name = assistant.persona_name or assistant.name
    company = assistant.company
    company_name = company.name

    # Get company info efficiently
    company_info = getattr(company, 'info', None)
    mission = getattr(company_info, 'mission', "No mission provided.") if company_info else "No mission provided."
    founded_year = getattr(company_info, 'founded', None) if company_info else None
    founded = str(founded_year) if founded_year else "N/A"

    system_context = (
        f"You are {display_name}, a friendly assistant for {company_name}. "
        f"Your company mission is: '{mission}', established in {founded}. "
        f"Today's date is {datetime.now().strftime('%B %d, %Y')}. "
        f"Do not start your responses with greetings like 'Hello!'. "
    )

    # Add website data for support assistants
    if assistant.assistant_type == assistant.TYPE_SUPPORT and navigation_items:
        system_context += _build_website_context_optimized(website_data, navigation_items)

    # Add extra context and system prompt
    if extra_context:
        system_context += f"\n\nAdditional context:\n{extra_context}"
    if assistant.system_prompt:
        system_context += f"\n\nSystem Instructions:\n{assistant.system_prompt}"

    # Cache for 1 hour
    cache.set(cache_key, system_context, 3600)
    return system_context


def _build_website_context_optimized(website_data: dict, navigation_items: list) -> str:
    """
    Efficiently build website context from navigation items.
    """
    context_parts = ["\n\nUse the following website data and navigation structure to answer questions accurately:\n"]

    if navigation_items:
        context_parts.append("Navigation Structure:\n")
        for item in navigation_items:
            if isinstance(item, dict) and all(k in item for k in ['label', 'unique_id']):
                context_parts.append(
                    f"- {item.get('label', 'Unknown')} "
                    f"(ID: {item.get('unique_id', 'N/A')}, "
                    f"Type: {item.get('section_type', 'N/A')})\n"
                )

        context_parts.append("\n--- Website Content Sections ---\n")
        for item in navigation_items:
            if isinstance(item, dict) and all(k in item for k in ['unique_id', 'label', 'id']):
                unique_id = item['unique_id']
                label = item['label']
                item_id = item['id']
                data_key = f"item_{item_id}"
                item_data = website_data.get(data_key)

                context_parts.append(f"\n## Content for Section: '{label}' (ID: {unique_id})\n")
                context_parts.append(f"When asked specifically about '{label}', use the following information:\n")

                if item_data:
                    if isinstance(item_data, (str, int, float, bool)):
                        context_parts.append(f"{item_data}\n")
                    else:
                        context_parts.append(f"{json.dumps(item_data, indent=2)}\n")
                else:
                    context_parts.append("(No specific data provided for this section)\n")

        context_parts.append("--- End Website Content Sections ---\n")

    return ''.join(context_parts)


def _generate_llm_response_optimized(assistant, messages: list, temperature: float, max_tokens: int) -> str:
    """
    Generate LLM response using optimized API clients.
    """
    model_name = assistant.model

    if model_name.startswith('gpt') or model_name.startswith('llama'):
        return _generate_openai_response_optimized(messages, model_name, temperature, max_tokens)
    elif model_name.startswith('claude'):
        return _generate_anthropic_response_optimized(messages, model_name, temperature, max_tokens)
    elif model_name.startswith('gemini'):
        if not gemini_client:
            raise ValueError("Gemini API key not configured")
        return _generate_gemini_response_optimized(messages, model_name, temperature, max_tokens)
    elif model_name == 'openai-compatible':
        return _generate_openai_compatible_response_optimized(messages, assistant, temperature, max_tokens)
    else:
        raise ValueError(f"Unsupported model: {model_name}")


def _generate_openai_response_optimized(messages: list, model: str, temperature: float, max_tokens: int) -> str:
    """Optimized OpenAI/Groq response generation."""
    client = groq_client if model.startswith('llama') else openai_client

    # Clean messages for Groq if needed
    if model.startswith('llama'):
        messages = [{"role": msg["role"], "content": msg["content"]} for msg in messages]

    response = client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,
        max_tokens=max_tokens
    )

    return response.choices[0].message.content.strip()


def _generate_anthropic_response_optimized(messages: list, model: str, temperature: float, max_tokens: int) -> str:
    """Optimized Anthropic response generation."""
    system_prompt = ""
    user_messages = []

    for msg in messages:
        if msg['role'] == 'system':
            system_prompt = msg['content']
        else:
            user_messages.append(msg)

    response = anthropic_client.messages.create(
        model=model,
        system=system_prompt,
        messages=user_messages,
        temperature=temperature,
        max_tokens=max_tokens
    )

    return response.content[0].text.strip() if response.content else ""


def _generate_gemini_response_optimized(messages: list, model: str, temperature: float, max_tokens: int) -> str:
    """Optimized Gemini response generation."""
    response = gemini_client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,
        max_tokens=max_tokens
    )
    return response.choices[0].message.content.strip()


def _generate_openai_compatible_response_optimized(messages: list, assistant, temperature: float, max_tokens: int) -> str:
    """Optimized OpenAI-compatible response generation."""
    # Validate required fields for OpenAI compatible models
    if not assistant.api_key or not assistant.api_key.strip():
        raise ValueError("API key is required for OpenAI Compatible model")

    if not assistant.base_url or not assistant.base_url.strip():
        raise ValueError("Base URL is required for OpenAI Compatible model")

    if not assistant.custom_model_name or not assistant.custom_model_name.strip():
        raise ValueError("Model name is required for OpenAI Compatible model")

    try:
        # Create custom client with proper error handling
        custom_client = openai.OpenAI(
            api_key=assistant.api_key.strip(),
            base_url=assistant.base_url.strip(),
            http_client=httpx.Client(timeout=HTTP_TIMEOUT, limits=HTTP_LIMITS)
        )

        # Make API call with error handling
        response = custom_client.chat.completions.create(
            model=assistant.custom_model_name.strip(),
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )

        # Validate response
        if not response.choices or not response.choices[0].message.content:
            raise ValueError("Empty response received from OpenAI Compatible API")

        return response.choices[0].message.content.strip()

    except openai.APIConnectionError as e:
        logger.error(f"Connection error with OpenAI Compatible API: {e}")
        raise ValueError(f"Failed to connect to OpenAI Compatible API: {e}")
    except openai.AuthenticationError as e:
        logger.error(f"Authentication error with OpenAI Compatible API: {e}")
        raise ValueError(f"Authentication failed with OpenAI Compatible API. Please check your API key: {e}")
    except openai.RateLimitError as e:
        logger.error(f"Rate limit error with OpenAI Compatible API: {e}")
        raise ValueError(f"Rate limit exceeded for OpenAI Compatible API: {e}")
    except openai.APIError as e:
        logger.error(f"API error with OpenAI Compatible API: {e}")
        raise ValueError(f"OpenAI Compatible API error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error with OpenAI Compatible API: {e}")
        traceback.print_exc()
        raise ValueError(f"Unexpected error calling OpenAI Compatible API: {e}")


def _record_interaction_optimized(assistant, user, user_input: str, response_data: dict, start_time: float):
    """Efficiently record interaction in database."""
    from .models import Interaction

    return Interaction.objects.create(
        assistant=assistant,
        user=user,
        prompt=user_input,
        response=response_data.get('content', ''),
        duration=response_data.get('duration', time.time() - start_time),
        token_count=response_data.get('token_count', 0)
    )


def _generate_suggestions_optimized(assistant, response_text: str, user_input: str) -> list:
    """Generate suggestions with caching and optimization."""
    # Simplified suggestion generation - can be enhanced later
    return []


def _get_preloaded_context(assistant_id: int, user) -> Optional[Dict[str, Any]]:
    """
    Get preloaded context for an assistant if available.
    """
    try:
        from .context_preloader import context_preloader
        return context_preloader.get_preloaded_context(assistant_id, user)
    except ImportError:
        logger.warning("Context preloader not available")
        return None
    except Exception as e:
        logger.warning(f"Error getting preloaded context: {e}")
        return None


def _create_error_response(message: str, context_id: Optional[str] = None) -> Dict[str, Any]:
    """Create standardized error response."""
    return {
        'content': message,
        'status': 'error',
        'images': [],
        'gallery': [],
        'context_id': context_id,
        'cached': False
    }
