# Generated by Django 4.2.21 on 2025-05-26 05:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("site_settings", "0004_remove_siteconfiguration_support_email"),
    ]

    operations = [
        migrations.AddField(
            model_name="siteconfiguration",
            name="support_email",
            field=models.EmailField(
                blank=True,
                default="<EMAIL>",
                help_text="Email address for support inquiries. Used in contact page and email templates.",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="siteconfiguration",
            name="support_url",
            field=models.URLField(
                blank=True,
                help_text="URL for the 'Support Center' links in emails and footer. If not set, will use contact_url or default support page.",
                max_length=255,
                null=True,
            ),
        ),
    ]
