# Generated migration to fix mutable default values in JSONField

from django.db import migrations, models


def default_dict():
    """Return an empty dict for JSONField default."""
    return {}


def default_list():
    """Return an empty list for JSONField default."""
    return []


def fix_shared_data(apps, schema_editor):
    """
    Fix any existing shared data by ensuring each instance has its own copy.
    This migration addresses the mutable default issue where all instances
    were sharing the same dict/list objects.
    """
    CompanyListing = apps.get_model('directory', 'CompanyListing')
    AssistantListing = apps.get_model('directory', 'AssistantListing')
    
    # Fix CompanyListing instances
    for listing in CompanyListing.objects.all():
        # Ensure social_links is a proper dict for each instance
        if listing.social_links is None:
            listing.social_links = {}
        elif not isinstance(listing.social_links, dict):
            listing.social_links = {}
        
        # Ensure tags is a proper list for each instance
        if listing.tags is None:
            listing.tags = []
        elif not isinstance(listing.tags, list):
            listing.tags = []
        
        listing.save(update_fields=['social_links', 'tags'])
    
    # Fix AssistantListing instances
    for listing in AssistantListing.objects.all():
        # Ensure categories is a proper list for each instance
        if listing.categories is None:
            listing.categories = []
        elif not isinstance(listing.categories, list):
            listing.categories = []
        
        # Ensure tags is a proper list for each instance
        if listing.tags is None:
            listing.tags = []
        elif not isinstance(listing.tags, list):
            listing.tags = []
        
        # Ensure capabilities is a proper list for each instance
        if listing.capabilities is None:
            listing.capabilities = []
        elif not isinstance(listing.capabilities, list):
            listing.capabilities = []
        
        listing.save(update_fields=['categories', 'tags', 'capabilities'])


def reverse_fix_shared_data(apps, schema_editor):
    """
    Reverse migration - no action needed as we're just ensuring data integrity.
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('directory', '0001_initial'),
    ]

    operations = [
        # First, run the data migration to fix existing data
        migrations.RunPython(fix_shared_data, reverse_fix_shared_data),
        
        # Then update the field definitions to use callable defaults
        migrations.AlterField(
            model_name='companylisting',
            name='social_links',
            field=models.JSONField(blank=True, default=default_dict),
        ),
        migrations.AlterField(
            model_name='companylisting',
            name='tags',
            field=models.JSONField(blank=True, default=default_list),
        ),
        migrations.AlterField(
            model_name='assistantlisting',
            name='categories',
            field=models.JSONField(blank=True, default=default_list),
        ),
        migrations.AlterField(
            model_name='assistantlisting',
            name='tags',
            field=models.JSONField(blank=True, default=default_list),
        ),
        migrations.AlterField(
            model_name='assistantlisting',
            name='capabilities',
            field=models.JSONField(blank=True, default=default_list),
        ),
    ]
