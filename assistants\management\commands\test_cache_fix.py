"""
Management command to test the QuerySet caching fix.
This command validates that the assistant list view works correctly
and doesn't encounter the 'bytes' object has no attribute 'order_by' error.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from django.http import Http404
from django.db import models
from assistants.views import assistant_list
from accounts.models import Company
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Test the QuerySet caching fix for assistant_list view'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to test (optional)',
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='User ID to test with (optional)',
        )
        parser.add_argument(
            '--clear-cache',
            action='store_true',
            help='Clear assistant cache before testing',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting QuerySet caching fix test...'))

        # Clear cache if requested
        if options['clear_cache']:
            self.stdout.write('Clearing assistant cache...')
            from assistants.advanced_caching import clear_assistant_cache
            clear_assistant_cache()
            self.stdout.write(self.style.SUCCESS('Cache cleared.'))

        # Get test user
        user_id = options.get('user_id')
        if user_id:
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'User with ID {user_id} not found'))
                return
        else:
            user = User.objects.filter(is_active=True).first()
            if not user:
                self.stdout.write(self.style.ERROR('No active users found'))
                return

        self.stdout.write(f'Testing with user: {user.username} (ID: {user.id})')

        # Get test company
        company_id = options.get('company_id')
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'Company with ID {company_id} not found'))
                return
        else:
            # Find a company the user has access to
            company = Company.objects.filter(
                models.Q(owner=user) | models.Q(memberships__user=user)
            ).first()
            if not company:
                self.stdout.write(self.style.ERROR(f'No accessible companies found for user {user.username}'))
                return

        self.stdout.write(f'Testing with company: {company.name} (ID: {company.id})')

        # Create a mock request
        factory = RequestFactory()
        request = factory.get(f'/assistant/company/{company.id}/assistants/')
        request.user = user

        # Test the view multiple times to test caching
        for i in range(3):
            self.stdout.write(f'Test run {i + 1}/3...')
            try:
                response = assistant_list(request, company.id)

                if response.status_code == 200:
                    self.stdout.write(self.style.SUCCESS(f'✓ Test run {i + 1} passed'))
                else:
                    self.stdout.write(self.style.WARNING(f'⚠ Test run {i + 1} returned status {response.status_code}'))

            except AttributeError as e:
                if "'bytes' object has no attribute 'order_by'" in str(e):
                    self.stdout.write(self.style.ERROR(f'✗ Test run {i + 1} failed with the original error: {e}'))
                    return
                else:
                    self.stdout.write(self.style.ERROR(f'✗ Test run {i + 1} failed with AttributeError: {e}'))
                    return
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'✗ Test run {i + 1} failed with error: {e}'))
                return

        # Test cache validation
        self.stdout.write('Testing cache validation...')
        from assistants.advanced_caching import validate_cache_integrity
        cache_report = validate_cache_integrity()

        self.stdout.write(f'Cache integrity report:')
        self.stdout.write(f'  Total entries: {cache_report["total_entries"]}')
        self.stdout.write(f'  Valid entries: {cache_report["valid_entries"]}')
        self.stdout.write(f'  Corrupted entries: {cache_report["corrupted_entries"]}')

        if cache_report['errors']:
            self.stdout.write(f'  Errors: {cache_report["errors"]}')

        self.stdout.write(self.style.SUCCESS('All tests passed! The QuerySet caching fix is working correctly.'))
