#!/usr/bin/env python
"""
Final verification script for cPanel deployment.
Checks all systems and confirms deployment readiness.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_cpanel_environment():
    """Setup cPanel environment for testing."""
    os.environ['CPANEL_ENV'] = 'True'
    os.environ['PRODUCTION'] = 'True'
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
    django.setup()

def check_django_system():
    """Run Django system check."""
    print("🔍 Running Django system check...")
    
    try:
        from django.core.management.commands.check import Command
        command = Command()
        issues = command.check()
        
        if not issues:
            print("✅ Django system check: PASSED (0 issues)")
            return True
        else:
            print(f"❌ Django system check: FAILED ({len(issues)} issues)")
            for issue in issues:
                print(f"   - {issue}")
            return False
    except Exception as e:
        print(f"❌ System check error: {e}")
        return False

def check_database_connection():
    """Test database connection."""
    print("🔍 Testing database connection...")
    
    try:
        from django.db import connection
        from django.conf import settings
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1;")
            result = cursor.fetchone()
            
            if result[0] == 1:
                print("✅ Database connection: WORKING")
                
                # Show database info
                db_config = settings.DATABASES['default']
                print(f"   📋 Database: {db_config['NAME']}")
                print(f"   📋 Engine: {db_config['ENGINE']}")
                return True
            else:
                print("❌ Database connection: FAILED")
                return False
                
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def check_cache_system():
    """Test cache system."""
    print("🔍 Testing cache system...")
    
    try:
        from django.core.cache import cache
        from django.conf import settings
        
        # Test cache operations
        test_key = 'deployment_test'
        test_value = 'working'
        
        cache.set(test_key, test_value, 30)
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            print("✅ Cache system: WORKING")
            
            # Show cache backend
            cache_backend = settings.CACHES['default']['BACKEND']
            print(f"   📋 Backend: {cache_backend}")
            
            if 'filebased' in cache_backend.lower():
                cache_location = settings.CACHES['default']['LOCATION']
                print(f"   📋 Location: {cache_location}")
            
            cache.delete(test_key)
            return True
        else:
            print("❌ Cache system: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Cache system error: {e}")
        return False

def check_installed_apps():
    """Check critical installed apps."""
    print("🔍 Checking installed apps...")
    
    try:
        from django.conf import settings
        
        installed_apps = settings.INSTALLED_APPS
        critical_apps = [
            'django.contrib.admin',
            'django.contrib.auth',
            'django.contrib.contenttypes',
            'django.contrib.sessions',
            'django.contrib.messages',
            'django.contrib.staticfiles',
            'accounts.apps.AccountsConfig',
            'assistants.apps.AssistantsConfig',
        ]
        
        missing_apps = []
        for app in critical_apps:
            if app not in installed_apps:
                missing_apps.append(app)
        
        if not missing_apps:
            print("✅ Critical apps: ALL PRESENT")
            print(f"   📋 Total apps: {len(installed_apps)}")
            
            # Check if impersonate is disabled
            if 'impersonate' not in installed_apps:
                print("   ✅ Impersonate: DISABLED (good for cPanel)")
            else:
                print("   ⚠️  Impersonate: ENABLED (may cause issues)")
            
            return True
        else:
            print("❌ Critical apps: MISSING")
            for app in missing_apps:
                print(f"   - {app}")
            return False
            
    except Exception as e:
        print(f"❌ Apps check error: {e}")
        return False

def check_static_files():
    """Check static files configuration."""
    print("🔍 Checking static files...")
    
    try:
        from django.conf import settings
        import os
        
        static_root = settings.STATIC_ROOT
        static_url = settings.STATIC_URL
        
        print(f"   📋 STATIC_URL: {static_url}")
        print(f"   📋 STATIC_ROOT: {static_root}")
        
        if os.path.exists(static_root):
            file_count = len([f for f in os.listdir(static_root) if os.path.isfile(os.path.join(static_root, f))])
            print(f"   📊 Static files: {file_count} files found")
            print("✅ Static files: CONFIGURED")
            return True
        else:
            print("⚠️  Static files: Directory not found (run collectstatic)")
            return False
            
    except Exception as e:
        print(f"❌ Static files error: {e}")
        return False

def check_environment_detection():
    """Check environment detection."""
    print("🔍 Checking environment detection...")
    
    try:
        from django.conf import settings
        
        in_cpanel = getattr(settings, 'IN_CPANEL', False)
        in_production = getattr(settings, 'IN_PRODUCTION', False)
        debug = getattr(settings, 'DEBUG', True)
        
        print(f"   📋 IN_CPANEL: {in_cpanel}")
        print(f"   📋 IN_PRODUCTION: {in_production}")
        print(f"   📋 DEBUG: {debug}")
        
        if in_cpanel and not debug:
            print("✅ Environment: CORRECTLY DETECTED (cPanel/Production)")
            return True
        elif not in_cpanel and debug:
            print("✅ Environment: CORRECTLY DETECTED (Development)")
            return True
        else:
            print("⚠️  Environment: MIXED SETTINGS")
            return True  # Not critical
            
    except Exception as e:
        print(f"❌ Environment check error: {e}")
        return False

def main():
    """Main verification function."""
    print("🚀 cPanel Deployment Verification")
    print("=" * 50)
    
    # Setup environment
    try:
        setup_cpanel_environment()
        print("✅ Django environment: LOADED")
    except Exception as e:
        print(f"❌ Django environment: FAILED - {e}")
        return False
    
    print("\n" + "=" * 50)
    
    # Run all checks
    checks = [
        ("Django System", check_django_system),
        ("Database Connection", check_database_connection),
        ("Cache System", check_cache_system),
        ("Installed Apps", check_installed_apps),
        ("Static Files", check_static_files),
        ("Environment Detection", check_environment_detection),
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_function in checks:
        print(f"\n📋 {check_name}:")
        if check_function():
            passed_checks += 1
        print("-" * 30)
    
    # Final summary
    print("\n" + "=" * 50)
    print("📊 DEPLOYMENT VERIFICATION SUMMARY")
    print("=" * 50)
    
    if passed_checks == total_checks:
        print("🎉 ALL CHECKS PASSED!")
        print("✅ Your Django application is ready for cPanel deployment")
        print("\n📋 Next steps:")
        print("1. Upload your files to cPanel")
        print("2. Restart your cPanel application")
        print("3. Test your website functionality")
        print("4. Monitor application logs")
        return True
    else:
        print(f"⚠️  {passed_checks}/{total_checks} checks passed")
        print("❌ Some issues need attention before deployment")
        print("\n📋 Review the failed checks above and fix any issues")
        return False

if __name__ == '__main__':
    main()
