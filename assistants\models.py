import uuid
import decimal # Import decimal for precise calculations
from django.db import models
from django.db.models import Avg # Import Avg
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify
from django.utils import timezone # Import timezone
from tinymce.models import HTMLField # Import HTMLField
# Ensure AssistantListing is NOT imported at the top level

User = get_user_model()


# Helper functions for J<PERSON>NField defaults to avoid mutable default issues
def default_dict():
    """Return an empty dict for <PERSON><PERSON><PERSON><PERSON> default."""
    return {}


def default_list():
    """Return an empty list for <PERSON><PERSON><PERSON><PERSON> default."""
    return []


class AssistantFolder(models.Model):
    """Organizes assistants within a company."""
    name = models.CharField(max_length=100)
    company = models.ForeignKey(
        'accounts.Company',
        on_delete=models.CASCADE,
        related_name='assistant_folders'
    )
    # For potential future nesting - keep simple for now
    # parent = models.ForeignKey(
    #     'self',
    #     on_delete=models.CASCADE,
    #     null=True,
    #     blank=True,
    #     related_name='subfolders'
    # )
    order = models.IntegerField(default=0, db_index=True, help_text="Order in which folders appear (lower numbers first).") # Added order field
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('company', 'name') # Folder names must be unique within a company
        ordering = ['order', 'name'] # Default ordering by new field, then name
        # Standard CRUD permissions are automatically created (add, change, delete, view)
        # We can add custom ones if needed, but the boolean 'can_manage_folders'
        # likely maps to these standard permissions checked via guardian.
        # Example custom permission if needed:
        # permissions = [
        #     ("manage_folder_structure", "Can manage folder structure"),
        # ]

    def __str__(self):
        return f"{self.name} ({self.company.name})"


from datetime import timedelta # Add timedelta for expiry calculation later

class Assistant(models.Model):
    """AI assistant model that can be configured and trained for specific tasks."""

    # Duration Choices for Tier/Featured status
    DURATION_MONTHLY = 'monthly'
    DURATION_QUARTERLY = 'quarterly'
    DURATION_ANNUALLY = 'annually'
    DURATION_CHOICES = [
        (DURATION_MONTHLY, 'Monthly'),
        (DURATION_QUARTERLY, 'Quarterly'),
        (DURATION_ANNUALLY, 'Annually'),
    ]

    # Assistant Types
    TYPE_GENERAL = 'general'
    TYPE_SUPPORT = 'support'
    TYPE_COMMUNITY = 'community'
    TYPE_CONTENT = 'content'
    TYPE_RESEARCH = 'research'
    TYPE_CODING = 'coding'
    TYPE_DATA = 'data'

    ASSISTANT_TYPES = [
        (TYPE_GENERAL, 'General Purpose'),
        (TYPE_SUPPORT, 'Customer Support'),
        (TYPE_COMMUNITY, 'Community Assistant'),
        # (TYPE_CONTENT, 'Content Creation'),
        # (TYPE_RESEARCH, 'Research Assistant'),
        # (TYPE_CODING, 'Code Assistant'),
        # (TYPE_DATA, 'Data Analysis')
    ]

    # Model Options - now configurable via environment variables
    MODEL_GPT35 = getattr(settings, 'MODEL_GPT35', 'gpt-3.5-turbo')
    MODEL_GPT4 = getattr(settings, 'MODEL_GPT4', 'gpt-4')
    MODEL_GPT4_TURBO = getattr(settings, 'MODEL_GPT4_TURBO', 'gpt-4-turbo')
    MODEL_CLAUDE = getattr(settings, 'MODEL_CLAUDE', 'claude-3-sonnet-20240229')
    MODEL_CLAUDE_INSTANT = getattr(settings, 'MODEL_CLAUDE_INSTANT', 'claude-3-haiku-20240307')
    MODEL_GEMINI_FLASH = getattr(settings, 'MODEL_GEMINI_FLASH', 'gemini-2.0-flash')
    MODEL_LLAMA_70B = getattr(settings, 'MODEL_LLAMA_70B', 'llama-3.3-70b-versatile')
    MODEL_OPENAI_COMPATIBLE = 'openai-compatible'  # This remains constant

    MODEL_CHOICES = [
        (MODEL_GPT35, 'GPT-3.5 Turbo'),
        (MODEL_GPT4, 'GPT-4'),
        (MODEL_GPT4_TURBO, 'GPT-4 Turbo'),
        (MODEL_CLAUDE, 'Claude 3 Sonnet'),
        (MODEL_CLAUDE_INSTANT, 'Claude 3 Haiku'),
        (MODEL_GEMINI_FLASH, 'Gemini 2.0 Flash'),
        (MODEL_LLAMA_70B, 'Llama 3.3 70B Versatile'),
        (MODEL_OPENAI_COMPATIBLE, 'OpenAI Compatible'),
    ]

    # Basic Information
    name = models.CharField(max_length=100, help_text="Internal name for identification.")
    persona_name = models.CharField(
        max_length=100,
        blank=True,
        default='Emilly', # Set default persona name
        help_text="Optional: The name the assistant uses in conversation (defaults to internal name)."
    )
    slug = models.SlugField(max_length=100, unique=True)
    description = HTMLField(blank=True) # Changed to HTMLField
    assistant_type = models.CharField(
        max_length=20,
        choices=ASSISTANT_TYPES,
        default=TYPE_GENERAL
    )
    model = models.CharField(
        max_length=50,
        choices=MODEL_CHOICES,
        default=MODEL_GPT35
    )

    # Tier Configuration
    TIER_FEATURED = 'Featured'
    TIER_GOLD = 'Gold'
    TIER_SILVER = 'Silver'
    TIER_BRONZE = 'Bronze'
    # TIER_FEATURED = 'Featured' # Removed Featured from Tier
    TIER_GOLD = 'Gold'
    TIER_SILVER = 'Silver'
    TIER_BRONZE = 'Bronze'
    TIER_STANDARD = 'Standard'

    TIER_CHOICES = [
        # (TIER_FEATURED, 'Featured'), # Removed Featured from Tier choices
        (TIER_GOLD, 'Gold'),
        (TIER_SILVER, 'Silver'),
        (TIER_BRONZE, 'Bronze'),
        (TIER_STANDARD, 'Standard'),
    ]

    # Choices specifically for the request dropdown (excluding Standard)
    TIER_REQUEST_CHOICES = [
        (TIER_GOLD, 'Gold'),
        (TIER_SILVER, 'Silver'),
        (TIER_BRONZE, 'Bronze'),
    ]

    tier = models.CharField(
        max_length=10,
        choices=TIER_CHOICES,
        default=TIER_STANDARD,
        db_index=True,
        help_text="Directory display tier for this assistant."
    )
    is_featured = models.BooleanField(
        default=False,
         db_index=True,
         help_text="Mark this assistant to appear in the featured carousel on the directory."
     )
    requested_tier = models.CharField(
        max_length=10,
        choices=TIER_CHOICES,
        blank=True,
        null=True,
        help_text="Tier requested for the assistant, pending superadmin approval."
    )
    tier_change_pending = models.BooleanField(
        default=False,
        db_index=True,
         help_text="Indicates if a tier change request is pending approval."
    )
    tier_expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date and time when the current tier expires (if applicable)."
    )
    requested_tier_duration = models.CharField(
        max_length=10,
        choices=DURATION_CHOICES,
        blank=True,
        null=True,
        help_text="Duration requested for the tier upgrade."
    )
    featured_request_pending = models.BooleanField(
        default=False,
        db_index=True,
        help_text="Indicates if a request to feature this assistant is pending approval."
    )
    featured_expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date and time when the featured status expires (if applicable)."
    )
    requested_featured_duration = models.CharField(
        max_length=10,
        choices=DURATION_CHOICES,
        blank=True,
        null=True,
        help_text="Duration requested for the featured status."
    )


    # Removed Featured Carousel Settings fields

    # Company and User Relations
    company = models.ForeignKey(
        'accounts.Company',
        on_delete=models.CASCADE,
        related_name='assistants'
    )
    linked_company = models.ForeignKey(
        'accounts.Company',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='linked_community_assistants',
        help_text="Optional company to link this community assistant to"
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_assistants'
    )
    folder = models.ForeignKey(
        AssistantFolder,
        on_delete=models.SET_NULL, # Keep assistant if folder is deleted, just unassign it
        null=True,
        blank=True,
        related_name='assistants',
        help_text="Optional folder to organize this assistant."
    )
    logo = models.ImageField(
        upload_to='assistant_logos/',
        blank=True,
        null=True,
        help_text="Optional logo specifically for this assistant. Falls back to company logo if not set."
    )

    def logo_url(self):
        """
        Safe method to get the logo URL, handling cases where the file exists but URL is not available.
        Returns None if the logo doesn't exist or URL is not available.
        """
        if self.logo and hasattr(self.logo, 'url'):
            try:
                # Make sure the logo name is not empty
                if not self.logo.name:
                    print(f"DEBUG MODEL logo_url [{self.id}]: Logo name is empty")
                    return None

                # Check if the file exists
                import os
                from django.conf import settings
                import shutil

                # Get the full path to the file
                file_path = os.path.join(settings.MEDIA_ROOT, self.logo.name)
                print(f"DEBUG MODEL logo_url [{self.id}]: Checking file at {file_path}")

                if not os.path.exists(file_path):
                    print(f"DEBUG MODEL logo_url [{self.id}]: Logo file does not exist at {file_path}")

                    # Try to fix the URL by ensuring it starts with the correct path
                    if not self.logo.name.startswith('assistant_logos/'):
                        corrected_name = f"assistant_logos/{os.path.basename(self.logo.name)}"
                        print(f"DEBUG MODEL logo_url [{self.id}]: Attempting to correct logo name from {self.logo.name} to {corrected_name}")

                        # Ensure the assistant_logos directory exists
                        target_dir = os.path.join(settings.MEDIA_ROOT, 'assistant_logos')
                        if not os.path.exists(target_dir):
                            os.makedirs(target_dir)
                            print(f"DEBUG MODEL logo_url [{self.id}]: Created directory {target_dir}")

                        # Check if the corrected file exists
                        corrected_path = os.path.join(settings.MEDIA_ROOT, corrected_name)
                        print(f"DEBUG MODEL logo_url [{self.id}]: Checking corrected path at {corrected_path}")

                        if os.path.exists(corrected_path):
                            print(f"DEBUG MODEL logo_url [{self.id}]: Found logo at corrected path {corrected_path}")
                            # Update the logo name in the database
                            self.logo.name = corrected_name
                            self.save(update_fields=['logo'])
                        else:
                            print(f"DEBUG MODEL logo_url [{self.id}]: Corrected file still doesn't exist")

                            # Check if the file exists in the media root directly
                            base_name = os.path.basename(self.logo.name)
                            direct_path = os.path.join(settings.MEDIA_ROOT, base_name)
                            print(f"DEBUG MODEL logo_url [{self.id}]: Checking direct path at {direct_path}")

                            if os.path.exists(direct_path):
                                print(f"DEBUG MODEL logo_url [{self.id}]: Found logo at direct path {direct_path}")
                                # Copy the file to the correct location
                                try:
                                    shutil.copy2(direct_path, corrected_path)
                                    print(f"DEBUG MODEL logo_url [{self.id}]: Copied file from {direct_path} to {corrected_path}")
                                    # Update the logo name in the database
                                    self.logo.name = corrected_name
                                    self.save(update_fields=['logo'])
                                except Exception as e:
                                    print(f"DEBUG MODEL logo_url [{self.id}]: Error copying file: {e}")
                                    # If copy fails, just use the direct path
                                    self.logo.name = base_name
                                    self.save(update_fields=['logo'])
                    else:
                        # Check if the file exists in the media root directly
                        base_name = os.path.basename(self.logo.name)
                        direct_path = os.path.join(settings.MEDIA_ROOT, base_name)
                        print(f"DEBUG MODEL logo_url [{self.id}]: Checking direct path at {direct_path}")

                        if os.path.exists(direct_path):
                            print(f"DEBUG MODEL logo_url [{self.id}]: Found logo at direct path {direct_path}")
                            # Copy the file to the correct location
                            target_path = os.path.join(settings.MEDIA_ROOT, self.logo.name)
                            target_dir = os.path.dirname(target_path)
                            if not os.path.exists(target_dir):
                                os.makedirs(target_dir)
                                print(f"DEBUG MODEL logo_url [{self.id}]: Created directory {target_dir}")

                            try:
                                shutil.copy2(direct_path, target_path)
                                print(f"DEBUG MODEL logo_url [{self.id}]: Copied file from {direct_path} to {target_path}")
                            except Exception as e:
                                print(f"DEBUG MODEL logo_url [{self.id}]: Error copying file: {e}")
                                # If copy fails, just use the direct path
                                self.logo.name = base_name
                                self.save(update_fields=['logo'])

                # After potential corrections, check if the file exists now
                file_path = os.path.join(settings.MEDIA_ROOT, self.logo.name)
                if os.path.exists(file_path):
                    print(f"DEBUG MODEL logo_url [{self.id}]: Logo exists, URL = {self.logo.url}")
                    return self.logo.url
                else:
                    print(f"DEBUG MODEL logo_url [{self.id}]: Logo still doesn't exist after correction attempts")
                    return None
            except (ValueError, AttributeError) as e:
                print(f"DEBUG MODEL logo_url [{self.id}]: Error getting logo URL: {e}")
                return None
        print(f"DEBUG MODEL logo_url [{self.id}]: No logo or no URL attribute")
        return None

    def get_logo_url(self):
        """
        Get the logo URL with fallback to company logo.
        Returns the assistant logo URL if available, otherwise falls back to company logo URL.
        """
        # First try to get the assistant's own logo
        logo_url = self.logo_url()
        if logo_url:
            print(f"DEBUG MODEL get_logo_url [{self.id}]: Using assistant logo: {logo_url}")
            return logo_url

        # Fall back to company logo if available
        if self.company and hasattr(self.company, 'info') and self.company.info:
            company_info = self.company.info
            print(f"DEBUG MODEL get_logo_url [{self.id}]: Checking company logo")

            if company_info.logo and hasattr(company_info.logo, 'url'):
                try:
                    # Check if company logo file exists
                    import os
                    from django.conf import settings

                    if company_info.logo.name:
                        company_logo_path = os.path.join(settings.MEDIA_ROOT, company_info.logo.name)
                        print(f"DEBUG MODEL get_logo_url [{self.id}]: Checking company logo at {company_logo_path}")

                        if os.path.exists(company_logo_path):
                            company_logo_url = company_info.logo.url
                            print(f"DEBUG MODEL get_logo_url [{self.id}]: Using company logo: {company_logo_url}")
                            return company_logo_url
                        else:
                            print(f"DEBUG MODEL get_logo_url [{self.id}]: Company logo file doesn't exist at {company_logo_path}")
                    else:
                        print(f"DEBUG MODEL get_logo_url [{self.id}]: Company logo name is empty")
                except (ValueError, AttributeError) as e:
                    print(f"DEBUG MODEL get_logo_url [{self.id}]: Error getting company logo URL: {e}")
                    pass
            else:
                print(f"DEBUG MODEL get_logo_url [{self.id}]: Company has no logo or URL attribute")

        # Try to get default logo from site configuration
        try:
            from site_settings.models import SiteConfiguration
            site_config = SiteConfiguration.objects.first()
            if site_config and site_config.default_assistant_logo:
                default_logo_url = site_config.default_assistant_logo.url
                print(f"DEBUG MODEL get_logo_url [{self.id}]: Using default site logo: {default_logo_url}")
                return default_logo_url
        except Exception as e:
            print(f"DEBUG MODEL get_logo_url [{self.id}]: Error getting default site logo: {e}")
            pass

        # If no logo is available, return None
        print(f"DEBUG MODEL get_logo_url [{self.id}]: No logo available")
        return None
    avatar = models.ImageField(
        upload_to='assistant_avatars/', # Use a different upload path
        blank=True,
        null=True,
        help_text="Optional avatar for the chat interface. Falls back to assistant logo, then company logo if not set."
    )

    # Customer Care / Gradio specific config
    website_data = models.JSONField(default=default_dict, blank=True, help_text="Structured website data for customer care assistants (content keyed by NavigationItem unique_id)")
    # nav_config = models.JSONField(default=list, blank=True, help_text="Navigation structure for customer care assistants") # Removed: Use NavigationItem model instead
    extra_context = HTMLField(blank=True, help_text="Additional context for the LLM") # Changed to HTMLField

    # OpenAI Compatible Configuration
    api_key = models.CharField(
        max_length=255,
        blank=True,
        help_text="API key for OpenAI Compatible models"
    )
    base_url = models.URLField(
        max_length=255,
        blank=True,
        default=getattr(settings, 'CUSTOM_LLM_BASE_URL', 'https://api.openai.com/v1'),
        help_text="Base URL for OpenAI Compatible API"
    )
    custom_model_name = models.CharField(
        max_length=100,
        blank=True,
        help_text="Model name for OpenAI Compatible API"
    )

    # Configuration
    temperature = models.FloatField(
        default=0.7,
        validators=[
            MinValueValidator(0.0),
            MaxValueValidator(2.0)
        ],
        help_text="Controls randomness in responses (0.0-2.0)"
    )
    max_tokens = models.IntegerField(
        default=2048,
        validators=[
            MinValueValidator(1),
            MaxValueValidator(4096)
        ],
        help_text="Maximum length of generated responses"
    )
    system_prompt = HTMLField( # Changed to HTMLField
        blank=True,
        help_text="Instructions that define the assistant's behavior"
    )
    greeting_message = HTMLField( # Changed to HTMLField
        blank=True,
        help_text="Optional: Custom initial greeting message displayed in the chat interface."
    )
    knowledge_base = models.FileField(
        upload_to='assistants/knowledge_base/',
        null=True,
        blank=True,
        help_text="Optional files for domain-specific knowledge"
    )
    custom_css = models.TextField(
        blank=True,
        help_text="Custom CSS rules to style the assistant's appearance (e.g., chat interface)."
    )
    show_sidebar = models.BooleanField(
        default=True,
        help_text="Whether to show the sidebar in the chat interface. Only applies to non-general assistants."
    )
    show_sidebar_public = models.BooleanField(
        default=True,
        help_text="Whether to show the sidebar in the public chat interface. Only applies when show_sidebar is True."
    )
    saved_suggestions = models.JSONField(
        default=default_list,
        blank=True,
        help_text="List of suggested questions saved from the Analyze & Suggest tab."
    )

    # Status and Stats
    is_active = models.BooleanField(
        default=False, # Changed default to False to require approval
        db_index=True,
        help_text="Whether this assistant is currently active and usable (approved)."
    )
    is_public = models.BooleanField(
        default=True,
        help_text="Controls visibility in the public directory and company pages"
    )
    # hide_if_standard field removed - replaced by global DirectorySetting
    qr_code = models.ImageField(upload_to='assistant_qrcodes/', blank=True, null=True)
    total_interactions = models.IntegerField(default=0)
    average_rating = models.FloatField(
        null=True,
        blank=True,
        validators=[
            MinValueValidator(1.0),
            MaxValueValidator(5.0)
        ]
    )
    last_trained = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the assistant was last trained"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['company', 'assistant_type']),
            models.Index(fields=['company', 'is_active']),
        ]
        # Define standard permissions corresponding to the old boolean flags
        permissions = [
            ("view_assistant_usage", "Can view assistant usage data"),
            ("view_assistant_analytics", "Can view assistant analytics"),
            ("create_assistant_token", "Can create access tokens for assistants"),
            ("access_all_private", "Can access all private assistants in the company"), # Global-like perm
        ]
        # Note: Standard add, change, delete, view permissions *should* be automatically created.
        # The old booleans 'can_create_assistants', 'can_edit_assistants', 'can_delete_assistants'
        # will map to these standard permissions checked via guardian.

    def __str__(self):
        return f"{self.name} ({self.get_assistant_type_display()})"

    def save(self, *args, **kwargs):
        """Generate a unique slug on save if not provided and handle logo."""
        # First save to get an ID if this is a new instance
        is_new = self.pk is None
        if is_new and not self.slug:
            # For new instances, save first with a temporary slug to get an ID
            import uuid
            temp_slug = f'temp-{uuid.uuid4().hex[:8]}'
            self.slug = temp_slug
            super().save(*args, **kwargs)
            # Now generate the proper slug with the ID
            self._generate_unique_slug()
            # Save again with the proper slug
            super().save(update_fields=['slug'])
            return  # Exit early for new instances
        elif not self.slug:
            # For existing instances without a slug, generate one
            self._generate_unique_slug()

        # Continue with normal save for existing instances
        super().save(*args, **kwargs)

    def _generate_unique_slug(self):
        """Generate a unique slug with company name, assistant name, and unique identifier."""
        import uuid

        # Include company name in slug for better uniqueness and identification
        company_slug = slugify(self.company.name) if self.company else 'unknown'
        assistant_slug = slugify(self.name)

        # Generate a short unique identifier (8 characters from UUID)
        unique_id = uuid.uuid4().hex[:8]

        # Create the slug with format: company-assistant-uniqueid
        base_slug = f'{company_slug}-{assistant_slug}-{unique_id}'

        # Ensure the slug is unique (very unlikely to conflict with UUID, but just in case)
        queryset = Assistant.objects.all()
        if self.pk:
            queryset = queryset.exclude(pk=self.pk)

        slug = base_slug
        num = 1
        while queryset.filter(slug=slug).exists():
            slug = f'{base_slug}-{num}'
            num += 1

        self.slug = slug

        # Debug logo state after saving
        if self.logo:
            print(f"DEBUG MODEL save [{self.id}]: Logo after save: {self.logo.name}")

            # Verify the logo file exists
            import os
            from django.conf import settings
            file_path = os.path.join(settings.MEDIA_ROOT, self.logo.name)
            if os.path.exists(file_path):
                print(f"DEBUG MODEL save [{self.id}]: Logo file exists at {file_path}")

                # Check if the file is readable
                try:
                    with open(file_path, 'rb') as f:
                        # Just read a few bytes to check if the file is accessible
                        f.read(10)
                    print(f"DEBUG MODEL save [{self.id}]: Logo file is readable")

                    # Verify the URL is accessible
                    try:
                        logo_url = self.logo.url
                        print(f"DEBUG MODEL save [{self.id}]: Logo URL is accessible: {logo_url}")
                    except Exception as e:
                        print(f"DEBUG MODEL save [{self.id}]: Logo URL is not accessible: {e}")
                except Exception as e:
                    print(f"DEBUG MODEL save [{self.id}]: Logo file exists but is not readable: {e}")
            else:
                print(f"DEBUG MODEL save [{self.id}]: Logo file does not exist at {file_path}")

                # Check if the file exists in the media root directly
                base_name = os.path.basename(self.logo.name)
                direct_path = os.path.join(settings.MEDIA_ROOT, base_name)
                if os.path.exists(direct_path):
                    print(f"DEBUG MODEL save [{self.id}]: Logo file exists at direct path {direct_path}")

                    # Copy the file to the correct location
                    import shutil
                    try:
                        # Ensure the assistant_logos directory exists
                        target_dir = os.path.dirname(file_path)
                        if not os.path.exists(target_dir):
                            os.makedirs(target_dir)
                            print(f"DEBUG MODEL save [{self.id}]: Created directory {target_dir}")

                        shutil.copy2(direct_path, file_path)
                        print(f"DEBUG MODEL save [{self.id}]: Copied file from {direct_path} to {file_path}")

                        # Force a refresh from the database
                        from django.db import connection
                        connection.close()
                        self.refresh_from_db()
                        print(f"DEBUG MODEL save [{self.id}]: Refreshed from database, logo name: {self.logo.name}")
                    except Exception as e:
                        print(f"DEBUG MODEL save [{self.id}]: Error copying file: {e}")
        else:
            print(f"DEBUG MODEL save [{self.id}]: No logo after save")

    def process_training_data(self, file):
        """Process and incorporate new training data."""
        # This would be implemented based on the specific LLM backend
        from .llm_utils import process_training_file
        process_training_file(self, file)
        self.last_trained = timezone.now()
        self.save()

    def validate_openai_compatible_config(self):
        """Validate configuration for OpenAI compatible models."""
        if self.model == self.MODEL_OPENAI_COMPATIBLE:
            errors = []

            if not self.api_key or not self.api_key.strip():
                errors.append("API key is required for OpenAI Compatible model")

            if not self.base_url or not self.base_url.strip():
                errors.append("Base URL is required for OpenAI Compatible model")

            if not self.custom_model_name or not self.custom_model_name.strip():
                errors.append("Model name is required for OpenAI Compatible model")

            # Validate base URL format
            if self.base_url and self.base_url.strip():
                from django.core.validators import URLValidator
                from django.core.exceptions import ValidationError
                validator = URLValidator()
                try:
                    validator(self.base_url.strip())
                except ValidationError:
                    errors.append("Base URL must be a valid URL")

            if errors:
                from django.core.exceptions import ValidationError
                raise ValidationError(errors)

        return True

    def clean(self):
        """Validate the model before saving."""
        super().clean()
        self.validate_openai_compatible_config()


class CommunityContext(models.Model):
    """Knowledge context contributed by community members."""
    assistant = models.ForeignKey(
        Assistant,
        on_delete=models.CASCADE,
        related_name='community_contexts',
        limit_choices_to={'assistant_type': Assistant.TYPE_COMMUNITY}
    )
    title = models.CharField(max_length=255, help_text="Title for this knowledge context", blank=True, null=True)
    text_content = models.TextField(help_text="The knowledge content to be used by the assistant")
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='contributed_contexts'
    )
    keywords = models.JSONField(default=default_list, blank=True, help_text="List of keywords to help categorize this context")
    is_active = models.BooleanField(default=True, help_text="Whether this context is available for use")
    times_used = models.PositiveIntegerField(default=0, help_text="Number of times this context was used in responses")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Community Context"
        verbose_name_plural = "Community Contexts"
        indexes = [
            models.Index(fields=['assistant', 'created_at']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.title} - {self.created_at.strftime('%Y-%m-%d')}"

class Interaction(models.Model):
    """Record of interactions between users and assistants."""

    assistant = models.ForeignKey(
        Assistant,
        on_delete=models.CASCADE,
        related_name='interactions'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='assistant_interactions'
    )
    prompt = models.TextField()
    response = models.TextField()
    context = models.TextField(blank=True)
    rating = models.IntegerField(
        null=True,
        blank=True,
        validators=[
            MinValueValidator(1),
            MaxValueValidator(5)
        ]
    )
    duration = models.FloatField(
        help_text="Time taken to generate response in seconds"
    )
    token_count = models.IntegerField(
        help_text="Total tokens used in the interaction"
    )
    use_community_context = models.BooleanField(
        default=False,
        help_text="Whether to use community context for this interaction"
    )
    used_contexts = models.ManyToManyField(
        CommunityContext,
        related_name='interactions_used_in',
        blank=True,
        help_text="Contexts used in generating this response"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['assistant', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f"Interaction with {self.assistant.name} by {self.user}"

    def save(self, *args, **kwargs):
        """Update assistant stats on save."""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # Update total interactions
            self.assistant.total_interactions += 1

            # Update average rating if rated
            # Update average rating on the Assistant model itself
            if self.rating is not None: # Check if a rating was actually given in this interaction
                interactions = self.assistant.interactions.filter(rating__isnull=False)
                rating_count = interactions.count()
                avg_rating_decimal = interactions.aggregate(Avg('rating'))['rating__avg']

                # Update Assistant model
                # Update the Assistant model's average_rating (optional, seems redundant now)
                # self.assistant.average_rating = float(avg_rating_decimal) if avg_rating_decimal else None
                # self.assistant.save(update_fields=['average_rating', 'total_interactions'])

                # !! IMPORTANT: Remove direct update to AssistantListing from here.
                # This will be handled by a signal to ensure the combined average is calculated.
                self.assistant.save(update_fields=['total_interactions']) # Still update interaction count

                # Trigger the update via signal (implemented in signals.py)
                # The signal handler will call assistant.listing.update_rating_stats()
            else:
                 # If interaction is saved without a rating, still update total interactions on Assistant
                 self.assistant.save(update_fields=['total_interactions'])


class AssistantAccessToken(models.Model):
    """Grants temporary or specific access to a single private assistant."""
    assistant = models.ForeignKey(
        Assistant,
        on_delete=models.CASCADE,
        related_name='access_tokens'
    )
    token = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_assistant_tokens'
    )
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Optional date/time when this token expires."
    )
    max_uses = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Optional limit on the number of times this token can be used."
    )
    uses_count = models.PositiveIntegerField(default=0, editable=False)
    is_active = models.BooleanField(default=True)
    notes = models.CharField(
        max_length=255,
        blank=True,
        help_text="Optional notes for the creator about this token's purpose."
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Assistant Access Token"
        verbose_name_plural = "Assistant Access Tokens"

    def __str__(self):
        return f"Token for {self.assistant.name} ({self.token})"

    @property
    def is_valid(self):
        """Checks if the token is currently valid (active, not expired, within use limits)."""
        if not self.is_active:
            return False
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        if self.max_uses is not None and self.uses_count >= self.max_uses:
            return False
        return True

    def increment_use(self):
        """Increments the use count."""
        if self.max_uses is not None: # Only increment if there's a limit
            self.uses_count = models.F('uses_count') + 1
            self.save(update_fields=['uses_count'])





# --- Community Assistant Models ---

class ContextImage(models.Model):
    """Images attached to community contexts."""
    context = models.ForeignKey(
        CommunityContext,
        on_delete=models.CASCADE,
        related_name='images'
    )
    image = models.ImageField(upload_to='community_context_images/')
    caption = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['created_at']
        verbose_name = "Context Image"
        verbose_name_plural = "Context Images"

    def __str__(self):
        return f"Image for {self.context.title}"


class FlaggedQuestion(models.Model):
    """Questions flagged by users as needing better answers."""
    assistant = models.ForeignKey(
        Assistant,
        on_delete=models.CASCADE,
        related_name='flagged_questions',
        limit_choices_to={'assistant_type': Assistant.TYPE_COMMUNITY}
    )
    tracking_id = models.CharField(max_length=50, unique=True,
                               help_text="Unique ID for tracking this flagged question, especially for anonymous users")
    question = models.TextField(help_text="The question that was flagged")
    question_text = models.TextField(help_text="The question that was flagged", null=True, blank=True)
    original_answer = models.TextField(help_text="The answer that was provided")
    reason = models.TextField(blank=True, help_text="Reason for flagging")
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,  # Allow blank for anonymous users
        related_name='flagged_questions'
    )
    flagged_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='flagged_questions_by',
        blank=True
    )
    is_anonymous = models.BooleanField(default=False, help_text="Whether this question was flagged by an anonymous user")
    email = models.EmailField(blank=True, null=True, help_text="Optional email for anonymous users to receive notifications")
    is_resolved = models.BooleanField(default=False, help_text="Whether this flagged question has been resolved")
    resolved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_questions'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Flagged Question"
        verbose_name_plural = "Flagged Questions"
        indexes = [
            models.Index(fields=['assistant', 'created_at']),
            models.Index(fields=['is_resolved']),
        ]

    def __str__(self):
        # Use question field if available, otherwise use question_text
        question_content = self.question if hasattr(self, 'question') and self.question else self.question_text
        return f"Flagged: {question_content[:50]}..."

    def save(self, *args, **kwargs):
        """Generate a unique tracking ID if not provided."""
        if not self.tracking_id:
            self.tracking_id = str(uuid.uuid4())
        super().save(*args, **kwargs)

    def resolve(self):
        """Mark this flagged question as resolved."""
        self.is_resolved = True
        self.resolved_at = timezone.now()
        self.save(update_fields=['is_resolved', 'resolved_at'])


class ContextNotification(models.Model):
    """Notifications for new context additions."""
    context = models.ForeignKey(
        CommunityContext,
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='context_notifications'
    )
    flagged_question = models.ForeignKey(
        FlaggedQuestion,
        on_delete=models.CASCADE,
        related_name='notifications',
        null=True,
        blank=True
    )
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Context Notification"
        verbose_name_plural = "Context Notifications"
        indexes = [
            models.Index(fields=['user', 'is_read']),
        ]

    def __str__(self):
        if hasattr(self, 'flagged_question') and self.flagged_question:
            return f"Notification for {self.user.username} about {self.flagged_question}"
        return f"Notification for {self.user.username} - {self.context.title}"


class ContextUpvote(models.Model):
    """Upvotes for community context contributions."""
    context = models.ForeignKey(
        CommunityContext,
        on_delete=models.CASCADE,
        related_name='upvotes'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='context_upvotes'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'context')
        verbose_name = "Context Upvote"
        verbose_name_plural = "Context Upvotes"

    def __str__(self):
        return f"Upvote by {self.user.username} for {self.context.title}"


class AnswerUpvote(models.Model):
    """Upvotes for assistant answers with context attribution."""
    interaction = models.ForeignKey(
        Interaction,
        on_delete=models.CASCADE,
        related_name='upvotes'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='answer_upvotes'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'interaction')
        verbose_name = "Answer Upvote"
        verbose_name_plural = "Answer Upvotes"

    def __str__(self):
        return f"Upvote by {self.user.username} for interaction {self.interaction.id}"


# --- Navigation & Website Data Models (for Support Assistant Type) ---

SECTION_TYPE_CHOICES = [
    ("text", "Text"),
    ("product", "Product"),
    ("service", "Service"),
    ("team", "Team"),
    ("location", "Location"),
    ("faq", "FAQ")
]

class NavigationItem(models.Model):
    """Defines a section in the support assistant's sidebar navigation."""
    assistant = models.ForeignKey(
        Assistant,
        on_delete=models.CASCADE,
        related_name='navigation_items'
    )
    label = models.CharField(max_length=100, help_text="Display name for the navigation item.")
    unique_id = models.SlugField(max_length=100, editable=False, help_text="Unique identifier for this section (auto-generated from label).")
    order = models.PositiveIntegerField(default=0, db_index=True, help_text="Order in sidebar (lower numbers first).")
    visible = models.BooleanField(default=True, help_text="Whether this item appears in the navigation.")
    section_type = models.CharField(
        max_length=20,
        choices=SECTION_TYPE_CHOICES,
        default='text',
        help_text="The type of content this navigation item represents."
    )
    # Add gallery field with default empty list to fix NOT NULL constraint
    gallery = models.JSONField(default=default_list, blank=True, help_text="Gallery images for this navigation item.")
    # entry_count field removed
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        # Generate unique_id from label if not set
        if not self.unique_id and self.label:
            self.unique_id = slugify(self.label)

        # Ensure gallery is never None
        if self.gallery is None:
            self.gallery = []

        super().save(*args, **kwargs)

    class Meta:
        unique_together = ('assistant', 'label') # Labels must be unique per assistant
        ordering = ['order', 'label']
        verbose_name = "Navigation Item"
        verbose_name_plural = "Navigation Items"

    def save(self, *args, **kwargs):
        """Generate unique_id from label on save."""
        if not self.unique_id:
            base_slug = slugify(self.label) if self.label else 'section'
            # Ensure uniqueness within the same assistant
            queryset = NavigationItem.objects.filter(assistant=self.assistant)
            if self.pk:
                queryset = queryset.exclude(pk=self.pk)

            slug = base_slug
            num = 1
            while queryset.filter(unique_id=slug).exists():
                slug = f'{base_slug}-{num}'
                num += 1
            self.unique_id = slug
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.label} ({self.assistant.name})"

# TODO (Phase 2): Define models for WebsiteDataEntry types (TextSection, ProductEntry, etc.)
# linked to NavigationItem via ForeignKey or OneToOneField based on section_type.


# Community Assistant Moderation Models
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

class UserReputation(models.Model):
    """Track reputation scores for users contributing to community assistants."""
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='reputation'
    )
    score = models.IntegerField(default=0)
    level = models.CharField(max_length=50, default='New Member')
    contributions_count = models.PositiveIntegerField(default=0)
    upvotes_received = models.PositiveIntegerField(default=0)
    reports_against = models.PositiveIntegerField(default=0)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Reputation: {self.score}"

    def update_level(self):
        """Update the user's level based on their score."""
        if self.score < 0:
            self.level = 'Restricted'
        elif self.score < 10:
            self.level = 'New Member'
        elif self.score < 50:
            self.level = 'Contributor'
        elif self.score < 200:
            self.level = 'Trusted Member'
        elif self.score < 500:
            self.level = 'Expert'
        else:
            self.level = 'Community Leader'
        self.save(update_fields=['level'])

class ReportedContent(models.Model):
    """Track content reported by users for moderation."""
    REPORT_TYPES = [
        ('spam', 'Spam'),
        ('inappropriate', 'Inappropriate Content'),
        ('offensive', 'Offensive Language'),
        ('misinformation', 'Misinformation'),
        ('other', 'Other')
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('removed', 'Content Removed')
    ]

    # Generic foreign key to allow reporting different types of content
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    reported_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='reported_content'
    )
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES)
    reason = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    assistant = models.ForeignKey(
        Assistant,
        on_delete=models.CASCADE,
        related_name='reported_content'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Reported Content"
        verbose_name_plural = "Reported Content"

    def __str__(self):
        return f"Report: {self.get_report_type_display()} - {self.status}"

class ModeratorAction(models.Model):
    """Track actions taken by moderators."""
    ACTION_TYPES = [
        ('approve', 'Approve Content'),
        ('reject', 'Reject Content'),
        ('remove', 'Remove Content'),
        ('warn', 'Warn User'),
        ('ban', 'Ban User'),
        ('unban', 'Unban User'),
        ('adjust_reputation', 'Adjust Reputation')
    ]

    moderator = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='moderator_actions'
    )
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    target_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='moderation_received'
    )
    reported_content = models.ForeignKey(
        ReportedContent,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='moderator_actions'
    )
    notes = models.TextField(blank=True)
    reputation_adjustment = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_action_type_display()} by {self.moderator}"

class UserBan(models.Model):
    """Track temporary or permanent bans for users."""
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='bans'
    )
    assistant = models.ForeignKey(
        Assistant,
        on_delete=models.CASCADE,
        related_name='banned_users'
    )
    reason = models.TextField()
    is_permanent = models.BooleanField(default=False)
    start_date = models.DateTimeField(auto_now_add=True)
    end_date = models.DateTimeField(null=True, blank=True)
    banned_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='bans_issued'
    )

    class Meta:
        ordering = ['-start_date']

    def __str__(self):
        ban_type = "Permanent" if self.is_permanent else "Temporary"
        return f"{ban_type} ban for {self.user.username}"

    @property
    def is_active(self):
        if self.is_permanent:
            return True
        return self.end_date > timezone.now() if self.end_date else True


class Comment(models.Model):
    """Comments on various content types (contexts, questions, etc.)"""
    # Generic foreign key to allow commenting on different types of content
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='comments'
    )
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Comment"
        verbose_name_plural = "Comments"
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f"Comment by {self.user.username} on {self.content_object}"

# Second set of community assistant models removed to fix duplicate model definitions
