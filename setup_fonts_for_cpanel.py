#!/usr/bin/env python3
"""
Font Setup Script for cPanel Linux Deployment
This script sets up fonts for your Django application on cPanel Linux servers.
"""

import os
import sys
import shutil
import urllib.request
import zipfile
from pathlib import Path

def create_font_directories():
    """Create necessary font directories."""
    directories = [
        'static/fonts',
        'static/fonts/system',
        'static/fonts/custom',
        'fonts',  # User home fonts directory
        'public_html/fonts'  # Web accessible fonts
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")

def download_free_fonts():
    """Download free fonts that work well on Linux servers."""
    fonts_to_download = [
        {
            'name': 'Liberation Sans',
            'url': 'https://github.com/liberationfonts/liberation-fonts/releases/download/2.1.5/liberation-fonts-ttf-2.1.5.tar.gz',
            'type': 'tar.gz'
        },
        {
            'name': 'DejaVu Sans',
            'url': 'https://github.com/dejavu-fonts/dejavu-fonts/releases/download/version_2_37/dejavu-fonts-ttf-2.37.tar.bz2',
            'type': 'tar.bz2'
        }
    ]
    
    for font in fonts_to_download:
        try:
            print(f"Downloading {font['name']}...")
            filename = f"temp_{font['name'].replace(' ', '_').lower()}.{font['type']}"
            urllib.request.urlretrieve(font['url'], filename)
            
            # Extract and copy fonts
            if font['type'] == 'tar.gz':
                import tarfile
                with tarfile.open(filename, 'r:gz') as tar:
                    tar.extractall('temp_fonts')
            elif font['type'] == 'tar.bz2':
                import tarfile
                with tarfile.open(filename, 'r:bz2') as tar:
                    tar.extractall('temp_fonts')
            
            # Copy TTF files to our fonts directory
            for root, dirs, files in os.walk('temp_fonts'):
                for file in files:
                    if file.endswith('.ttf'):
                        src = os.path.join(root, file)
                        dst = os.path.join('static/fonts/system', file)
                        shutil.copy2(src, dst)
                        print(f"Copied font: {file}")
            
            # Cleanup
            os.remove(filename)
            shutil.rmtree('temp_fonts', ignore_errors=True)
            
        except Exception as e:
            print(f"Error downloading {font['name']}: {e}")

def create_font_css():
    """Create CSS file for web fonts."""
    css_content = """
/* Web Fonts for cPanel Deployment */

@font-face {
    font-family: 'Liberation Sans';
    src: url('../fonts/system/LiberationSans-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Liberation Sans';
    src: url('../fonts/system/LiberationSans-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DejaVu Sans';
    src: url('../fonts/system/DejaVuSans.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DejaVu Sans';
    src: url('../fonts/system/DejaVuSans-Bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* Font fallback stack for better compatibility */
.font-system {
    font-family: 'Liberation Sans', 'DejaVu Sans', -apple-system, BlinkMacSystemFont, 
                 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.font-monospace {
    font-family: 'Liberation Mono', 'DejaVu Sans Mono', 'Courier New', monospace;
}
"""
    
    with open('static/css/fonts.css', 'w') as f:
        f.write(css_content)
    print("Created fonts.css")

def create_font_installation_script():
    """Create a script to install fonts system-wide on cPanel."""
    script_content = """#!/bin/bash
# Font Installation Script for cPanel Linux
# Run this script to install fonts system-wide

echo "Installing fonts for cPanel Linux..."

# Create user fonts directory
mkdir -p ~/.fonts
mkdir -p ~/fonts
mkdir -p ~/public_html/fonts

# Copy fonts to user directory
if [ -d "static/fonts/system" ]; then
    cp static/fonts/system/*.ttf ~/.fonts/ 2>/dev/null || true
    cp static/fonts/system/*.ttf ~/fonts/ 2>/dev/null || true
    cp static/fonts/system/*.ttf ~/public_html/fonts/ 2>/dev/null || true
    echo "Fonts copied to user directories"
fi

# Update font cache (if fc-cache is available)
if command -v fc-cache &> /dev/null; then
    fc-cache -f -v ~/.fonts
    echo "Font cache updated"
else
    echo "fc-cache not available, fonts may need manual refresh"
fi

# Set proper permissions
chmod 644 ~/.fonts/*.ttf 2>/dev/null || true
chmod 644 ~/fonts/*.ttf 2>/dev/null || true
chmod 644 ~/public_html/fonts/*.ttf 2>/dev/null || true

echo "Font installation complete!"
echo "Available fonts:"
if command -v fc-list &> /dev/null; then
    fc-list | grep -E "(Liberation|DejaVu)" | head -10
else
    ls -la ~/.fonts/
fi
"""
    
    with open('install_fonts.sh', 'w') as f:
        f.write(script_content)
    os.chmod('install_fonts.sh', 0o755)
    print("Created install_fonts.sh script")

def update_qr_generator_for_fonts():
    """Update QR generator to use the installed fonts."""
    qr_generator_path = 'utils/qr_generator.py'
    
    if not os.path.exists(qr_generator_path):
        print("QR generator not found, skipping update")
        return
    
    # Add font paths for our installed fonts
    font_paths_addition = '''
    # Project-specific font paths for cPanel
    project_font_dirs = [
        os.path.join(BASE_DIR, 'static', 'fonts', 'system'),
        os.path.join(BASE_DIR, 'static', 'fonts', 'custom'),
        os.path.join(BASE_DIR, 'fonts'),
        os.path.expanduser("~/fonts"),
        os.path.expanduser("~/.fonts"),
        os.path.expanduser("~/public_html/fonts")
    ]
    font_dirs.extend(project_font_dirs)
'''
    
    try:
        with open(qr_generator_path, 'r') as f:
            content = f.read()
        
        # Find where to insert the font paths
        if 'font_dirs.extend(cpanel_font_dirs)' in content:
            content = content.replace(
                'font_dirs.extend(cpanel_font_dirs)',
                'font_dirs.extend(cpanel_font_dirs)' + font_paths_addition
            )
            
            with open(qr_generator_path, 'w') as f:
                f.write(content)
            print("Updated QR generator with project font paths")
        
    except Exception as e:
        print(f"Error updating QR generator: {e}")

def main():
    """Main function to set up fonts for cPanel."""
    print("=== Font Setup for cPanel Linux ===")
    
    # Create directories
    create_font_directories()
    
    # Download free fonts
    print("\nDownloading free fonts...")
    download_free_fonts()
    
    # Create CSS
    print("\nCreating font CSS...")
    create_font_css()
    
    # Create installation script
    print("\nCreating installation script...")
    create_font_installation_script()
    
    # Update QR generator
    print("\nUpdating QR generator...")
    update_qr_generator_for_fonts()
    
    print("\n=== Setup Complete ===")
    print("Next steps:")
    print("1. Run: chmod +x install_fonts.sh && ./install_fonts.sh")
    print("2. Add fonts.css to your base template")
    print("3. Run: python manage.py collectstatic")
    print("4. Test font loading with your QR generator")

if __name__ == "__main__":
    main()
