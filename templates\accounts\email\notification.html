{% extends 'accounts/email/base_email.html' %}

{% block email_title %}{{ notification.title|default:"Notification" }} - 24seven{% endblock %}

{% block email_icon %}{{ notification.icon|default:"🔔" }}{% endblock %}

{% block email_heading %}{{ notification.title|default:"Notification" }}{% endblock %}

{% block email_subtitle %}{{ notification.subtitle|default:"Stay updated with your team" }}{% endblock %}

{% block email_content %}
    <p style="font-size: 18px; margin-bottom: 24px;">
        <strong>Hello {{ user.get_full_name|default:user.username }},</strong>
    </p>

    {% if notification.message %}
        <p style="font-size: 16px; margin-bottom: 24px;">
            {{ notification.message }}
        </p>
    {% endif %}

    {% if notification.type == 'success' %}
        <div class="success-box">
            <h4 style="margin: 0 0 12px 0; color: #2f855a; display: flex; align-items: center;">
                ✅ {{ notification.title|default:"Success" }}
            </h4>
            <p style="margin: 0; font-size: 16px;">
                {{ notification.content|default:"Your action was completed successfully." }}
            </p>
        </div>
    {% elif notification.type == 'warning' %}
        <div class="warning-box">
            <h4 style="margin: 0 0 12px 0; color: #c05621; display: flex; align-items: center;">
                ⚠️ {{ notification.title|default:"Warning" }}
            </h4>
            <p style="margin: 0; font-size: 16px;">
                {{ notification.content|default:"Please review this important information." }}
            </p>
        </div>
    {% elif notification.type == 'error' %}
        <div class="error-box">
            <h4 style="margin: 0 0 12px 0; color: #c53030; display: flex; align-items: center;">
                ❌ {{ notification.title|default:"Error" }}
            </h4>
            <p style="margin: 0; font-size: 16px;">
                {{ notification.content|default:"An error occurred that requires your attention." }}
            </p>
        </div>
    {% else %}
        <div class="info-box">
            <h4 style="margin: 0 0 12px 0; color: #2b6cb0; display: flex; align-items: center;">
                ℹ️ {{ notification.title|default:"Information" }}
            </h4>
            <p style="margin: 0; font-size: 16px;">
                {{ notification.content|default:"Here's an update for you." }}
            </p>
        </div>
    {% endif %}

    {% if notification.details %}
        <div style="background-color: #f8fafc; padding: 20px; border-radius: 12px; margin: 24px 0;">
            <h4 style="margin: 0 0 12px 0; color: #4a5568;">📋 Details</h4>
            {% if notification.details.items %}
                <ul style="margin: 0; padding-left: 20px;">
                    {% for item in notification.details.items %}
                        <li style="margin-bottom: 8px; color: #4a5568;">{{ item }}</li>
                    {% endfor %}
                </ul>
            {% else %}
                <p style="margin: 0; font-size: 14px; color: #718096;">
                    {{ notification.details }}
                </p>
            {% endif %}
        </div>
    {% endif %}

    {% if notification.action_url %}
        <div class="button-container">
            <a href="{{ notification.action_url }}" class="button" style="font-size: 16px; padding: 16px 32px;">
                {{ notification.action_text|default:"Take Action" }}
            </a>
        </div>
    {% endif %}

    {% if notification.timestamp %}
        <div style="text-align: center; margin-top: 24px; padding: 16px; background-color: #f1f5f9; border-radius: 8px;">
            <p style="margin: 0; font-size: 13px; color: #64748b;">
                <strong>Notification sent:</strong> {{ notification.timestamp|date:"F j, Y, g:i a" }}
            </p>
        </div>
    {% endif %}

    {% if notification.related_items %}
        <div class="security-info">
            <h4>🔗 Related Items</h4>
            <ul>
                {% for item in notification.related_items %}
                    <li>
                        {% if item.url %}
                            <a href="{{ item.url }}" style="color: #3182ce; text-decoration: none; font-weight: 600;">{{ item.title }}</a>
                        {% else %}
                            {{ item.title }}
                        {% endif %}
                        {% if item.description %}
                            <br><small style="color: #718096;">{{ item.description }}</small>
                        {% endif %}
                    </li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}

    <div style="text-align: center; margin-top: 32px; padding: 20px; background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-radius: 12px;">
        <p style="margin: 0; font-size: 14px; color: #718096;">
            Need help or have questions?<br>
            <a href="mailto:{{ site_config.support_email|default:'<EMAIL>' }}" style="color: #3182ce; text-decoration: none; font-weight: 600;">{{ site_config.support_email|default:'<EMAIL>' }}</a>
            or visit our <a href="{{ site_config.support_url|default:site_config.contact_url|default:'/support/' }}" style="color: #3182ce; text-decoration: none; font-weight: 600;">support center</a>
        </p>
    </div>

    {% if notification.unsubscribe_info %}
        <div style="text-align: center; margin-top: 24px; padding: 16px; background-color: #f1f5f9; border-radius: 8px;">
            <p style="margin: 0; font-size: 13px; color: #64748b;">
                Don't want to receive these notifications?<br>
                <a href="{{ notification.unsubscribe_url|default:'/accounts/settings/notifications/' }}"
                   style="color: #3182ce; text-decoration: none; font-weight: 600;">
                    Update your notification preferences
                </a>
            </p>
        </div>
    {% endif %}
{% endblock %}
