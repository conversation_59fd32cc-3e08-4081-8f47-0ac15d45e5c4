/**
 * Unified Header CSS
 * Styles for the consolidated header component
 */

/* Main Header Styles */
.unified-header {
    background: rgba(248, 249, 250, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #dee2e6;
    z-index: 1000;
    transition: all 0.3s ease;
}

.unified-header .navbar-brand-section {
    flex: 0 0 auto;
}

.unified-header .navbar-brand {
    color: #333333 !important;
    font-weight: 600;
    text-decoration: none;
    transition: color 0.2s ease;
    display: flex !important;
    align-items: center !important;
}

.unified-header .navbar-brand:hover {
    color: #E31B23 !important;
}

/* NUP Header Logo */
.unified-header .nup-header-logo {
    height: 40px !important;
    width: auto !important;
    object-fit: contain !important;
    margin-right: 0.5rem !important;
    transition: all 0.2s ease !important;
}

.unified-header .nup-header-fallback {
    display: none !important;
}

.unified-header .nup-header-logo:hover {
    transform: scale(1.05) !important;
}

/* Company Branding */
.unified-header .company-brand {
    border-left: 1px solid #dee2e6;
    padding-left: 1rem;
    margin-left: 0.5rem;
}

.unified-header .company-name {
    font-size: 0.875rem;
    color: #6c757d !important;
    font-weight: 500;
}

.unified-header .company-header-logo {
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: border-color 0.2s ease;
}

.unified-header .company-header-logo:hover {
    border-color: rgba(255, 255, 255, 0.3);
}

/* Navigation Links */
.unified-header .nav-link {
    color: #ffffff !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.unified-header .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
}

.unified-header .nav-link.active {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0d6efd !important;
}

/* Icon Containers */
.unified-header .nav-icon-container {
    display: inline-flex;
    align-items: center;
    margin-right: 0.5rem;
}

/* Dropdown Menus */
.unified-header .dropdown-menu {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    margin-top: 0.5rem;
}

.unified-header .dropdown-item {
    color: #ffffff;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
}

.unified-header .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.unified-header .dropdown-item.active {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0d6efd;
}

.unified-header .dropdown-header {
    color: #adb5bd;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
}

.unified-header .dropdown-divider {
    border-color: #333;
}

/* Company Actions */
.unified-header .navbar-company-actions {
    border-left: 1px solid #444;
    padding-left: 1rem;
    margin-left: 1rem;
}

.unified-header .navbar-company-actions .btn {
    transition: all 0.2s ease;
}

.unified-header .navbar-company-actions .like-button {
    font-size: 1.1rem;
}

.unified-header .navbar-company-actions .like-button:hover {
    transform: scale(1.1);
}

.unified-header .navbar-company-actions .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.unified-header .navbar-company-actions .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
}

/* Rating Display */
.unified-header #rating-display-{{ company.id|default:'0' }} {
    font-size: 0.875rem;
}

.unified-header .btn-link {
    color: #adb5bd !important;
    font-size: 0.75rem;
    text-decoration: none !important;
}

.unified-header .btn-link:hover {
    color: #ffffff !important;
}

/* Mobile Styles */
@media (max-width: 991.98px) {
    /* Fix main navbar container spacing */
    .unified-header .container {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
    }

    /* Optimize navbar brand section for mobile */
    .unified-header .navbar-brand-section {
        flex: 1 1 auto;
        margin-right: 0.5rem;
    }

    .unified-header .navbar-brand {
        font-size: 1.1rem !important;
        margin-right: 0.5rem !important;
        padding: 0.25rem 0 !important;
    }

    .unified-header .company-brand {
        border-left: none;
        padding-left: 0;
        margin-left: 0;
        margin-top: 0;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }

    .unified-header .company-name {
        font-size: 0.8rem !important;
        margin-left: 0.25rem;
    }

    /* Fix navbar toggler positioning and spacing */
    .unified-header .navbar-toggler {
        margin-left: auto;
        margin-right: 0;
        padding: 0.4rem 0.6rem !important;
        border-radius: 0.375rem !important;
    }

    /* Optimize collapsed navigation content */
    .unified-header .navbar-collapse {
        margin-top: 0.75rem !important;
        padding-top: 0.75rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .unified-header .navbar-nav {
        margin-top: 0 !important;
        margin-bottom: 0.5rem;
    }

    .unified-header .navbar-nav .nav-item {
        margin-bottom: 0.25rem;
    }

    .unified-header .nav-link {
        padding: 0.6rem 0.75rem !important;
        margin-bottom: 0.1rem;
        border-radius: 0.375rem !important;
        font-size: 0.95rem !important;
    }

    /* Company actions section mobile optimization */
    .unified-header .navbar-company-actions {
        border-left: none;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding: 0.75rem 0 0 0 !important;
        margin: 0.75rem 0 0 0 !important;
        width: 100%;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    /* User dropdown optimization */
    .unified-header .navbar-nav:last-child {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .unified-header .dropdown-menu {
        background-color: rgba(26, 26, 26, 0.95);
        backdrop-filter: blur(10px);
        margin-top: 0.25rem !important;
        border-radius: 0.5rem !important;
    }

    .unified-header .dropdown-item {
        padding: 0.6rem 0.75rem !important;
        font-size: 0.9rem !important;
    }
}

/* Navbar Toggler */
.unified-header .navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
}

.unified-header .navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.unified-header .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Theme Compatibility */
[data-theme="light"] .unified-header {
    background: rgba(248, 249, 250, 0.95);
    border-bottom-color: #dee2e6;
}

[data-theme="light"] .unified-header .navbar-brand,
[data-theme="light"] .unified-header .nav-link {
    color: #212529 !important;
}

[data-theme="light"] .unified-header .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .unified-header .company-brand {
    border-left-color: #dee2e6;
}

[data-theme="light"] .unified-header .company-name {
    color: #6c757d !important;
}

[data-theme="light"] .unified-header .dropdown-menu {
    background-color: #ffffff;
    border-color: #dee2e6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

[data-theme="light"] .unified-header .dropdown-item {
    color: #212529;
}

[data-theme="light"] .unified-header .dropdown-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #212529;
}

[data-theme="light"] .unified-header .dropdown-divider {
    border-color: #dee2e6;
}

[data-theme="light"] .unified-header .navbar-company-actions {
    border-left-color: #dee2e6;
}

[data-theme="light"] .unified-header .navbar-toggler {
    border-color: rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .unified-header .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Animation and Transitions */
.unified-header .navbar-collapse {
    transition: all 0.3s ease;
}

.unified-header .navbar-nav .nav-item {
    transition: all 0.2s ease;
}

/* Accessibility */
.unified-header .nav-link:focus,
.unified-header .dropdown-item:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .unified-header {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .unified-header {
        background: #000000;
        border-bottom-color: #ffffff;
    }

    .unified-header .nav-link,
    .unified-header .navbar-brand {
        color: #ffffff !important;
    }

    .unified-header .dropdown-menu {
        background-color: #000000;
        border-color: #ffffff;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .unified-header,
    .unified-header .nav-link,
    .unified-header .dropdown-item,
    .unified-header .btn {
        transition: none;
    }
}
