# Assistant Data Isolation Fix

## Problem Description

The issue was that changing settings (like welcome messages) in one assistant would affect all other assistants. This was caused by a classic Python anti-pattern: using mutable objects (`dict` and `list`) as default values in Django model fields.

## Root Cause

In Django models, when you define a J<PERSON><PERSON>ield with a mutable default like this:

```python
# PROBLEMATIC CODE
website_data = models.JSONField(default=dict, blank=True)
saved_suggestions = models.JSONField(default=list, blank=True)
```

All instances of the model share the same dictionary/list object in memory. When you modify the data in one instance, it affects all other instances because they're all pointing to the same object.

## Files Affected

### 1. `assistants/models.py`
- `Assistant.website_data` - Used `default=dict`
- `Assistant.saved_suggestions` - Used `default=list`
- `CommunityContext.keywords` - Used `default=list`
- `NavigationItem.gallery` - Used `default=list`

### 2. `directory/models.py`
- `CompanyListing.social_links` - Used `default=dict`
- `CompanyListing.tags` - Used `default=list`
- `AssistantListing.categories` - Used `default=list`
- `AssistantListing.tags` - Used `default=list`
- `AssistantListing.capabilities` - Used `default=list`

## Solution

### 1. Added Helper Functions

Added callable functions that return new instances of mutable objects:

```python
def default_dict():
    """Return an empty dict for JSONField default."""
    return {}

def default_list():
    """Return an empty list for JSONField default."""
    return []
```

### 2. Updated Field Definitions

Changed all problematic fields to use the callable functions:

```python
# FIXED CODE
website_data = models.JSONField(default=default_dict, blank=True)
saved_suggestions = models.JSONField(default=default_list, blank=True)
```

### 3. Created Data Migrations

Created migrations to:
1. Fix existing data that might have been affected
2. Update the field definitions in the database
3. Ensure data integrity going forward

## Files Created/Modified

### New Files:
- `assistants/migrations/0002_fix_mutable_defaults.py`
- `directory/migrations/0002_fix_mutable_defaults.py`
- `fix_assistant_isolation.py` (test script)
- `ASSISTANT_ISOLATION_FIX.md` (this document)

### Modified Files:
- `assistants/models.py` - Fixed mutable defaults
- `directory/models.py` - Fixed mutable defaults

## How to Apply the Fix

1. **Run the fix script:**
   ```bash
   python fix_assistant_isolation.py
   ```

2. **Or manually run migrations:**
   ```bash
   python manage.py migrate assistants 0002_fix_mutable_defaults
   python manage.py migrate directory 0002_fix_mutable_defaults
   ```

## Testing

The fix includes comprehensive tests that verify:

1. **Greeting Message Isolation**: Changing one assistant's greeting doesn't affect others
2. **Website Data Isolation**: Modifying website_data in one assistant doesn't affect others
3. **Saved Suggestions Isolation**: Adding suggestions to one assistant doesn't affect others

## Prevention

To prevent this issue in the future:

1. **Never use mutable objects as default values** in Django model fields
2. **Always use callable functions** for mutable defaults:
   ```python
   # GOOD
   data = models.JSONField(default=dict)  # Returns a new dict each time
   items = models.JSONField(default=list)  # Returns a new list each time
   
   # BETTER (more explicit)
   data = models.JSONField(default=lambda: {})
   items = models.JSONField(default=lambda: [])
   
   # BEST (reusable helper functions)
   data = models.JSONField(default=default_dict)
   items = models.JSONField(default=default_list)
   ```

3. **Use linting tools** that can catch this pattern
4. **Code review** should always check for mutable defaults

## Impact

After applying this fix:

- ✅ Each assistant will have its own isolated data
- ✅ Changes to one assistant won't affect others
- ✅ Existing data integrity is preserved
- ✅ Future instances will be properly isolated
- ✅ No breaking changes to the API or user interface

## Verification

You can verify the fix is working by:

1. Creating or editing an assistant's welcome message
2. Checking that other assistants' welcome messages remain unchanged
3. Running the test script: `python fix_assistant_isolation.py`

The issue should now be completely resolved, and each assistant will maintain its own independent settings and data.
