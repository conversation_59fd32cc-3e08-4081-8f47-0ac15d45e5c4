#!/usr/bin/env python
"""
Simple test to identify the exact impersonation logout issue.
"""

import os
import django

# Setup Django environment FIRST
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from accounts.models import Company
from assistants.models import Assistant

def test_impersonation_logout():
    """Test the exact logout scenario with existing data."""
    print("🎯 Testing Impersonation Logout with Existing Data")
    print("=" * 60)
    
    # Use existing data instead of creating new
    try:
        # Find existing superuser
        superuser = User.objects.filter(is_superuser=True).first()
        if not superuser:
            print("❌ No superuser found. Please create one first.")
            return False
        print(f"✅ Using superuser: {superuser.username}")
        
        # Find existing company with owner
        company = Company.objects.filter(owner__isnull=False).first()
        if not company:
            print("❌ No company with owner found.")
            return False
        print(f"✅ Using company: {company.name} (Owner: {company.owner.username})")
        
        # Find existing assistant
        assistant = Assistant.objects.filter(company=company).first()
        if not assistant:
            print("❌ No assistant found for this company.")
            return False
        print(f"✅ Using assistant: {assistant.name}")
        
    except Exception as e:
        print(f"❌ Error finding existing data: {e}")
        return False
    
    # Create test client
    client = Client()
    
    print(f"\n📋 Step 1: Login as superuser")
    # Try to login (we don't know the password, so we'll force login)
    client.force_login(superuser)
    print(f"✅ Logged in as superuser: {superuser.username}")
    
    print(f"\n📋 Step 2: Start impersonation")
    # Set impersonation session
    session = client.session
    session['_impersonate'] = company.owner.id
    session.save()
    print(f"✅ Impersonation started: _impersonate = {company.owner.id}")
    print(f"Session keys: {list(session.keys())}")
    
    print(f"\n📋 Step 3: Access assistant list")
    assistant_list_url = f'/assistants/company/{company.id}/assistants/'
    print(f"Accessing: {assistant_list_url}")
    
    response = client.get(assistant_list_url, follow=True)
    print(f"Response status: {response.status_code}")
    print(f"Session after list: {list(client.session.keys())}")
    print(f"Impersonation preserved: {'_impersonate' in client.session}")
    
    if '_impersonate' not in client.session:
        print("❌ LOGOUT DETECTED: Lost impersonation at assistant list")
        return False
    
    print(f"\n📋 Step 4: Click settings button (THE CRITICAL TEST)")
    settings_url = f'/assistants/company/{company.id}/assistants/{assistant.id}/update/'
    print(f"Clicking settings: {settings_url}")
    
    # Store session before critical request
    session_before = dict(client.session)
    print(f"Session before settings: {list(session_before.keys())}")
    
    response = client.get(settings_url, follow=True)
    print(f"Settings response: {response.status_code}")
    
    # Check session after critical request
    session_after = dict(client.session)
    print(f"Session after settings: {list(session_after.keys())}")
    print(f"Impersonation preserved: {'_impersonate' in client.session}")
    
    if '_impersonate' not in client.session:
        print("🎯 FOUND THE ISSUE: Settings button causes logout!")
        print("\n🔍 Analyzing the problem...")
        
        # Check redirects
        if response.redirect_chain:
            print(f"Redirects occurred: {response.redirect_chain}")
            for redirect_url, status_code in response.redirect_chain:
                print(f"  -> {redirect_url} ({status_code})")
        
        # Check final URL
        final_url = response.request.get('PATH_INFO', 'Unknown')
        print(f"Final URL: {final_url}")
        
        # Check if it redirected to login
        if '/login/' in final_url or '/accounts/login/' in final_url:
            print("🔍 ISSUE: Redirected to login page - permission/authentication issue")
        
        return False
    
    print(f"\n✅ Settings button works - impersonation preserved!")
    return True

def implement_session_fix():
    """Implement a robust session preservation fix."""
    print("\n🔧 Implementing Robust Session Fix")
    print("=" * 40)
    
    # Create a comprehensive session preservation decorator
    decorator_code = '''
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def preserve_impersonation_robust(view_func):
    """
    Robust decorator to preserve impersonation session across view calls.
    This addresses the specific logout issue when clicking assistant buttons.
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Store impersonation state before view execution
        impersonate_id = request.session.get('_impersonate')
        session_key = request.session.session_key
        
        logger.debug(f"Before view: impersonate_id={impersonate_id}, session_key={session_key}")
        
        # Execute the view
        response = view_func(request, *args, **kwargs)
        
        # Check if impersonation was lost and restore it
        current_impersonate_id = request.session.get('_impersonate')
        
        if impersonate_id and not current_impersonate_id:
            logger.warning(f"Impersonation lost during view execution, restoring: {impersonate_id}")
            request.session['_impersonate'] = impersonate_id
            request.session.save()
        
        logger.debug(f"After view: impersonate_id={request.session.get('_impersonate')}")
        
        return response
    return wrapper
'''
    
    print("✅ Session preservation decorator created")
    
    # Create middleware fix
    middleware_code = '''
class RobustImpersonationMiddleware:
    """
    Middleware to robustly preserve impersonation sessions.
    This specifically addresses logout issues in assistant views.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Store impersonation state before processing
        impersonate_id = request.session.get('_impersonate')
        
        # Process the request
        response = self.get_response(request)
        
        # Restore impersonation if lost during processing
        if impersonate_id and not request.session.get('_impersonate'):
            request.session['_impersonate'] = impersonate_id
            request.session.save()
        
        return response
'''
    
    print("✅ Robust middleware created")
    
    return decorator_code, middleware_code

def run_simple_test():
    """Run the simple logout test."""
    print("🔍 SIMPLE IMPERSONATION LOGOUT TEST")
    print("=" * 50)
    
    # Test the logout scenario
    test_passed = test_impersonation_logout()
    
    if not test_passed:
        print("\n🔧 IMPLEMENTING ROBUST FIX...")
        decorator_code, middleware_code = implement_session_fix()
        
        print("\n" + "="*60)
        print("🔧 APPLY THESE FIXES:")
        print("="*60)
        print("\n1. ADD THIS DECORATOR TO assistants/views.py:")
        print("-" * 50)
        print(decorator_code)
        print("\n2. ADD THIS MIDDLEWARE:")
        print("-" * 30)
        print(middleware_code)
        print("\n3. APPLY DECORATOR TO VIEWS:")
        print("-" * 35)
        print("@preserve_impersonation_robust")
        print("def assistant_update(request, company_id, assistant_id):")
        print("    # existing view code...")
        
        return False
    else:
        print("\n🎉 No logout issue detected!")
        return True

if __name__ == '__main__':
    run_simple_test()
