{% load rating_tags %}
<div class="list-group-item mb-3 border rounded shadow-sm favorite-item" data-company-id="{{ item.company.id }}">
    <div class="row g-3">
        {# Link wrapper covers first 4 columns (col-md-10) and contains an inner row - Match company_list.html structure #}
        <a href="{% url 'accounts:public_company_detail' slug=item.company.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none text-body">
            {# Column 1: Logo (col-md-2 within link row) #}
            <div class="col-md-2 d-flex justify-content-center align-items-start pt-1">
                 {% if item.company.info.logo %}
                    <img src="{{ item.company.info.logo.url }}" alt="{{ item.company.name }} logo" class="rounded" style="height: 64px; width: auto; max-width: 100%; object-fit: contain; display: block; margin: auto;">
                {% else %}
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 64px; width: 75px;">
                         <i class="bi bi-building text-muted fs-3"></i>
                    </div>
                {% endif %}
            </div>
            {# Column 2: Name, Industry, Tags/Categories (col-md-3 within link row) #}
            <div class="col-md-3">
                <h6 class="mb-1 fs-6 fw-semibold">
                    {{ item.company.name }}
                </h6>
                 {% if item.company.info.industry %}
                <p class="mb-1 text-muted small">
                    <i class="bi bi-building me-1"></i>{{ item.company.info.industry }}
                </p>
                {% endif %}
                {% if item.company.listing %}
                <div class="mb-2">
                    {% for category in item.company.listing.categories.all|slice:":3" %}
                        <span class="badge bg-secondary tag-badge">{{ category.name }}</span>
                    {% endfor %}
                    {% for tag in item.company.listing.tags|slice:":3" %}
                        <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {# Column 3: Description (col-md-4 within link row) #}
            <div class="col-md-4">
                <p class="text-muted mb-0 item-description">
                    {{ item.company.listing.description|default:item.company.info.description|default:"No description available."|truncatewords:50 }}
                </p>
            </div>
             {# Column 4: Contact Info (col-md-3 within link row) #}
            <div class="col-md-3">
                <ul class="list-unstyled small text-muted contact-info mb-0">
                    {% if item.company.info.address_line1 or item.company.info.city %}
                        <li>
                        <i class="bi bi-geo-alt me-1 pt-1"></i>
                        <span>
                            {% if item.company.info.address_line1 %}{{ item.company.info.address_line1 }}{% if item.company.info.address_line2 %}, {{ item.company.info.address_line2 }}{% endif %}{% endif %}
                            {% if item.company.info.city %}{% if item.company.info.address_line1 %}, {% endif %}{{ item.company.info.city }}{% endif %}
                            {% if item.company.info.postal_code %} {{ item.company.info.postal_code }}{% endif %}
                            {% if item.company.info.country %}, {{ item.company.info.country }}{% endif %}
                        </span>
                    </li>
                    {% endif %}
                    {% if item.company.info.contact_phone %}
                        <li><i class="bi bi-telephone me-1"></i>{{ item.company.info.contact_phone }}</li>
                    {% endif %}
                     {% if item.company.info.contact_email %}
                        <li><i class="bi bi-envelope me-1"></i>{{ item.company.info.contact_email }}</li>
                    {% endif %}
                    {% if item.company.listing.website|default:item.company.info.website %}
                        <li><i class="bi bi-link-45deg me-1"></i>{{ item.company.listing.website|default:item.company.info.website }}</li>
                    {% endif %}
                </ul>
            </div>
        </a> {# End of clickable area link #}

        {# Column 5: Rating & Actions (col-md-2) - Outside the link #}
        <div class="col-md-2 d-flex flex-column align-items-end justify-content-start">
            {# Unlike Button #}
            <div class="w-100 d-flex justify-content-end mb-1">
                <button
                    class="like-button btn btn-sm p-0 text-danger" {# Always red on this page #}
                    data-item-id="{{ item.company.id }}"
                    data-item-type="company"
                    title="Unlike"
                    style="background: none; border: none; cursor: pointer; z-index: 10;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16" style="pointer-events: none;">
                        <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314"/>
                    </svg>
                </button>
            </div>
            {# Rating Display - Use listing fields #}
            {% if item.company.listing %}
                {% with avg_rating=item.company.listing.avg_rating total_ratings=item.company.listing.total_ratings %}
                    <div class="rating-display-container mb-2 w-100 d-flex justify-content-end" id="rating-display-company-{{ item.company.id }}">
                        {% if avg_rating > 0 %}
                            {% render_stars avg_rating total_ratings %}
                        {% else %}
                            <div class="small text-muted fst-italic w-100 text-end" id="no-rating-placeholder-company-{{ item.company.id }}">(No ratings yet)</div>
                        {% endif %}
                    </div>
                {% endwith %}
            {% else %}
                <div class="mb-2 small text-muted fst-italic w-100 text-end">(No rating info)</div>
            {% endif %}
            {# Rate Button #}
            {% if user.is_authenticated and item.company.listing %} {# Only show if listing exists #}
            <div class="w-100 d-flex justify-content-end mb-1">
                <button type="button"
                        class="btn btn-outline-secondary btn-sm rate-company-btn"
                        data-bs-toggle="modal"
                        data-bs-target="#ratingModal"
                        data-company-id="{{ item.company.id }}"
                        data-company-name="{{ item.company.name|escapejs }}"
                        style="z-index: 5;">
                    <i class="bi bi-star me-1"></i> Rate
                </button>
            </div>
            <div class="w-100 d-flex justify-content-end">
                <span class="rating-update-message small text-success mt-1" id="rating-msg-company-{{ item.company.id }}" style="display: none;"></span>
            </div>
            {% endif %}
            {# Contact Info (Optional - Keep or remove based on preference for favorites page) #}
            {# <div class="mt-auto w-100"> ... contact info ... </div> #}
        </div> {# /col-md-2 #}
    </div> {# /row #}
</div> {# /list-group-item #}
