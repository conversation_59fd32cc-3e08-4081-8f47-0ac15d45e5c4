#!/usr/bin/env python
"""
Quick email test to verify the system is working.
"""

import os
import sys
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    
    from django.core.mail import send_mail
    from django.conf import settings
    
    print("🚀 Quick Email System Test")
    print("=" * 40)
    
    # Check configuration
    print("📧 Email Configuration:")
    print(f"  Host: {settings.EMAIL_HOST}")
    print(f"  Port: {settings.EMAIL_PORT}")
    print(f"  User: {settings.EMAIL_HOST_USER}")
    print(f"  From: {settings.DEFAULT_FROM_EMAIL}")
    print(f"  SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    
    # Check password
    if not settings.EMAIL_HOST_PASSWORD:
        print("\n❌ EMAIL_HOST_PASSWORD not set!")
        print("Please update your .env file with the actual password.")
        sys.exit(1)
    elif settings.EMAIL_HOST_PASSWORD == 'your-actual-email-password':
        print("\n❌ EMAIL_HOST_PASSWORD is still placeholder!")
        print("Please update your .env file with the real password.")
        sys.exit(1)
    else:
        print("  ✅ Password: Configured")
    
    # Get recipient
    recipient = input("\nEnter email address to test (or press <NAME_EMAIL>): ").strip()
    if not recipient:
        recipient = '<EMAIL>'
    
    print(f"\n📤 Sending test email to: {recipient}")
    
    # Send test email
    try:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        result = send_mail(
            subject=f'Django Email Test - {timestamp}',
            message=f'''
Django Email System Test

This test email was sent at {timestamp} from your Django application.

Configuration Details:
- From: {settings.DEFAULT_FROM_EMAIL}
- SMTP Server: {settings.EMAIL_HOST}:{settings.EMAIL_PORT}
- Security: SSL enabled
- User: {settings.EMAIL_HOST_USER}

If you receive this email, your Django email system is working correctly!

You can now use email features like:
- User registration emails
- Password reset emails  
- Team invitation emails
- System notifications

Best regards,
Your Django Application
24seven.site
''',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient],
            fail_silently=False
        )
        
        if result == 1:
            print("✅ Email sent successfully!")
            print(f"   📧 To: {recipient}")
            print(f"   📝 Subject: Django Email Test - {timestamp}")
            print(f"   🕒 Time: {timestamp}")
            print("\n🎉 Your email system is working!")
            print("\nNext steps:")
            print("1. Check the recipient's inbox for the test email")
            print("2. Also check spam/junk folder")
            print("3. Test user registration and password reset emails")
        else:
            print("❌ Email sending failed (returned 0)")
            
    except Exception as e:
        print(f"❌ Error sending email: {e}")
        
        # Provide specific guidance based on error
        error_str = str(e).lower()
        print("\n🔍 Troubleshooting:")
        
        if '535' in error_str or 'authentication' in error_str:
            print("  Authentication Error (535):")
            print("  - Verify <EMAIL> email account exists in cPanel")
            print("  - Check EMAIL_HOST_PASSWORD is correct in .env file")
            print("  - Ensure no typos in email address or password")
            
        elif 'timeout' in error_str or 'connection' in error_str:
            print("  Connection Error:")
            print("  - Check if mail.24seven.site resolves correctly")
            print("  - Verify firewall allows port 465 outbound")
            print("  - Confirm email server is running")
            
        elif 'ssl' in error_str or 'certificate' in error_str:
            print("  SSL Error:")
            print("  - Check SSL certificate for mail.24seven.site")
            print("  - Contact hosting provider about SSL setup")
            
        else:
            print("  General Error:")
            print("  - Check all email settings in .env file")
            print("  - Verify cPanel email service is active")
            print("  - Contact hosting provider for support")

except Exception as e:
    print(f"❌ Error setting up Django: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
