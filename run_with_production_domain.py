#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run Django with production domain behavior while in development mode.
This allows you to test email links with the production domain without changing DEBUG settings.
"""

import os
import sys
import subprocess

def main():
    """Run Django server with production domain behavior."""
    print("🚀 Starting Django with Production Domain Behavior")
    print("=" * 60)
    print()
    print("This will:")
    print("✅ Keep DEBUG=True for development features")
    print("✅ Use production domain (24seven.site) for email links")
    print("✅ Allow you to test email behavior without full production mode")
    print()

    try:
        # Set environment variable to force production domain
        env = os.environ.copy()
        env['FORCE_PRODUCTION_DOMAIN'] = 'True'

        print("🌐 Environment configured:")
        print(f"   FORCE_PRODUCTION_DOMAIN: True")
        print(f"   DEBUG: Will remain True")
        print()
        print("🔗 Email links will now use: http://24seven.site")
        print()
        print("Starting server...")
        print("=" * 60)

        # Run the Django development server with the environment variable
        subprocess.run([
            sys.executable, "manage.py", "runserver"
        ], env=env, check=True)

    except subprocess.CalledProcessError as e:
        print(f"❌ Error running server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user.")
        print("✅ Production domain behavior disabled.")
        sys.exit(0)

if __name__ == "__main__":
    main()
