#!/usr/bin/env python3
"""
Fix Company Logo Display Issues
This script checks and fixes company logo display problems.
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
sys.path.append(str(Path(__file__).parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company, CompanyInformation
from django.core.files.base import ContentFile
from django.conf import settings
import shutil

def check_company_logos():
    """Check all companies and their logo status"""
    print("=== Company Logo Status Check ===")
    
    companies = Company.objects.all()
    total_companies = companies.count()
    companies_with_logos = 0
    companies_with_broken_logos = 0
    companies_without_logos = 0
    
    print(f"Total companies: {total_companies}")
    print()
    
    for company in companies:
        print(f"Company: {company.name}")
        
        # Ensure CompanyInformation exists
        if not hasattr(company, 'info') or not company.info:
            info, created = CompanyInformation.objects.get_or_create(
                company=company,
                defaults={
                    'mission': '',
                    'description': '',
                    'website': '',
                    'contact_email': '',
                    'contact_phone': '',
                    'timezone': 'UTC',
                    'language': 'en',
                    'list_in_directory': True,
                }
            )
            if created:
                print(f"  ✓ Created CompanyInformation")
            company.refresh_from_db()
        
        # Check logo status
        if company.info.logo:
            logo_path = company.info.logo.path
            if os.path.exists(logo_path):
                print(f"  ✓ Logo exists: {company.info.logo.url}")
                companies_with_logos += 1
            else:
                print(f"  ✗ Logo file missing: {logo_path}")
                companies_with_broken_logos += 1
        else:
            print(f"  - No logo assigned")
            companies_without_logos += 1
        
        print()
    
    print("=== Summary ===")
    print(f"Companies with working logos: {companies_with_logos}")
    print(f"Companies with broken logos: {companies_with_broken_logos}")
    print(f"Companies without logos: {companies_without_logos}")
    print()
    
    return {
        'total': total_companies,
        'with_logos': companies_with_logos,
        'broken_logos': companies_with_broken_logos,
        'without_logos': companies_without_logos
    }

def ensure_media_directories():
    """Ensure all necessary media directories exist"""
    print("=== Ensuring Media Directories ===")
    
    directories = [
        'company_logos',
        'assistant_logos',
        'assistant_avatars',
        'site_defaults',
    ]
    
    for directory in directories:
        dir_path = os.path.join(settings.MEDIA_ROOT, directory)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"✓ Created directory: {dir_path}")
        else:
            print(f"✓ Directory exists: {dir_path}")
    
    print()

def check_default_images():
    """Check if default images exist"""
    print("=== Checking Default Images ===")
    
    default_images = [
        'static/img/default-company-logo.svg',
        'static/img/default-assistant-logo.png',
        'static/img/logos/company-1.svg',
    ]
    
    for image_path in default_images:
        if os.path.exists(image_path):
            print(f"✓ Default image exists: {image_path}")
        else:
            print(f"✗ Default image missing: {image_path}")
    
    print()

def test_image_urls():
    """Test image URL generation"""
    print("=== Testing Image URLs ===")
    
    companies = Company.objects.filter(info__logo__isnull=False)[:3]
    
    for company in companies:
        if company.info and company.info.logo:
            try:
                url = company.info.logo.url
                print(f"✓ {company.name}: {url}")
            except Exception as e:
                print(f"✗ {company.name}: Error getting URL - {e}")
    
    print()

def main():
    """Main function"""
    print("Company Logo Fix Script")
    print("=" * 50)
    
    # Check media directories
    ensure_media_directories()
    
    # Check default images
    check_default_images()
    
    # Check company logos
    status = check_company_logos()
    
    # Test image URLs
    test_image_urls()
    
    # Recommendations
    print("=== Recommendations ===")
    if status['broken_logos'] > 0:
        print(f"- Fix {status['broken_logos']} broken logo file paths")
    
    if status['without_logos'] > 0:
        print(f"- Consider adding logos for {status['without_logos']} companies")
    
    print("- Ensure fallback images are properly configured in templates")
    print("- Test image loading in browser developer tools")
    
    print("\nScript completed!")

if __name__ == '__main__':
    main()
