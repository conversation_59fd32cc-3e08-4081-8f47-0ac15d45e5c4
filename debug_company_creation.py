#!/usr/bin/env python
"""
Debug script to check what companies exist and their data.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company, CompanyInformation
from django.contrib.auth.models import User

def debug_companies():
    """Debug existing companies"""
    print("🔍 DEBUGGING COMPANY DATA")
    print("=" * 50)
    
    # Check recent companies
    companies = Company.objects.all().order_by('-created_at')[:10]
    print(f"📊 Found {companies.count()} total companies")
    
    if companies:
        print("\n📋 Recent companies:")
        for company in companies:
            print(f"  - {company.name} (ID: {company.id})")
            print(f"    Owner: {company.owner.username}")
            print(f"    Entity Type: {company.entity_type}")
            print(f"    Created: {company.created_at}")
            
            # Check if CompanyInformation exists
            try:
                info = CompanyInformation.objects.get(company=company)
                print(f"    ✅ Has CompanyInformation")
                print(f"    Mission: {info.mission[:50]}...")
                print(f"    Website: {info.website}")
                print(f"    Industry: {info.industry}")
            except CompanyInformation.DoesNotExist:
                print(f"    ❌ No CompanyInformation")
            print()
    
    # Check test users
    test_users = User.objects.filter(username__startswith='web_test_').order_by('-date_joined')[:5]
    print(f"👥 Found {test_users.count()} test users")
    
    if test_users:
        print("\n👤 Recent test users:")
        for user in test_users:
            user_companies = Company.objects.filter(owner=user)
            print(f"  - {user.username} (ID: {user.id})")
            print(f"    Companies: {user_companies.count()}")
            for company in user_companies:
                print(f"      * {company.name}")
            print()

if __name__ == "__main__":
    debug_companies()
