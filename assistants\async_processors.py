"""
Asynchronous processing for background tasks and non-blocking operations.
"""

import asyncio
import threading
import time
import uuid
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, List, Optional, Callable, Awaitable
from dataclasses import dataclass, field
from enum import Enum
import logging
from queue import Queue, PriorityQueue, Empty
import json

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority levels."""
    LOW = 3
    NORMAL = 2
    HIGH = 1
    CRITICAL = 0


@dataclass
class AsyncTask:
    """Represents an asynchronous task."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    func: Callable = None
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    callback: Optional[Callable] = None
    error_callback: Optional[Callable] = None
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    created_at: float = field(default_factory=time.time)
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[Exception] = None
    
    def __lt__(self, other):
        """For priority queue ordering."""
        return self.priority.value < other.priority.value


class AsyncTaskProcessor:
    """
    Processes tasks asynchronously with priority queuing and retry logic.
    """
    
    def __init__(self, max_workers: int = 5, max_queue_size: int = 1000):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        self.task_queue = PriorityQueue(maxsize=max_queue_size)
        self.active_tasks = {}
        self.completed_tasks = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.running = False
        self.workers = []
        self.stats = {
            'tasks_submitted': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'tasks_cancelled': 0
        }
    
    def start(self) -> None:
        """Start the task processor."""
        if self.running:
            return
        
        self.running = True
        
        # Start worker threads
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"AsyncWorker-{i}",
                daemon=True
            )
            worker.start()
            self.workers.append(worker)
        
        logger.info(f"AsyncTaskProcessor started with {self.max_workers} workers")
    
    def stop(self, timeout: float = 30.0) -> None:
        """Stop the task processor."""
        self.running = False
        
        # Cancel active tasks
        for task in self.active_tasks.values():
            task.status = TaskStatus.CANCELLED
            self.stats['tasks_cancelled'] += 1
        
        # Wait for workers to finish
        for worker in self.workers:
            worker.join(timeout=timeout / len(self.workers))
        
        self.executor.shutdown(wait=True)
        logger.info("AsyncTaskProcessor stopped")
    
    def submit_task(self, func: Callable, *args, priority: TaskPriority = TaskPriority.NORMAL,
                   callback: Callable = None, error_callback: Callable = None,
                   timeout: float = None, max_retries: int = 3, **kwargs) -> str:
        """Submit a task for asynchronous execution."""
        task = AsyncTask(
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            callback=callback,
            error_callback=error_callback,
            timeout=timeout,
            max_retries=max_retries
        )
        
        try:
            self.task_queue.put(task, block=False)
            self.stats['tasks_submitted'] += 1
            logger.debug(f"Task {task.id} submitted with priority {priority.name}")
            return task.id
        except:
            logger.error("Task queue is full, cannot submit task")
            raise RuntimeError("Task queue is full")
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """Get the status of a task."""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id].status
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id].status
        return None
    
    def get_task_result(self, task_id: str) -> Any:
        """Get the result of a completed task."""
        if task_id in self.completed_tasks:
            task = self.completed_tasks[task_id]
            if task.status == TaskStatus.COMPLETED:
                return task.result
            elif task.status == TaskStatus.FAILED:
                raise task.error
        return None
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or running task."""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                task.status = TaskStatus.CANCELLED
                self.stats['tasks_cancelled'] += 1
                return True
        return False
    
    def _worker_loop(self) -> None:
        """Main worker loop."""
        while self.running:
            try:
                # Get next task with timeout
                task = self.task_queue.get(timeout=1.0)
                
                if task.status == TaskStatus.CANCELLED:
                    continue
                
                # Execute task
                self._execute_task(task)
                
            except Empty:
                continue
            except Exception as e:
                logger.error(f"Worker error: {e}")
    
    def _execute_task(self, task: AsyncTask) -> None:
        """Execute a single task."""
        task.status = TaskStatus.RUNNING
        task.started_at = time.time()
        self.active_tasks[task.id] = task
        
        try:
            # Execute with timeout if specified
            if task.timeout:
                future = self.executor.submit(task.func, *task.args, **task.kwargs)
                task.result = future.result(timeout=task.timeout)
            else:
                task.result = task.func(*task.args, **task.kwargs)
            
            task.status = TaskStatus.COMPLETED
            task.completed_at = time.time()
            self.stats['tasks_completed'] += 1
            
            # Call success callback
            if task.callback:
                try:
                    task.callback(task.result)
                except Exception as e:
                    logger.warning(f"Callback error for task {task.id}: {e}")
            
        except Exception as e:
            task.error = e
            task.status = TaskStatus.FAILED
            task.completed_at = time.time()
            
            # Retry if possible
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                task.started_at = None
                
                # Re-queue with delay
                threading.Timer(2.0 ** task.retry_count, lambda: self.task_queue.put(task)).start()
                logger.info(f"Retrying task {task.id} (attempt {task.retry_count})")
                return
            
            self.stats['tasks_failed'] += 1
            
            # Call error callback
            if task.error_callback:
                try:
                    task.error_callback(e)
                except Exception as callback_error:
                    logger.warning(f"Error callback failed for task {task.id}: {callback_error}")
            
            logger.error(f"Task {task.id} failed: {e}")
        
        finally:
            # Move to completed tasks
            self.active_tasks.pop(task.id, None)
            self.completed_tasks[task.id] = task
            
            # Cleanup old completed tasks (keep last 1000)
            if len(self.completed_tasks) > 1000:
                oldest_tasks = sorted(
                    self.completed_tasks.items(),
                    key=lambda x: x[1].completed_at or 0
                )[:100]
                for task_id, _ in oldest_tasks:
                    self.completed_tasks.pop(task_id, None)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get processor statistics."""
        return {
            **self.stats,
            'active_tasks': len(self.active_tasks),
            'queued_tasks': self.task_queue.qsize(),
            'completed_tasks': len(self.completed_tasks),
            'workers': len(self.workers),
            'running': self.running
        }


class EventBus:
    """
    Event-driven architecture for real-time updates.
    """
    
    def __init__(self):
        self.subscribers = {}
        self.lock = threading.RLock()
    
    def subscribe(self, event_type: str, callback: Callable) -> str:
        """Subscribe to an event type."""
        with self.lock:
            if event_type not in self.subscribers:
                self.subscribers[event_type] = {}
            
            subscriber_id = str(uuid.uuid4())
            self.subscribers[event_type][subscriber_id] = callback
            return subscriber_id
    
    def unsubscribe(self, event_type: str, subscriber_id: str) -> bool:
        """Unsubscribe from an event type."""
        with self.lock:
            if event_type in self.subscribers:
                return self.subscribers[event_type].pop(subscriber_id, None) is not None
            return False
    
    def publish(self, event_type: str, data: Any) -> None:
        """Publish an event to all subscribers."""
        with self.lock:
            subscribers = self.subscribers.get(event_type, {}).copy()
        
        # Notify subscribers asynchronously
        for subscriber_id, callback in subscribers.items():
            try:
                threading.Thread(
                    target=callback,
                    args=(data,),
                    daemon=True
                ).start()
            except Exception as e:
                logger.warning(f"Event callback error for {event_type}: {e}")
    
    def get_subscriber_count(self, event_type: str) -> int:
        """Get number of subscribers for an event type."""
        with self.lock:
            return len(self.subscribers.get(event_type, {}))


class BackgroundTaskManager:
    """
    Manages long-running background tasks.
    """
    
    def __init__(self):
        self.tasks = {}
        self.lock = threading.Lock()
    
    def start_task(self, name: str, func: Callable, *args, **kwargs) -> str:
        """Start a background task."""
        with self.lock:
            if name in self.tasks:
                if self.tasks[name].is_alive():
                    logger.warning(f"Background task '{name}' is already running")
                    return name
            
            task_thread = threading.Thread(
                target=func,
                args=args,
                kwargs=kwargs,
                name=f"BgTask-{name}",
                daemon=True
            )
            task_thread.start()
            self.tasks[name] = task_thread
            
            logger.info(f"Started background task: {name}")
            return name
    
    def stop_task(self, name: str, timeout: float = 10.0) -> bool:
        """Stop a background task."""
        with self.lock:
            if name not in self.tasks:
                return False
            
            task = self.tasks[name]
            if task.is_alive():
                # Note: Python threads cannot be forcefully stopped
                # The task function should check for a stop condition
                task.join(timeout=timeout)
                
                if task.is_alive():
                    logger.warning(f"Background task '{name}' did not stop within timeout")
                    return False
                else:
                    logger.info(f"Stopped background task: {name}")
                    return True
            
            return True
    
    def is_running(self, name: str) -> bool:
        """Check if a background task is running."""
        with self.lock:
            return name in self.tasks and self.tasks[name].is_alive()
    
    def get_running_tasks(self) -> List[str]:
        """Get list of running background tasks."""
        with self.lock:
            return [name for name, task in self.tasks.items() if task.is_alive()]


# Global instances
task_processor = AsyncTaskProcessor()
event_bus = EventBus()
background_manager = BackgroundTaskManager()


# Convenience functions
def submit_async_task(func: Callable, *args, priority: TaskPriority = TaskPriority.NORMAL,
                     callback: Callable = None, **kwargs) -> str:
    """Submit an asynchronous task."""
    return task_processor.submit_task(func, *args, priority=priority, callback=callback, **kwargs)


def submit_llm_task(assistant, user_input: str, callback: Callable = None) -> str:
    """Submit an LLM generation task."""
    from .llm_utils_optimized import generate_assistant_response_optimized
    
    def llm_task():
        return generate_assistant_response_optimized(
            assistant=assistant,
            user_input=user_input,
            user=None,
            use_cache=True
        )
    
    return submit_async_task(
        llm_task,
        priority=TaskPriority.HIGH,
        callback=callback
    )


def publish_event(event_type: str, data: Any) -> None:
    """Publish an event."""
    event_bus.publish(event_type, data)


def subscribe_to_event(event_type: str, callback: Callable) -> str:
    """Subscribe to an event."""
    return event_bus.subscribe(event_type, callback)


def start_background_task(name: str, func: Callable, *args, **kwargs) -> str:
    """Start a background task."""
    return background_manager.start_task(name, func, *args, **kwargs)


def start_async_processing() -> None:
    """Start the async processing system."""
    task_processor.start()
    logger.info("Async processing system started")


def stop_async_processing() -> None:
    """Stop the async processing system."""
    task_processor.stop()
    logger.info("Async processing system stopped")


def get_async_stats() -> Dict[str, Any]:
    """Get async processing statistics."""
    return {
        'task_processor': task_processor.get_stats(),
        'event_bus_subscribers': len(event_bus.subscribers),
        'background_tasks': background_manager.get_running_tasks()
    }


# Decorators
def async_task(priority: TaskPriority = TaskPriority.NORMAL, timeout: float = None):
    """Decorator to make a function run asynchronously."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            return submit_async_task(func, *args, priority=priority, timeout=timeout, **kwargs)
        return wrapper
    return decorator


def event_handler(event_type: str):
    """Decorator to register a function as an event handler."""
    def decorator(func):
        subscribe_to_event(event_type, func)
        return func
    return decorator


# Context manager for async operations
class AsyncContext:
    """Context manager for async operations."""
    
    def __init__(self):
        self.task_ids = []
    
    def submit(self, func: Callable, *args, **kwargs) -> str:
        """Submit a task within this context."""
        task_id = submit_async_task(func, *args, **kwargs)
        self.task_ids.append(task_id)
        return task_id
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Wait for all tasks to complete or timeout
        timeout = 30.0
        start_time = time.time()
        
        while self.task_ids and (time.time() - start_time) < timeout:
            completed_tasks = []
            for task_id in self.task_ids:
                status = task_processor.get_task_status(task_id)
                if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    completed_tasks.append(task_id)
            
            for task_id in completed_tasks:
                self.task_ids.remove(task_id)
            
            if self.task_ids:
                time.sleep(0.1)
        
        # Cancel remaining tasks
        for task_id in self.task_ids:
            task_processor.cancel_task(task_id)
