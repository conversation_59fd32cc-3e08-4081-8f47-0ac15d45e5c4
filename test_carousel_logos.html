<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carousel Logo Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .logo-container {
            width: 180px;
            height: 180px;
            border: 1px solid #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px;
            background: white;
            overflow: hidden;
        }
        .logo-container img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .logo-row {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }
        .logo-item {
            text-align: center;
            margin: 10px;
        }
        .status {
            margin-top: 5px;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            display: inline-block;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.loading { background: #d1ecf1; color: #0c5460; }
        .console-log {
            background: #333;
            color: #fff;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }
        .path-info {
            font-family: monospace;
            background: #f5f5f5;
            padding: 5px;
            border-radius: 4px;
            margin-bottom: 5px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Carousel Logo Test</h1>
        <p>This page tests the display of company logos in different scenarios to diagnose the issue with the homepage carousel.</p>

        <div class="test-section">
            <h2>1. Direct Media URL Test</h2>
            <p>Testing direct access to company logo files in the media directory:</p>
            <div class="logo-row">
                <!-- Test company logos directly from media folder -->
                <div class="logo-item">
                    <div class="path-info">/media/company_logos/bot.png</div>
                    <div class="logo-container">
                        <img src="/media/company_logos/bot.png" alt="Company Logo 1" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                    </div>
                    <div class="status loading">Loading...</div>
                </div>
                <div class="logo-item">
                    <div class="path-info">/media/company_logos/company-1.svg</div>
                    <div class="logo-container">
                        <img src="/media/company_logos/company-1.svg" alt="Company Logo 2" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                    </div>
                    <div class="status loading">Loading...</div>
                </div>
                <div class="logo-item">
                    <div class="path-info">/media/company_logos/company-2.svg</div>
                    <div class="logo-container">
                        <img src="/media/company_logos/company-2.svg" alt="Company Logo 3" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                    </div>
                    <div class="status loading">Loading...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>2. Static URL Test</h2>
            <p>Testing access to static logo files:</p>
            <div class="logo-row">
                <!-- Test static logo files -->
                <div class="logo-item">
                    <div class="path-info">/static/img/logos/company-1.svg</div>
                    <div class="logo-container">
                        <img src="/static/img/logos/company-1.svg" alt="Static Logo 1" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                    </div>
                    <div class="status loading">Loading...</div>
                </div>
                <div class="logo-item">
                    <div class="path-info">/static/img/default-company-logo.svg</div>
                    <div class="logo-container">
                        <img src="/static/img/default-company-logo.svg" alt="Default Company Logo" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                    </div>
                    <div class="status loading">Loading...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>3. Absolute URL Test</h2>
            <p>Testing with absolute URLs (with domain):</p>
            <div class="logo-row">
                <!-- Test with absolute URLs -->
                <div class="logo-item">
                    <div class="path-info">http://localhost:8000/media/company_logos/bot.png</div>
                    <div class="logo-container">
                        <img src="http://localhost:8000/media/company_logos/bot.png" alt="Absolute URL Logo" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                    </div>
                    <div class="status loading">Loading...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>4. Fallback Test</h2>
            <p>Testing fallback mechanism for missing logos:</p>
            <div class="logo-row">
                <!-- Test fallback mechanism -->
                <div class="logo-item">
                    <div class="path-info">/media/company_logos/non-existent.png</div>
                    <div class="logo-container">
                        <img src="/media/company_logos/non-existent.png" alt="Non-existent Logo" 
                             data-fallback="/static/img/default-company-logo.svg"
                             onload="updateStatus(this, 'success')" 
                             onerror="handleFallback(this); updateStatus(this, 'error', 'Using fallback')">
                    </div>
                    <div class="status loading">Loading...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>5. Relative Path Test</h2>
            <p>Testing with relative paths:</p>
            <div class="logo-row">
                <!-- Test with relative paths -->
                <div class="logo-item">
                    <div class="path-info">media/company_logos/bot.png</div>
                    <div class="logo-container">
                        <img src="media/company_logos/bot.png" alt="Relative Path Logo" 
                             onload="updateStatus(this, 'success')" 
                             onerror="updateStatus(this, 'error')">
                    </div>
                    <div class="status loading">Loading...</div>
                </div>
            </div>
        </div>

        <div class="console-log" id="console-log">
            <div>Console output will appear here...</div>
        </div>
    </div>

    <script>
        // Capture console logs
        (function() {
            const oldLog = console.log;
            const oldError = console.error;
            const logElement = document.getElementById('console-log');
            
            console.log = function(message) {
                logElement.innerHTML += `<div>[LOG] ${message}</div>`;
                oldLog.apply(console, arguments);
            };
            
            console.error = function(message) {
                logElement.innerHTML += `<div style="color:#ff6b6b">[ERROR] ${message}</div>`;
                oldError.apply(console, arguments);
            };
        })();

        // Update status of image loading
        function updateStatus(imgElement, status, message) {
            const statusElement = imgElement.closest('.logo-item').querySelector('.status');
            statusElement.className = `status ${status}`;
            statusElement.textContent = message || (status === 'success' ? 'Loaded successfully' : 'Failed to load');
            
            console.log(`${imgElement.alt}: ${statusElement.textContent} (${imgElement.src})`);
        }

        // Handle fallback for failed images
        function handleFallback(imgElement) {
            const fallbackSrc = imgElement.getAttribute('data-fallback');
            if (fallbackSrc) {
                console.log(`Applying fallback for ${imgElement.alt}: ${fallbackSrc}`);
                imgElement.src = fallbackSrc;
            }
        }

        // Log page load
        window.addEventListener('load', function() {
            console.log('Page fully loaded');
            
            // Check for any images still in loading state after 3 seconds
            setTimeout(function() {
                document.querySelectorAll('.status.loading').forEach(function(element) {
                    element.className = 'status error';
                    element.textContent = 'Timeout - Failed to load';
                });
            }, 3000);
        });
    </script>
</body>
</html>