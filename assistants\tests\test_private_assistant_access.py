"""
Test for private assistant access via direct link/QR code.

This test verifies that private assistants (is_public=False) can be accessed
via direct link or QR code by authorized users, but are not visible in the
public directory listing.
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.exceptions import PermissionDenied
from accounts.models import Company, Membership
from assistants.models import Assistant
from directory.models import AssistantListing


class PrivateAssistantAccessTest(TestCase):
    """Test private assistant access permissions."""

    def setUp(self):
        """Set up test data."""
        # Create test users
        self.owner_user = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.member_user = User.objects.create_user(
            username='member',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.external_user = User.objects.create_user(
            username='external',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test company
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.owner_user,
            entity_type='company'
        )

        # Add member to company
        Membership.objects.create(
            user=self.member_user,
            company=self.company,
            role='member'
        )

        # Create private assistant (not public)
        self.private_assistant = Assistant.objects.create(
            name='Private Test Assistant',
            company=self.company,
            assistant_type=Assistant.TYPE_GENERAL,
            system_prompt='Test prompt',
            is_public=False,  # This is the key - private assistant
            is_active=True,
            slug='private-test-assistant'
        )

        # Create public assistant for comparison
        self.public_assistant = Assistant.objects.create(
            name='Public Test Assistant',
            company=self.company,
            assistant_type=Assistant.TYPE_GENERAL,
            system_prompt='Test prompt',
            is_public=True,
            is_active=True,
            slug='public-test-assistant'
        )

        # Create assistant listing for public assistant only
        AssistantListing.objects.create(
            assistant=self.public_assistant,
            is_listed=True
        )

        self.client = Client()

    def test_private_assistant_accessible_by_owner_via_direct_link(self):
        """Test that company owner can access private assistant via direct link."""
        self.client.login(username='owner', password='testpass123')
        
        response = self.client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': self.private_assistant.slug
        }))
        
        # Should be accessible
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Private Test Assistant')

    def test_private_assistant_accessible_by_member_via_direct_link(self):
        """Test that company member can access private assistant via direct link."""
        self.client.login(username='member', password='testpass123')
        
        response = self.client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': self.private_assistant.slug
        }))
        
        # Should be accessible
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Private Test Assistant')

    def test_private_assistant_not_accessible_by_external_user(self):
        """Test that external user cannot access private assistant via direct link."""
        self.client.login(username='external', password='testpass123')
        
        response = self.client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': self.private_assistant.slug
        }))
        
        # Should be denied
        self.assertEqual(response.status_code, 403)

    def test_private_assistant_not_accessible_by_anonymous_user(self):
        """Test that anonymous user cannot access private assistant via direct link."""
        # Don't login - test as anonymous user
        
        response = self.client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': self.private_assistant.slug
        }))
        
        # Should be denied
        self.assertEqual(response.status_code, 403)

    def test_public_assistant_accessible_by_all_via_direct_link(self):
        """Test that public assistant is accessible by all users via direct link."""
        # Test with anonymous user
        response = self.client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': self.public_assistant.slug
        }))
        self.assertEqual(response.status_code, 200)
        
        # Test with external user
        self.client.login(username='external', password='testpass123')
        response = self.client.get(reverse('assistants:assistant_chat', kwargs={
            'slug': self.public_assistant.slug
        }))
        self.assertEqual(response.status_code, 200)

    def test_private_assistant_not_in_directory_listing(self):
        """Test that private assistant does not appear in directory listing."""
        response = self.client.get(reverse('directory:assistant_list'))
        
        # Should not contain private assistant
        self.assertNotContains(response, 'Private Test Assistant')
        # Should contain public assistant
        self.assertContains(response, 'Public Test Assistant')

    def test_private_assistant_interact_api_permissions(self):
        """Test that private assistant interact API respects permissions."""
        # Test with owner
        self.client.login(username='owner', password='testpass123')
        response = self.client.post(reverse('assistants:public_interact', kwargs={
            'slug': self.private_assistant.slug
        }), {
            'message': 'Hello'
        }, content_type='application/json')
        
        # Should work for owner
        self.assertNotEqual(response.status_code, 403)
        
        # Test with external user
        self.client.login(username='external', password='testpass123')
        response = self.client.post(reverse('assistants:public_interact', kwargs={
            'slug': self.private_assistant.slug
        }), {
            'message': 'Hello'
        }, content_type='application/json')
        
        # Should be denied for external user
        self.assertEqual(response.status_code, 403)

    def test_community_assistants_list_respects_permissions(self):
        """Test that community assistants list shows private assistants only to authorized users."""
        # Create community assistant (private)
        community_assistant = Assistant.objects.create(
            name='Private Community Assistant',
            company=self.company,
            assistant_type=Assistant.TYPE_COMMUNITY,
            system_prompt='Test prompt',
            is_public=False,
            is_active=True,
            slug='private-community-assistant'
        )

        # Test with owner - should see private community assistant
        self.client.login(username='owner', password='testpass123')
        response = self.client.get(reverse('assistants:assistant_list_redirect'))
        self.assertContains(response, 'Private Community Assistant')

        # Test with external user - should not see private community assistant
        self.client.login(username='external', password='testpass123')
        response = self.client.get(reverse('assistants:assistant_list_redirect'))
        self.assertNotContains(response, 'Private Community Assistant')
