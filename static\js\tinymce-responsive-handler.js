/**
 * TinyMCE Responsive Handler
 * Ensures TinyMCE content is properly responsive and handles tables and images correctly
 */

document.addEventListener('DOMContentLoaded', function() {
    // Process all TinyMCE content areas on page load
    processTinyMCEContent();

    // Also process content when TinyMCE is initialized
    if (typeof tinymce !== 'undefined' && tinymce.EditorManager) {
        // Use the correct TinyMCE API for listening to editor events
        tinymce.EditorManager.on('AddEditor', function(e) {
            const editor = e.editor;

            editor.on('init', function() {
                // Make the editor responsive
                makeEditorResponsive(editor);

                // Handle content changes
                editor.on('change', function() {
                    // Process content in the editor
                    processEditorContent(editor);
                });

                // Handle paste events
                editor.on('PastePostProcess', function(e) {
                    processPastedContent(e.node, editor);
                });
            });
        });
    }

    // Process content when window is resized
    window.addEventListener('resize', debounce(function() {
        processTinyMCEContent();
    }, 250));
});

/**
 * Process all TinyMCE content areas on the page
 */
function processTinyMCEContent() {
    // Find all elements with tinymce-content class
    const contentAreas = document.querySelectorAll('.tinymce-content, .message-content');

    contentAreas.forEach(function(contentArea) {
        // Process tables
        processTables(contentArea);

        // Process images
        processImages(contentArea);
    });
}

/**
 * Make the TinyMCE editor responsive
 * @param {Object} editor - TinyMCE editor instance
 */
function makeEditorResponsive(editor) {
    // Set editor container to be responsive
    const editorContainer = editor.getContainer();
    if (editorContainer) {
        editorContainer.style.maxWidth = '100%';
        editorContainer.style.width = '100%';
    }

    // Add responsive classes to the editor body
    editor.on('init', function() {
        editor.dom.addClass(editor.getBody(), 'tinymce-content');
    });
}

/**
 * Process content in the TinyMCE editor
 * @param {Object} editor - TinyMCE editor instance
 */
function processEditorContent(editor) {
    // Process tables in the editor
    const tables = editor.getBody().querySelectorAll('table');
    tables.forEach(function(table) {
        processTable(table, editor.getBody());
    });

    // Process images in the editor
    const images = editor.getBody().querySelectorAll('img');
    images.forEach(function(img) {
        processImage(img);
    });
}

/**
 * Process tables in a content area
 * @param {Element} contentArea - The content area containing tables
 */
function processTables(contentArea) {
    const tables = contentArea.querySelectorAll('table');

    tables.forEach(function(table) {
        processTable(table, contentArea);
    });
}

/**
 * Process a single table
 * @param {Element} table - The table element
 * @param {Element} container - The container element
 */
function processTable(table, container) {
    // Skip if table is already in a table-wrapper
    if (table.parentElement && table.parentElement.classList.contains('table-wrapper')) {
        return;
    }

    // Add Bootstrap table class if not present
    if (!table.classList.contains('table')) {
        table.classList.add('table');
    }

    // Create wrapper for horizontal scrolling
    const wrapper = document.createElement('div');
    wrapper.className = 'table-wrapper';

    // Insert wrapper before table
    table.parentNode.insertBefore(wrapper, table);

    // Move table into wrapper
    wrapper.appendChild(table);
}

/**
 * Process images in a content area
 * @param {Element} contentArea - The content area containing images
 */
function processImages(contentArea) {
    const images = contentArea.querySelectorAll('img');

    images.forEach(function(img) {
        processImage(img);
    });
}

/**
 * Process a single image
 * @param {Element} img - The image element
 */
function processImage(img) {
    // Add responsive class if not present
    if (!img.classList.contains('img-fluid')) {
        img.classList.add('img-fluid');
    }

    // Ensure max-width is set
    img.style.maxWidth = '100%';

    // Ensure height is auto
    img.style.height = 'auto';

    // Fix image alignment if needed
    if (img.align === 'left') {
        img.classList.add('float-start');
        img.classList.add('me-3');
        img.removeAttribute('align');
    } else if (img.align === 'right') {
        img.classList.add('float-end');
        img.classList.add('ms-3');
        img.removeAttribute('align');
    }
}

/**
 * Process pasted content
 * @param {Element} node - The pasted content node
 * @param {Object} editor - TinyMCE editor instance
 */
function processPastedContent(node, editor) {
    // Process tables in pasted content
    const tables = node.querySelectorAll('table');
    tables.forEach(function(table) {
        processTable(table, node);
    });

    // Process images in pasted content
    const images = node.querySelectorAll('img');
    images.forEach(function(img) {
        processImage(img);
    });
}

/**
 * Debounce function to limit how often a function is called
 * @param {Function} func - The function to debounce
 * @param {number} wait - The debounce wait time in milliseconds
 * @returns {Function} - The debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(function() {
            func.apply(context, args);
        }, wait);
    };
}
