"""
Tests for enhanced session management and user isolation.
"""
import json
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.contrib.sessions.models import Session
from accounts.models import Company
from assistants.models import Assistant, Interaction
from accounts.session_management import UserSessionManager

User = get_user_model()


class UserSessionManagerTests(TestCase):
    """Test the UserSessionManager class."""
    
    def setUp(self):
        """Set up test data."""
        self.user1 = User.objects.create_user(
            username='testuser1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user1
        )
        
        self.assistant = Assistant.objects.create(
            name='Test Assistant',
            company=self.company,
            system_prompt='Test prompt',
            is_public=True
        )
    
    def test_user_session_key_generation(self):
        """Test that session keys are generated correctly and uniquely."""
        # Test authenticated user
        key1 = UserSessionManager.get_user_session_key(self.user1, self.assistant.id, 'chat')
        key2 = UserSessionManager.get_user_session_key(self.user1, self.assistant.id, 'chat')
        self.assertEqual(key1, key2)  # Same user, same assistant should generate same key
        
        # Test different users generate different keys
        key3 = UserSessionManager.get_user_session_key(self.user2, self.assistant.id, 'chat')
        self.assertNotEqual(key1, key3)
        
        # Test anonymous user
        key4 = UserSessionManager.get_user_session_key(None, self.assistant.id, 'chat')
        self.assertNotEqual(key1, key4)
        self.assertTrue(key4.startswith('user_session_chat_'))
    
    def test_chat_history_isolation(self):
        """Test that chat history is properly isolated between users."""
        # Save history for user1
        history1 = [
            {'role': 'user', 'content': 'Hello from user1'},
            {'role': 'assistant', 'content': 'Hi user1!'}
        ]
        UserSessionManager.save_user_chat_history(self.user1, self.assistant.id, history1)
        
        # Save different history for user2
        history2 = [
            {'role': 'user', 'content': 'Hello from user2'},
            {'role': 'assistant', 'content': 'Hi user2!'}
        ]
        UserSessionManager.save_user_chat_history(self.user2, self.assistant.id, history2)
        
        # Retrieve and verify isolation
        retrieved1 = UserSessionManager.get_user_chat_history(self.user1, self.assistant.id)
        retrieved2 = UserSessionManager.get_user_chat_history(self.user2, self.assistant.id)
        
        self.assertEqual(len(retrieved1), 2)
        self.assertEqual(len(retrieved2), 2)
        self.assertEqual(retrieved1[0]['content'], 'Hello from user1')
        self.assertEqual(retrieved2[0]['content'], 'Hello from user2')
        self.assertNotEqual(retrieved1, retrieved2)
    
    def test_chat_history_size_limit(self):
        """Test that chat history is limited to prevent excessive storage."""
        # Create a large history (more than 100 messages)
        large_history = []
        for i in range(150):
            large_history.append({'role': 'user', 'content': f'Message {i}'})
            large_history.append({'role': 'assistant', 'content': f'Response {i}'})
        
        UserSessionManager.save_user_chat_history(self.user1, self.assistant.id, large_history)
        retrieved = UserSessionManager.get_user_chat_history(self.user1, self.assistant.id)
        
        # Should be limited to 100 messages
        self.assertEqual(len(retrieved), 100)
        # Should contain the most recent messages
        self.assertEqual(retrieved[-1]['content'], 'Response 149')
    
    def test_context_data_isolation(self):
        """Test that context data is properly isolated between users."""
        # Save context data for user1
        context1 = {'key': 'value1', 'data': 'user1_data'}
        UserSessionManager.save_user_context_data(self.user1, self.assistant.id, 'test_context', context1)
        
        # Save different context data for user2
        context2 = {'key': 'value2', 'data': 'user2_data'}
        UserSessionManager.save_user_context_data(self.user2, self.assistant.id, 'test_context', context2)
        
        # Retrieve and verify isolation
        retrieved1 = UserSessionManager.get_user_context_data(self.user1, self.assistant.id, 'test_context')
        retrieved2 = UserSessionManager.get_user_context_data(self.user2, self.assistant.id, 'test_context')
        
        self.assertEqual(retrieved1['data'], 'user1_data')
        self.assertEqual(retrieved2['data'], 'user2_data')
        self.assertNotEqual(retrieved1, retrieved2)
    
    def test_session_validation(self):
        """Test session validation functionality."""
        # Create a mock request
        from django.test import RequestFactory
        factory = RequestFactory()
        
        # Test with authenticated user
        request = factory.get('/test/')
        request.user = self.user1
        request.session = {}
        request.session.session_key = 'test_session_key'
        
        # Mock session exists
        Session.objects.create(
            session_key='test_session_key',
            session_data='test_data',
            expire_date='2025-01-01 00:00:00'
        )
        
        # Should validate successfully for public assistant
        is_valid = UserSessionManager.validate_user_session(request, self.assistant.id)
        self.assertTrue(is_valid)
    
    def test_anonymous_user_handling(self):
        """Test that anonymous users are handled correctly."""
        # Test chat history for anonymous user
        history = [{'role': 'user', 'content': 'Anonymous message'}]
        UserSessionManager.save_user_chat_history(None, self.assistant.id, history)
        
        retrieved = UserSessionManager.get_user_chat_history(None, self.assistant.id)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0]['content'], 'Anonymous message')
        
        # Test context data for anonymous user
        context = {'anonymous': True}
        UserSessionManager.save_user_context_data(None, self.assistant.id, 'test', context)
        
        retrieved_context = UserSessionManager.get_user_context_data(None, self.assistant.id, 'test')
        self.assertEqual(retrieved_context['anonymous'], True)


class SessionIsolationIntegrationTests(TestCase):
    """Integration tests for session isolation in views."""
    
    def setUp(self):
        """Set up test data."""
        self.client1 = Client()
        self.client2 = Client()
        
        self.user1 = User.objects.create_user(
            username='testuser1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user1
        )
        
        self.assistant = Assistant.objects.create(
            name='Test Assistant',
            company=self.company,
            system_prompt='Test prompt',
            is_public=True
        )
    
    def test_interaction_user_isolation(self):
        """Test that interactions are properly isolated by user."""
        # Login users
        self.client1.login(username='testuser1', password='testpass123')
        self.client2.login(username='testuser2', password='testpass123')
        
        # Create interactions for each user
        Interaction.objects.create(
            assistant=self.assistant,
            user=self.user1,
            prompt='User1 prompt',
            response='User1 response',
            duration=1.0,
            token_count=10
        )
        
        Interaction.objects.create(
            assistant=self.assistant,
            user=self.user2,
            prompt='User2 prompt',
            response='User2 response',
            duration=1.0,
            token_count=10
        )
        
        # Test that each user only sees their own interactions
        from assistants.optimized_queries import OptimizedInteractionQueries
        
        user1_interactions = OptimizedInteractionQueries.get_user_interactions_optimized(
            self.user1, self.assistant
        )
        user2_interactions = OptimizedInteractionQueries.get_user_interactions_optimized(
            self.user2, self.assistant
        )
        
        self.assertEqual(user1_interactions.count(), 1)
        self.assertEqual(user2_interactions.count(), 1)
        self.assertEqual(user1_interactions.first().prompt, 'User1 prompt')
        self.assertEqual(user2_interactions.first().prompt, 'User2 prompt')
    
    def tearDown(self):
        """Clean up after tests."""
        cache.clear()
        Session.objects.all().delete()
