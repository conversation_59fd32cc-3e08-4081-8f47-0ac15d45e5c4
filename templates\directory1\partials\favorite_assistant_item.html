{% load rating_tags %}
<div class="list-group-item mb-3 border rounded shadow-sm favorite-item" data-assistant-id="{{ item.assistant.id }}">
    <div class="row g-3">
        {# Link wrapper covers first 3 columns (col-md-10) and contains an inner row #}
        <a href="{% url 'assistants:assistant_chat' slug=item.assistant.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none text-body"> {# Added text-body #}
            {# Column 1: Logo (col-md-2 within link row) - With Fallback Logic #}
            <div class="col-md-2 d-flex justify-content-center align-items-start pt-1">
                {% with logo_url=item.assistant.get_logo_url %}
                    {% if logo_url %}
                        <img src="{{ logo_url }}" alt="{{ item.assistant.name }} logo" class="rounded" style="height: 64px; width: auto; max-width: 100%; object-fit: contain; display: block; margin: auto;">
                    {% else %}
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 64px; width: 75px;">
                             <i class="bi bi-robot text-muted fs-3"></i>
                        </div>
                    {% endif %}
                {% endwith %}
            </div>
            {# Column 2: Name, Company, Type, Tags (col-md-3 within link row) #}
            <div class="col-md-3">
                <h6 class="mb-1 fs-6 fw-semibold"> {# Match company list style #}
                    {{ item.assistant.name }}
                </h6>
                <p class="mb-1 text-muted small">
                    By {{ item.assistant.company.name }}
                </p>
                <span class="badge bg-primary bg-opacity-10 text-primary mb-2 tag-badge">{{ item.assistant.get_assistant_type_display }}</span>
                {% if item.assistant.listing %}
                <div class="mt-1 mb-2">
                    {% for category in item.assistant.listing.categories|slice:":3" %}
                        <span class="badge bg-secondary tag-badge">{{ category }}</span>
                    {% endfor %}
                    {% for tag in item.assistant.listing.tags|slice:":3" %}
                        <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {# Column 3: Description (col-md-7 within link row) #}
            <div class="col-md-7">
                {% with full_desc=item.assistant.listing.short_description|default:item.assistant.description|default:"" %}
                <p class="text-muted mb-0 item-description"> {# Use class from main list #}
                    {{ full_desc|truncatewords:50 }}
                    {% if full_desc|wordcount > 50 %}
                        {# Removed Read More span as whole area is clickable #}
                    {% elif not full_desc %}
                        No description available.
                    {% endif %}
                </p>
                {% endwith %}
            </div>
        </a> {# End of clickable area link #}

        {# Column 4: Rating, Unlike Button (col-md-2) - Outside the link #}
        <div class="col-md-2 d-flex flex-column align-items-end justify-content-start">
            {# Unlike Button - Adjusted style like company list #}
            <button
                class="like-button btn btn-sm p-0 text-danger" {# Always red on this page #}
                data-item-id="{{ item.assistant.id }}"
                data-item-type="assistant"
                title="Unlike"
                style="background: none; border: none; cursor: pointer; margin-bottom: 0.5rem; z-index: 5;">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16" style="pointer-events: none;">
                    <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314"/>
                </svg>
            </button>
            {# Static Star Rating Display #}
            <div class="rating-display-container mb-1 w-100 d-flex justify-content-end" id="rating-display-{{ item.assistant.id }}">
                {% if item.assistant.listing %}
                    {% render_stars item.assistant.listing.avg_rating item.assistant.listing.total_ratings %}
                {% else %}
                    <span class="small text-muted fst-italic">(No rating info)</span>
                {% endif %}
            </div>
            {# Rate Button #}
            {% if user.is_authenticated and item.assistant.listing %} {# Only show if listing exists #}
            <div class="w-100 d-flex justify-content-end">
                <button type="button"
                        class="btn btn-outline-secondary btn-sm rate-assistant-btn"
                        data-bs-toggle="modal"
                        data-bs-target="#ratingModal"
                        data-assistant-id="{{ item.assistant.id }}"
                        data-assistant-name="{{ item.assistant.name|escapejs }}"
                        style="z-index: 5;">
                    <i class="bi bi-star me-1"></i> Rate
                </button>
            </div>
            <div class="w-100 d-flex justify-content-end">
                <span class="rating-update-message small text-success mt-1" id="rating-msg-{{ item.assistant.id }}" style="display: none;"></span>
            </div>
            {% endif %}
        </div>
    </div> {# /row #}
</div> {# /list-group-item #}
