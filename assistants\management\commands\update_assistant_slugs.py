from django.core.management.base import BaseCommand
from django.utils.text import slugify
from django.db import transaction
from assistants.models import Assistant


class Command(BaseCommand):
    help = 'Update assistant slugs to include company names for better uniqueness'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update even if slug already contains company name',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']

        self.stdout.write(
            self.style.SUCCESS('Starting assistant slug update process...')
        )

        # Get all assistants
        assistants = Assistant.objects.select_related('company').all()

        updated_count = 0
        skipped_count = 0
        error_count = 0

        for assistant in assistants:
            try:
                # Generate new slug format with unique identifier
                import uuid
                company_slug = slugify(assistant.company.name) if assistant.company else 'unknown'
                assistant_slug = slugify(assistant.name)
                unique_id = uuid.uuid4().hex[:8]
                new_slug_base = f'{company_slug}-{assistant_slug}-{unique_id}'

                # Check if slug already follows the new format with unique ID (unless force is used)
                if not force and len(assistant.slug.split('-')) >= 3 and assistant.slug.startswith(f'{company_slug}-'):
                    # Check if the slug has the pattern: company-assistant-uniqueid
                    parts = assistant.slug.split('-')
                    if len(parts) >= 3 and len(parts[-1]) == 8:  # Last part should be 8-char unique ID
                        self.stdout.write(
                            f'Skipping {assistant.name} (ID: {assistant.id}) - slug already has unique identifier'
                        )
                        skipped_count += 1
                        continue

                # Find a unique slug (very unlikely to conflict with UUID, but just in case)
                new_slug = new_slug_base
                num = 1
                while Assistant.objects.filter(slug=new_slug).exclude(pk=assistant.pk).exists():
                    new_slug = f'{new_slug_base}-{num}'
                    num += 1

                old_slug = assistant.slug

                if dry_run:
                    self.stdout.write(
                        f'Would update: {assistant.name} (ID: {assistant.id})'
                    )
                    self.stdout.write(f'  Company: {assistant.company.name if assistant.company else "Unknown"}')
                    self.stdout.write(f'  Old slug: {old_slug}')
                    self.stdout.write(f'  New slug: {new_slug}')
                    self.stdout.write('')
                    updated_count += 1
                else:
                    # Update the slug
                    with transaction.atomic():
                        assistant.slug = new_slug
                        assistant.save(update_fields=['slug'])

                    self.stdout.write(
                        self.style.SUCCESS(f'Updated: {assistant.name} (ID: {assistant.id})')
                    )
                    self.stdout.write(f'  Company: {assistant.company.name if assistant.company else "Unknown"}')
                    self.stdout.write(f'  Old slug: {old_slug}')
                    self.stdout.write(f'  New slug: {new_slug}')
                    self.stdout.write('')
                    updated_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error updating {assistant.name} (ID: {assistant.id}): {str(e)}')
                )
                error_count += 1

        # Summary
        self.stdout.write(self.style.SUCCESS('=== Summary ==='))
        if dry_run:
            self.stdout.write(f'Assistants that would be updated: {updated_count}')
        else:
            self.stdout.write(f'Assistants updated: {updated_count}')
        self.stdout.write(f'Assistants skipped: {skipped_count}')
        self.stdout.write(f'Errors encountered: {error_count}')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('This was a dry run. No changes were made.')
            )
            self.stdout.write(
                'Run without --dry-run to apply the changes.'
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('Assistant slug update completed!')
            )
