/**
 * NUP Color Theme Overrides
 * This file overrides Bootstrap's primary colors with NUP colors
 */

:root {
  /* Override Bootstrap primary colors with NUP red */
  --bs-primary: #cf2e2e !important;
  --bs-primary-rgb: 207, 46, 46 !important;

  /* Custom gradient variables */
  --vibrant-blue-gradient: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
  --vibrant-blue-light: #f7f7f7 !important;
}

/* Override primary background */
.bg-primary {
  background-color: #cf2e2e !important;
  background: var(--vibrant-blue-gradient) !important;
}

/* Override primary buttons */
.btn-primary {
  background-color: #cf2e2e !important;
  border-color: #b82626 !important;
  box-shadow: 0 2px 4px rgba(207, 46, 46, 0.2);
  transition: all 0.3s ease !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
  background-color: #b82626 !important;
  border-color: #a02222 !important;
  box-shadow: 0 4px 8px rgba(207, 46, 46, 0.3);
  transform: translateY(-1px);
}

/* Override primary outline buttons */
.btn-outline-primary {
  color: #cf2e2e !important;
  border-color: #cf2e2e !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 1px 2px rgba(207, 46, 46, 0.1);
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
  background-color: #cf2e2e !important;
  color: #ffffff !important;
  box-shadow: 0 3px 6px rgba(207, 46, 46, 0.2);
  transform: translateY(-1px);
}

/* Override primary text */
.text-primary {
  color: #cf2e2e !important;
}

/* Override links */
a {
  color: #cf2e2e;
}

a:hover {
  color: #b82626;
}

/* Override form elements */
.form-check-input:checked {
  background-color: #cf2e2e !important;
  border-color: #cf2e2e !important;
}

/* Hero section specific enhancements */
section.py-5.bg-primary {
  position: relative;
  overflow: hidden;
}

/* Add a subtle pattern overlay to the hero section */
section.py-5.bg-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
  z-index: 0;
}

/* Ensure content stays above the pattern */
section.py-5.bg-primary .container {
  position: relative;
  z-index: 1;
}

/* Enhanced vibrant hero section */
.vibrant-hero {
  background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
  box-shadow: 0 4px 20px rgba(207, 46, 46, 0.3);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Add a subtle shine effect to the hero section */
.vibrant-hero::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: linear-gradient(to bottom right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.05) 100%);
  transform: rotate(30deg);
  pointer-events: none;
  z-index: 0;
}

/* Make buttons in the hero section more vibrant */
.vibrant-hero .btn-primary {
  background-color: #b82626 !important;
  border-color: #a02222 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.vibrant-hero .btn-primary:hover {
  background-color: #252638 !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.vibrant-hero .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}
