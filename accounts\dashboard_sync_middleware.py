"""
Dashboard synchronization middleware to ensure dashboard reflects logout state.
"""
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.contrib.auth import logout
from django.shortcuts import redirect
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)


class DashboardSyncMiddleware(MiddlewareMixin):
    """
    Middleware to ensure dashboard pages properly reflect authentication state.
    """
    
    def process_request(self, request):
        """
        Check if user should be logged out based on session state.
        """
        # Only check for dashboard and authenticated pages
        dashboard_paths = [
            '/accounts/dashboard/',
            '/accounts/settings/',
            '/company/',
            '/assistants/',
        ]
        
        # Check if this is a dashboard-related request
        is_dashboard_request = any(request.path.startswith(path) for path in dashboard_paths)
        
        if is_dashboard_request and request.user.is_authenticated:
            # Check if session is still valid
            if not self._is_session_valid(request):
                logger.info(f"Invalid session detected for user {request.user.username}, logging out")
                
                # Log the user out
                logout(request)
                
                # Handle AJAX requests differently
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'error': 'Session expired',
                        'redirect': reverse('accounts:login')
                    }, status=401)
                else:
                    # Redirect to login page
                    return redirect('accounts:login')
        
        return None
    
    def _is_session_valid(self, request):
        """
        Check if the current session is valid.
        """
        try:
            # Check if session exists and is not expired
            if not hasattr(request, 'session') or not request.session.session_key:
                return False
            
            # Check if session exists in database
            from django.contrib.sessions.models import Session
            from django.utils import timezone
            
            try:
                session = Session.objects.get(
                    session_key=request.session.session_key,
                    expire_date__gte=timezone.now()
                )
                
                # Check if session data is valid
                session_data = session.get_decoded()
                if not session_data:
                    return False
                
                # Check if user ID matches
                session_user_id = session_data.get('_auth_user_id')
                if not session_user_id or str(request.user.id) != session_user_id:
                    return False
                
                return True
                
            except Session.DoesNotExist:
                return False
            
        except Exception as e:
            logger.error(f"Error validating session: {e}")
            return False
    
    def process_response(self, request, response):
        """
        Add headers to prevent caching of authenticated pages.
        """
        # Add no-cache headers for dashboard pages when user is authenticated
        dashboard_paths = [
            '/accounts/dashboard/',
            '/accounts/settings/',
            '/company/',
            '/assistants/',
        ]
        
        is_dashboard_request = any(request.path.startswith(path) for path in dashboard_paths)
        
        if is_dashboard_request and hasattr(request, 'user') and request.user.is_authenticated:
            response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response['Pragma'] = 'no-cache'
            response['Expires'] = '0'
        
        return response
