#!/usr/bin/env python
"""
Test company creation and settings through web interface (HTTP requests).
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from accounts.models import Company, CompanyInformation
import uuid

def test_web_interface():
    """Test company creation through web interface"""
    print("🌐 WEB INTERFACE WORKFLOW TEST")
    print("=" * 50)
    
    # Create test user
    username = f"web_test_{uuid.uuid4().hex[:6]}"
    user = User.objects.create_user(
        username=username,
        email=f'{username}@test.com',
        password='testpass123'
    )
    print(f"✅ Created user: {user.username}")
    
    # Create Django test client
    client = Client()
    
    # Log in the user
    login_success = client.login(username=username, password='testpass123')
    if not login_success:
        print("❌ Failed to log in user")
        return False
    print("✅ User logged in successfully")
    
    # Test data for company creation
    company_name = f'Web Test Company {uuid.uuid4().hex[:4]}'
    form_data = {
        'name': company_name,
        'entity_type': 'company',
        'mission': 'Web interface test mission',
        'description': 'Testing through web interface',
        'website': 'https://web-test.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '******-WEB-TEST',
        'timezone': 'UTC',
        'language': 'en',
        'industry': 'Web Testing',
        'size': '1-10',
        'city': 'Web City',
        'country': 'Web Country',
        'founded': 2024,
        'list_in_directory': True
    }
    
    try:
        # Step 1: Test company creation via POST
        print("\n📝 Step 1: Creating company via web interface...")
        
        create_url = reverse('accounts:company_create')
        response = client.post(create_url, form_data)
        
        if response.status_code == 302:  # Redirect after successful creation
            print("✅ Company creation request successful (redirected)")
        else:
            print(f"❌ Company creation failed. Status: {response.status_code}")
            if hasattr(response, 'context') and response.context and 'form' in response.context:
                print(f"Form errors: {response.context['form'].errors}")
            return False
        
        # Step 2: Verify company was created
        print("\n🔍 Step 2: Verifying company creation...")
        
        try:
            company = Company.objects.get(name=company_name, owner=user)
            print(f"✅ Company found: {company.name}")
        except Company.DoesNotExist:
            print("❌ Company not found in database")
            return False
        
        # Step 3: Verify company information was saved
        print("\n📊 Step 3: Checking saved company information...")
        
        try:
            company_info = CompanyInformation.objects.get(company=company)
            print("✅ CompanyInformation found")
        except CompanyInformation.DoesNotExist:
            print("❌ CompanyInformation not found")
            return False
        
        # Check key fields
        checks = [
            ('mission', company_info.mission, form_data['mission']),
            ('website', company_info.website, form_data['website']),
            ('contact_email', company_info.contact_email, form_data['contact_email']),
            ('industry', company_info.industry, form_data['industry']),
            ('city', company_info.city, form_data['city']),
        ]
        
        all_good = True
        for field, saved, expected in checks:
            if saved == expected:
                print(f"✅ {field}: {saved}")
            else:
                print(f"❌ {field}: {saved} (expected: {expected})")
                all_good = False
        
        # Step 4: Test company settings page
        print("\n⚙️ Step 4: Testing company settings page...")
        
        settings_url = reverse('accounts:company_settings', kwargs={'company_id': company.id})
        response = client.get(settings_url)
        
        if response.status_code == 200:
            print("✅ Company settings page accessible")
            
            # Check if form is pre-populated with saved data
            if hasattr(response, 'context') and 'form' in response.context:
                form = response.context['form']
                
                for field, _, expected in checks:
                    if field in form.initial:
                        initial_value = form.initial[field]
                        if initial_value == expected:
                            print(f"✅ Settings form {field} pre-populated: {initial_value}")
                        else:
                            print(f"❌ Settings form {field}: {initial_value} (expected: {expected})")
                            all_good = False
            else:
                print("⚠️ Could not access form context")
        else:
            print(f"❌ Company settings page not accessible. Status: {response.status_code}")
            all_good = False
        
        # Step 5: Test updating through settings
        print("\n🔄 Step 5: Testing settings update...")
        
        updated_data = form_data.copy()
        updated_data.update({
            'mission': 'Updated web interface mission',
            'website': 'https://updated-web-test.com',
            'industry': 'Updated Web Testing'
        })
        
        response = client.post(settings_url, updated_data)
        
        if response.status_code in [200, 302]:  # Success or redirect
            print("✅ Settings update request successful")
            
            # Verify updates were saved
            company_info.refresh_from_db()
            if (company_info.mission == updated_data['mission'] and 
                company_info.website == updated_data['website'] and
                company_info.industry == updated_data['industry']):
                print("✅ Settings updates saved correctly")
            else:
                print("❌ Settings updates not saved correctly")
                all_good = False
        else:
            print(f"❌ Settings update failed. Status: {response.status_code}")
            all_good = False
        
        # Clean up
        company.delete()
        user.delete()
        
        if all_good:
            print("\n🎉 WEB INTERFACE TEST PASSED!")
            print("✅ Company creation and settings workflow working correctly")
            return True
        else:
            print("\n⚠️ WEB INTERFACE TEST FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_web_interface()
    sys.exit(0 if success else 1)
