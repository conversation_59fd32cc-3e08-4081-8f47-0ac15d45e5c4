#!/usr/bin/env python
"""
Test direct login functionality to isolate the issue.
"""

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import RequestFactory, Client
from django.contrib.auth import get_user_model, login
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from django.contrib.messages.middleware import MessageMiddleware
from django.contrib.messages.storage.fallback import FallbackStorage
from accounts.auth_views import approve_signin
from accounts.auth_utils import store_signin_approval

User = get_user_model()

print("🔧 Testing Direct Login Functionality")
print("=" * 50)

# Create test user
user, created = User.objects.get_or_create(
    username='directtest', 
    defaults={'email': '<EMAIL>', 'is_active': True}
)
print(f"✅ Test user: {user.username} (ID: {user.id})")

# Test 1: Direct login using Django's login function
print("\n1️⃣ Testing direct login with Django's login function...")
client = Client()

# Manually log the user in
client.force_login(user)
print(f"✅ Force login successful")

# Check if user is logged in
response = client.get('/')
if hasattr(response, 'wsgi_request') and response.wsgi_request.user.is_authenticated:
    print(f"✅ User is authenticated: {response.wsgi_request.user.username}")
else:
    print("❌ User is not authenticated after force login")

# Test 2: Test the approve_signin view directly
print("\n2️⃣ Testing approve_signin view directly...")

# Generate a token
token = store_signin_approval(user, 1)
print(f"✅ Token generated: {token}")

# Create a new client (fresh session)
client2 = Client()

# Test the approve_signin view
response = client2.get(f'/accounts/approve-signin/{token}/', follow=True)
print(f"✅ Response status: {response.status_code}")

# Check if user is logged in after approval
session = client2.session
user_id = session.get('_auth_user_id')
if user_id:
    print(f"✅ User logged in via approval: Session user ID = {user_id}")
    print(f"✅ Expected user ID: {user.id}")
    if str(user_id) == str(user.id):
        print("🎉 SUCCESS: Approval login working!")
    else:
        print("⚠️  User ID mismatch")
else:
    print("❌ FAILED: User not logged in after approval")
    print(f"Session keys: {list(session.keys())}")

# Test 3: Check session engine
print("\n3️⃣ Testing session configuration...")
from django.conf import settings
print(f"Session engine: {settings.SESSION_ENGINE}")
print(f"Session cookie age: {settings.SESSION_COOKIE_AGE}")
print(f"Session save every request: {settings.SESSION_SAVE_EVERY_REQUEST}")

# Test 4: Manual login test
print("\n4️⃣ Testing manual login process...")
try:
    from django.contrib.auth import login as auth_login
    
    # Create a request factory
    factory = RequestFactory()
    request = factory.get('/test/')
    
    # Add session middleware
    middleware = SessionMiddleware(lambda r: None)
    middleware.process_request(request)
    request.session.save()
    
    # Add auth middleware
    auth_middleware = AuthenticationMiddleware(lambda r: None)
    auth_middleware.process_request(request)
    
    # Add messages middleware
    messages_middleware = MessageMiddleware(lambda r: None)
    messages_middleware.process_request(request)
    request._messages = FallbackStorage(request)
    
    # Set backend on user
    user.backend = 'accounts.backends.EmailOrUsernameModelBackend'
    
    # Try to log in
    auth_login(request, user)
    
    print(f"✅ Manual login successful")
    print(f"✅ Request user: {request.user}")
    print(f"✅ Is authenticated: {request.user.is_authenticated}")
    print(f"✅ Session key: {request.session.session_key}")
    
except Exception as e:
    print(f"❌ Manual login failed: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("📊 DIRECT LOGIN TEST COMPLETE")
print("=" * 50)

print("\n🔍 Analysis:")
print("- If force_login works but approve_signin doesn't, the issue is in the view")
print("- If manual login works, the issue might be middleware interference")
print("- Check Django server logs for detailed error messages")

print("\n💡 Next Steps:")
print("1. Check the Django development server console for error messages")
print("2. Try the approval URL in a real browser")
print("3. Check if any middleware is interfering with the login process")

print("\n" + "=" * 50)
