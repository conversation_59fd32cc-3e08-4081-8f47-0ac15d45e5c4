<div class="card-header bg-light d-flex align-items-center">
    <!-- Company Logo -->
    {% if company.info.logo and company.info.logo.url %}
        <img src="{{ company.info.logo.url }}" alt="{{ company.name }} Logo" class="company-header-logo me-3"
             onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'bg-light rounded-circle d-flex align-items-center justify-content-center me-3\' style=\'width: 60px; height: 60px;\'><i class=\'bi bi-building text-muted\' style=\'font-size: 1.8rem;\'></i></div>';">
    {% else %}
        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
            <i class="bi bi-building text-muted" style="font-size: 1.8rem;"></i>
        </div>
    {% endif %}

    <!-- Company Name -->
    <h1 class="h3 mb-0 me-auto">{{ company.name }}</h1>

    <!-- Rating and Actions -->
    <div class="d-flex align-items-center">
        <!-- Rating Display -->
        <div class="me-3">
            <div id="rating-display-{{ company.id }}">
                {% if company_listing %}
                    {% render_stars company_listing.avg_rating company_listing.total_ratings %}
                {% else %}
                    {% render_stars 0 0 %}
                {% endif %}
            </div>
            {% if user.is_authenticated %}
                <button type="button" class="btn btn-sm btn-link text-decoration-none p-0 mt-1"
                        data-bs-toggle="modal" data-bs-target="#ratingModal"
                        data-company-id="{{ company.id }}"
                        data-company-name="{{ company.name }}">
                    <i class="bi bi-star me-1"></i>Rate
                </button>
            {% endif %}
        </div>

        <!-- Like Button -->
        {% if user.is_authenticated %}
            <button
                class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %} me-2"
                data-item-id="{{ company.id }}"
                data-item-type="company"
                title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                style="background: none; border: none;">
                <i class="bi bi-heart{% if is_favorited %}-fill{% endif %}"></i>
            </button>
        {% endif %}

        <!-- QR Code Button -->
        <button type="button" class="btn btn-sm btn-outline-secondary me-1" id="company-qr-code-btn"
                title="Show Company QR Code" data-bs-toggle="modal" data-bs-target="#companyQrCodeModal"
                data-qr-url="{{ company.qr_code.url }}">
            <i class="bi bi-qr-code"></i>
        </button>

        <!-- Share Button -->
        <button type="button" class="btn btn-sm btn-outline-secondary" id="copy-company-url-btn"
                title="Copy Company Profile URL">
            <i class="bi bi-share me-1"></i> Share URL
        </button>
    </div>
</div>
