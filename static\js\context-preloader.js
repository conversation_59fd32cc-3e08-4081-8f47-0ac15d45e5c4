/**
 * Context Preloader for LLM Assistants
 * Proactively loads context data when assistants are clicked or hovered
 */

class ContextPreloader {
    constructor() {
        this.preloadCache = new Map();
        this.preloadPromises = new Map();
        this.enabled = true;
        this.preloadTimeout = 30000; // 30 seconds timeout
        this.hoverDelay = 500; // 500ms hover delay before preloading
        this.hoverTimeouts = new Map();
        
        this.init();
    }
    
    init() {
        console.log('Context Preloader initialized');
        
        // Set up event listeners for assistant interactions
        this.setupAssistantListeners();
        
        // Preload context for current assistant if on chat page
        this.preloadCurrentAssistant();
        
        // Set up intersection observer for lazy preloading
        this.setupIntersectionObserver();
    }
    
    setupAssistantListeners() {
        // Listen for assistant clicks in lists
        document.addEventListener('click', (event) => {
            const assistantLink = event.target.closest('a[href*="/assistants/"]');
            if (assistantLink) {
                this.handleAssistantClick(assistantLink);
            }
        });
        
        // Listen for assistant hovers for predictive preloading
        document.addEventListener('mouseover', (event) => {
            const assistantElement = event.target.closest('[data-assistant-id]');
            if (assistantElement) {
                this.handleAssistantHover(assistantElement);
            }
        });
        
        // Clear hover timeouts on mouseout
        document.addEventListener('mouseout', (event) => {
            const assistantElement = event.target.closest('[data-assistant-id]');
            if (assistantElement) {
                this.clearHoverTimeout(assistantElement);
            }
        });
    }
    
    handleAssistantClick(assistantLink) {
        const assistantId = this.extractAssistantId(assistantLink.href);
        if (assistantId) {
            console.log(`Assistant ${assistantId} clicked, preloading context...`);
            this.preloadAssistantContext(assistantId, true); // High priority
        }
    }
    
    handleAssistantHover(assistantElement) {
        const assistantId = assistantElement.dataset.assistantId;
        if (!assistantId) return;
        
        // Clear any existing timeout for this assistant
        this.clearHoverTimeout(assistantElement);
        
        // Set new timeout for preloading
        const timeoutId = setTimeout(() => {
            console.log(`Assistant ${assistantId} hovered, preloading context...`);
            this.preloadAssistantContext(assistantId, false); // Lower priority
        }, this.hoverDelay);
        
        this.hoverTimeouts.set(assistantId, timeoutId);
    }
    
    clearHoverTimeout(assistantElement) {
        const assistantId = assistantElement.dataset.assistantId;
        if (assistantId && this.hoverTimeouts.has(assistantId)) {
            clearTimeout(this.hoverTimeouts.get(assistantId));
            this.hoverTimeouts.delete(assistantId);
        }
    }
    
    preloadCurrentAssistant() {
        // Check if we're on a chat page
        const assistantId = document.querySelector('meta[name="assistant-id"]')?.content;
        if (assistantId) {
            console.log(`Preloading context for current assistant ${assistantId}`);
            this.preloadAssistantContext(assistantId, true);
        }
    }
    
    setupIntersectionObserver() {
        // Preload context for assistants that come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const assistantId = entry.target.dataset.assistantId;
                    if (assistantId && !this.preloadCache.has(assistantId)) {
                        // Delay preloading to avoid overwhelming the server
                        setTimeout(() => {
                            this.preloadAssistantContext(assistantId, false);
                        }, Math.random() * 2000); // Random delay 0-2 seconds
                    }
                }
            });
        }, {
            rootMargin: '100px' // Start preloading 100px before element is visible
        });
        
        // Observe all assistant elements
        document.querySelectorAll('[data-assistant-id]').forEach(element => {
            observer.observe(element);
        });
    }
    
    async preloadAssistantContext(assistantId, highPriority = false) {
        if (!this.enabled || !assistantId) return;
        
        // Check if already cached
        if (this.preloadCache.has(assistantId)) {
            console.log(`Context for assistant ${assistantId} already cached`);
            return this.preloadCache.get(assistantId);
        }
        
        // Check if already being preloaded
        if (this.preloadPromises.has(assistantId)) {
            console.log(`Context for assistant ${assistantId} already being preloaded`);
            return this.preloadPromises.get(assistantId);
        }
        
        // Start preloading
        const preloadPromise = this.fetchAssistantContext(assistantId, highPriority);
        this.preloadPromises.set(assistantId, preloadPromise);
        
        try {
            const contextData = await preloadPromise;
            this.preloadCache.set(assistantId, contextData);
            console.log(`Context preloaded for assistant ${assistantId}`);
            return contextData;
        } catch (error) {
            console.warn(`Failed to preload context for assistant ${assistantId}:`, error);
            return null;
        } finally {
            this.preloadPromises.delete(assistantId);
        }
    }
    
    async fetchAssistantContext(assistantId, highPriority = false) {
        const companyId = document.querySelector('meta[name="company-id"]')?.content;
        if (!companyId) {
            throw new Error('Company ID not found');
        }
        
        const url = `/assistants/company/${companyId}/assistants/${assistantId}/preload-context/`;
        const csrfToken = this.getCsrfToken();
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.preloadTimeout);
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    high_priority: highPriority,
                    preload_type: 'full'
                }),
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'success') {
                return data.context_data;
            } else {
                throw new Error(data.error || 'Unknown error');
            }
            
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }
    
    getPreloadedContext(assistantId) {
        return this.preloadCache.get(assistantId) || null;
    }
    
    hasPreloadedContext(assistantId) {
        return this.preloadCache.has(assistantId);
    }
    
    clearCache(assistantId = null) {
        if (assistantId) {
            this.preloadCache.delete(assistantId);
            this.preloadPromises.delete(assistantId);
            console.log(`Cleared cache for assistant ${assistantId}`);
        } else {
            this.preloadCache.clear();
            this.preloadPromises.clear();
            console.log('Cleared all preload cache');
        }
    }
    
    extractAssistantId(url) {
        // Extract assistant ID from URL patterns like:
        // /assistants/company/123/assistants/456/
        // /assistant/company/123/assistants/456/chat/
        const match = url.match(/\/assistants?\/company\/\d+\/assistants\/(\d+)/);
        return match ? match[1] : null;
    }
    
    getCsrfToken() {
        const csrfInput = document.querySelector('[name=csrfmiddlewaretoken]');
        if (csrfInput) return csrfInput.value;
        
        const csrfMeta = document.querySelector('meta[name=csrf-token]');
        if (csrfMeta) return csrfMeta.content;
        
        const csrfCookie = document.cookie
            .split('; ')
            .find(row => row.startsWith('csrftoken='));
        if (csrfCookie) return csrfCookie.split('=')[1];
        
        return '';
    }
    
    // Public API methods
    enable() {
        this.enabled = true;
        console.log('Context preloader enabled');
    }
    
    disable() {
        this.enabled = false;
        console.log('Context preloader disabled');
    }
    
    getStats() {
        return {
            cacheSize: this.preloadCache.size,
            activePreloads: this.preloadPromises.size,
            enabled: this.enabled
        };
    }
}

// Initialize context preloader when DOM is ready
let contextPreloader;

document.addEventListener('DOMContentLoaded', function() {
    contextPreloader = new ContextPreloader();
    
    // Make it globally available for debugging
    window.contextPreloader = contextPreloader;
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContextPreloader;
}
