/* Solid Colors CSS Override */

/* Force background color */
html, body {
    background-color: #121212 !important;
    background: #121212 !important;
    background-image: none !important;
}

/* Force chat container styles */
.chat-container, .general-chat-container {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    background-image: none !important;
    border: 1px solid #333333 !important;
    border-radius: 1.25rem !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

/* Force chat box styles */
.chat-box, .general-chat-box, #chat-box {
    background-color: #252525 !important;
    background: #252525 !important;
    background-image: none !important;
    border: 1px solid #333333 !important;
    border-radius: 1.25rem !important;
}

/* Force user message styles */
.user-message .message-content {
    background-color: #3b7dd8 !important;
    background: #3b7dd8 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: none !important;
}

/* Force assistant message styles */
.assistant-message .message-content {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    background-image: none !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
}

/* Force chat form styles */
#chat-form {
    background-color: transparent !important;
    background: transparent !important;
    background-image: none !important;
    width: 100% !important;
    margin: 0 auto !important;
    position: relative !important;
}

/* Force send button styles */
#send-button {
    background-color: #3b7dd8 !important;
    background: #3b7dd8 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 1.5rem !important;
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
}

/* Force input field styles */
#message-input {
    background-color: #252525 !important;
    background: #252525 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    border-radius: 1.5rem !important;
    height: 46px !important;
}

/* Force hover effects */
.user-message:hover .message-content {
    background-color: #3069b9 !important;
    background: #3069b9 !important;
    background-image: none !important;
}

.assistant-message:hover .message-content {
    background-color: #ffffff !important;
    background: #ffffff !important;
    background-image: none !important;
}

#send-button:hover {
    background-color: #3069b9 !important;
    background: #3069b9 !important;
    background-image: none !important;
}
