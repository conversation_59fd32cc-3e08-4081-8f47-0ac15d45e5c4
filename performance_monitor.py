#!/usr/bin/env python
"""
Performance monitoring script for the Django AI assistant platform.
Tracks and reports on all optimization metrics.
"""

import os
import sys
import django
import time
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Set up Django environment with performance monitoring
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
os.environ['PRODUCTION'] = 'True'  # Enable production optimizations for monitoring
django.setup()


class PerformanceMonitor:
    """Monitors and reports performance metrics."""

    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()

    def collect_all_metrics(self):
        """Collect all performance metrics."""
        print("🔍 Collecting Performance Metrics...")
        print("=" * 60)

        self.collect_cache_metrics()
        self.collect_memory_metrics()
        self.collect_database_metrics()
        self.collect_llm_metrics()
        self.collect_async_metrics()
        self.collect_system_metrics()

        return self.metrics

    def collect_cache_metrics(self):
        """Collect caching performance metrics."""
        try:
            print("📊 Cache Performance:")

            # Advanced caching metrics
            from assistants.advanced_caching import multi_level_cache, get_cache_stats

            cache_stats = get_cache_stats()
            self.metrics['cache'] = cache_stats

            print(f"  L1 Cache Utilization: {cache_stats.get('l1_utilization', 0):.1%}")
            print(f"  Total Cache Entries: {cache_stats.get('l1_entries', 0)}")
            print(f"  Total Accesses: {cache_stats.get('total_accesses', 0)}")
            print(f"  Dependencies Tracked: {cache_stats.get('dependencies_tracked', 0)}")

            # LLM cache metrics
            from assistants.llm_cache import LLMCache

            # Test cache hit rate
            test_hits = 0
            test_total = 10

            for i in range(test_total):
                test_key = f"test_key_{i}"
                if multi_level_cache.get(test_key):
                    test_hits += 1

            hit_rate = test_hits / test_total if test_total > 0 else 0
            print(f"  Cache Hit Rate: {hit_rate:.1%}")

        except Exception as e:
            print(f"  ❌ Error collecting cache metrics: {e}")
            self.metrics['cache'] = {'error': str(e)}

    def collect_memory_metrics(self):
        """Collect memory performance metrics."""
        try:
            print("\n🧠 Memory Performance:")

            from assistants.memory_manager import get_memory_stats

            memory_stats = get_memory_stats()
            self.metrics['memory'] = memory_stats

            memory_info = memory_stats.get('memory', {})
            print(f"  RSS Memory: {memory_info.get('rss_mb', 0):.1f} MB")
            print(f"  Virtual Memory: {memory_info.get('vms_mb', 0):.1f} MB")
            print(f"  Memory Percentage: {memory_info.get('percent', 0):.1f}%")

            # Object pool stats
            object_pools = memory_stats.get('object_pools', {})
            if object_pools:
                print(f"  Object Pools: {len(object_pools)}")
                for pool_name, pool_stats in object_pools.items():
                    reuse_ratio = pool_stats.get('reuse_ratio', 0)
                    print(f"    {pool_name}: {reuse_ratio:.1%} reuse rate")

        except Exception as e:
            print(f"  ❌ Error collecting memory metrics: {e}")
            self.metrics['memory'] = {'error': str(e)}

    def collect_database_metrics(self):
        """Collect database performance metrics."""
        try:
            print("\n🗄️ Database Performance:")

            from assistants.advanced_queries import get_performance_stats
            from django.db import connection

            perf_stats = get_performance_stats()
            self.metrics['database'] = perf_stats

            # Query pool stats
            query_pool_stats = perf_stats.get('query_pool', {})
            print(f"  Query Pool Size: {query_pool_stats.get('pool_size', 0)}")
            print(f"  Total Query Usage: {query_pool_stats.get('total_usage', 0)}")

            # Slow queries
            slow_queries = perf_stats.get('slow_queries', [])
            print(f"  Slow Queries: {len(slow_queries)}")
            if slow_queries:
                slowest = slow_queries[0]
                print(f"    Slowest: {slowest.get('execution_time', 0):.2f}s")

            # Database connection info
            print(f"  Database Connections: {len(connection.queries)}")

        except Exception as e:
            print(f"  ❌ Error collecting database metrics: {e}")
            self.metrics['database'] = {'error': str(e)}

    def collect_llm_metrics(self):
        """Collect LLM performance metrics."""
        try:
            print("\n🤖 LLM Performance:")

            from assistants.llm_utils_advanced import parallel_processor

            llm_stats = parallel_processor.get_stats()
            self.metrics['llm'] = llm_stats

            print(f"  Tasks Submitted: {llm_stats.get('tasks_submitted', 0)}")
            print(f"  Tasks Completed: {llm_stats.get('tasks_completed', 0)}")
            print(f"  Tasks Failed: {llm_stats.get('tasks_failed', 0)}")
            print(f"  Active Tasks: {llm_stats.get('active_tasks', 0)}")
            print(f"  Queued Tasks: {llm_stats.get('queued_tasks', 0)}")

            # Calculate success rate
            total_processed = llm_stats.get('tasks_completed', 0) + llm_stats.get('tasks_failed', 0)
            if total_processed > 0:
                success_rate = llm_stats.get('tasks_completed', 0) / total_processed
                print(f"  Success Rate: {success_rate:.1%}")

        except Exception as e:
            print(f"  ❌ Error collecting LLM metrics: {e}")
            self.metrics['llm'] = {'error': str(e)}

    def collect_async_metrics(self):
        """Collect async processing metrics."""
        try:
            print("\n⚡ Async Processing:")

            from assistants.async_processors import get_async_stats

            async_stats = get_async_stats()
            self.metrics['async'] = async_stats

            task_processor = async_stats.get('task_processor', {})
            print(f"  Active Tasks: {task_processor.get('active_tasks', 0)}")
            print(f"  Queued Tasks: {task_processor.get('queued_tasks', 0)}")
            print(f"  Completed Tasks: {task_processor.get('completed_tasks', 0)}")
            print(f"  Workers: {task_processor.get('workers', 0)}")
            print(f"  Running: {task_processor.get('running', False)}")

            # Event bus stats
            event_subscribers = async_stats.get('event_bus_subscribers', 0)
            print(f"  Event Subscribers: {event_subscribers}")

            # Background tasks
            background_tasks = async_stats.get('background_tasks', [])
            print(f"  Background Tasks: {len(background_tasks)}")

        except Exception as e:
            print(f"  ❌ Error collecting async metrics: {e}")
            self.metrics['async'] = {'error': str(e)}

    def collect_system_metrics(self):
        """Collect system-level metrics."""
        try:
            print("\n💻 System Performance:")

            import psutil

            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            print(f"  CPU Usage: {cpu_percent:.1f}%")

            # Memory usage
            memory = psutil.virtual_memory()
            print(f"  System Memory: {memory.percent:.1f}% used")
            print(f"  Available Memory: {memory.available / 1024 / 1024 / 1024:.1f} GB")

            # Disk usage
            disk = psutil.disk_usage('/')
            print(f"  Disk Usage: {disk.percent:.1f}%")

            # Network stats (if available)
            try:
                network = psutil.net_io_counters()
                print(f"  Network Sent: {network.bytes_sent / 1024 / 1024:.1f} MB")
                print(f"  Network Received: {network.bytes_recv / 1024 / 1024:.1f} MB")
            except:
                pass

            self.metrics['system'] = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk.percent
            }

        except Exception as e:
            print(f"  ❌ Error collecting system metrics: {e}")
            self.metrics['system'] = {'error': str(e)}

    def generate_report(self):
        """Generate a comprehensive performance report."""
        print("\n" + "=" * 60)
        print("📈 PERFORMANCE SUMMARY REPORT")
        print("=" * 60)

        # Overall health score
        health_score = self.calculate_health_score()
        print(f"🏥 Overall Health Score: {health_score:.1f}/100")

        # Recommendations
        self.generate_recommendations()

        # Save report to file
        self.save_report()

    def calculate_health_score(self):
        """Calculate an overall health score."""
        score = 100

        # Deduct points for errors
        for category, data in self.metrics.items():
            if isinstance(data, dict) and 'error' in data:
                score -= 15

        # Deduct points for high resource usage
        system_metrics = self.metrics.get('system', {})
        if system_metrics.get('cpu_percent', 0) > 80:
            score -= 10
        if system_metrics.get('memory_percent', 0) > 80:
            score -= 10

        # Deduct points for low cache hit rates
        cache_metrics = self.metrics.get('cache', {})
        l1_utilization = cache_metrics.get('l1_utilization', 0)
        if l1_utilization < 0.5:
            score -= 5

        return max(0, score)

    def generate_recommendations(self):
        """Generate performance recommendations."""
        print("\n💡 RECOMMENDATIONS:")

        recommendations = []

        # Cache recommendations
        cache_metrics = self.metrics.get('cache', {})
        if cache_metrics.get('l1_utilization', 0) < 0.3:
            recommendations.append("Consider increasing cache size for better hit rates")

        # Memory recommendations
        memory_metrics = self.metrics.get('memory', {})
        memory_info = memory_metrics.get('memory', {})
        if memory_info.get('percent', 0) > 80:
            recommendations.append("High memory usage detected - consider memory optimization")

        # Database recommendations
        db_metrics = self.metrics.get('database', {})
        slow_queries = db_metrics.get('slow_queries', [])
        if len(slow_queries) > 5:
            recommendations.append("Multiple slow queries detected - review database indexes")

        # LLM recommendations
        llm_metrics = self.metrics.get('llm', {})
        failed_tasks = llm_metrics.get('tasks_failed', 0)
        total_tasks = llm_metrics.get('tasks_submitted', 0)
        if total_tasks > 0 and failed_tasks / total_tasks > 0.1:
            recommendations.append("High LLM task failure rate - check API configurations")

        if not recommendations:
            recommendations.append("System is performing well - no immediate optimizations needed")

        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")

    def save_report(self):
        """Save the performance report to a file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = project_dir / f"performance_report_{timestamp}.json"

        report_data = {
            'timestamp': datetime.now().isoformat(),
            'metrics': self.metrics,
            'health_score': self.calculate_health_score(),
            'duration': time.time() - self.start_time
        }

        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)

        print(f"\n📄 Report saved to: {report_file}")


def main():
    """Main function to run performance monitoring."""
    print("🚀 Starting Performance Monitoring...")
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    monitor = PerformanceMonitor()

    try:
        # Collect all metrics
        metrics = monitor.collect_all_metrics()

        # Generate report
        monitor.generate_report()

        print("\n✅ Performance monitoring completed successfully!")
        return True

    except Exception as e:
        print(f"\n❌ Error during performance monitoring: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
