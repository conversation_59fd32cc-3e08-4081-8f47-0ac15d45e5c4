"""
Optimized middleware for cPanel environments.
Combines multiple middleware functions to reduce overhead.
"""

import logging
from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from impersonate.middleware import ImpersonateMiddleware

logger = logging.getLogger(__name__)
User = get_user_model()


class OptimizedCombinedMiddleware(MiddlewareMixin):
    """
    Combined middleware that handles multiple functions in one pass
    to reduce overhead in cPanel environments.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process request with optimized operations."""
        # Skip processing for static files and admin media
        if self.should_skip_processing(request):
            return None
        
        # Optimize session handling
        self.optimize_session(request)
        
        # Handle impersonation efficiently
        self.handle_impersonation(request)
        
        return None
    
    def process_response(self, request, response):
        """Process response with cleanup operations."""
        # Skip processing for static files
        if self.should_skip_processing(request):
            return response
        
        # Cleanup expired sessions periodically
        self.cleanup_sessions(request)
        
        return response
    
    def should_skip_processing(self, request):
        """Check if we should skip processing for this request."""
        path = request.path
        return (
            path.startswith('/static/') or
            path.startswith('/media/') or
            path.startswith('/admin/jsi18n/') or
            path.endswith('.css') or
            path.endswith('.js') or
            path.endswith('.png') or
            path.endswith('.jpg') or
            path.endswith('.jpeg') or
            path.endswith('.gif') or
            path.endswith('.ico') or
            path.endswith('.svg')
        )
    
    def optimize_session(self, request):
        """Optimize session handling."""
        if not hasattr(request, 'session'):
            return
        
        # Don't save session on every request for static content
        if request.path.startswith('/api/') and request.method == 'GET':
            request.session.modified = False
        
        # Clean up old session data
        session_key = request.session.session_key
        if session_key:
            # Cache session existence to avoid repeated DB queries
            cache_key = f"session_exists:{session_key}"
            if not cache.get(cache_key):
                try:
                    Session.objects.get(session_key=session_key)
                    cache.set(cache_key, True, 300)  # Cache for 5 minutes
                except Session.DoesNotExist:
                    # Session doesn't exist, create new one
                    request.session.create()
    
    def handle_impersonation(self, request):
        """Handle impersonation with optimized checks."""
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return
        
        # Check for impersonation in session
        impersonate_id = request.session.get('_impersonate')
        if impersonate_id:
            # Cache user lookup to avoid repeated DB queries
            cache_key = f"impersonate_user:{impersonate_id}"
            impersonated_user = cache.get(cache_key)
            
            if impersonated_user is None:
                try:
                    impersonated_user = User.objects.get(id=impersonate_id)
                    cache.set(cache_key, impersonated_user, 300)  # Cache for 5 minutes
                except User.DoesNotExist:
                    # Invalid impersonation, clear it
                    del request.session['_impersonate']
                    return
            
            # Set the impersonated user
            request.user = impersonated_user
            request.impersonator = request.user
    
    def cleanup_sessions(self, request):
        """Periodically cleanup expired sessions."""
        # Only run cleanup occasionally to avoid overhead
        cleanup_key = "session_cleanup_last_run"
        last_cleanup = cache.get(cleanup_key)
        
        if last_cleanup is None:
            # Run cleanup
            try:
                expired_sessions = Session.objects.filter(
                    expire_date__lt=timezone.now()
                )[:100]  # Limit to 100 to avoid long-running queries
                
                if expired_sessions:
                    session_keys = [s.session_key for s in expired_sessions]
                    Session.objects.filter(session_key__in=session_keys).delete()
                    logger.info(f"Cleaned up {len(session_keys)} expired sessions")
                
                # Set cache to prevent running again for 1 hour
                cache.set(cleanup_key, timezone.now(), 3600)
                
            except Exception as e:
                logger.error(f"Error cleaning up sessions: {e}")


class OptimizedImpersonateMiddleware(ImpersonateMiddleware):
    """
    Optimized version of ImpersonateMiddleware with caching.
    """
    
    def process_request(self, request):
        """Process request with caching optimizations."""
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return
        
        # Skip for static files
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return
        
        # Use cached impersonation check
        impersonate_id = request.session.get('_impersonate')
        if impersonate_id:
            cache_key = f"impersonate_user:{impersonate_id}"
            impersonated_user = cache.get(cache_key)
            
            if impersonated_user is None:
                try:
                    impersonated_user = User.objects.select_related().get(id=impersonate_id)
                    cache.set(cache_key, impersonated_user, 300)
                except User.DoesNotExist:
                    del request.session['_impersonate']
                    return
            
            # Store original user and set impersonated user
            request.impersonator = request.user
            request.user = impersonated_user


class OptimizedSessionCleanupMiddleware(MiddlewareMixin):
    """
    Lightweight session cleanup middleware.
    """
    
    def process_response(self, request, response):
        """Clean up sessions efficiently."""
        # Only run cleanup on specific conditions
        if (hasattr(request, 'user') and 
            request.user.is_authenticated and 
            request.path == '/accounts/logout/'):
            
            # Clean up user's other sessions on logout
            try:
                user_sessions = Session.objects.filter(
                    expire_date__gte=timezone.now()
                )
                
                for session in user_sessions:
                    session_data = session.get_decoded()
                    if session_data.get('_auth_user_id') == str(request.user.id):
                        if session.session_key != request.session.session_key:
                            session.delete()
                            
            except Exception as e:
                logger.error(f"Error cleaning up user sessions: {e}")
        
        return response


class OptimizedPermissionsMiddleware(MiddlewareMixin):
    """
    Optimized permissions middleware with caching.
    """
    
    def process_request(self, request):
        """Process request with cached permissions."""
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return
        
        # Skip for static files and API endpoints
        if (request.path.startswith('/static/') or 
            request.path.startswith('/media/') or
            request.path.startswith('/api/check-')):
            return
        
        # Cache user permissions to avoid repeated DB queries
        cache_key = f"user_perms:{request.user.id}"
        user_permissions = cache.get(cache_key)
        
        if user_permissions is None:
            # Get user permissions and cache them
            user_permissions = {
                'is_superuser': request.user.is_superuser,
                'is_staff': request.user.is_staff,
                'groups': list(request.user.groups.values_list('name', flat=True)),
                'permissions': list(request.user.user_permissions.values_list('codename', flat=True))
            }
            cache.set(cache_key, user_permissions, 600)  # Cache for 10 minutes
        
        # Attach cached permissions to request
        request.user_permissions_cache = user_permissions


class MinimalMiddleware(MiddlewareMixin):
    """
    Minimal middleware for essential functionality only.
    Use this in production to reduce overhead.
    """
    
    def process_request(self, request):
        """Minimal request processing."""
        # Only handle essential session operations
        if hasattr(request, 'session') and request.session.session_key:
            # Mark session as accessed to prevent unnecessary saves
            request.session.accessed = True
        
        return None
    
    def process_response(self, request, response):
        """Minimal response processing."""
        # Only save session if it was actually modified
        if (hasattr(request, 'session') and 
            hasattr(request.session, 'modified') and 
            request.session.modified):
            request.session.save()
        
        return response


# Middleware configuration for different environments
CPANEL_MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'accounts.middleware_optimized.OptimizedCombinedMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

MINIMAL_MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'accounts.middleware_optimized.MinimalMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
]
