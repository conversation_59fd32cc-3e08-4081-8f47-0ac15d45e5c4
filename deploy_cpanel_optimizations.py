#!/usr/bin/env python
"""
Quick deployment script for cPanel optimizations.
Run this script to apply all optimizations automatically.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_step(step_num, description):
    """Print a formatted step."""
    print(f"\n{'='*60}")
    print(f"STEP {step_num}: {description}")
    print('='*60)

def run_command(command, description=""):
    """Run a command and handle errors."""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Success: {description}")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"✗ Error: {description}")
            print(result.stderr)
            return False
        return True
    except Exception as e:
        print(f"✗ Exception: {e}")
        return False

def create_backup():
    """Create backup of current configuration."""
    print_step(1, "Creating Backup")

    backup_files = [
        'passenger_wsgi.py',
        'company_assistant/settings.py',
    ]

    backup_dir = Path('backup_before_optimization')
    backup_dir.mkdir(exist_ok=True)

    for file_path in backup_files:
        if os.path.exists(file_path):
            backup_path = backup_dir / Path(file_path).name
            shutil.copy2(file_path, backup_path)
            print(f"✓ Backed up: {file_path} -> {backup_path}")

def check_environment():
    """Check if we're in the right environment."""
    print_step(2, "Checking Environment")

    required_files = [
        'manage.py',
        'company_assistant/settings.py',
        'requirements.txt',
    ]

    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"✗ Missing required file: {file_path}")
            return False
        print(f"✓ Found: {file_path}")

    return True

def install_dependencies():
    """Install any missing dependencies."""
    print_step(3, "Installing Dependencies")

    # Check if psycopg2 is installed (for PostgreSQL)
    try:
        import psycopg2
        print("✓ psycopg2 is installed")
    except ImportError:
        print("Installing psycopg2-binary...")
        run_command("pip install psycopg2-binary", "Installing PostgreSQL adapter")

    # Check if whitenoise is installed
    try:
        import whitenoise
        print("✓ whitenoise is installed")
    except ImportError:
        print("Installing whitenoise...")
        run_command("pip install whitenoise", "Installing Whitenoise")

def create_directories():
    """Create necessary directories."""
    print_step(4, "Creating Directories")

    directories = [
        'logs',
        'cache',
        'session',
        'media',
        'staticfiles',
        'tmp',
        'backup_before_optimization',
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def run_django_optimizations():
    """Run Django optimization commands."""
    print_step(5, "Running Django Optimizations")

    commands = [
        ("python manage.py migrate", "Running database migrations"),
        ("python manage.py optimize_for_cpanel", "Running cPanel optimizations"),
        ("python manage.py collectstatic --noinput", "Collecting static files"),
    ]

    for command, description in commands:
        if not run_command(command, description):
            print(f"Warning: {description} failed, continuing...")

def update_passenger_wsgi():
    """Update passenger_wsgi.py to use optimized settings."""
    print_step(6, "Updating WSGI Configuration")

    # The passenger_wsgi.py file should already be updated
    # Just verify it's pointing to the optimized settings

    try:
        with open('passenger_wsgi.py', 'r') as f:
            content = f.read()

        if 'CPANEL_ENV' in content and 'company_assistant.settings' in content:
            print("✓ passenger_wsgi.py is already optimized")
        else:
            print("⚠ passenger_wsgi.py may need manual update")
            print("Make sure it points to: company_assistant.settings with CPANEL_ENV=True")

    except Exception as e:
        print(f"✗ Error checking passenger_wsgi.py: {e}")

def create_env_file():
    """Create optimized .env file."""
    print_step(7, "Creating Optimized Environment File")

    env_content = """# Optimized environment variables for cPanel
DEBUG=False
CPANEL_ENV=True

# Database optimization
DB_CONN_MAX_AGE=300
DB_TIMEOUT=5

# Cache settings
CACHE_TIMEOUT=3600
CACHE_MAX_ENTRIES=500

# Session settings
SESSION_COOKIE_AGE=86400
SESSION_SAVE_EVERY_REQUEST=False

# Logging
LOG_LEVEL=WARNING

# Security (update these for production)
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database (update these with your actual credentials)
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_HOST=localhost
DB_PORT=5432

# Email settings (update these)
EMAIL_HOST=your-smtp-host
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
EMAIL_USE_TLS=True
"""

    env_file = '.env'
    print(f"✓ Environment variables should be configured in {env_file}")
    print("⚠ Please update the values in .env with your actual production credentials")
    print("   Set CPANEL_ENV=True and PRODUCTION=True for production deployment")

def run_tests():
    """Run basic tests to verify everything works."""
    print_step(8, "Running Basic Tests")

    test_commands = [
        ("python manage.py check", "Django system check"),
        ("python manage.py check --deploy", "Deployment check"),
    ]

    for command, description in test_commands:
        run_command(command, description)

def print_summary():
    """Print deployment summary."""
    print_step(9, "Deployment Summary")

    print("""
✓ Backup created in 'backup_before_optimization/' directory
✓ Optimized settings configured
✓ Database cache tables created
✓ Static files collected
✓ Directories created
✓ Environment file template created

NEXT STEPS:
1. Update .env with your actual database and email credentials
2. Set CPANEL_ENV=True and PRODUCTION=True in .env for production
3. Test your application locally
4. Upload to cPanel and restart your application
5. Monitor resource usage in cPanel

ROLLBACK (if needed):
1. Restore files from backup_before_optimization/
2. Restart your application

MONITORING:
- Check logs/django.log for any errors
- Monitor cPanel resource usage graphs
- Expected: 50-70% reduction in processes and memory usage
""")

def main():
    """Main deployment function."""
    print("🚀 cPanel Optimization Deployment Script")
    print("This script will optimize your Django application for cPanel hosting")

    # Confirm before proceeding
    response = input("\nDo you want to proceed? (y/N): ")
    if response.lower() != 'y':
        print("Deployment cancelled.")
        return

    # Run deployment steps
    try:
        create_backup()

        if not check_environment():
            print("Environment check failed. Please fix issues and try again.")
            return

        install_dependencies()
        create_directories()
        run_django_optimizations()
        update_passenger_wsgi()
        create_env_file()
        run_tests()
        print_summary()

        print("\n🎉 Optimization deployment completed successfully!")

    except KeyboardInterrupt:
        print("\n\nDeployment interrupted by user.")
    except Exception as e:
        print(f"\n\n✗ Deployment failed with error: {e}")
        print("Check the backup_before_optimization/ directory to restore if needed.")

if __name__ == "__main__":
    main()
