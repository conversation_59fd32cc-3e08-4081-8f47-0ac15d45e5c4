#!/bin/bash

# cPanel Django Deployment Fix Script
# This script fixes common deployment issues on cPanel

echo "🔧 Fixing cPanel Django deployment issues..."
echo "================================================"

# Check if we're in the right directory
if [ ! -f "manage.py" ]; then
    echo "❌ Error: manage.py not found. Please run this script from your Django project root."
    exit 1
fi

# Set environment variables for cPanel
export CPANEL_ENV=True
export PRODUCTION=True

echo "📋 Step 1: Running database migrations..."
python manage.py migrate --noinput
if [ $? -eq 0 ]; then
    echo "✅ Migrations completed successfully"
else
    echo "❌ Migration failed. Please check your database connection."
    exit 1
fi

echo ""
echo "📋 Step 2: Creating cache table..."
python manage.py createcachetable
if [ $? -eq 0 ]; then
    echo "✅ Cache table created successfully"
else
    echo "⚠️  Warning: Cache table creation failed. This might be okay if using file cache."
fi

echo ""
echo "📋 Step 3: Collecting static files..."
python manage.py collectstatic --noinput
if [ $? -eq 0 ]; then
    echo "✅ Static files collected successfully"
else
    echo "⚠️  Warning: Static file collection failed."
fi

echo ""
echo "📋 Step 4: Creating necessary directories..."
mkdir -p cache
mkdir -p logs
mkdir -p session
mkdir -p media
mkdir -p staticfiles
mkdir -p tmp
echo "✅ Directories created"

echo ""
echo "📋 Step 5: Setting permissions..."
chmod 755 cache logs session media staticfiles tmp
echo "✅ Permissions set"

echo ""
echo "🎉 cPanel deployment fix completed!"
echo "================================================"
echo ""
echo "Next steps:"
echo "1. Restart your cPanel application"
echo "2. Check if the django_cache_table error is resolved"
echo "3. Monitor your application logs for any remaining issues"
echo ""
echo "If you still get cache-related errors, the settings have been"
echo "updated to use file-based caching instead of database caching."
