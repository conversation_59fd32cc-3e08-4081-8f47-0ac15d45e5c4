#!/usr/bin/env python
"""
Showcase enhanced email designs by sending direct HTML emails.
"""

import os
import sys
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    
    from django.core.mail import EmailMessage
    from django.conf import settings
    
    print("🎨 Enhanced Email Design Showcase")
    print("=" * 50)
    
    # Get recipient
    recipient = '<EMAIL>'
    print(f"📧 Sending to: {recipient}")
    
    # Enhanced Welcome Email HTML
    welcome_html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to 24seven</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 16px; box-shadow: 0 10px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; padding: 40px 30px; }
        .header h1 { margin: 0; font-size: 32px; font-weight: 700; }
        .header .subtitle { margin: 8px 0 0 0; font-size: 16px; opacity: 0.9; }
        .content { padding: 40px 30px; }
        .button { display: inline-block; padding: 16px 32px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white !important; text-decoration: none; border-radius: 12px; font-weight: 600; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); }
        .feature-box { background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%); border-left: 4px solid #38a169; padding: 20px; border-radius: 8px; margin: 24px 0; }
        .footer { background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%); color: #e2e8f0; padding: 40px 30px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="font-size: 48px; margin-bottom: 16px;">🎉</div>
            <h1>Welcome to 24seven!</h1>
            <div class="subtitle">Your AI-powered collaboration journey begins now</div>
        </div>
        
        <div class="content">
            <p style="font-size: 18px; margin-bottom: 24px;"><strong>Hello there!</strong></p>
            
            <p>Welcome to 24seven! We're thrilled to have you join our community of forward-thinking teams who are revolutionizing collaboration with AI-powered tools.</p>
            
            <div class="feature-box">
                <h4 style="margin: 0 0 16px 0; color: #2f855a;">✨ What You Can Do Now</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>🤖 Explore AI-powered assistants</li>
                    <li>🏢 Create your company profile</li>
                    <li>📚 Build your knowledge base</li>
                    <li>🎯 Customize your experience</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin: 32px 0;">
                <a href="https://24seven.site/dashboard/" class="button">🚀 Get Started</a>
            </div>
            
            <p style="text-align: center; font-size: 14px; color: #718096;">
                Questions? We're here to help at <a href="mailto:<EMAIL>" style="color: #3182ce;"><EMAIL></a>
            </p>
        </div>
        
        <div class="footer">
            <h3 style="margin: 0 0 8px 0;">24seven</h3>
            <div style="opacity: 0.8;">Secure AI-Powered Collaboration</div>
        </div>
    </div>
</body>
</html>
"""
    
    # Enhanced Notification Email HTML
    notification_html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Alert - 24seven</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 16px; box-shadow: 0 10px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white; text-align: center; padding: 40px 30px; }
        .header h1 { margin: 0; font-size: 32px; font-weight: 700; }
        .content { padding: 40px 30px; }
        .warning-box { background: linear-gradient(135deg, #fffbeb 0%, #fef5e7 100%); border-left: 4px solid #d69e2e; padding: 20px; border-radius: 8px; margin: 24px 0; }
        .button { display: inline-block; padding: 16px 32px; background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%); color: white !important; text-decoration: none; border-radius: 12px; font-weight: 600; }
        .footer { background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%); color: #e2e8f0; padding: 40px 30px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
            <h1>Security Alert</h1>
            <div style="opacity: 0.9;">Unusual activity detected on your account</div>
        </div>
        
        <div class="content">
            <p style="font-size: 18px; margin-bottom: 24px;"><strong>Hello,</strong></p>
            
            <p>We detected unusual activity on your 24seven account and wanted to alert you immediately.</p>
            
            <div class="warning-box">
                <h4 style="margin: 0 0 16px 0; color: #c05621;">🔍 Activity Details</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li><strong>Location:</strong> Unknown (IP: ***********)</li>
                    <li><strong>Time:</strong> 15 minutes ago</li>
                    <li><strong>Attempts:</strong> 5 failed logins</li>
                    <li><strong>Status:</strong> Account temporarily secured</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin: 32px 0;">
                <a href="https://24seven.site/security/" class="button">🔐 Review Security</a>
            </div>
            
            <p style="background: #f8fafc; padding: 16px; border-radius: 8px; font-size: 14px;">
                <strong>What to do:</strong> If this wasn't you, please change your password immediately. 
                If this was you, you can safely ignore this alert.
            </p>
        </div>
        
        <div class="footer">
            <h3 style="margin: 0 0 8px 0;">24seven Security Team</h3>
            <div style="opacity: 0.8;">Protecting your account 24/7</div>
        </div>
    </div>
</body>
</html>
"""
    
    # Enhanced Team Invitation HTML
    invitation_html = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Invitation - 24seven</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background-color: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 16px; box-shadow: 0 10px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; text-align: center; padding: 40px 30px; }
        .header h1 { margin: 0; font-size: 32px; font-weight: 700; }
        .content { padding: 40px 30px; }
        .info-box { background: linear-gradient(135deg, #ebf8ff 0%, #e6fffa 100%); border-left: 4px solid #3182ce; padding: 20px; border-radius: 8px; margin: 24px 0; }
        .feature-grid { display: grid; gap: 12px; margin: 20px 0; }
        .feature-item { display: flex; align-items: center; padding: 8px 0; }
        .button { display: inline-block; padding: 18px 36px; background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white !important; text-decoration: none; border-radius: 12px; font-weight: 600; font-size: 18px; }
        .footer { background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%); color: #e2e8f0; padding: 40px 30px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="font-size: 48px; margin-bottom: 16px;">🎉</div>
            <h1>You've Been Invited!</h1>
            <div style="opacity: 0.9;">Join Acme Corporation on 24seven</div>
        </div>
        
        <div class="content">
            <p style="font-size: 18px; margin-bottom: 24px;"><strong>Hello there!</strong></p>
            
            <p><strong>John Smith</strong> has invited you to join <strong>Acme Corporation</strong> on 24seven, our AI-powered collaboration platform.</p>
            
            <div class="info-box">
                <h4 style="margin: 0 0 12px 0; color: #2b6cb0;">💬 Personal Message from John Smith</h4>
                <p style="margin: 0; font-style: italic; color: #4a5568;">
                    "We'd love to have you join our team! Your expertise would be a great addition to our AI-powered workflow."
                </p>
            </div>
            
            <div style="text-align: center; margin: 32px 0;">
                <a href="https://24seven.site/invitations/accept/" class="button">🎯 Accept Invitation</a>
            </div>
            
            <div style="background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%); border-left: 4px solid #38a169; padding: 20px; border-radius: 8px; margin: 24px 0;">
                <h4 style="margin: 0 0 16px 0; color: #2f855a;">✨ What You'll Get Access To</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <span style="font-size: 20px; margin-right: 12px;">🤖</span>
                        <div>
                            <strong>AI-Powered Assistants</strong><br>
                            <small style="color: #718096;">Smart AI tools that understand your team's context</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <span style="font-size: 20px; margin-right: 12px;">📚</span>
                        <div>
                            <strong>Shared Knowledge Base</strong><br>
                            <small style="color: #718096;">Centralized repository for team knowledge</small>
                        </div>
                    </div>
                    <div class="feature-item">
                        <span style="font-size: 20px; margin-right: 12px;">🤝</span>
                        <div>
                            <strong>Team Collaboration</strong><br>
                            <small style="color: #718096;">Real-time collaboration features</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <p style="text-align: center; background: #f1f5f9; padding: 16px; border-radius: 8px; font-size: 13px; color: #64748b;">
                This invitation expires in 7 days. Don't miss out on joining your team!
            </p>
        </div>
        
        <div class="footer">
            <h3 style="margin: 0 0 8px 0;">24seven</h3>
            <div style="opacity: 0.8;">AI-Powered Team Collaboration</div>
        </div>
    </div>
</body>
</html>
"""
    
    # Send emails
    emails = [
        {
            'subject': '🎉 Welcome to 24seven - Enhanced Design',
            'html': welcome_html,
            'description': 'Welcome Email'
        },
        {
            'subject': '⚠️ Security Alert - Enhanced Design',
            'html': notification_html,
            'description': 'Security Notification'
        },
        {
            'subject': '🎯 Team Invitation - Enhanced Design',
            'html': invitation_html,
            'description': 'Team Invitation'
        }
    ]
    
    success_count = 0
    for i, email_data in enumerate(emails, 1):
        print(f"\n{i}️⃣ Sending {email_data['description']}...")
        try:
            msg = EmailMessage(
                subject=email_data['subject'],
                body=email_data['html'],
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[recipient]
            )
            msg.content_subtype = 'html'
            msg.send()
            print(f"✅ {email_data['description']} sent successfully!")
            success_count += 1
        except Exception as e:
            print(f"❌ {email_data['description']} failed: {e}")
    
    print("\n" + "=" * 50)
    print("📊 ENHANCED EMAIL SHOWCASE COMPLETE")
    print("=" * 50)
    print(f"✅ Successfully sent {success_count}/{len(emails)} enhanced emails")
    print(f"📧 Check {recipient} inbox for the enhanced designs!")
    
    print("\n🎨 Enhanced Design Features:")
    print("   ✅ Modern gradient backgrounds")
    print("   ✅ Professional typography")
    print("   ✅ Responsive mobile design")
    print("   ✅ Interactive buttons")
    print("   ✅ Visual hierarchy")
    print("   ✅ Consistent branding")
    print("   ✅ Accessibility features")
    
    print("\n🚀 Your email templates are now professional and engaging!")

except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
