"""
Middleware to fix impersonation session issues when accessing assistant settings.
"""
from django.utils.deprecation import MiddlewareMixin
import logging

logger = logging.getLogger(__name__)

class ImpersonateSessionFixMiddleware(MiddlewareMixin):
    """
    Middleware to preserve impersonation sessions when accessing assistant views.
    This prevents logout when clicking settings buttons during impersonation.
    """

    def process_request(self, request):
        """Preserve impersonation session data."""
        # Check if this is an assistant-related request
        if '/assistants/' in request.path or '/assistant/' in request.path:
            # Check for impersonation session
            impersonate_id = request.session.get('_impersonate')
            
            if impersonate_id and request.user.is_authenticated:
                # Ensure impersonation flags are properly set
                if not getattr(request, 'is_impersonate', False):
                    request.is_impersonate = True
                    logger.debug(f"Fixed missing is_impersonate flag for assistant request: {request.path}")
                
                if not getattr(request, 'show_impersonation_ui', False):
                    request.show_impersonation_ui = True
                    logger.debug(f"Fixed missing show_impersonation_ui flag for assistant request: {request.path}")
                
                # Store the impersonation data in request for later use
                request._impersonate_preserved = True
                
        return None

    def process_response(self, request, response):
        """Ensure impersonation session is preserved in response."""
        # Check if we preserved impersonation data
        if getattr(request, '_impersonate_preserved', False):
            # Ensure the session still has the impersonation data
            impersonate_id = request.session.get('_impersonate')
            if impersonate_id:
                # Force save the session to ensure it's not lost
                request.session.save()
                logger.debug(f"Preserved impersonation session for response: {request.path}")
        
        return response
