/**
 * Homepage Optimized JavaScript
 * Lightweight, performance-focused homepage interactions
 */

(function() {
    'use strict';

    // Performance monitoring
    const perfStart = performance.now();

    // Optimized scroll animation with throttling
    let scrollTimeout;
    let isScrolling = false;

    // Intersection Observer for better performance
    let observer;

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    function init() {
        // Apply immediate dark mode
        applyImmediateDarkMode();

        // Hide preloader
        hidePreloader();

        // Initialize optimized scroll animations
        initOptimizedScrollAnimations();

        // Initialize carousel
        initOptimizedCarousel();

        // Initialize search form
        initSearchForm();

        // Performance logging
        const perfEnd = performance.now();
        const initTime = perfEnd - perfStart;
        console.log(`✅ Homepage initialized in ${initTime.toFixed(2)}ms`);

        // Performance grade
        let grade = 'A+';
        if (initTime > 2000) grade = 'F';
        else if (initTime > 1000) grade = 'D';
        else if (initTime > 500) grade = 'C';
        else if (initTime > 200) grade = 'B';
        else if (initTime > 100) grade = 'A';

        console.log(`🏆 Homepage Performance Grade: ${grade}`);

        // Track additional metrics
        setTimeout(() => {
            trackAdditionalMetrics();
        }, 1000);
    }

    function hidePreloader() {
        const preloader = document.getElementById('homepage-preloader');
        if (preloader) {
            // Add a small delay to ensure content is ready
            setTimeout(() => {
                preloader.classList.add('hidden');
                // Remove from DOM after animation
                setTimeout(() => {
                    preloader.remove();
                }, 300);
            }, 100);
        }
    }

    function applyImmediateDarkMode() {
        // Only apply if dark theme is detected or default
        const currentTheme = document.documentElement.getAttribute('data-theme');
        if (currentTheme !== 'dark' && currentTheme !== null) return;

        // Set dark theme if not already set
        if (!currentTheme) {
            document.documentElement.setAttribute('data-theme', 'dark');
        }

        // Apply minimal immediate styles
        document.body.style.backgroundColor = '#121212';
        document.body.style.transition = 'none';

        // Apply to hero section immediately
        const heroSection = document.querySelector('.hero-section, .bg-primary');
        if (heroSection) {
            heroSection.style.backgroundColor = '#121212';
            heroSection.style.backgroundImage =
                'radial-gradient(circle at 25% 25%, rgba(0, 102, 255, 0.2) 0%, transparent 50%), ' +
                'radial-gradient(circle at 75% 75%, rgba(0, 60, 180, 0.2) 0%, transparent 50%), ' +
                'linear-gradient(to bottom, #121212, #0a0a0a)';
            heroSection.style.color = '#ffffff';
        }
    }

    function initOptimizedScrollAnimations() {
        // Use Intersection Observer for better performance
        if ('IntersectionObserver' in window) {
            observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        // Stop observing once visible
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '50px'
            });

            // Observe cards that need animation (but don't break existing functionality)
            const animatedCards = document.querySelectorAll('.card:not(.search-form-card)');
            animatedCards.forEach(card => {
                // Only add animation if card doesn't already have custom styling
                if (!card.style.opacity && !card.style.transform) {
                    card.classList.add('card-animate');
                    observer.observe(card);
                }
            });
        } else {
            // Fallback for older browsers
            const cards = document.querySelectorAll('.card:not(.search-form-card)');
            cards.forEach(card => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            });
        }
    }

    function initOptimizedCarousel() {
        const carousel = document.querySelector('.company-logo-carousel');
        const carouselContainer = document.querySelector('.company-logo-carousel-container');

        if (!carousel || !carouselContainer) return;

        // Mark as loaded (but ensure it's visible)
        carouselContainer.classList.add('loaded');
        carouselContainer.style.opacity = '1';
        carouselContainer.style.visibility = 'visible';

        // Optimized hover effects with passive listeners
        let isHovered = false;

        carouselContainer.addEventListener('mouseenter', () => {
            if (!isHovered) {
                isHovered = true;
                carousel.style.animationPlayState = 'paused';
            }
        }, { passive: true });

        carouselContainer.addEventListener('mouseleave', () => {
            if (isHovered) {
                isHovered = false;
                carousel.style.animationPlayState = 'running';
            }
        }, { passive: true });

        // Pause animation when tab is not visible (performance optimization)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                carousel.style.animationPlayState = 'paused';
            } else if (!isHovered) {
                carousel.style.animationPlayState = 'running';
            }
        });
    }

    function initSearchForm() {
        const searchForm = document.querySelector('.search-form-card form');
        if (!searchForm) return;

        // Add loading state on submit
        searchForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="bi bi-search"></i> Searching...';
            }
        });

        // Auto-focus search input on desktop
        if (window.innerWidth > 768) {
            const searchInput = searchForm.querySelector('input[name="q"]');
            if (searchInput) {
                // Delay focus to avoid interfering with page load
                setTimeout(() => {
                    searchInput.focus();
                }, 1000);
            }
        }
    }

    // Optimized theme change handler
    function handleThemeChange(isDark) {
        if (isDark) {
            applyImmediateDarkMode();
        } else {
            // Light mode styles
            document.body.style.backgroundColor = '#ffffff';

            const heroSection = document.querySelector('.hero-section, .bg-primary');
            if (heroSection) {
                heroSection.style.backgroundColor = '#0066ff';
                heroSection.style.backgroundImage = 'linear-gradient(135deg, #0066ff 0%, #0052cc 100%)';
                heroSection.style.color = '#ffffff';
            }

            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.style.backgroundColor = '#ffffff';
                card.style.borderColor = 'rgba(0, 0, 0, 0.125)';
                card.style.color = '#000000';
            });
        }
    }

    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        handleThemeChange(e.detail.theme === 'dark');
    });

    // Check initial theme
    const currentTheme = document.documentElement.getAttribute('data-theme');
    if (currentTheme === 'dark') {
        handleThemeChange(true);
    }

    // Cleanup function
    window.addEventListener('beforeunload', function() {
        if (observer) {
            observer.disconnect();
        }
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
    });

    // Performance optimization: Reduce layout thrashing
    let resizeTimeout;
    window.addEventListener('resize', function() {
        if (resizeTimeout) {
            clearTimeout(resizeTimeout);
        }
        resizeTimeout = setTimeout(function() {
            // Handle responsive changes if needed
            const searchInput = document.querySelector('input[name="q"]');
            if (searchInput && window.innerWidth <= 768) {
                searchInput.blur(); // Remove focus on mobile
            }
        }, 250);
    }, { passive: true });

    // Preload critical images
    function preloadCriticalImages() {
        const criticalImages = document.querySelectorAll('.company-logo[src]');
        criticalImages.forEach(img => {
            if (img.src && !img.complete) {
                const preloadImg = new Image();
                preloadImg.src = img.src;
            }
        });
    }

    // Start preloading after initial render
    setTimeout(preloadCriticalImages, 100);

    function trackAdditionalMetrics() {
        // Track paint metrics
        if ('getEntriesByType' in performance) {
            const paintEntries = performance.getEntriesByType('paint');
            paintEntries.forEach(entry => {
                if (entry.name === 'first-paint') {
                    console.log(`🎨 First Paint: ${entry.startTime.toFixed(2)}ms`);
                }
                if (entry.name === 'first-contentful-paint') {
                    console.log(`🎨 First Contentful Paint: ${entry.startTime.toFixed(2)}ms`);
                }
            });

            // Track resource loading
            const resources = performance.getEntriesByType('resource');
            let cssCount = 0;
            let jsCount = 0;

            resources.forEach(resource => {
                if (resource.name.includes('.css')) cssCount++;
                if (resource.name.includes('.js')) jsCount++;
            });

            console.log(`📄 CSS Files Loaded: ${cssCount}`);
            console.log(`📜 JS Files Loaded: ${jsCount}`);
        }

        // Memory usage (if available)
        if ('memory' in performance) {
            const memory = performance.memory;
            console.log(`💾 Memory Usage: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
        }
    }

    // Export for debugging
    window.homepageOptimized = {
        init,
        applyImmediateDarkMode,
        handleThemeChange,
        trackAdditionalMetrics
    };

})();
