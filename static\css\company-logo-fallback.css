/**
 * Company Logo Fallback Styles
 * Styles for company logo placeholders and fallback handling
 */

/* Base placeholder styles */
.logo-fallback-placeholder {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    min-width: 120px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* Dark mode placeholder styles */
html[data-theme="dark"] .logo-fallback-placeholder,
.dark-mode .logo-fallback-placeholder {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    border-color: #4a5568;
    color: #e2e8f0;
}

/* Placeholder icon */
.logo-fallback-placeholder i {
    font-size: 2.5rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

html[data-theme="dark"] .logo-fallback-placeholder i,
.dark-mode .logo-fallback-placeholder i {
    color: #a0aec0;
}

/* Placeholder text */
.logo-fallback-placeholder span {
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    max-width: 90%;
    word-wrap: break-word;
    color: #6c757d;
}

html[data-theme="dark"] .logo-fallback-placeholder span,
.dark-mode .logo-fallback-placeholder span {
    color: #a0aec0;
}

/* Hover effects */
.logo-fallback-placeholder:hover {
    border-color: #cf2e2e;
    background: linear-gradient(135deg, #f7f7f7 0%, #e5e5e5 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.15);
}

html[data-theme="dark"] .logo-fallback-placeholder:hover,
.dark-mode .logo-fallback-placeholder:hover {
    border-color: #cf2e2e;
    background: linear-gradient(135deg, #252638 0%, #1e1f2e 100%);
}

.logo-fallback-placeholder:hover i {
    color: #cf2e2e;
    transform: scale(1.1);
}

html[data-theme="dark"] .logo-fallback-placeholder:hover i,
.dark-mode .logo-fallback-placeholder:hover i {
    color: #cf2e2e;
}

/* Carousel specific styles */
.company-logo-carousel .logo-fallback-placeholder,
.featured-carousel-items .logo-fallback-placeholder {
    height: 120px;
    width: 120px;
    min-height: 120px;
    min-width: 120px;
    border-radius: 12px;
    margin: 0 auto;
}

.home-carousel-logo .logo-fallback-placeholder {
    height: 100%;
    width: 100%;
    border-radius: 12px;
}

/* Directory card specific styles */
.directory-card .logo-fallback-placeholder,
.company-card .logo-fallback-placeholder {
    height: 80px;
    width: 80px;
    min-height: 80px;
    min-width: 80px;
    border-radius: 8px;
}

.directory-card .logo-fallback-placeholder i,
.company-card .logo-fallback-placeholder i {
    font-size: 2rem;
}

.directory-card .logo-fallback-placeholder span,
.company-card .logo-fallback-placeholder span {
    font-size: 0.75rem;
}

/* Featured carousel specific styles */
.featured-carousel-item .logo-fallback-placeholder {
    height: 150px;
    width: 150px;
    min-height: 150px;
    min-width: 150px;
    border-radius: 16px;
    border-width: 3px;
}

.featured-carousel-item .logo-fallback-placeholder i {
    font-size: 3rem;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .logo-fallback-placeholder {
        min-height: 100px;
        min-width: 100px;
    }
    
    .logo-fallback-placeholder i {
        font-size: 2rem;
    }
    
    .logo-fallback-placeholder span {
        font-size: 0.75rem;
    }
    
    .company-logo-carousel .logo-fallback-placeholder,
    .featured-carousel-items .logo-fallback-placeholder {
        height: 100px;
        width: 100px;
        min-height: 100px;
        min-width: 100px;
    }
    
    .featured-carousel-item .logo-fallback-placeholder {
        height: 120px;
        width: 120px;
        min-height: 120px;
        min-width: 120px;
    }
    
    .featured-carousel-item .logo-fallback-placeholder i {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .logo-fallback-placeholder {
        min-height: 80px;
        min-width: 80px;
    }
    
    .logo-fallback-placeholder i {
        font-size: 1.75rem;
    }
    
    .logo-fallback-placeholder span {
        font-size: 0.7rem;
    }
    
    .company-logo-carousel .logo-fallback-placeholder,
    .featured-carousel-items .logo-fallback-placeholder {
        height: 80px;
        width: 80px;
        min-height: 80px;
        min-width: 80px;
    }
    
    .featured-carousel-item .logo-fallback-placeholder {
        height: 100px;
        width: 100px;
        min-height: 100px;
        min-width: 100px;
    }
    
    .featured-carousel-item .logo-fallback-placeholder i {
        font-size: 2rem;
    }
}

/* Loading animation for placeholders */
.logo-fallback-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shimmer 2s infinite;
}

html[data-theme="dark"] .logo-fallback-placeholder::before,
.dark-mode .logo-fallback-placeholder::before {
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Ensure placeholders don't break layout */
.logo-container .logo-fallback-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

/* Fix for carousel items */
.company-logo-item .logo-fallback-placeholder,
.featured-carousel-item .logo-fallback-placeholder {
    position: relative;
}

/* Accessibility improvements */
.logo-fallback-placeholder {
    role: img;
    aria-label: "Company logo placeholder";
}

.logo-fallback-placeholder:focus {
    outline: 2px solid #cf2e2e;
    outline-offset: 2px;
}

html[data-theme="dark"] .logo-fallback-placeholder:focus,
.dark-mode .logo-fallback-placeholder:focus {
    outline-color: #cf2e2e;
}

/* Print styles */
@media print {
    .logo-fallback-placeholder {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
        color: #000 !important;
    }
    
    .logo-fallback-placeholder::before {
        display: none;
    }
}
