"""
Utility functions for superadmin dashboard analytics and notifications.
"""

from django.utils import timezone
from django.db.models import Count, Avg, Sum, Q
from django.contrib.auth import get_user_model
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

def calculate_platform_analytics():
    """
    Calculate comprehensive platform analytics with error handling.
    Returns a dictionary with all analytics data.
    """
    try:
        from accounts.models import Company
        from assistants.models import Assistant, Interaction

        now = timezone.now()
        last_30_days = now - timedelta(days=30)
        last_7_days = now - timedelta(days=7)

        analytics = {}

        # User analytics
        User = get_user_model()
        try:
            analytics.update({
                'total_users': User.objects.count(),
                'active_users_30_days': User.objects.filter(last_login__gte=last_30_days).count(),
                'new_users_30_days': User.objects.filter(date_joined__gte=last_30_days).count(),
            })
        except Exception as e:
            logger.error(f"Error calculating user analytics: {e}")
            analytics.update({
                'total_users': 0,
                'active_users_30_days': 0,
                'new_users_30_days': 0,
            })

        # Company analytics
        try:
            analytics.update({
                'total_companies': Company.objects.count(),
                'active_companies': Company.objects.filter(is_active=True).count(),
                'featured_companies': Company.objects.filter(is_featured=True).count(),
                'new_companies_30_days': Company.objects.filter(created_at__gte=last_30_days).count(),
            })
        except Exception as e:
            logger.error(f"Error calculating company analytics: {e}")
            analytics.update({
                'total_companies': 0,
                'active_companies': 0,
                'featured_companies': 0,
                'new_companies_30_days': 0,
            })

        # Assistant analytics
        try:
            analytics.update({
                'total_assistants': Assistant.objects.count(),
                'active_assistants': Assistant.objects.filter(is_active=True).count(),
                'public_assistants': Assistant.objects.filter(is_public=True, is_active=True).count(),
                'featured_assistants': Assistant.objects.filter(is_featured=True).count(),
                'new_assistants_30_days': Assistant.objects.filter(created_at__gte=last_30_days).count(),
            })
        except Exception as e:
            logger.error(f"Error calculating assistant analytics: {e}")
            analytics.update({
                'total_assistants': 0,
                'active_assistants': 0,
                'public_assistants': 0,
                'featured_assistants': 0,
                'new_assistants_30_days': 0,
            })

        # Interaction analytics
        try:
            interaction_stats = Interaction.objects.aggregate(
                total=Count('id'),
                total_30_days=Count('id', filter=Q(created_at__gte=last_30_days)),
                total_7_days=Count('id', filter=Q(created_at__gte=last_7_days)),
                avg_rating=Avg('rating'),
                total_tokens=Sum('token_count')
            )

            analytics.update({
                'total_interactions': interaction_stats['total'] or 0,
                'interactions_30_days': interaction_stats['total_30_days'] or 0,
                'interactions_7_days': interaction_stats['total_7_days'] or 0,
                'average_rating': interaction_stats['avg_rating'],
                'total_tokens_used': interaction_stats['total_tokens'] or 0,
            })

            # Calculate growth rate
            if analytics['total_interactions'] > 0:
                analytics['interaction_growth_rate'] = round(
                    (analytics['interactions_30_days'] / analytics['total_interactions']) * 100, 1
                )
            else:
                analytics['interaction_growth_rate'] = 0

        except Exception as e:
            logger.error(f"Error calculating interaction analytics: {e}")
            analytics.update({
                'total_interactions': 0,
                'interactions_30_days': 0,
                'interactions_7_days': 0,
                'average_rating': None,
                'total_tokens_used': 0,
                'interaction_growth_rate': 0,
            })

        # Top performing assistants
        try:
            top_assistants_qs = Assistant.objects.filter(
                is_active=True
            ).annotate(
                interaction_count=Count('interactions')
            ).order_by('-interaction_count')[:5]

            analytics['top_assistants'] = [
                {
                    'id': assistant.id,
                    'name': assistant.name,
                    'company_name': assistant.company.name if assistant.company else 'No Company',
                    'interaction_count': assistant.interaction_count,
                    'is_featured': assistant.is_featured,
                }
                for assistant in top_assistants_qs
            ]
        except Exception as e:
            logger.error(f"Error calculating top assistants: {e}")
            analytics['top_assistants'] = []

        # Recent activity
        try:
            recent_companies_qs = Company.objects.filter(
                created_at__gte=last_7_days
            ).order_by('-created_at')[:5]

            analytics['recent_companies'] = [
                {
                    'id': company.id,
                    'name': company.name,
                    'owner_username': company.owner.username if company.owner else 'No Owner',
                    'entity_type': company.entity_type,
                    'is_active': company.is_active,
                    'created_at': company.created_at,
                }
                for company in recent_companies_qs
            ]

            recent_assistants_qs = Assistant.objects.filter(
                created_at__gte=last_7_days
            ).order_by('-created_at')[:5]

            analytics['recent_assistants'] = [
                {
                    'id': assistant.id,
                    'name': assistant.name,
                    'company_name': assistant.company.name if assistant.company else 'No Company',
                    'is_active': assistant.is_active,
                    'is_public': assistant.is_public,
                    'created_at': assistant.created_at,
                }
                for assistant in recent_assistants_qs
            ]
        except Exception as e:
            logger.error(f"Error calculating recent activity: {e}")
            analytics['recent_companies'] = []
            analytics['recent_assistants'] = []

        return analytics

    except Exception as e:
        logger.error(f"Critical error in calculate_platform_analytics: {e}")
        # Return safe defaults
        return {
            'total_users': 0,
            'active_users_30_days': 0,
            'new_users_30_days': 0,
            'total_companies': 0,
            'active_companies': 0,
            'featured_companies': 0,
            'new_companies_30_days': 0,
            'total_assistants': 0,
            'active_assistants': 0,
            'public_assistants': 0,
            'featured_assistants': 0,
            'new_assistants_30_days': 0,
            'total_interactions': 0,
            'interactions_30_days': 0,
            'interactions_7_days': 0,
            'average_rating': None,
            'total_tokens_used': 0,
            'interaction_growth_rate': 0,
            'top_assistants': [],
            'recent_companies': [],
            'recent_assistants': [],
        }

def calculate_pending_notifications():
    """
    Calculate all pending notifications with error handling.
    Returns a dictionary with notification counts.
    """
    try:
        from accounts.models import Company
        from assistants.models import Assistant

        notifications = {}

        # Pending approvals and changes
        try:
            notifications.update({
                'pending_company_approvals': Company.objects.filter(is_active=False, entity_type='company').count(),
                'pending_community_approvals': Company.objects.filter(is_active=False, entity_type='community').count(),
                'pending_company_tier_changes': Company.objects.filter(tier_change_pending=True).count(),
                'pending_assistant_tier_changes': Assistant.objects.filter(tier_change_pending=True).count(),
                'pending_company_featured_requests': Company.objects.filter(featured_request_pending=True).count(),
                'pending_assistant_featured_requests': Assistant.objects.filter(featured_request_pending=True).count(),
            })
        except Exception as e:
            logger.error(f"Error calculating pending notifications: {e}")
            notifications.update({
                'pending_company_approvals': 0,
                'pending_community_approvals': 0,
                'pending_company_tier_changes': 0,
                'pending_assistant_tier_changes': 0,
                'pending_company_featured_requests': 0,
                'pending_assistant_featured_requests': 0,
            })

        # Calculate total
        notifications['total_pending_notifications'] = sum([
            notifications['pending_company_approvals'],
            notifications['pending_community_approvals'],
            notifications['pending_company_tier_changes'],
            notifications['pending_assistant_tier_changes'],
            notifications['pending_company_featured_requests'],
            notifications['pending_assistant_featured_requests'],
        ])

        # Additional counts
        try:
            notifications['community_assistants_count'] = Assistant.objects.filter(
                assistant_type=Assistant.TYPE_COMMUNITY
            ).count()
        except Exception as e:
            logger.error(f"Error calculating community assistants count: {e}")
            notifications['community_assistants_count'] = 0

        return notifications

    except Exception as e:
        logger.error(f"Critical error in calculate_pending_notifications: {e}")
        return {
            'pending_company_approvals': 0,
            'pending_community_approvals': 0,
            'pending_company_tier_changes': 0,
            'pending_assistant_tier_changes': 0,
            'pending_company_featured_requests': 0,
            'pending_assistant_featured_requests': 0,
            'total_pending_notifications': 0,
            'community_assistants_count': 0,
        }

def get_system_info():
    """
    Get system information with error handling.
    """
    import sys
    import django
    from django.db import connection

    try:
        # Database information
        db_engine = connection.vendor
        if db_engine == 'sqlite':
            db_name = 'SQLite'
        elif db_engine == 'postgresql':
            db_name = 'PostgreSQL'
        elif db_engine == 'mysql':
            db_name = 'MySQL'
        else:
            db_name = db_engine.capitalize()

        return {
            'django_version': django.get_version(),
            'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            'database_engine': db_name,
        }
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        return {
            'django_version': 'Unknown',
            'python_version': 'Unknown',
            'database_engine': 'Unknown',
        }
