/**
 * Assistant Management Interface Improvements
 * Enhanced styling for the manage assistants page
 */

/* ===== FORCE OVERRIDE EXISTING STYLES ===== */

/* Override any existing button group styles */
.list-group-item .btn-group,
.list-group-item .btn-group-sm,
.directory-card .btn-group,
.directory-card .btn-group-sm {
    display: none !important;
}

/* ===== ASSISTANT CARD IMPROVEMENTS ===== */

/* Enhanced assistant card styling */
.directory-card {
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 12px !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%) !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    margin-bottom: 1rem !important;
    overflow: hidden !important;
    position: relative !important;
}

.directory-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    border-color: rgba(66, 153, 225, 0.3) !important;
}

/* ===== BUTTON GROUP IMPROVEMENTS ===== */

/* Container for action buttons */
.assistant-actions-container {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.375rem !important;
    align-items: stretch !important;
    min-width: 140px !important;
    max-width: 160px !important;
    background: rgba(255, 255, 255, 0.02) !important;
    border-radius: 8px !important;
    padding: 0.5rem !important;
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    position: relative !important;
    z-index: 10 !important;
}

/* Force display of our new button container */
.directory-card .assistant-actions-container {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Secondary action buttons (bottom row) */
.assistant-secondary-actions {
    display: flex !important;
    gap: 0.25rem !important;
    flex-wrap: wrap !important;
    justify-content: flex-end !important;
    align-items: center !important;
}

/* Primary action buttons with visual separation */
.assistant-primary-actions {
    display: flex !important;
    gap: 0.25rem !important;
    justify-content: flex-end !important;
    align-items: center !important;
    position: relative !important;
    padding-bottom: 0.25rem !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08) !important;
    margin-bottom: 0.25rem !important;
}

/* Button hover effects for the container */
.assistant-actions-container:hover {
    background: rgba(255, 255, 255, 0.04) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-1px) !important;
    transition: all 0.2s ease !important;
}

/* Enhanced button styling */
.assistant-action-btn,
.directory-card .assistant-action-btn,
.list-group-item .assistant-action-btn {
    border-radius: 6px !important;
    padding: 0.375rem 0.5rem !important;
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border: 1px solid transparent !important;
    position: relative !important;
    overflow: hidden !important;
    min-width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    cursor: pointer !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    z-index: 5 !important;
}

/* Primary action button (Chat) */
.assistant-action-btn.btn-primary {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
    color: white !important;
    box-shadow: 0 1px 3px rgba(66, 153, 225, 0.3) !important;
    flex: 1 !important;
    min-width: 60px !important;
    max-width: 80px !important;
}

.assistant-action-btn.btn-primary:hover {
    background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 6px rgba(66, 153, 225, 0.4) !important;
}

/* Settings button */
.assistant-action-btn.btn-outline-secondary {
    background: rgba(113, 128, 150, 0.1) !important;
    border-color: rgba(113, 128, 150, 0.3) !important;
    color: #718096 !important;
}

.assistant-action-btn.btn-outline-secondary:hover {
    background: rgba(113, 128, 150, 0.2) !important;
    border-color: rgba(113, 128, 150, 0.5) !important;
    color: #4a5568 !important;
    transform: translateY(-1px) !important;
}

/* Analytics button */
.assistant-action-btn.btn-outline-info {
    background: rgba(56, 178, 172, 0.1) !important;
    border-color: rgba(56, 178, 172, 0.3) !important;
    color: #38b2ac !important;
}

.assistant-action-btn.btn-outline-info:hover {
    background: rgba(56, 178, 172, 0.2) !important;
    border-color: rgba(56, 178, 172, 0.5) !important;
    color: #2c7a7b !important;
    transform: translateY(-1px) !important;
}

/* Public chat button */
.assistant-action-btn.btn-outline-success {
    background: rgba(72, 187, 120, 0.1) !important;
    border-color: rgba(72, 187, 120, 0.3) !important;
    color: #48bb78 !important;
}

.assistant-action-btn.btn-outline-success:hover {
    background: rgba(72, 187, 120, 0.2) !important;
    border-color: rgba(72, 187, 120, 0.5) !important;
    color: #38a169 !important;
    transform: translateY(-1px) !important;
}

/* Rate button */
.assistant-action-btn.btn-outline-warning {
    background: rgba(237, 137, 54, 0.1) !important;
    border-color: rgba(237, 137, 54, 0.3) !important;
    color: #ed8936 !important;
}

.assistant-action-btn.btn-outline-warning:hover {
    background: rgba(237, 137, 54, 0.2) !important;
    border-color: rgba(237, 137, 54, 0.5) !important;
    color: #dd6b20 !important;
    transform: translateY(-1px) !important;
}

/* Delete button */
.assistant-action-btn.btn-outline-danger {
    background: rgba(245, 101, 101, 0.1) !important;
    border-color: rgba(245, 101, 101, 0.3) !important;
    color: #f56565 !important;
}

.assistant-action-btn.btn-outline-danger:hover {
    background: rgba(245, 101, 101, 0.2) !important;
    border-color: rgba(245, 101, 101, 0.5) !important;
    color: #e53e3e !important;
    transform: translateY(-1px) !important;
}

/* ===== DROPDOWN IMPROVEMENTS ===== */

/* Folder assignment dropdown */
.assistant-folder-dropdown {
    position: relative !important;
}

.assistant-folder-dropdown .dropdown-toggle {
    background: rgba(113, 128, 150, 0.1) !important;
    border: 1px solid rgba(113, 128, 150, 0.3) !important;
    color: #718096 !important;
    border-radius: 6px !important;
    padding: 0.25rem !important;
    min-width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.75rem !important;
}

.assistant-folder-dropdown .dropdown-toggle:hover {
    background: rgba(113, 128, 150, 0.2) !important;
    border-color: rgba(113, 128, 150, 0.5) !important;
    transform: translateY(-1px) !important;
}

.assistant-folder-dropdown .dropdown-menu {
    border-radius: 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: rgba(26, 32, 44, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
}

.assistant-folder-dropdown .dropdown-item {
    color: #e2e8f0 !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.2s ease !important;
}

.assistant-folder-dropdown .dropdown-item:hover {
    background: rgba(66, 153, 225, 0.2) !important;
    color: #ffffff !important;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */

/* Mobile responsiveness */
@media (max-width: 768px) {
    .assistant-actions-container {
        min-width: 100% !important;
        margin-top: 1rem !important;
    }

    .assistant-primary-actions,
    .assistant-secondary-actions {
        justify-content: center !important;
    }

    .assistant-action-btn {
        flex: 1 !important;
        min-width: 40px !important;
    }
}

/* ===== ICON IMPROVEMENTS ===== */

.assistant-action-btn i {
    font-size: 0.875rem !important;
    line-height: 1 !important;
}

/* Add subtle animation to icons */
.assistant-action-btn:hover i {
    transform: scale(1.05) !important;
    transition: transform 0.15s ease !important;
}

/* Compact button group styling */
.assistant-primary-actions .assistant-action-btn:not(.btn-primary) {
    min-width: 28px !important;
    height: 28px !important;
    padding: 0.25rem !important;
}

.assistant-secondary-actions .assistant-action-btn {
    min-width: 28px !important;
    height: 28px !important;
    padding: 0.25rem !important;
}

/* Make icons slightly smaller for compact buttons */
.assistant-primary-actions .assistant-action-btn:not(.btn-primary) i,
.assistant-secondary-actions .assistant-action-btn i {
    font-size: 0.75rem !important;
}

/* ===== TOOLTIP IMPROVEMENTS ===== */

.assistant-action-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 0.25rem;
}

/* ===== LOADING STATES ===== */

.assistant-action-btn.loading {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

.assistant-action-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== FOCUS STATES ===== */

.assistant-action-btn:focus {
    outline: 2px solid rgba(66, 153, 225, 0.5) !important;
    outline-offset: 2px !important;
}

/* ===== BADGE IMPROVEMENTS ===== */

.tier-badge {
    border-radius: 6px !important;
    font-size: 0.75rem !important;
    font-weight: 600 !important;
    padding: 0.25rem 0.5rem !important;
}

/* ===== CARD CONTENT IMPROVEMENTS ===== */

.assistant-name a {
    color: #e2e8f0 !important;
    font-weight: 600 !important;
    font-size: 1.125rem !important;
    text-decoration: none !important;
    transition: color 0.2s ease !important;
}

.assistant-name a:hover {
    color: #4299e1 !important;
}

.assistant-meta {
    color: #a0aec0 !important;
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
}

.assistant-description {
    color: #cbd5e0 !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
}

/* ===== DARK MODE SUPPORT ===== */

[data-theme="dark"] .directory-card {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.8) 0%, rgba(26, 32, 44, 0.9) 100%) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .directory-card:hover {
    background: linear-gradient(135deg, rgba(45, 55, 72, 0.9) 0%, rgba(26, 32, 44, 1) 100%) !important;
    border-color: rgba(66, 153, 225, 0.4) !important;
}

[data-theme="dark"] .assistant-name a {
    color: #f7fafc !important;
}

[data-theme="dark"] .assistant-meta {
    color: #cbd5e0 !important;
}

[data-theme="dark"] .assistant-description {
    color: #e2e8f0 !important;
}

/* Dark mode button adjustments */
[data-theme="dark"] .assistant-action-btn.btn-outline-secondary {
    background: rgba(160, 174, 192, 0.1) !important;
    border-color: rgba(160, 174, 192, 0.3) !important;
    color: #cbd5e0 !important;
}

[data-theme="dark"] .assistant-action-btn.btn-outline-secondary:hover {
    background: rgba(160, 174, 192, 0.2) !important;
    border-color: rgba(160, 174, 192, 0.5) !important;
    color: #f7fafc !important;
}

[data-theme="dark"] .assistant-folder-dropdown .dropdown-toggle {
    background: rgba(160, 174, 192, 0.1) !important;
    border-color: rgba(160, 174, 192, 0.3) !important;
    color: #cbd5e0 !important;
}

[data-theme="dark"] .assistant-folder-dropdown .dropdown-toggle:hover {
    background: rgba(160, 174, 192, 0.2) !important;
    border-color: rgba(160, 174, 192, 0.5) !important;
    color: #f7fafc !important;
}

[data-theme="dark"] .assistant-folder-dropdown .dropdown-menu {
    background: rgba(26, 32, 44, 0.95) !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .assistant-folder-dropdown .dropdown-item {
    color: #e2e8f0 !important;
}

[data-theme="dark"] .assistant-folder-dropdown .dropdown-item:hover {
    background: rgba(66, 153, 225, 0.2) !important;
    color: #ffffff !important;
}

/* Light mode adjustments */
[data-theme="light"] .assistant-name a {
    color: #2d3748 !important;
}

[data-theme="light"] .assistant-meta {
    color: #4a5568 !important;
}

[data-theme="light"] .assistant-description {
    color: #2d3748 !important;
}

[data-theme="light"] .directory-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(247, 250, 252, 0.95) 100%) !important;
    border-color: rgba(0, 0, 0, 0.1) !important;
}

[data-theme="light"] .directory-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(247, 250, 252, 1) 100%) !important;
    border-color: rgba(66, 153, 225, 0.3) !important;
}
