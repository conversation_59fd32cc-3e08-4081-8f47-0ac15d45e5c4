#!/usr/bin/env python
"""
Check the latest company data to verify persistence.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company, CompanyInformation

def check_latest_company():
    """Check the latest company's data"""
    print("🔍 CHECKING LATEST COMPANY DATA")
    print("=" * 50)
    
    # Get the latest company
    try:
        company = Company.objects.latest('created_at')
        print(f"📋 Latest Company: {company.name} (ID: {company.id})")
        print(f"   Owner: {company.owner.username}")
        print(f"   Entity Type: {company.entity_type}")
        print(f"   Slug: {company.slug}")
        print(f"   Created: {company.created_at}")
        
        # Get company information
        try:
            info = CompanyInformation.objects.get(company=company)
            print(f"\n📊 Company Information:")
            print(f"   Mission: '{info.mission}'")
            print(f"   Description: '{info.description}'")
            print(f"   Website: '{info.website}'")
            print(f"   Contact Email: '{info.contact_email}'")
            print(f"   Contact Phone: '{info.contact_phone}'")
            print(f"   Timezone: '{info.timezone}'")
            print(f"   Language: '{info.language}'")
            print(f"   Industry: '{info.industry}'")
            print(f"   Size: '{info.size}'")
            print(f"   City: '{info.city}'")
            print(f"   Country: '{info.country}'")
            print(f"   Founded: {info.founded}")
            print(f"   Address Line 1: '{info.address_line1}'")
            print(f"   Address Line 2: '{info.address_line2}'")
            print(f"   Postal Code: '{info.postal_code}'")
            print(f"   LinkedIn: '{info.linkedin}'")
            print(f"   Twitter: '{info.twitter}'")
            print(f"   Facebook: '{info.facebook}'")
            print(f"   Custom Domain: '{info.custom_domain}'")
            print(f"   List in Directory: {info.list_in_directory}")
            
            # Check if data was actually saved (not empty)
            non_empty_fields = []
            if info.mission: non_empty_fields.append('mission')
            if info.description: non_empty_fields.append('description')
            if info.website: non_empty_fields.append('website')
            if info.contact_email: non_empty_fields.append('contact_email')
            if info.contact_phone: non_empty_fields.append('contact_phone')
            if info.industry: non_empty_fields.append('industry')
            if info.city: non_empty_fields.append('city')
            if info.country: non_empty_fields.append('country')
            
            print(f"\n✅ Non-empty fields: {', '.join(non_empty_fields)}")
            print(f"📈 Data persistence: {len(non_empty_fields)}/8 key fields have data")
            
            if len(non_empty_fields) >= 4:
                print("🎉 GOOD: Company data persistence is working!")
            else:
                print("⚠️ WARNING: Limited data was saved")
                
        except CompanyInformation.DoesNotExist:
            print("❌ No CompanyInformation found")
            
    except Company.DoesNotExist:
        print("❌ No companies found")

if __name__ == "__main__":
    check_latest_company()
