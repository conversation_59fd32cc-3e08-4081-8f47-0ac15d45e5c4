from django.core.management.base import BaseCommand
from django.utils.text import slugify
from django.db import transaction
from accounts.models import Company


class Command(BaseCommand):
    help = 'Update company slugs to include unique identifiers for better uniqueness'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update even if slug already contains unique identifier',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        self.stdout.write(
            self.style.SUCCESS('Starting company slug update process...')
        )
        
        # Get all companies
        companies = Company.objects.all()
        
        updated_count = 0
        skipped_count = 0
        error_count = 0
        
        for company in companies:
            try:
                # Generate new slug format with unique identifier
                import uuid
                base_slug = slugify(company.name)
                unique_id = uuid.uuid4().hex[:8]
                new_slug_base = f'{base_slug}-{unique_id}'
                
                # Check if slug already follows the new format with unique ID (unless force is used)
                if not force:
                    # Check if the slug has the pattern: company-name-uniqueid
                    slug_parts = company.slug.split('-')
                    # If slug has multiple parts and last part is 8 characters, assume it has unique ID
                    if len(slug_parts) >= 2 and len(slug_parts[-1]) == 8:
                        # Check if last part looks like a hex string
                        try:
                            int(slug_parts[-1], 16)  # Try to parse as hex
                            self.stdout.write(
                                f'Skipping {company.name} (ID: {company.id}) - slug already has unique identifier'
                            )
                            skipped_count += 1
                            continue
                        except ValueError:
                            # Last part is not hex, so it doesn't have unique ID
                            pass
                
                # Find a unique slug (very unlikely to conflict with UUID, but just in case)
                new_slug = new_slug_base
                num = 1
                while Company.objects.filter(slug=new_slug).exclude(pk=company.pk).exists():
                    new_slug = f'{new_slug_base}-{num}'
                    num += 1
                
                old_slug = company.slug
                
                if dry_run:
                    self.stdout.write(
                        f'Would update: {company.name} (ID: {company.id})'
                    )
                    self.stdout.write(f'  Old slug: {old_slug}')
                    self.stdout.write(f'  New slug: {new_slug}')
                    self.stdout.write('')
                    updated_count += 1
                else:
                    # Update the slug
                    with transaction.atomic():
                        company.slug = new_slug
                        company.save(update_fields=['slug'])
                    
                    self.stdout.write(
                        self.style.SUCCESS(f'Updated: {company.name} (ID: {company.id})')
                    )
                    self.stdout.write(f'  Old slug: {old_slug}')
                    self.stdout.write(f'  New slug: {new_slug}')
                    self.stdout.write('')
                    updated_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error updating {company.name} (ID: {company.id}): {str(e)}')
                )
                error_count += 1
        
        # Summary
        self.stdout.write(self.style.SUCCESS('=== Summary ==='))
        if dry_run:
            self.stdout.write(f'Companies that would be updated: {updated_count}')
        else:
            self.stdout.write(f'Companies updated: {updated_count}')
        self.stdout.write(f'Companies skipped: {skipped_count}')
        self.stdout.write(f'Errors encountered: {error_count}')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('This was a dry run. No changes were made.')
            )
            self.stdout.write(
                'Run without --dry-run to apply the changes.'
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('Company slug update completed!')
            )
