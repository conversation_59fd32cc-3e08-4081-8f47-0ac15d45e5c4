{% load static account_tags rating_tags %}

<!-- Unified Header Component -->
<nav class="navbar navbar-expand-lg navbar-light sticky-top unified-header">
    <div class="container">
        <!-- Brand Section -->
        <div class="navbar-brand-section d-flex align-items-center">
            <!-- Home/Logo -->
            <a class="navbar-brand me-3" href="{% url 'home' %}">
                <img src="{% static 'img/nup-logo.jpg' %}"
                     alt="National Unity Platform Logo"
                     class="nup-header-logo"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                <span class="nup-header-fallback">
                    <i class="bi bi-flag me-2 text-nup-red"></i>
                </span>
                Home
            </a>

            <!-- Company Branding (when applicable) -->
            {% if active_company %}
                <div class="company-brand d-flex align-items-center">
                    <!-- Company Logo -->
                    {% if active_company.info.logo and active_company.info.logo.url %}
                        <img src="{{ active_company.info.logo.url }}"
                             alt="{{ active_company.name }} Logo"
                             class="company-header-logo me-2"
                             style="width: 32px; height: 32px; object-fit: cover; border-radius: 50%;"
                             onerror="this.style.display='none';">
                    {% endif %}

                    <!-- Company Name -->
                    <span class="company-name text-muted small d-none d-md-inline">
                        {{ active_company.name }}
                    </span>
                </div>
            {% endif %}
        </div>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#unifiedNavContent" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Content -->
        <div class="collapse navbar-collapse" id="unifiedNavContent">
            {% if user.is_authenticated %}
                <!-- Authenticated User Navigation -->
                <ul class="navbar-nav me-auto">
                    <!-- Dashboard Link -->
                    {% if can_view_active_dashboard or request.user.is_superuser %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:dashboard' %}">
                            <span class="nav-icon-container">
                                <i class="bi bi-speedometer2"></i>
                            </span>
                            Dashboard
                        </a>
                    </li>
                    {% elif not can_view_active_dashboard and not request.user.is_superuser %}
                    <!-- Fallback for authenticated users without dashboard access -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:company_create' %}">
                            <span class="nav-icon-container">
                                <i class="bi bi-plus-circle"></i>
                            </span>
                            Get Started
                        </a>
                    </li>
                    {% endif %}

                    <!-- Superuser Links -->
                    {% if request.user.is_superuser %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'superadmin:dashboard' %}">
                            <i class="bi bi-shield-lock me-1"></i> Superadmin
                        </a>
                    </li>
                    {% endif %}

                    <!-- Company Management Links -->
                    {% if can_manage_active_assistants %}
                    <li class="nav-item">
                        {% if active_company %}
                        <a class="nav-link" href="{% url 'assistants:list' company_id=active_company.id %}">
                            <span class="nav-icon-container">
                                <i class="bi bi-robot"></i>
                            </span>
                            Manage
                        </a>
                        {% endif %}
                    </li>
                    {% endif %}

                    <!-- NUP Directory Links -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="directoryDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-people me-1"></i> Our People
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'directory:company_list' %}">
                                <i class="bi bi-geo-alt"></i> Constituencies
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'directory:assistant_list' %}">
                                <i class="bi bi-person-badge"></i> Candidates
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'directory:my_favorites' %}">
                                <i class="bi bi-heart"></i> My Favorites
                            </a></li>
                        </ul>
                    </li>

                    <!-- Campaign Teams -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'assistants:community_assistants_list' %}">
                            <span class="nav-icon-container">
                                <i class="bi bi-people"></i>
                            </span>
                            Campaign Teams
                        </a>
                    </li>

                    <!-- Info Links -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="infoDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-info-circle me-1"></i> Info
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'about' %}">
                                <i class="bi bi-info-circle"></i> About
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'pricing' %}">
                                <i class="bi bi-tag"></i> Pricing
                            </a></li>
                            <li><a class="dropdown-item" href="{{ site_config.contact_url|default:'/contact/' }}">
                                <i class="bi bi-envelope"></i> Contact
                            </a></li>
                        </ul>
                    </li>
                </ul>

                <!-- Company Actions (when on company page) -->
                {% if company and company_listing %}
                <div class="navbar-company-actions d-flex align-items-center me-3">
                    <!-- Rating Display -->
                    <div class="me-3">
                        <div id="rating-display-{{ company.id }}" class="d-flex align-items-center">
                            {% render_stars company_listing.avg_rating company_listing.total_ratings %}
                        </div>
                        <button type="button" class="btn btn-sm btn-link text-decoration-none p-0 mt-1"
                                data-bs-toggle="modal" data-bs-target="#ratingModal"
                                data-company-id="{{ company.id }}"
                                data-company-name="{{ company.name }}">
                            <i class="bi bi-star me-1"></i>Rate
                        </button>
                    </div>

                    <!-- Like Button -->
                    <button class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %} me-2"
                            data-item-id="{{ company.id }}"
                            data-item-type="company"
                            title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                            style="background: none; border: none;">
                        <i class="bi bi-heart{% if is_favorited %}-fill{% endif %}"></i>
                    </button>

                    <!-- QR Code Button -->
                    {% if company.qr_code %}
                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"
                            title="Show Company QR Code" data-bs-toggle="modal" data-bs-target="#companyQrCodeModal"
                            data-qr-url="{{ company.qr_code.url }}">
                        <i class="bi bi-qr-code"></i>
                    </button>
                    {% endif %}

                    <!-- Share Button -->
                    <button type="button" class="btn btn-sm btn-outline-secondary"
                            id="copy-company-url-btn" title="Copy Company Profile URL">
                        <i class="bi bi-share me-1"></i> Share
                    </button>
                </div>
                {% endif %}

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-2"></i>
                            <span class="d-none d-md-inline">{{ user.get_full_name|default:user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">{{ user.get_full_name|default:user.username }}</h6></li>
                            <li><hr class="dropdown-divider"></li>

                            <!-- Profile & Settings -->
                            <li><a class="dropdown-item" href="{% url 'accounts:user_settings' %}">
                                <i class="bi bi-gear"></i> Settings
                            </a></li>

                            <!-- Company Switching -->
                            {% if all_user_companies|length > 1 %}
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">Switch Company</h6></li>
                            {% for company in all_user_companies %}
                            <li>
                                <a class="dropdown-item {% if company == active_company %}active{% endif %}"
                                   href="{% url 'accounts:company_switch' %}?company={{ company.id }}">
                                    {% if company.info.logo %}
                                        <img src="{{ company.info.logo.url }}" class="me-2" style="width: 16px; height: 16px; border-radius: 50%;">
                                    {% else %}
                                        <i class="bi bi-building me-2"></i>
                                    {% endif %}
                                    {{ company.name }} ({{ request.user|company_role:company|title }})
                                    {% if company == active_company %}<i class="bi bi-check-lg ms-auto"></i>{% endif %}
                                </a>
                            </li>
                            {% endfor %}
                            {% endif %}

                            <!-- Create Company Links -->
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{% url 'accounts:company_create' %}?type=company">
                                    <i class="bi bi-plus-circle"></i> Create Company
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'accounts:company_create' %}?type=community">
                                    <i class="bi bi-people"></i> Create Community
                                </a>
                            </li>

                            <!-- Company Settings -->
                            {% if active_company and user == active_company.owner %}
                            <li>
                                <a class="dropdown-item" href="{% url 'accounts:company_settings' active_company.id %}">
                                    <i class="bi bi-building-gear"></i> Company Settings
                                </a>
                            </li>
                            {% endif %}

                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="{% url 'accounts:logout' %}">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> Log Out
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            {% else %}
                <!-- Public/Unauthenticated Navigation -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'about' %}">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ site_config.contact_url|default:'/contact/' }}">Contact</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'directory:company_list' %}">Constituencies</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'directory:assistant_list' %}">Candidates</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'assistants:community_assistants_list' %}">Campaign Teams</a>
                    </li>
                </ul>

                <!-- Auth Buttons -->
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:login' %}">
                            <i class="bi bi-box-arrow-in-right me-1"></i> Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-nup-purple btn-sm ms-2" href="{% url 'accounts:register' %}">
                            Sign Up
                        </a>
                    </li>
                </ul>
            {% endif %}
        </div>
    </div>
</nav>

<!-- Company QR Code Modal (when applicable) -->
{% if company and company.qr_code %}
<div class="modal fade" id="companyQrCodeModal" tabindex="-1" aria-labelledby="companyQrCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="companyQrCodeModalLabel">{{ company.name }} QR Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{{ company.qr_code.url }}" alt="QR Code" class="img-fluid">
                <p class="mt-2 small text-muted">Scan to visit company page</p>
            </div>
        </div>
    </div>
</div>
{% endif %}


