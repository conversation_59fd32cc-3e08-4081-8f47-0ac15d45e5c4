"""
Management command to clean up old user sessions and cache data.
"""
from django.core.management.base import BaseCommand
from django.contrib.sessions.models import Session
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth import get_user_model
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = 'Clean up expired sessions and old user cache data'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Remove sessions older than this many days (default: 7)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )
    
    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        cutoff_date = timezone.now() - timedelta(days=days)
        
        if verbose:
            self.stdout.write(f"Cleaning up sessions older than {days} days (before {cutoff_date})")
        
        # Clean up expired sessions
        expired_sessions = Session.objects.filter(expire_date__lt=cutoff_date)
        expired_count = expired_sessions.count()
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f"DRY RUN: Would delete {expired_count} expired sessions")
            )
        else:
            if expired_count > 0:
                expired_sessions.delete()
                self.stdout.write(
                    self.style.SUCCESS(f"Deleted {expired_count} expired sessions")
                )
            else:
                self.stdout.write("No expired sessions to delete")
        
        # Clean up old cache entries (this is a simplified approach)
        # In a production environment, you might want to implement a more sophisticated
        # cache cleanup strategy based on your specific cache backend
        
        if not dry_run:
            try:
                # Clear cache entries that match user session patterns
                # Note: This is a simplified approach. For production, consider
                # implementing a more targeted cleanup based on your cache backend
                
                if verbose:
                    self.stdout.write("Cleaning up old cache entries...")
                
                # This is a basic cleanup - you might want to implement
                # more sophisticated cache key tracking and cleanup
                
                self.stdout.write(
                    self.style.SUCCESS("Cache cleanup completed")
                )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error during cache cleanup: {e}")
                )
        
        # Clean up orphaned user data
        if not dry_run:
            try:
                self._cleanup_orphaned_data(verbose)
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error during orphaned data cleanup: {e}")
                )
        
        if verbose:
            self.stdout.write("Session cleanup completed")
    
    def _cleanup_orphaned_data(self, verbose=False):
        """Clean up orphaned user data."""
        from accounts.session_management import UserSessionManager
        
        if verbose:
            self.stdout.write("Cleaning up orphaned user data...")
        
        # Get all active users
        active_users = User.objects.filter(is_active=True)
        
        # This is where you would implement cleanup of orphaned data
        # For example, removing cache entries for deleted users
        
        if verbose:
            self.stdout.write(f"Processed {active_users.count()} active users")
    
    def _get_cache_stats(self):
        """Get cache statistics if available."""
        try:
            # This depends on your cache backend
            # For Redis, you might use different commands
            # For database cache, you might query the cache table
            return "Cache stats not available for this backend"
        except Exception:
            return "Unable to retrieve cache stats"
