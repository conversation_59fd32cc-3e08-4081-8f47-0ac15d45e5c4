{% extends 'accounts/email/base_email.html' %}

{% block email_title %}Reset Your Password - 24seven{% endblock %}

{% block email_icon %}🔐{% endblock %}

{% block email_heading %}Password Reset Request{% endblock %}

{% block email_subtitle %}Secure your account with a new password{% endblock %}

{% block email_content %}
    <p style="font-size: 18px; margin-bottom: 24px;">
        <strong>Hello {{ user.get_full_name|default:user.username }},</strong>
    </p>

    <p>You're receiving this email because you requested a password reset for your 24seven account.</p>

    <div class="info-box">
        <h4 style="margin: 0 0 12px 0; color: #2b6cb0; display: flex; align-items: center;">
            👤 Account Information
        </h4>
        <p style="margin: 0; font-size: 16px;">
            <strong>Username:</strong> {{ user.username }}<br>
            <strong>Email:</strong> {{ user.email }}
        </p>
    </div>

    <p style="text-align: center; font-size: 16px; margin: 32px 0 24px 0;">
        Click the button below to create a new password for your account:
    </p>

    <div class="button-container">
        <a href="{{ protocol }}://{{ domain }}{% url 'accounts:password_reset_confirm' uidb64=uid token=token %}" class="button">
            🔐 Reset My Password
        </a>
    </div>

    <div style="background-color: #f8fafc; padding: 20px; border-radius: 12px; margin: 24px 0;">
        <h4 style="margin: 0 0 12px 0; color: #4a5568;">Alternative Access</h4>
        <p style="margin: 0 0 8px 0; font-size: 14px; color: #718096;">
            If the button above doesn't work, copy and paste this link into your browser:
        </p>
        <p style="word-break: break-all; font-family: 'Courier New', monospace; background-color: #edf2f7; padding: 12px; border-radius: 6px; font-size: 13px; margin: 0;">
            {{ protocol }}://{{ domain }}{% url 'accounts:password_reset_confirm' uidb64=uid token=token %}
        </p>
    </div>

    <div class="warning-box">
        <h4 style="margin: 0 0 16px 0; color: #c05621; display: flex; align-items: center;">
            ⚠️ Important Security Information
        </h4>
        <ul style="margin: 0; padding-left: 20px;">
            <li><strong>Expires in 24 hours:</strong> This reset link will automatically expire for security</li>
            <li><strong>One-time use:</strong> The link can only be used once to reset your password</li>
            <li><strong>Didn't request this?</strong> If you didn't request a password reset, you can safely ignore this email</li>
            <li><strong>Account security:</strong> Your current password remains unchanged until you complete the reset</li>
        </ul>
    </div>

    <div class="security-info">
        <h4>🛡️ Security Tips</h4>
        <ul>
            <li>Choose a strong password with at least 8 characters</li>
            <li>Include a mix of uppercase, lowercase, numbers, and symbols</li>
            <li>Don't reuse passwords from other accounts</li>
            <li>Consider using a password manager for better security</li>
        </ul>
    </div>

    <div style="text-align: center; margin-top: 32px; padding: 20px; background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-radius: 12px;">
        <p style="margin: 0; font-size: 14px; color: #718096;">
            Need help? Contact our support team at<br>
            <a href="mailto:{{ site_config.support_email|default:'<EMAIL>' }}" style="color: #3182ce; text-decoration: none; font-weight: 600;">{{ site_config.support_email|default:'<EMAIL>' }}</a>
            or visit our <a href="{{ site_config.support_url|default:site_config.contact_url|default:'/support/' }}" style="color: #3182ce; text-decoration: none; font-weight: 600;">support center</a>
        </p>
    </div>
{% endblock %}
