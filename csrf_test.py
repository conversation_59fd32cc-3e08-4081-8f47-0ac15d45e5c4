#!/usr/bin/env python3
"""
Simple script to test CSRF functionality
"""
import requests
from bs4 import BeautifulSou<PERSON>

def test_csrf_login():
    """Test CSRF token functionality on login page"""
    
    # Base URL
    base_url = "http://127.0.0.1:8000"
    login_url = f"{base_url}/accounts/login/"
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    print("Testing CSRF functionality...")
    print(f"Login URL: {login_url}")
    
    try:
        # Step 1: Get the login page to retrieve CSRF token
        print("\n1. Getting login page...")
        response = session.get(login_url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code != 200:
            print(f"ERROR: Failed to get login page. Status: {response.status_code}")
            return False
        
        # Step 2: Parse the CSRF token
        soup = BeautifulSoup(response.content, 'html.parser')
        csrf_token = None
        
        # Look for CSRF token in form
        csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
        if csrf_input:
            csrf_token = csrf_input.get('value')
            print(f"Found CSRF token in form: {csrf_token[:20]}...")
        
        # Also check meta tag
        csrf_meta = soup.find('meta', {'name': 'csrf-token'})
        if csrf_meta:
            meta_token = csrf_meta.get('content')
            print(f"Found CSRF token in meta: {meta_token[:20]}...")
            if not csrf_token:
                csrf_token = meta_token
        
        if not csrf_token:
            print("ERROR: No CSRF token found!")
            return False
        
        # Step 3: Test form submission with CSRF token
        print("\n2. Testing form submission with CSRF token...")
        form_data = {
            'csrfmiddlewaretoken': csrf_token,
            'username': 'test_user',  # Invalid credentials for testing
            'password': 'test_password',
        }
        
        response = session.post(login_url, data=form_data)
        print(f"Status Code: {response.status_code}")
        
        # Check if we get a CSRF error
        if "CSRF verification failed" in response.text:
            print("ERROR: CSRF verification failed!")
            return False
        elif "Please enter a correct username and password" in response.text or response.status_code == 200:
            print("SUCCESS: CSRF token accepted (got authentication error as expected)")
            return True
        else:
            print("SUCCESS: No CSRF error detected")
            return True
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

def test_csrf_without_token():
    """Test what happens when we don't include CSRF token"""
    
    base_url = "http://127.0.0.1:8000"
    login_url = f"{base_url}/accounts/login/"
    
    session = requests.Session()
    
    print("\n3. Testing form submission WITHOUT CSRF token...")
    
    try:
        # Submit form without CSRF token
        form_data = {
            'username': 'test_user',
            'password': 'test_password',
        }
        
        response = session.post(login_url, data=form_data)
        print(f"Status Code: {response.status_code}")
        
        if "CSRF verification failed" in response.text:
            print("EXPECTED: CSRF verification failed (this is correct behavior)")
            return True
        else:
            print("WARNING: No CSRF error when token was missing")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception occurred: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("CSRF FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Test with CSRF token
    success1 = test_csrf_login()
    
    # Test without CSRF token
    success2 = test_csrf_without_token()
    
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    print(f"CSRF with token: {'PASS' if success1 else 'FAIL'}")
    print(f"CSRF without token: {'PASS' if success2 else 'FAIL'}")
    
    if success1 and success2:
        print("\n✅ CSRF functionality is working correctly!")
    else:
        print("\n❌ CSRF functionality has issues!")
    print("=" * 50)
