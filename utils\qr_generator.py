import qrcode
from PIL import Image, ImageDraw, ImageFont
import os
from io import BytesIO
import platform # Added for OS detection in font loading

# Constants for QR code generation
# Vertical offset to ensure the letter "A" is perfectly centered
# Negative value moves the letter up, positive value moves it down
QR_LETTER_VERTICAL_OFFSET = -50

# Assuming Django context for the following imports,
# these are kept for the full context of the original code.
# If you are running generate_qr_with_a in isolation, these are not needed.
try:
    from django.core.files.base import ContentFile
    from django.urls import reverse
    from django.contrib.sites.models import Site
    from django.conf import settings
except ImportError:
    # Provide dummy classes/functions if Django isn't available, for testing core QR generation
    print("Django modules not found. Django-related functions will not work.")
    class ContentFile:
        def __init__(self, content, name=None):
            self.content = content
            self.name = name
        def read(self): return self.content
        def seek(self, pos): pass
    def reverse(*args, **kwargs): return "/dummy-url/"
    class Site:
        class objects:
            @staticmethod
            def get_current():
                class CurrentSite:
                    domain = "example.com"
                return CurrentSite()
    class settings:
        SECURE_SSL_REDIRECT = False


def load_font(font_name, font_size):
    """
    Try to load the specified font or fall back to alternatives.
    Includes more robust path searching for common OS.

    Args:
        font_name (str): Name of the font to load (e.g., "Arial Black")
        font_size (int): Size of the font

    Returns:
        PIL.ImageFont: The loaded font
    """
    # Common font names to try (case-insensitive and variations)
    # Prioritize Arial Black as specified in the documentation
    if "arial black" in font_name.lower():
        font_names_to_try = [
            "Arial Black",
            "ArialBlack",
            "Arial-Black",
            "arialblk",    # Common Windows filename for Arial Black
            "ariblk",      # Short Windows filename for Arial Black
            font_name,     # Original name passed
            "Impact",      # Alternative as per specification
            "Arial Bold",  # Alternative as per specification
            "arialbd",     # Arial Bold (common Windows filename)
            "arial",       # Regular Arial (fallback)
            "DejaVuSans-Bold",  # Common Linux font
            "DejaVuSans",       # Common Linux font
            "FreeSans",         # Common Linux font
            "LiberationSans-Bold", # Common Linux font
            "LiberationSans",      # Common Linux font
            "NotoSans-Bold",    # Common on Android/Linux
            "NotoSans",         # Common on Android/Linux
            "sans-serif-bold",  # Generic bold fallback
            "sans-serif",       # Generic fallback
        ]
    else:
        font_names_to_try = [
            font_name,
            font_name.replace(" ", ""),  # e.g., ArialBold
            font_name.replace(" ", "-"), # e.g., Arial-Bold
            font_name.lower(),
            font_name.lower().replace(" ", ""),
            font_name.lower().replace(" ", "-"),
            "arialbd",    # Arial Bold (common Windows filename)
            "arialbold",  # Arial Bold alternative
            "arial-bold", # Arial Bold with hyphen
            "arial",      # Regular Arial (fallback)
            "DejaVuSans-Bold",  # Common Linux font
            "DejaVuSans",       # Common Linux font
            "FreeSans",         # Common Linux font
            "LiberationSans-Bold", # Common Linux font
            "LiberationSans",      # Common Linux font
            "NotoSans-Bold",    # Common on Android/Linux
            "NotoSans",         # Common on Android/Linux
            "sans-serif-bold", # Generic bold fallback
            "sans-serif", # Generic fallback
        ]

    # Common font directories for different OS
    current_os = platform.system()
    font_dirs = []
    if current_os == "Windows":
        font_dirs.append(os.path.join(os.environ.get("WINDIR", "C:\\Windows"), "Fonts"))
        font_dirs.append(os.path.join(os.environ.get("LOCALAPPDATA", ""), "Microsoft", "Windows", "Fonts"))
    elif current_os == "Darwin": # macOS
        font_dirs.append("/Library/Fonts")
        font_dirs.append("/System/Library/Fonts")
        font_dirs.append(os.path.expanduser("~/Library/Fonts"))
    elif current_os == "Linux":
        # Standard Linux font directories
        font_dirs.append("/usr/share/fonts/truetype")
        font_dirs.append("/usr/share/fonts/opentype")
        font_dirs.append("/usr/local/share/fonts")
        font_dirs.append(os.path.expanduser("~/.fonts"))

        # Additional Linux font directories (common on cPanel servers)
        font_dirs.append("/usr/share/fonts")
        font_dirs.append("/usr/share/fonts/truetype/dejavu")
        font_dirs.append("/usr/share/fonts/truetype/liberation")
        font_dirs.append("/usr/share/fonts/truetype/freefont")
        font_dirs.append("/usr/share/fonts/truetype/noto")

        # cPanel specific directories
        cpanel_font_dirs = [
            "/usr/local/cpanel/base/webmail/skins/fonts",
            os.path.expanduser("~/fonts"),
            os.path.expanduser("~/public_html/fonts")
        ]
        font_dirs.extend(cpanel_font_dirs)

        # Project-specific font paths for cPanel
        try:
            from django.conf import settings
            BASE_DIR = getattr(settings, 'BASE_DIR', os.getcwd())
        except ImportError:
            BASE_DIR = os.getcwd()

        project_font_dirs = [
            os.path.join(BASE_DIR, 'static', 'fonts', 'system'),
            os.path.join(BASE_DIR, 'static', 'fonts', 'custom'),
            os.path.join(BASE_DIR, 'static', 'fonts'),
            os.path.join(BASE_DIR, 'fonts'),
            os.path.expanduser("~/.fonts"),
            os.path.expanduser("~/fonts"),
            os.path.expanduser("~/public_html/fonts")
        ]
        font_dirs.extend(project_font_dirs)

    # Initialize logging
    print(f"Attempting to load font '{font_name}' at size {font_size}")
    attempted_fonts = []

    # Try loading by name first (Pillow often finds system fonts)
    for name_variant in font_names_to_try:
        try:
            print(f"Trying to load font by name: '{name_variant}'")
            font = ImageFont.truetype(name_variant, size=font_size)
            print(f"Successfully loaded font: '{name_variant}'")
            return font
        except (IOError, OSError) as e:
            attempted_fonts.append(f"'{name_variant}' (by name): {str(e)}")
            pass # Continue to try paths

    # If direct name loading fails, try specific paths
    for font_dir in font_dirs:
        if not os.path.exists(font_dir):
            continue

        print(f"Searching for fonts in directory: {font_dir}")

        for name_variant in font_names_to_try:
            for ext in ['.ttf', '.otf']:
                # Common specific filenames for Arial Black with more comprehensive options
                if "arial black" in name_variant.lower():
                    possible_files = [
                        "ariblk.ttf", "ariblk.otf",  # Common Windows filenames
                        "Arial Black.ttf", "Arial Black.otf",
                        "ArialBlack.ttf", "ArialBlack.otf",
                        "Arial-Black.ttf", "Arial-Black.otf",
                        "arialblack.ttf", "arialblack.otf",
                        "arial-black.ttf", "arial-black.otf"
                    ]
                else:
                    possible_files = [
                        f"{name_variant}{ext}",
                        f"{name_variant.replace(' ', '')}{ext}",
                        f"{name_variant.replace(' ', '-')}{ext}",
                        f"{name_variant.lower()}{ext}",
                        f"{name_variant.lower().replace(' ', '')}{ext}",
                        f"{name_variant.lower().replace(' ', '-')}{ext}"
                    ]

                for filename in possible_files:
                    font_path = os.path.join(font_dir, filename)
                    if os.path.exists(font_path):
                        try:
                            print(f"Found font file: {font_path}")
                            font = ImageFont.truetype(font_path, size=font_size)
                            print(f"Successfully loaded font from path: {font_path}")
                            return font
                        except (IOError, OSError) as e:
                            attempted_fonts.append(f"'{font_path}': {str(e)}")
                            pass

    # Try to create a custom font if all else fails
    try:
        print("Attempting to create a custom font as fallback")
        # Create a simple bold font using PIL's built-in capabilities
        img = Image.new('RGB', (font_size*2, font_size*2), color='white')
        draw = ImageDraw.Draw(img)
        default_font = ImageFont.load_default()
        print("Successfully created custom font fallback")
        return default_font
    except Exception as e:
        print(f"Failed to create custom font: {e}")

    # Log all attempted fonts for debugging
    print(f"Warning: Could not find font '{font_name}' or alternatives. Using default font.")
    print(f"Attempted {len(attempted_fonts)} font variations")
    for i, attempt in enumerate(attempted_fonts[:5]):  # Show only first 5 attempts to avoid log spam
        print(f"  Attempt {i+1}: {attempt}")
    if len(attempted_fonts) > 5:
        print(f"  ... and {len(attempted_fonts) - 5} more attempts")
    return ImageFont.load_default()

def get_text_dimensions(text, font):
    """
    Get the dimensions of text with the given font.
    Works with both older and newer Pillow versions.

    Args:
        text (str): The text to measure
        font (PIL.ImageFont): The font to use

    Returns:
        tuple: (width, height) of the text
    """
    # Create a temporary drawing context
    img = Image.new('RGBA', (1, 1), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    try:
        # For newer Pillow versions (Pillow 8.0.0+)
        bbox = draw.textbbox((0, 0), text, font=font)
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]
    except AttributeError:
        # For older Pillow versions
        width, height = draw.textsize(text, font=font)

    return width, height

def generate_qr_with_a(data, letter="A"):
    """
    Generate a QR code with a centered letter A following the standard specification.

    Implementation follows the specifications in qr_code_design_specification.md.

    The design creates a white letter A (35% of QR code size) as background protection,
    with a black letter A (10% smaller) perfectly centered inside it, creating a
    padding effect where the white A acts as a border around the black A.

    The letter is positioned with a vertical offset of QR_LETTER_VERTICAL_OFFSET pixels
    to ensure it is perfectly centered with equal distance from top and bottom.
    This offset was determined through testing and verification.

    Args:
        data (str): The data to encode in the QR code (typically a URL)
        letter (str, optional): The letter to display in the center. Defaults to "A".

    Returns:
        PIL.Image.Image: The generated QR code image
    """
    print(f"Generating standard QR code with letter '{letter}' for data: {data}")

    # 1. Generate Base QR Code with parameters exactly as specified in the documentation
    qr = qrcode.QRCode(
        version=1,  # Fixed version as per specification
        error_correction=qrcode.constants.ERROR_CORRECT_H,  # High error correction for logo
        box_size=10, # Exactly 10 pixels per module as per specification
        border=4,    # 4 modules border as per specification
    )
    qr.add_data(data)
    qr.make(fit=True)
    qr_img = qr.make_image(fill_color="black", back_color="white").convert('RGBA')

    # 2. Calculate Dimensions
    qr_width, qr_height = qr_img.size
    print(f"QR code dimensions: {qr_width}x{qr_height} pixels")

    # Calculate center coordinates exactly as in the specification
    center_x, center_y = qr_width // 2, qr_height // 2

    # 3. Calculate Letter Sizes (from the "above" code's logic)
    # White 'A' size - increased to 40% for better padding protection from QR code
    white_letter_size = int(min(qr_width, qr_height) * 0.40)
    # Black 'A' size - exactly 10% smaller than white A (so white A acts as padding)
    black_letter_size = int(white_letter_size * 0.9)

    print(f"Black letter size: {black_letter_size}, White letter size: {white_letter_size}")

    # 4. Load Fonts
    black_font = load_font("Arial Black", black_letter_size)
    white_font = load_font("Arial Black", white_letter_size)

    # Check if we got the default font (which is too small)
    # If so, we need to scale up the font size significantly
    default_font = ImageFont.load_default()

    # Test if the loaded font is actually the default font
    test_img = Image.new('RGB', (100, 100), 'white')
    test_draw = ImageDraw.Draw(test_img)

    try:
        bbox_loaded = test_draw.textbbox((0, 0), "A", font=black_font)
        loaded_width = bbox_loaded[2] - bbox_loaded[0]
        loaded_height = bbox_loaded[3] - bbox_loaded[1]
    except AttributeError:
        loaded_width, loaded_height = test_draw.textsize("A", font=black_font)

    try:
        bbox_default = test_draw.textbbox((0, 0), "A", font=default_font)
        default_width = bbox_default[2] - bbox_default[0]
        default_height = bbox_default[3] - bbox_default[1]
    except AttributeError:
        default_width, default_height = test_draw.textsize("A", font=default_font)

    # If dimensions are the same, we're using the default font
    if loaded_width == default_width and loaded_height == default_height:
        print("Warning: Font loading failed, creating custom letter A")

        # Create WHITE letter A - thick for better padding/protection
        def create_white_letter_a(size, color=(255, 255, 255, 255)):
            """Create a thick white letter A for background protection."""
            letter_img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(letter_img)

            # Calculate letter dimensions with more padding for protection
            margin = size // 8  # Smaller margin = larger letter for better protection
            width = size - 2 * margin
            height = size - 2 * margin

            # Thick stroke for solid white background protection
            stroke_thickness = max(12, size // 4)  # Thicker for better protection

            # Left diagonal stroke
            left_points = [
                (margin - stroke_thickness//2, size - margin),
                (margin + stroke_thickness//2, size - margin),
                (size // 2 + stroke_thickness//2, margin),
                (size // 2 - stroke_thickness//2, margin)
            ]
            draw.polygon(left_points, fill=color)

            # Right diagonal stroke
            right_points = [
                (size // 2 - stroke_thickness//2, margin),
                (size // 2 + stroke_thickness//2, margin),
                (size - margin + stroke_thickness//2, size - margin),
                (size - margin - stroke_thickness//2, size - margin)
            ]
            draw.polygon(right_points, fill=color)

            # Thick crossbar for solid protection
            crossbar_y = margin + height * 0.6
            crossbar_left = margin + width * 0.1  # Extend crossbar more
            crossbar_right = size - margin - width * 0.1
            crossbar_thickness = max(8, size // 6)  # Thick crossbar

            crossbar_rect = [
                (crossbar_left, crossbar_y - crossbar_thickness),
                (crossbar_right, crossbar_y + crossbar_thickness)
            ]
            draw.rectangle(crossbar_rect, fill=color)

            return letter_img

        # Create BLACK letter A - normal thickness for clean appearance
        def create_black_letter_a(size, color=(0, 0, 0, 255)):
            """Create a normal-thickness black letter A for clean appearance."""
            letter_img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
            draw = ImageDraw.Draw(letter_img)

            # Calculate letter dimensions for normal appearance
            margin = size // 6  # Normal margin
            width = size - 2 * margin
            height = size - 2 * margin

            # Normal stroke thickness for clean, readable letter
            stroke_thickness = max(6, size // 8)  # Thinner for normal appearance

            # Left diagonal stroke
            left_points = [
                (margin - stroke_thickness//2, size - margin),
                (margin + stroke_thickness//2, size - margin),
                (size // 2 + stroke_thickness//2, margin),
                (size // 2 - stroke_thickness//2, margin)
            ]
            draw.polygon(left_points, fill=color)

            # Right diagonal stroke
            right_points = [
                (size // 2 - stroke_thickness//2, margin),
                (size // 2 + stroke_thickness//2, margin),
                (size - margin + stroke_thickness//2, size - margin),
                (size - margin - stroke_thickness//2, size - margin)
            ]
            draw.polygon(right_points, fill=color)

            # Normal crossbar
            crossbar_y = margin + height * 0.6
            crossbar_left = margin + width * 0.2  # Normal crossbar positioning
            crossbar_right = size - margin - width * 0.2
            crossbar_thickness = max(4, size // 12)  # Normal crossbar thickness

            crossbar_rect = [
                (crossbar_left, crossbar_y - crossbar_thickness),
                (crossbar_right, crossbar_y + crossbar_thickness)
            ]
            draw.rectangle(crossbar_rect, fill=color)

            return letter_img

        # Create both letters with different characteristics
        black_letter_img = create_black_letter_a(black_letter_size, (0, 0, 0, 255))
        white_letter_img = create_white_letter_a(white_letter_size, (255, 255, 255, 255))

        print(f"Created custom letter A: black={black_letter_size}x{black_letter_size}, white={white_letter_size}x{white_letter_size}")

        # Use custom drawing instead of text
        use_custom_letter = True
    else:
        print(f"Font loading successful: {loaded_width}x{loaded_height} vs default {default_width}x{default_height}")
        use_custom_letter = False

    # 5. Calculate Text Dimensions and Positions
    if use_custom_letter:
        # Use custom letter dimensions
        black_width, black_height = black_letter_size, black_letter_size
        white_width, white_height = white_letter_size, white_letter_size
    else:
        # Use font-based dimensions
        black_width, black_height = get_text_dimensions(letter, black_font)
        white_width, white_height = get_text_dimensions(letter, white_font)

    # Calculate WHITE letter position first (this is now our base/reference point)
    white_x = center_x - white_width // 2

    if use_custom_letter:
        # For custom letters, center them perfectly without the offset
        # since the custom letter is already well-centered within its bounds
        white_y = center_y - white_height // 2
    else:
        # For font-based letters, use the original offset
        white_y = center_y - white_height / 2 + QR_LETTER_VERTICAL_OFFSET

    # Calculate BLACK letter position to be EXACTLY centered inside the white letter
    # The black letter should be perfectly centered within the white letter padding
    if use_custom_letter:
        # For custom letters: position black letter so it's perfectly centered inside white letter
        size_diff = (white_letter_size - black_letter_size) // 2
        black_x = white_x + size_diff
        black_y = white_y + size_diff
    else:
        # For font-based letters: position black letter centered inside white letter
        black_x = white_x + (white_width - black_width) // 2
        black_y = white_y + (white_height - black_height) // 2

    # Print positioning information for debugging
    print(f"QR center point: ({center_x}, {center_y})")
    print(f"White letter dimensions: {white_width}x{white_height}")
    print(f"White letter position: ({white_x}, {white_y})")
    print(f"Black letter dimensions: {black_width}x{black_height}")
    print(f"Black letter position: ({black_x}, {black_y})")

    # 6. Draw White 'A' Background
    white_temp = Image.new('RGBA', qr_img.size, (0, 0, 0, 0))
    white_draw = ImageDraw.Draw(white_temp)

    if use_custom_letter:
        # Use custom letter image with clean white letter A background
        # The white letter is already positioned exactly behind the black letter
        # No additional adjustments needed - use the calculated white_x and white_y directly

        # Paste the clean white letter A background exactly where calculated
        # This creates perfect protection for the black letter
        white_temp.paste(white_letter_img, (int(white_x), int(white_y)), white_letter_img)
    else:
        # Use font-based drawing
        # Use exactly 5 pixels padding as specified
        padding = 5

        # Draw the white 'A' multiple times with offsets to ensure solidity
        for dx in range(-padding-2, padding+3, 2):
            for dy in range(-padding-2, padding+3, 2):
                white_draw.text(
                    (white_x + dx, white_y + dy),
                    letter,
                    fill=(255, 255, 255, 255),
                    font=white_font
                )

        # Then draw the regular white 'A' on top for a cleaner edge
        for dx in range(-padding, padding+1):
            for dy in range(-padding, padding+1):
                white_draw.text(
                    (white_x + dx, white_y + dy),
                    letter,
                    fill=(255, 255, 255, 255),
                    font=white_font
                )

    # 7. Draw Black 'A' on Top
    black_temp = Image.new('RGBA', qr_img.size, (0, 0, 0, 0))
    black_draw = ImageDraw.Draw(black_temp)

    if use_custom_letter:
        # Use custom letter image
        black_temp.paste(black_letter_img, (int(black_x), int(black_y)), black_letter_img)
    else:
        # Use font-based drawing
        black_draw.text(
            (black_x, black_y),
            letter,
            fill=(0, 0, 0, 255),
            font=black_font
        )

    # 8. Composite All Layers
    overlay_layer = Image.new('RGBA', qr_img.size, (0, 0, 0, 0))
    overlay_layer = Image.alpha_composite(overlay_layer, white_temp)
    overlay_layer = Image.alpha_composite(overlay_layer, black_temp)
    final_image = Image.alpha_composite(qr_img, overlay_layer)

    # Convert back to RGB for compatibility
    return final_image.convert('RGB')

def generate_qr_content_file(data, model_name, model_slug, letter="A"):
    """
    Generate a QR code and return it as a ContentFile for saving to a Django ImageField.

    Args:
        data (str): The data to encode in the QR code (typically a URL)
        model_name (str): The name of the model (e.g., 'company', 'assistant')
        model_slug (str): The slug of the model instance
        letter (str, optional): The letter to display in the center. Defaults to "A".

    Returns:
        django.core.files.base.ContentFile: The QR code image as a ContentFile
    """
    qr_img = generate_qr_with_a(data, letter=letter)

    # Save to BytesIO
    buffer = BytesIO()
    qr_img.save(buffer, format="PNG")
    buffer.seek(0)

    # Create filename
    filename = f'qr_codes/{model_name}_qr_{model_slug}.png'

    # Return ContentFile
    return ContentFile(buffer.getvalue(), name=filename)

def generate_model_qr_code(instance, url_path=None, field_name='qr_code', letter="A"):
    """
    Generate a QR code for a model instance and save it to the specified field.

    Args:
        instance: The model instance
        url_path (str, optional): The URL path to encode in the QR code. If None,
                                 will try to determine based on model type.
        field_name (str, optional): The name of the ImageField to save the QR code to.
        letter (str, optional): The letter to display in the center. Defaults to "A".

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get model name and slug
        model_name = instance.__class__.__name__.lower()
        model_slug = getattr(instance, 'slug', str(instance.pk))

        # If no URL path provided, try to determine based on model type
        if not url_path:
            if model_name == 'company':
                try:
                    # Try with slug first
                    url_path = reverse('accounts:company_detail', kwargs={'slug': model_slug})
                except Exception: # Catch specific exception if possible, e.g., NoReverseMatch
                    # Fall back to company_id
                    url_path = reverse('accounts:company_detail', kwargs={'company_id': instance.id})
            elif model_name == 'assistant':
                url_path = reverse('assistants:assistant_chat', kwargs={'slug': model_slug})
            elif model_name == 'registrationlink':
                url_path = instance.get_absolute_url()
            else:
                raise ValueError(f"Cannot determine URL path for model type: {model_name}")

        # Ensure URL path starts with a slash
        if not url_path.startswith('/'):
            url_path = '/' + url_path

        # Get current site domain
        current_site = Site.objects.get_current()
        protocol = 'https' if getattr(settings, 'SECURE_SSL_REDIRECT', False) else 'http'
        full_url = f"{protocol}://{current_site.domain}{url_path}"

        # Generate QR code
        content_file = generate_qr_content_file(full_url, model_name, model_slug, letter)

        # Save to instance field
        field = getattr(instance, field_name)
        field.save(content_file.name, content_file, save=False)

        return True
    except Exception as e:
        import traceback
        print(f"Error generating QR code for {instance}: {e}")
        traceback.print_exc()
        return False