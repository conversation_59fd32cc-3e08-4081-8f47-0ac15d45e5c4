{% extends 'base/layout.html' %}
{% load static %}

{% block title %}NUP - National Unity Platform | Building the Uganda We Need{% endblock %}

{% block meta_description %}
The National Unity Platform is committed to building a better Uganda for all. Join our movement for a brighter, more equitable, and just country. Meet our candidates and support the change Uganda needs.
{% endblock %}

{% block extra_head %}
<!-- Prevent caching of homepage to ensure authentication state is current -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">
<!-- Authentication state meta tags for JavaScript -->
<meta name="user-authenticated" content="{% if user.is_authenticated %}true{% else %}false{% endif %}">
<meta name="user-id" content="{% if user.is_authenticated %}{{ user.id }}{% else %}{% endif %}">
<meta name="user-username" content="{% if user.is_authenticated %}{{ user.username }}{% else %}{% endif %}">
{% endblock %}

{% block body_class %}bg-light{% endblock %}

{% block navbar %}
{% include 'components/unified_header.html' %}
{% endblock %}

{% block content %}
<!-- Homepage Preloader -->
<div id="homepage-preloader">
    <div class="text-center">
        <div class="preloader-spinner"></div>
        <div class="preloader-text">Loading...</div>
    </div>
</div>

{% if user.is_authenticated %}
    {% load account_tags %}
    {% get_pending_companies user as pending_companies %}
    {% get_pending_communities user as pending_communities %}
    {% get_pending_assistants user as pending_assistants %}

    <!-- Show a single consolidated notification for all pending items -->
    <div id="pending-notifications-container" class="container">
        <div class="row">
            <div class="col-lg-6 mx-auto mx-lg-0">
                {% include 'accounts/tags/consolidated_simple_notification.html' with pending_companies=pending_companies pending_communities=pending_communities pending_assistants=pending_assistants %}
            </div>
        </div>
    </div>
{% endif %}

<!-- Hero Section -->
<section class="nup-section-red position-relative overflow-hidden hero-section">
    <div class="container py-3">
        <div class="row align-items-center">
            <div class="col-lg-6 mx-auto mx-lg-0">
                <h1 class="display-4 fw-bold mb-4">
                    Building the Uganda<br>
                    We Need
                </h1>
                <p class="lead mb-4">
                    The National Unity Platform is committed to fighting for a brighter, more equitable, and just country. Join our movement for transformational change.
                </p>
                <div class="d-grid gap-3 d-sm-flex mb-4">
                    <a href="{% url 'directory:assistant_list' %}" class="btn btn-outline-light btn-lg px-4">
                        Meet Our Candidates
                    </a>
                </div>

                <!-- Search Form -->
                <div class="card bg-white text-dark p-3 shadow-sm search-form-card">
                    <form action="{% url 'search' %}" method="get" class="mb-0">
                        <div class="row g-2">
                            <div class="col-12 mb-2">
                                <label for="searchQuery" class="form-label fw-bold mb-1">Find NUP Representatives</label>
                                <input type="text" class="form-control" id="searchQuery" name="q" placeholder="Search for candidates, campaign managers, or constituencies..." required>
                            </div>
                            <div class="col-12 d-flex flex-wrap">
                                <div class="form-check form-check-inline me-3">
                                    <input class="form-check-input" type="radio" name="type" id="searchCandidates" value="assistant" checked>
                                    <label class="form-check-label" for="searchCandidates">Candidates</label>
                                </div>
                                <div class="form-check form-check-inline me-3">
                                    <input class="form-check-input" type="radio" name="type" id="searchCampaignTeams" value="community">
                                    <label class="form-check-label" for="searchCampaignTeams">Campaign Teams</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="type" id="searchConstituencies" value="company">
                                    <label class="form-check-label" for="searchConstituencies">Constituencies</label>
                                </div>
                                <div class="ms-auto mt-2 mt-sm-0">
                                    <button type="submit" class="btn btn-nup-primary">
                                        <i class="bi bi-search"></i> Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="col-lg-6 d-none d-lg-block">
                <img src="{% static 'img/blue1.png' %}"
                     alt="24seven Platform"
                     class="img-fluid">
            </div>
        </div>
    </div>
</section>

<!-- Core Values Section -->
<section class="nup-section-white">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold">Our Core Values</h2>
            <div class="nup-accent-bar"></div>
            <p class="lead text-muted">
                The principles that guide our fight for a better Uganda
            </p>
        </div>

        <div class="nup-grid-3x2">
            <!-- Discipline -->
            <div class="nup-card nup-card-red-accent text-center">
                <div class="nup-icon-circle">
                    <i class="bi bi-shield-check"></i>
                </div>
                <h3 class="fw-bold mb-3">Discipline</h3>
                <p class="text-muted">
                    We maintain the highest standards of conduct and commitment to our principles in all our actions.
                </p>
            </div>

            <!-- Reliability -->
            <div class="nup-card nup-card-purple-accent text-center">
                <div class="nup-icon-circle">
                    <i class="bi bi-clock-history"></i>
                </div>
                <h3 class="fw-bold mb-3">Reliability</h3>
                <p class="text-muted">
                    Our candidates and representatives are dependable leaders you can count on to deliver on their promises.
                </p>
            </div>

            <!-- Integrity -->
            <div class="nup-card nup-card-red-accent text-center">
                <div class="nup-icon-circle">
                    <i class="bi bi-heart"></i>
                </div>
                <h3 class="fw-bold mb-3">Integrity</h3>
                <p class="text-muted">
                    We uphold honesty, transparency, and moral principles in all our political and social endeavors.
                </p>
            </div>

            <!-- Inclusiveness -->
            <div class="nup-card nup-card-purple-accent text-center">
                <div class="nup-icon-circle">
                    <i class="bi bi-people"></i>
                </div>
                <h3 class="fw-bold mb-3">Inclusiveness</h3>
                <p class="text-muted">
                    We embrace diversity and ensure every Ugandan has a voice in building our nation's future.
                </p>
            </div>

            <!-- Patriotism -->
            <div class="nup-card nup-card-red-accent text-center">
                <div class="nup-icon-circle">
                    <i class="bi bi-flag"></i>
                </div>
                <h3 class="fw-bold mb-3">Patriotism</h3>
                <p class="text-muted">
                    Our love for Uganda drives our commitment to creating a better future for all citizens.
                </p>
            </div>

            <!-- Service -->
            <div class="nup-card nup-card-purple-accent text-center">
                <div class="nup-icon-circle">
                    <i class="bi bi-hand-thumbs-up"></i>
                </div>
                <h3 class="fw-bold mb-3">Service</h3>
                <p class="text-muted">
                    We are dedicated to serving the people of Uganda with humility and unwavering commitment.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Leadership Section -->
<section class="nup-section-white">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="h3">Meet Our Leadership</h2>
            <div class="nup-accent-bar"></div>
            <p class="lead text-muted">The dedicated leaders working to build the Uganda we need</p>
        </div>

        <div class="row g-4 justify-content-center">
            <!-- President -->
            <div class="col-md-6 col-lg-4">
                <div class="nup-candidate-card nup-leader-card" data-leader="president">
                    <div class="card-header text-center nup-leader-header">
                        <h5 class="mb-0 nup-leader-name">Robert Kyagulanyi Ssentamu</h5>
                        <small class="nup-leader-title">President</small>
                    </div>
                    <div class="card-body text-center nup-leader-body">
                        <div class="nup-leader-image-container mb-3">
                            <img src="{% static 'img/Kyagulanyi-Ssentamu-Robert-2-270x270.jpg' %}"
                                 alt="Robert Kyagulanyi Ssentamu"
                                 class="nup-leader-image">
                        </div>
                        <p class="text-muted small nup-leader-description">
                            Leading the generational and transformational People Power Movement for a better Uganda.
                        </p>
                        <a href="#" class="btn btn-nup-primary btn-sm nup-leader-btn">
                            <span class="nup-btn-text">Learn More</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Secretary General -->
            <div class="col-md-6 col-lg-4">
                <div class="nup-candidate-card nup-leader-card purple" data-leader="secretary-general">
                    <div class="card-header text-center nup-leader-header">
                        <h5 class="mb-0 nup-leader-name">David Lewis Rubongoya</h5>
                        <small class="nup-leader-title">Secretary-General</small>
                    </div>
                    <div class="card-body text-center nup-leader-body">
                        <div class="nup-leader-image-container mb-3">
                            <img src="{% static 'img/David_Lewis_Rubongoya.jpg' %}"
                                 alt="David Lewis Rubongoya"
                                 class="nup-leader-image">
                        </div>
                        <p class="text-muted small nup-leader-description">
                            Coordinating party operations and ensuring effective organizational management.
                        </p>
                        <a href="#" class="btn btn-nup-purple btn-sm nup-leader-btn">
                            <span class="nup-btn-text">Learn More</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Spokesperson -->
            <div class="col-md-6 col-lg-4">
                <div class="nup-candidate-card nup-leader-card" data-leader="spokesperson">
                    <div class="card-header text-center nup-leader-header">
                        <h5 class="mb-0 nup-leader-name">Joel Ssenyonyi</h5>
                        <small class="nup-leader-title">Spokesperson</small>
                    </div>
                    <div class="card-body text-center nup-leader-body">
                        <div class="nup-leader-image-container mb-3">
                            <img src="{% static 'img/Joel-Ssenyonyi-270x270.png' %}"
                                 alt="Joel Ssenyonyi"
                                 class="nup-leader-image">
                        </div>
                        <p class="text-muted small nup-leader-description">
                            Communicating NUP's vision and representing the party in public discourse.
                        </p>
                        <a href="#" class="btn btn-nup-primary btn-sm nup-leader-btn">
                            <span class="nup-btn-text">Learn More</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Constituencies Section -->
<section class="nup-section-white" id="candidates">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="h3">Our Constituencies</h2>
            <div class="nup-accent-bar"></div>
            <p class="lead text-muted">NUP representatives serving communities across Uganda</p>
        </div>

        <!-- Featured Companies Carousel -->
        <div class="company-logo-carousel-container">
            <div class="company-logo-carousel">
                {% if featured_companies %}
                    <!-- Display featured companies -->
                    {% for company in featured_companies %}
                        <div class="company-logo-item">
                            <a href="{% url 'accounts:public_company_detail' slug=company.slug %}" title="{{ company.name }}" class="text-center">
                                {% if company.info.logo %}
                                    <div class="logo-container home-carousel-logo">
                                        <img src="{{ company.info.logo.url }}"
                                             alt="{{ company.name }} Logo"
                                             class="company-logo"
                                             onerror="this.onerror=null; this.src='/static/img/default-company-logo.svg';">
                                    </div>
                                {% else %}
                                    <div class="company-logo-placeholder home-carousel-placeholder">
                                        <i class="bi bi-building"></i>
                                        <span>{{ company.name }}</span>
                                    </div>
                                {% endif %}
                                <div class="company-info">
                                    <h5 class="company-name">{{ company.name }}</h5>
                                    <p class="assistant-count">
                                        <i class="bi bi-robot"></i>
                                        {{ company.public_assistants_count }} Public Assistant{{ company.public_assistants_count|pluralize }}
                                    </p>
                                </div>
                            </a>
                        </div>
                    {% endfor %}

                    <!-- Only duplicate logos if we have at least 3 companies for a good scrolling effect -->
                    {% if featured_companies|length >= 3 %}
                        {% for company in featured_companies %}
                            <div class="company-logo-item">
                                <a href="{% url 'accounts:public_company_detail' slug=company.slug %}" title="{{ company.name }}" class="text-center">
                                    {% if company.info.logo %}
                                        <div class="logo-container home-carousel-logo">
                                            <img src="{{ company.info.logo.url }}"
                                                 alt="{{ company.name }} Logo"
                                                 class="company-logo"
                                                 onerror="this.onerror=null; this.src='/static/img/default-company-logo.svg';">
                                        </div>
                                    {% else %}
                                        <div class="company-logo-placeholder home-carousel-placeholder">
                                            <i class="bi bi-building"></i>
                                            <span>{{ company.name }}</span>
                                        </div>
                                    {% endif %}
                                    <div class="company-info">
                                        <h5 class="company-name">{{ company.name }}</h5>
                                        <p class="assistant-count">
                                            <i class="bi bi-robot"></i>
                                            {{ company.public_assistants_count }} Public Assistant{{ company.public_assistants_count|pluralize }}
                                        </p>
                                    </div>
                                </a>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% else %}
                    <!-- Fallback static logos if no featured companies -->
                    {% for i in "12345" %}
                        <div class="company-logo-item">
                            <a href="{% url 'directory:company_list' %}" title="Browse Companies" class="text-center">
                                <div class="logo-container home-carousel-logo">
                                    <img src="{% static 'img/logos/company-'|add:forloop.counter|add:'.svg' %}" alt="Company Logo" class="company-logo">
                                </div>
                                <div class="company-info">
                                    <h5 class="company-name">Example Company {{ forloop.counter }}</h5>
                                    <p class="assistant-count">
                                        <i class="bi bi-robot"></i>
                                        {{ forloop.counter }} Public Assistant{{ forloop.counter|pluralize }}
                                    </p>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                    <!-- Only duplicate if we have enough for a good scrolling effect -->
                    {% for i in "12345" %}
                        <div class="company-logo-item">
                            <a href="{% url 'directory:company_list' %}" title="Browse Companies" class="text-center">
                                <div class="logo-container home-carousel-logo">
                                    <img src="{% static 'img/logos/company-'|add:forloop.counter|add:'.svg' %}" alt="Company Logo" class="company-logo">
                                </div>
                                <div class="company-info">
                                    <h5 class="company-name">Example Company {{ forloop.counter }}</h5>
                                    <p class="assistant-count">
                                        <i class="bi bi-robot"></i>
                                        {{ forloop.counter }} Public Assistant{{ forloop.counter|pluralize }}
                                    </p>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- NUP in Numbers Section -->
<section class="nup-section-purple">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="h3">NUP in Numbers</h2>
            <div class="nup-accent-bar"></div>
            <p class="lead">Our growing impact across Uganda</p>
        </div>

        <div class="row g-4 text-center">
            <div class="col-md-3">
                <div class="nup-card">
                    <div class="nup-icon-circle">
                        <i class="bi bi-people"></i>
                    </div>
                    <h3 class="display-6 fw-bold text-nup-purple">57</h3>
                    <p class="text-muted mb-0">Members of Parliament</p>
                </div>
            </div>

            <div class="col-md-3">
                <div class="nup-card">
                    <div class="nup-icon-circle">
                        <i class="bi bi-geo-alt"></i>
                    </div>
                    <h3 class="display-6 fw-bold text-nup-red">112</h3>
                    <p class="text-muted mb-0">Districts Covered</p>
                </div>
            </div>

            <div class="col-md-3">
                <div class="nup-card">
                    <div class="nup-icon-circle">
                        <i class="bi bi-person-check"></i>
                    </div>
                    <h3 class="display-6 fw-bold text-nup-purple">1000+</h3>
                    <p class="text-muted mb-0">Active Candidates</p>
                </div>
            </div>

            <div class="col-md-3">
                <div class="nup-card">
                    <div class="nup-icon-circle">
                        <i class="bi bi-heart"></i>
                    </div>
                    <h3 class="display-6 fw-bold text-nup-red">Millions</h3>
                    <p class="text-muted mb-0">Supporters Nationwide</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="nup-section-red position-relative cta-section">
    <div class="container">
        <div class="text-center">
            <h2 class="display-6 fw-bold mb-4">Ready to Join the Movement?</h2>
            <p class="lead mb-4">
                Be part of the change Uganda needs. Support NUP candidates and help build a better future for all Ugandans.
            </p>
            <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                <a href="{{ site_config.contact_url|default:'/contact/' }}" class="btn btn-nup-secondary btn-lg px-4 me-sm-3">
                    Contact Us
                </a>
                <a href="{% url 'search' %}" class="btn btn-nup-secondary btn-lg px-4">
                    <i class="bi bi-search"></i> Find Your Representative
                </a>
            </div>
        </div>
    </div>
</section>

{% include 'includes/nup-official-footer.html' %}
{% endblock %}

{% block extra_css %}
<!-- NUP Theme CSS -->
<link rel="stylesheet" href="{% static 'css/nup-theme.css' %}?v=2024">

<!-- Progressive Icon Placeholders CSS -->
<link rel="stylesheet" href="{% static 'css/progressive-icon-placeholders.css' %}">

<!-- Preloader CSS -->
<link rel="stylesheet" href="{% static 'css/homepage-preloader.css' %}?v=2024">
<!-- Critical CSS for immediate loading -->
<link rel="stylesheet" href="{% static 'css/homepage-critical.css' %}?v=2024">
<!-- Optimized homepage CSS -->
<link rel="stylesheet" href="{% static 'css/homepage-optimized.css' %}?v=2024">
<!-- Mobile fixes -->
<link rel="stylesheet" href="{% static 'css/home-carousel-mobile-fix.css' %}?v=2024">
<link rel="stylesheet" href="{% static 'css/hero-mobile-center.css' %}?v=2024">
<style>
/* Feature cards are now handled by optimized CSS */
.opacity-10 {
    opacity: 0.1;
}

/* FORCE REMOVE ANY BLUE BARS/BORDERS FROM FEATURE CARDS */
.modern-feature-card,
.feature-card,
.card,
.card.card-accent,
[data-theme="dark"] .card.card-accent {
    border-top: none !important;
    border-top-color: transparent !important;
    border-top-width: 0 !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: none !important;
}

/* Ensure no blue borders anywhere on feature cards */
.modern-feature-card::before,
.feature-card::before,
.card::before {
    display: none !important;
    content: none !important;
}

.modern-feature-card::after,
.feature-card::after,
.card::after {
    display: none !important;
    content: none !important;
    border-top: none !important;
    border-color: transparent !important;
}

/* Specifically override directory-cards-dark.css rule */
.features-section .card,
.features-section .card.card-accent,
.features-section [data-theme="dark"] .card.card-accent,
.features-section .modern-feature-card,
.features-section .modern-feature-card.card-accent,
.features-section [data-theme="dark"] .modern-feature-card.card-accent {
    border-top: none !important;
    border-top-color: transparent !important;
    border-top-width: 0 !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: none !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Additional overrides for any potential blue accent classes */
.modern-feature-card.card-accent::before,
.modern-feature-card.card-accent::after,
.card-accent::before,
.card-accent::after {
    display: none !important;
    content: none !important;
}

/* Override any Bootstrap card accent styles */
.card-accent,
.card.card-accent {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: none !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Force override any inherited styles */
.features-section .modern-feature-card {
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: none !important;
}

/* ULTIMATE OVERRIDE - Specifically target the directory-cards-dark.css rule */
[data-theme="dark"] .features-section .modern-feature-card,
[data-theme="dark"] .features-section .modern-feature-card.card,
[data-theme="dark"] .features-section .modern-feature-card.card-accent,
[data-theme="dark"] .modern-feature-card,
[data-theme="dark"] .modern-feature-card.card,
[data-theme="dark"] .modern-feature-card.card-accent {
    border-top: none !important;
    border-top-color: transparent !important;
    border-top-width: 0 !important;
    border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* NUCLEAR OPTION - Remove all possible blue borders */
.modern-feature-card,
.modern-feature-card *,
.modern-feature-card::before,
.modern-feature-card::after,
.modern-feature-card *::before,
.modern-feature-card *::after {
    border-top-color: transparent !important;
    border-left-color: rgba(255, 255, 255, 0.1) !important;
    border-right-color: rgba(255, 255, 255, 0.1) !important;
    border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

/* Ensure no blue color anywhere in feature cards */
.modern-feature-card {
    border-top: none !important;
    border-left: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Override any possible pseudo-element borders */
.modern-feature-card::before,
.modern-feature-card::after {
    border: none !important;
    background: none !important;
    content: none !important;
    display: none !important;
}

/* Custom blue background class for robot icon */
.bg-blue {
    background-color: #0066ff !important;
    background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%) !important;
}

/* Minimal icon - just the icon without circle background */
.minimal-icon {
    width: auto !important;
    height: auto !important;
    border-radius: 0 !important;
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 48px !important;
    color: #0066ff !important;
    margin: 0 auto 1.5rem auto !important;
    transition: all 0.3s ease !important;
    padding: 0 !important;
}

.minimal-icon i {
    font-size: 48px !important;
    color: #0066ff !important;
    transition: all 0.3s ease !important;
}

.modern-feature-card:hover .minimal-icon {
    transform: scale(1.1) !important;
}

.modern-feature-card:hover .minimal-icon i {
    color: #ffffff !important;
    text-shadow: 0 0 20px rgba(0, 102, 255, 0.8) !important;
}
/* Responsive spacing fixes for zoom levels */
.hero-section {
    padding-top: clamp(2rem, 4vw, 3rem) !important;
    padding-bottom: clamp(2rem, 4vw, 3rem) !important;
}

.hero-section .container {
    padding-top: clamp(1rem, 3vw, 2rem) !important;
    padding-bottom: clamp(1rem, 3vw, 2rem) !important;
}

.features-section {
    padding-top: clamp(2rem, 4vw, 3rem) !important;
    padding-bottom: clamp(2rem, 4vw, 3rem) !important;
}

/* Reduce margin between sections at higher zoom levels */
@media (min-width: 1200px) {
    .hero-section {
        margin-bottom: -1rem;
    }
}

/* Additional zoom-responsive fixes */
@media (min-width: 768px) {
    .hero-section {
        min-height: auto !important;
        height: auto !important;
    }

    .features-section .text-center.mb-5 {
        margin-bottom: clamp(2rem, 3vw, 3rem) !important;
    }
}

/* Prevent excessive spacing on very high zoom levels */
@media (min-width: 1400px) {
    .hero-section,
    .features-section {
        padding-top: 2.5rem !important;
        padding-bottom: 2.5rem !important;
    }

    .hero-section .container {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important;
    }
}

/* Notification styling - aligned with main content */
#pending-notifications-container {
    padding-top: 1rem;
    padding-bottom: 0.5rem;
}

#pending-notifications-container .glass-notification {
    margin-bottom: 1rem;
}

/* Responsive notification alignment */
@media (max-width: 991.98px) {
    #pending-notifications-container .col-lg-6 {
        max-width: 100%;
        padding-left: 15px;
        padding-right: 15px;
    }
}

@media (min-width: 992px) {
    #pending-notifications-container .col-lg-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

/* Search form styles */
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.search-form-card {
    width: 100%;
    max-width: 600px;
}

@media (max-width: 767.98px) {
    .search-form-card {
        max-width: 100%;
    }
}

/* Company Logo Carousel Styles */
/* Company Logo Carousel base styles - mobile styles in home-carousel-mobile-fix.css */
.company-logo-carousel-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    padding: 30px 0 70px 0; /* More padding at bottom for company info */
    margin-bottom: 30px;
}

.company-logo-carousel {
    display: flex;
    animation: scroll 60s linear infinite; /* Slower animation for larger logos */
    width: max-content;
}

.company-logo-item {
    flex: 0 0 auto;
    margin: 0 30px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

.company-logo-item a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-decoration: none;
    width: 360px; /* Match logo width */
    color: #333;
    transition: all 0.3s ease;
}

.company-logo-item a:hover {
    color: #0d6efd;
    text-decoration: none;
}

/* Desktop logo container styles - mobile styles in home-carousel-mobile-fix.css */
@media (min-width: 769px) {
    .logo-container {
        height: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
    }

    .company-info {
        text-align: center;
        width: 100%;
    }
}

.company-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.assistant-count {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0;
}

.assistant-count i {
    color: #0d6efd;
    margin-right: 5px;
}

/* Desktop logo styles - mobile styles in home-carousel-mobile-fix.css */
@media (min-width: 769px) {
    .company-logo {
        height: 180px; /* 3x larger */
        width: auto;
        max-width: 360px; /* 3x larger */
        object-fit: contain;
        opacity: 0.8;
        transition: all 0.3s ease;
        margin-bottom: 10px;
    }

    .company-logo:hover {
        opacity: 1;
        transform: scale(1.05);
    }

    .company-logo-placeholder {
        height: 180px; /* 3x larger */
        width: 360px; /* 3x larger */
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border-radius: 8px;
        opacity: 0.8;
        transition: all 0.3s ease;
        margin-bottom: 10px;
    }

    .company-logo-placeholder:hover {
        opacity: 1;
    }

    .company-logo-placeholder i {
        font-size: 48px; /* Larger icon */
        color: #6c757d;
        margin-bottom: 10px;
        display: block;
    }

    .company-logo-placeholder span {
        font-size: 16px; /* Larger text */
        color: #6c757d;
        text-align: center;
        max-width: 300px; /* Wider to accommodate larger text */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Homepage-specific: Ensure no footer spacing since footer is removed */
body {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* Remove any footer-related margins from the last section */
.features-section:last-of-type,
section:last-of-type {
    margin-bottom: 0 !important;
    padding-bottom: 2rem !important;
}

/* Disable mobile footer enhanced styles on homepage */
@media (max-width: 767.98px) {
    footer.footer,
    .footer-accordion,
    .mobile-footer-enhanced {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
        overflow: hidden !important;
    }
}

/* CTA Section Container Fixes */
.cta-section {
    padding: 3rem 0 !important;
    margin: 0 !important;
}

.cta-section .container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
}

.cta-section .bg-white {
    margin: 0 !important;
    padding: 3rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Responsive CTA fixes */
@media (max-width: 991.98px) {
    .cta-section .container {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }

    .cta-section .bg-white {
        padding: 2.5rem 2rem !important;
    }
}

@media (max-width: 767.98px) {
    .cta-section .container {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .cta-section .bg-white {
        padding: 2rem 1.5rem !important;
    }
}

@media (max-width: 575.98px) {
    .cta-section .container {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    .cta-section .bg-white {
        padding: 1.5rem 1rem !important;
    }
}

/* Button container fixes */
.cta-section .d-grid {
    margin-top: 2rem !important;
}

.cta-section .btn {
    min-width: 160px !important;
    white-space: nowrap !important;
}

/* Ensure proper button spacing on mobile */
@media (max-width: 575.98px) {
    .cta-section .d-grid.gap-2.d-sm-flex {
        gap: 0.75rem !important;
    }

    .cta-section .btn {
        width: 100% !important;
        margin-bottom: 0.5rem !important;
    }

    .cta-section .btn:last-child {
        margin-bottom: 0 !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- Optimized homepage JavaScript -->
<script src="{% static 'js/homepage-optimized.js' %}" defer></script>
<!-- Include mobile footer enhanced JavaScript -->
<script src="{% static 'js/mobile-footer-enhanced.js' %}"></script>
<!-- Include mobile enhancements JavaScript -->
<script src="{% static 'js/mobile-enhancements.js' %}"></script>
<script src="{% static 'js/touch-interactions.js' %}"></script>

<!-- Initialize mobile footer enhancements and progressive mode on homepage -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile footer enhancements on homepage
    if (window.innerWidth <= 767.98) {
        initializeMobileFooterEnhancements();
    }

    // Re-initialize on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 767.98) {
            initializeMobileFooterEnhancements();
        }
    });

    // Initialize progressive mode
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 992;

    // Apply progressive mode based on device type
    if (isMobile) {
        console.log('[Progressive Mode] Applying mobile progressive mode');
        document.body.classList.add('progressive-mobile-mode');
        enhanceFooterForMobile();
    } else if (isTablet) {
        console.log('[Progressive Mode] Applying tablet progressive mode');
        document.body.classList.add('progressive-tablet-mode');
        enhanceFooterForTablet();
    } else {
        console.log('[Progressive Mode] Applying desktop progressive mode');
        document.body.classList.add('progressive-desktop-mode');
    }

    // Function to enhance footer for mobile devices
    function enhanceFooterForMobile() {
        const footer = document.querySelector('footer.footer');
        if (footer) {
            footer.classList.add('mobile-enhanced');

            // Add touch feedback to accordion buttons
            const accordionButtons = footer.querySelectorAll('.accordion-button');
            accordionButtons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'translateX(6px) scale(0.98)';
                    this.style.transition = 'all 0.1s ease';
                }, { passive: true });

                button.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                        this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                    }, 100);
                }, { passive: true });
            });

            // Add visual indicators
            const socialIcons = footer.querySelectorAll('.social-icons-mobile a');
            socialIcons.forEach(icon => {
                icon.classList.add('progressive-icon');
            });
        }
    }

    // Function to enhance footer for tablet devices
    function enhanceFooterForTablet() {
        const footer = document.querySelector('footer.footer');
        if (footer) {
            footer.classList.add('tablet-enhanced');

            // Add tablet-specific enhancements
            const footerLinks = footer.querySelectorAll('a');
            footerLinks.forEach(link => {
                link.classList.add('tablet-optimized');
                link.style.padding = '8px 0';
                link.style.display = 'inline-block';
            });
        }
    }

    // Listen for orientation changes
    window.addEventListener('orientationchange', function() {
        console.log('[Progressive Mode] Orientation changed');
        setTimeout(function() {
            // Re-apply progressive mode
            const isMobile = window.innerWidth <= 768;
            const isTablet = window.innerWidth > 768 && window.innerWidth <= 992;

            if (isMobile) {
                document.body.classList.remove('progressive-tablet-mode', 'progressive-desktop-mode');
                document.body.classList.add('progressive-mobile-mode');
                enhanceFooterForMobile();
            } else if (isTablet) {
                document.body.classList.remove('progressive-mobile-mode', 'progressive-desktop-mode');
                document.body.classList.add('progressive-tablet-mode');
                enhanceFooterForTablet();
            } else {
                document.body.classList.remove('progressive-mobile-mode', 'progressive-tablet-mode');
                document.body.classList.add('progressive-desktop-mode');
            }
        }, 300);
    });
});
</script>

<!-- Homepage Authentication Fix -->
<script src="{% static 'js/homepage-auth-fix.js' %}"></script>
{% endblock %}

{% block footer %}
<!-- Footer completely removed from homepage -->
{% endblock %}
