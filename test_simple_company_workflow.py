#!/usr/bin/env python
"""
Simple test to verify basic company creation and settings workflow.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth.models import User
from accounts.forms import CompanyCreationForm, CompanySettingsForm
from accounts.models import Company, CompanyInformation
import uuid

def test_simple_workflow():
    """Test basic company creation and settings retrieval"""
    print("🔬 SIMPLE COMPANY WORKFLOW TEST")
    print("=" * 50)

    # Create test user
    username = f"simple_test_{uuid.uuid4().hex[:6]}"
    user = User.objects.create_user(
        username=username,
        email=f'{username}@test.com',
        password='testpass'
    )
    print(f"✅ Created user: {user.username}")

    # Simple test data
    data = {
        'name': f'Simple Test Company {uuid.uuid4().hex[:4]}',
        'entity_type': 'company',
        'mission': 'Simple test mission',
        'website': 'https://simple-test.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '******-SIMPLE',
        'timezone': 'UTC',
        'language': 'en',
        'industry': 'Testing',
        'list_in_directory': True
    }

    try:
        # Step 1: Create company
        print("\n📝 Step 1: Creating company...")
        form = CompanyCreationForm(data=data, user=user)

        if form.is_valid():
            company = form.save()
            print(f"✅ Company created: {company.name}")
        else:
            print(f"❌ Form invalid: {form.errors}")
            return False

        # Step 2: Verify data saved
        print("\n🔍 Step 2: Checking saved data...")
        company_info = CompanyInformation.objects.get(company=company)

        checks = [
            ('mission', company_info.mission, data['mission']),
            ('website', company_info.website, data['website']),
            ('contact_email', company_info.contact_email, data['contact_email']),
            ('industry', company_info.industry, data['industry']),
        ]

        all_good = True
        for field, saved, expected in checks:
            if saved == expected:
                print(f"✅ {field}: {saved}")
            else:
                print(f"❌ {field}: {saved} (expected: {expected})")
                all_good = False

        # Step 3: Test settings form
        print("\n⚙️ Step 3: Testing settings form...")
        settings_form = CompanySettingsForm(instance=company_info)

        for field, _, expected in checks:
            if field in settings_form.initial:
                initial_value = settings_form.initial[field]
                if initial_value == expected:
                    print(f"✅ Settings form {field}: {initial_value}")
                else:
                    print(f"❌ Settings form {field}: {initial_value} (expected: {expected})")
                    all_good = False

        # Clean up
        company.delete()
        user.delete()

        if all_good:
            print("\n🎉 SIMPLE TEST PASSED!")
            return True
        else:
            print("\n⚠️ SIMPLE TEST FAILED!")
            return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_workflow()
    sys.exit(0 if success else 1)
