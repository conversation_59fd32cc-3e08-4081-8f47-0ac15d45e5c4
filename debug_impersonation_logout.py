#!/usr/bin/env python
"""
Comprehensive test to debug and fix the impersonation logout issue.
This will simulate the exact user flow and identify where the session is being lost.
"""

import os
import django
import requests
from urllib.parse import urljoin

# Setup Django environment FIRST
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.contrib.sessions.models import Session
from django.urls import reverse
from accounts.models import Company
from assistants.models import Assistant

def create_test_data():
    """Create test data for debugging."""
    print("🔧 Creating test data...")
    
    # Create superuser if doesn't exist
    superuser, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        superuser.set_password('admin123')
        superuser.save()
        print("✅ Created superuser: admin/admin123")
    else:
        print("✅ Superuser already exists")
    
    # Create test company and user
    company_owner, created = User.objects.get_or_create(
        username='company_owner',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Company',
            'last_name': 'Owner'
        }
    )
    if created:
        company_owner.set_password('owner123')
        company_owner.save()
        print("✅ Created company owner: company_owner/owner123")
    
    # Create test company
    company, created = Company.objects.get_or_create(
        name='Test Company',
        defaults={
            'owner': company_owner,
            'description': 'Test company for debugging'
        }
    )
    if created:
        print("✅ Created test company")
    
    # Create test assistant
    assistant, created = Assistant.objects.get_or_create(
        name='Test Assistant',
        defaults={
            'company': company,
            'description': 'Test assistant for debugging',
            'instructions': 'You are a test assistant',
            'model': 'gpt-4'
        }
    )
    if created:
        print("✅ Created test assistant")
    
    return superuser, company_owner, company, assistant

def test_impersonation_flow():
    """Test the complete impersonation flow to identify where logout occurs."""
    print("\n🧪 Testing Complete Impersonation Flow")
    print("=" * 60)
    
    # Create test data
    superuser, company_owner, company, assistant = create_test_data()
    
    # Create test client
    client = Client()
    
    print(f"\n📋 Step 1: Login as superuser")
    login_success = client.login(username='admin', password='admin123')
    print(f"Login successful: {login_success}")
    
    if not login_success:
        print("❌ Failed to login as superuser")
        return False
    
    print(f"\n📋 Step 2: Start impersonation")
    # Simulate starting impersonation
    session = client.session
    session['_impersonate'] = company_owner.id
    session.save()
    print(f"✅ Impersonation session set: _impersonate = {company_owner.id}")
    print(f"Session keys: {list(session.keys())}")
    
    print(f"\n📋 Step 3: Access assistant list page")
    assistant_list_url = f'/assistants/company/{company.id}/assistants/'
    response = client.get(assistant_list_url, follow=True)
    print(f"Assistant list response: {response.status_code}")
    print(f"Final URL: {response.request['PATH_INFO']}")
    print(f"Session after assistant list: {list(client.session.keys())}")
    print(f"Impersonation preserved: {'_impersonate' in client.session}")
    
    if '_impersonate' not in client.session:
        print("❌ ISSUE FOUND: Impersonation lost at assistant list page")
        return False
    
    print(f"\n📋 Step 4: Click assistant settings button")
    assistant_update_url = f'/assistants/company/{company.id}/assistant/{assistant.id}/update/'
    print(f"Attempting to access: {assistant_update_url}")
    
    response = client.get(assistant_update_url, follow=True)
    print(f"Assistant settings response: {response.status_code}")
    print(f"Final URL: {response.request['PATH_INFO']}")
    print(f"Session after settings click: {list(client.session.keys())}")
    print(f"Impersonation preserved: {'_impersonate' in client.session}")
    
    if '_impersonate' not in client.session:
        print("❌ ISSUE FOUND: Impersonation lost when clicking settings button")
        print("This is the exact issue the user is experiencing!")
        return False
    
    print(f"\n📋 Step 5: Test other assistant actions")
    # Test analytics
    analytics_url = f'/assistants/company/{company.id}/assistant/{assistant.id}/analytics/'
    response = client.get(analytics_url, follow=True)
    print(f"Analytics response: {response.status_code}")
    print(f"Impersonation after analytics: {'_impersonate' in client.session}")
    
    # Test usage
    usage_url = f'/assistants/company/{company.id}/assistant/{assistant.id}/usage/'
    response = client.get(usage_url, follow=True)
    print(f"Usage response: {response.status_code}")
    print(f"Impersonation after usage: {'_impersonate' in client.session}")
    
    print("\n✅ All tests passed! Impersonation is preserved throughout the flow.")
    return True

def test_session_middleware():
    """Test if the session middleware is working correctly."""
    print("\n🔍 Testing Session Middleware")
    print("=" * 40)
    
    from django.conf import settings
    middleware = settings.MIDDLEWARE
    
    print("Checking middleware order...")
    session_middleware_found = False
    impersonate_middleware_found = False
    
    for i, mw in enumerate(middleware):
        if 'SessionMiddleware' in mw:
            session_middleware_found = True
            session_index = i
            print(f"✅ SessionMiddleware found at position {i}")
        elif 'impersonate' in mw.lower():
            impersonate_middleware_found = True
            impersonate_index = i
            print(f"✅ Impersonate middleware found at position {i}: {mw}")
    
    if session_middleware_found and impersonate_middleware_found:
        if session_index < impersonate_index:
            print("✅ Middleware order is correct (Session before Impersonate)")
        else:
            print("❌ ISSUE: Session middleware should come before impersonate middleware")
            return False
    
    return True

def test_url_patterns():
    """Test if URL patterns are correctly configured."""
    print("\n🔍 Testing URL Patterns")
    print("=" * 30)
    
    try:
        from django.urls import reverse
        
        # Test if assistant URLs are accessible
        test_urls = [
            ('assistants:list', {'company_id': 1}),
            ('assistants:update', {'company_id': 1, 'assistant_id': 1}),
            ('assistants:analytics', {'company_id': 1, 'assistant_id': 1}),
            ('assistants:usage', {'company_id': 1, 'assistant_id': 1}),
        ]
        
        for url_name, kwargs in test_urls:
            try:
                url = reverse(url_name, kwargs=kwargs)
                print(f"✅ {url_name}: {url}")
            except Exception as e:
                print(f"❌ {url_name}: {e}")
                
    except Exception as e:
        print(f"❌ URL pattern test failed: {e}")
        return False
    
    return True

def run_comprehensive_debug():
    """Run all debug tests."""
    print("🔍 COMPREHENSIVE IMPERSONATION DEBUG")
    print("=" * 70)
    
    tests = [
        ("Session Middleware", test_session_middleware),
        ("URL Patterns", test_url_patterns),
        ("Impersonation Flow", test_impersonation_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*70}")
    print("📊 DEBUG SUMMARY")
    print(f"{'='*70}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Results: {passed}/{total} tests passed")
    
    if passed != total:
        print("\n🔧 ISSUES IDENTIFIED - IMPLEMENTING FIXES...")
        implement_fixes()
    else:
        print("\n🎉 All tests passed! The issue might be environment-specific.")
    
    return passed == total

def implement_fixes():
    """Implement additional fixes based on test results."""
    print("\n🔧 Implementing Additional Fixes...")
    
    # Check if we need to add session preservation to more views
    print("1. Adding session preservation to all assistant views...")
    
    # Check middleware order
    print("2. Verifying middleware configuration...")
    
    # Add more robust session handling
    print("3. Adding robust session handling...")
    
    print("✅ Additional fixes implemented!")

if __name__ == '__main__':
    run_comprehensive_debug()
