{% extends "base/layout.html" %}
{% load static account_tags rating_tags assistant_tags %} {# Load assistant_tags for query_string #}

{% block title %}{{ company.name }} - Public Profile{% endblock %}

{% block head_extra %}
{# Load company detail contact colors CSS #}
<link rel="stylesheet" href="{% static 'css/company-detail-contact-colors.css' %}">
<link rel="stylesheet" href="{% static 'css/tablet-mobile-contact-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/mobile-style-for-tablet.css' %}">{# Added mobile style for tablet #}
<link rel="stylesheet" href="{% static 'css/public-company-detail-tablet-fix.css' %}">{# Tablet mode layout fix #}
{# Load NUP company detail styling - LOAD LAST to override other styles #}
<link rel="stylesheet" href="{% static 'css/nup-company-detail-fix.css' %}">{# NUP company colors and complete design #}

<style>
/* Desktop mode company logo - small thumbnail positioned left of company name */
@media screen and (min-width: 769px) {
    .card-header .company-logo-container {
        width: 24px !important;
        height: 24px !important;
        min-width: 24px !important;
        min-height: 24px !important;
        max-width: 24px !important;
        max-height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        overflow: hidden !important;
        border-radius: 3px !important;
        background-color: #f8f9fa !important;
        flex-shrink: 0 !important;
        padding: 1px !important;
        margin-right: 0.5rem !important;
        border: none !important;
        border-bottom: none !important;
        position: relative !important;
        top: 0 !important;
        left: 0 !important;
    }

    .card-header .company-header-logo {
        width: 22px !important;
        height: 22px !important;
        max-width: 22px !important;
        max-height: 22px !important;
        object-fit: contain !important;
        border-radius: 2px !important;
        display: block !important;
    }

    .card-header .company-logo-fallback {
        font-size: 0.75rem !important;
        color: #6c757d !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
    }

    /* Ensure company header has proper flex layout */
    .company-header-main {
        display: flex !important;
        align-items: center !important;
    }

    /* Ensure the company name is properly aligned with the small logo */
    .card-header .company-header-main h1.h3 {
        margin-bottom: 0 !important;
        line-height: 1.2 !important;
        display: inline-block !important;
        vertical-align: middle !important;
    }
}

/* Mobile responsive adjustments */
@media screen and (max-width: 768px) {
    .company-logo-container {
        width: 32px !important;
        height: 32px !important;
        min-width: 32px !important;
        min-height: 32px !important;
        max-width: 32px !important;
        max-height: 32px !important;
        margin-right: 0.75rem !important;
    }

    .company-header-logo {
        max-width: 28px !important;
        max-height: 28px !important;
    }
}

/* Also control assistant logos to prevent them from being too large */
.directory-item-link-wrapper img,
.list-group-item img.rounded {
    max-height: 48px !important;
    max-width: 48px !important;
    height: 48px !important;
    width: auto !important;
}

/* Control assistant placeholder containers */
.list-group-item .bg-light.rounded {
    height: 48px !important;
    width: 48px !important;
    max-height: 48px !important;
    max-width: 48px !important;
}
</style>
{# Basic styling for display stars (read-only) #}
<style>
.star-rating { display: inline-flex; align-items: center; font-size: 1em; }
.star-rating .bi-star-fill { color: #f5c518; }
.star-rating .bi-star { color: #ddd; }
.star-rating .rating-count { margin-left: 0.5em; font-size: 0.9em; color: #6c757d; }
.contact-info li { margin-bottom: 0.3rem !important; }
.list-group-item { transition: box-shadow 0.2s ease-in-out; }
.list-group-item:hover { box-shadow: 0 .125rem .25rem rgba(0,0,0,.075)!important; }
.tag-badge { font-size: 0.8em; margin-right: 0.3rem; margin-bottom: 0.3rem; }
.list-group-item .like-button { z-index: 10; }
/* Added from assistant_list for consistency */
.item-description { font-size: 0.9em; line-height: 1.4; text-align: left; }

</style>
{% endblock %}


{% block content %}
<div class="container mt-4 mb-5">
    <div class="card shadow-sm mb-4">
        {# Updated Card Header with Logo and Flexbox - Mobile Optimized #}
        <div class="card-header bg-light">
            <div class="d-flex align-items-center company-header-main">
                {# Company Logo #}
                {% if company.info.logo and company.info.logo.url %}
                    <div class="company-logo-container" style="width: 72px !important; height: 72px !important; min-width: 72px !important; min-height: 72px !important; max-width: 72px !important; max-height: 72px !important; margin-right: 0.75rem !important; padding: 2px !important; display: flex; align-items: center; justify-content: center; overflow: hidden; border-radius: 6px; background-color: #f8f9fa; flex-shrink: 0;">
                        <img src="{{ company.info.logo.url }}" alt="{{ company.name }} Logo" class="company-header-logo" style="width: 66px !important; height: 66px !important; max-width: 66px !important; max-height: 66px !important; object-fit: contain !important;"
                             onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'company-logo-fallback\'><i class=\'bi bi-building\'></i></div>';">
                    </div>
                {% else %}
                    {# Fallback icon/placeholder #}
                    <div class="company-logo-container" style="width: 72px !important; height: 72px !important; min-width: 72px !important; min-height: 72px !important; max-width: 72px !important; max-height: 72px !important; margin-right: 0.75rem !important; padding: 2px !important; display: flex; align-items: center; justify-content: center; overflow: hidden; border-radius: 6px; background-color: #f8f9fa; flex-shrink: 0;">
                        <div class="company-logo-fallback" style="font-size: 2rem !important;">
                            <i class="bi bi-building"></i>
                        </div>
                    </div>
                {% endif %}

                {# Company Name and Rating #}
                <div class="me-auto">
                    <h1 class="h3 mb-0">{{ company.name }}</h1>

                    {# Rating Display #}
                    <div id="rating-display-{{ company.id }}" class="mt-1">
                        {% if company.listing %}
                            {% render_stars company.listing.avg_rating company.listing.total_ratings %}
                        {% else %}
                            {% render_stars 0 0 %}
                        {% endif %}
                    </div>
                </div>

                {# --- Actions (Desktop Only) --- #}
                <div class="d-flex align-items-center action-buttons d-none d-lg-flex">
                    {# Like Button #}
                    {% if user.is_authenticated %}
                        <button
                            class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %} me-2"
                            data-item-id="{{ company.id }}"
                            data-item-type="company"
                            title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                            style="background: none; border: none;">
                            <i class="bi bi-heart{% if is_favorited %}-fill{% endif %}"></i>
                        </button>
                    {% endif %}

                    {# QR Code Button #}
                    {% if company_qr_code_url %}
                    <button type="button" class="btn btn-sm me-1" id="company-qr-code-btn" title="Show Company QR Code"
                            data-bs-toggle="modal" data-bs-target="#companyQrCodeModal" data-qr-url="{{ company_qr_code_url }}" style="background-color: #252638; border-color: #252638; color: #ffffff;">
                        <i class="bi bi-qr-code"></i>
                    </button>
                    {% endif %}

                    {# Share Button #}
                    <button type="button" class="btn btn-sm" id="copy-company-url-btn" title="Copy Company Profile URL" style="background-color: #252638; border-color: #252638; color: #ffffff;">
                        <i class="bi bi-share me-1"></i> Share URL
                    </button>
                </div>
            </div>

            {# --- Actions (Below Rating) - Mobile/Tablet Only --- #}
            <div class="d-flex align-items-center action-buttons-mobile d-lg-none mt-2">
                {# Rate Company Button #}
                {% if user.is_authenticated %}
                    <button type="button" class="btn btn-sm btn-outline-primary me-2"
                            data-bs-toggle="modal" data-bs-target="#ratingModal"
                            data-company-id="{{ company.id }}"
                            data-company-name="{{ company.name }}">
                        <i class="bi bi-star me-1"></i>Rate this company
                    </button>
                {% endif %}

                {# Like Button #}
                {% if user.is_authenticated %}
                    <button
                        class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %} me-2"
                        data-item-id="{{ company.id }}"
                        data-item-type="company"
                        title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                        style="background: none; border: none;">
                        <i class="bi bi-heart{% if is_favorited %}-fill{% endif %}"></i>
                    </button>
                {% endif %}

                {# QR Code Button #}
                {% if company_qr_code_url %}
                <button type="button" class="btn btn-sm me-1" id="company-qr-code-btn-mobile" title="Show Company QR Code"
                        data-bs-toggle="modal" data-bs-target="#companyQrCodeModal" data-qr-url="{{ company_qr_code_url }}" style="background-color: #252638; border-color: #252638; color: #ffffff;">
                    <i class="bi bi-qr-code"></i>
                </button>
                {% endif %}

                {# Share Button #}
                <button type="button" class="btn btn-sm" id="copy-company-url-btn-mobile" title="Copy Company Profile URL" style="background-color: #252638; border-color: #252638; color: #ffffff;">
                    <i class="bi bi-share me-1"></i> Share URL
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                {# Left Column: Description, Mission, Tags/Categories #}
                <div class="col-md-7 mb-3 mb-md-0">
                    {% if company.info.description %}
                        <p class="lead mb-3">{{ company.info.description }}</p>
                    {% endif %}
                    {% if company.info.mission %}
                        <p class="fst-italic text-muted mb-3"><strong>Mission:</strong> {{ company.info.mission }}</p>
                    {% endif %}
                     {# Categories & Tags from CompanyListing #}
                     {% if company.listing %}
                        <div class="mt-2">
                             {% for category in company.listing.categories.all %} {# Use .all for ManyToMany #}
                                <span class="badge bg-secondary tag-badge">{{ category.name }}</span> {# Access name attribute #}
                             {% endfor %}
                             {% for tag in company.listing.tags %} {# Tags remain JSON #}
                                <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                             {% endfor %}
                        </div>
                    {% endif %}
                </div>
                {# Right Column: Contact Info, Address, Rating - Added border, padding. Aligning list items to the end. #}
                <div class="col-md-5 border-start ps-md-4"> {# Removed flex alignment from column #}
                    <h5 class="mb-3">Contact & Location</h5> {# Removed text-end from heading #}
                    <ul class="list-unstyled small contact-info"> {# Removed text-muted class #}
                        {% if company.info.address_line1 or company.info.city %}
                            <li class="d-flex justify-content-end mb-2"> {# Align this list item content to the end #}
                                <i class="bi bi-geo-alt fs-5 me-2"></i>
                                <div class="contact-text"> {# Added contact-text class #}
                                    {% if company.info.address_line1 %}{{ company.info.address_line1 }}{% if company.info.address_line2 %}, {{ company.info.address_line2 }}{% endif %}<br>{% endif %}
                                    {% if company.info.city %}{{ company.info.city }}{% endif %}
                                    {% if company.info.postal_code %} {{ company.info.postal_code }}{% endif %}
                                    {% if company.info.country %}<br>{{ company.info.country }}{% endif %}
                                </div>
                            </li>
                        {% endif %}
                        {% if company.info.contact_phone %}
                            <li class="d-flex justify-content-end mb-2">
                                <i class="bi bi-telephone me-2"></i>
                                <span class="contact-text">{{ company.info.contact_phone }}</span>
                            </li>
                        {% endif %}
                         {% if company.info.contact_email %}
                            <li class="d-flex justify-content-end mb-2">
                                <i class="bi bi-envelope me-2"></i>
                                <a href="mailto:{{ company.info.contact_email }}" class="contact-text">{{ company.info.contact_email }}</a>
                            </li>
                        {% endif %}
                        {% if company.listing.website|default:company.info.website %}
                            <li class="d-flex justify-content-end mb-2">
                                <i class="bi bi-link-45deg me-2"></i>
                                <a href="{{ company.listing.website|default:company.info.website }}" target="_blank" rel="noopener noreferrer" class="contact-text website-url">{{ company.listing.website|default:company.info.website }}</a>
                            </li>
                        {% endif %}
                    </ul>

                     {# Company Average Assistant Rating - Align to end as well #}
                     {% with avg_rating=company.average_assistant_rating %}
                        {% if avg_rating %}
                            <div class="star-rating mt-3 d-flex justify-content-end"> {# Align rating to end #}
                                {% with avg_rating_int=avg_rating|floatformat:"0" %}
                                    {% for i in "12345"|make_list %}
                                        {% if i|add:"0" <= avg_rating_int|add:"0" %}<i class="bi bi-star-fill"></i>{% else %}<i class="bi bi-star"></i>{% endif %}
                                    {% endfor %}
                                {% endwith %}
                                <span class="rating-count">
                                    ({{ avg_rating|floatformat:1 }} avg. assistant rating)
                                </span>
                            </div>
                        {% else %}
                            <div class="mt-3 small text-muted italic text-end">(No assistant ratings yet)</div> {# Align fallback text too #}
                        {% endif %}
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>

    <hr class="my-4">

    <h2 class="h4 mb-3">Available Assistants</h2>

    {# Search/Filter Form (Layout matching my_favorites.html assistant filter) #}
    {# Search/Filter Form (Layout matching my_favorites.html assistant filter) #}
    <form method="get" class="mb-4 p-3 border rounded bg-light">
        {# No hidden tab input needed here #}
        <div class="row g-2 align-items-center">
            <div class="col-lg"> {# Name & Description Filter #}
                <input type="text" name="q_name" id="q_name_pub" class="form-control form-control-sm" placeholder="Search name or description..." value="{{ q_name|default:'' }}">
            </div>
            {# Company filter omitted as we are on a company page #}
            <div class="col-lg"> {# Category Filter #}
                <input type="text" name="q_category" id="q_category_pub" class="form-control form-control-sm" placeholder="Filter by category..." value="{{ q_category|default:'' }}">
            </div>
            <div class="col-lg"> {# Tag Filter #}
                <input type="text" name="q_tag" id="q_tag_pub" class="form-control form-control-sm" placeholder="Filter by tag..." value="{{ q_tag|default:'' }}">
            </div>
            {# Buttons column #}
            <div class="col-auto">
                <button type="submit" class="btn btn-primary btn-sm">Filter</button> {# Changed text to Filter #}
                {# Reset link should clear all relevant query params #}
                {% with current_params=request.GET.copy %}
                    {% query_string current_params q_name="" q_category="" q_tag="" folder_id="" as reset_url %}
                    <a href="{{ request.path }}{{ reset_url }}" class="btn btn-sm" style="background-color: #252638; border-color: #252638; color: #ffffff;">Reset</a>
                {% endwith %}
            </div>
        </div>
    </form>
    {# End Search Form #}

    {# Display Active Filters #}
    {% if q_name or q_category or q_tag %}
    <div class="mb-3 small text-muted">
        Filtering by:
        {% if q_name %}<span>Name/Desc "{{ q_name }}"</span>{% endif %}
        {% if q_category %}{% if q_name %}, {% endif %}<span>Category "{{ q_category }}"</span>{% endif %}
        {% if q_tag %}{% if q_name or q_category %}, {% endif %}<span>Tag "{{ q_tag }}"</span>{% endif %}
    </div>
    {% endif %}
    {# End Display Active Filters #}

    {# Folder Filter Links (Similar to assistant_list.html but without edit/delete) #}
    <div class="folder-filter-buttons mb-3 d-flex flex-wrap align-items-center border-bottom pb-2" role="group" aria-label="Filter by folder">
        {% with current_params=request.GET.copy %}
            {% query_string current_params folder_id="" as all_url %}
            <a href="{{ request.path }}{{ all_url }}" class="btn btn-link text-decoration-none p-1 me-3 {% if not selected_folder_id or selected_folder_id == "" %}active{% endif %}">
                <i class="bi bi-grid-fill me-1"></i>Show All
            </a>
            {% query_string current_params folder_id="unassigned" as unassigned_url %}
            <a href="{{ request.path }}{{ unassigned_url }}" class="btn btn-link text-decoration-none p-1 me-3 {% if selected_folder_id == 'unassigned' %}active{% endif %}">
                <i class="bi bi-box me-1"></i>Unassigned
            </a>
            {% for folder in folders %} {# Loop through folders passed from view #}
                {% query_string current_params folder_id=folder.id as folder_url %}
                <a href="{{ request.path }}{{ folder_url }}" class="btn btn-link text-decoration-none p-1 me-3 {% if selected_folder_id == folder.id|stringformat:"s" %}active{% endif %}" data-folder-id="{{ folder.id }}">
                    <i class="bi bi-folder me-1"></i>{{ folder.name }}
                </a>
            {% endfor %}
        {% endwith %}
        {# No Add Folder button on public page #}
    </div>
    {# End Folder Filter Links #}

    {# Use the correct context variable 'grouped_assistants_list' #}
    {% if grouped_assistants_list %}
        {% csrf_token %} {# Add CSRF token once for the page #}
        <div id="company-assistants-list"> {# Container for grouped assistants #}
            {% for folder, assistants_in_folder in grouped_assistants_list %} {# Iterate over the list of tuples #}
                {# Display Folder Heading #}
                <h5 class="mt-4 mb-2 text-muted">
                    {# Check if folder object exists (it's None for Unassigned) #}
                    <i class="bi {% if folder %}bi-folder{% else %}bi-box{% endif %} me-2"></i>
                    {% if folder %}{{ folder.name }}{% else %}Unassigned{% endif %}
                </h5>
                <div class="list-group mb-3"> {# Group items within this folder #}
                    {% for assistant in assistants_in_folder %}
                    <div class="list-group-item mb-3 border rounded shadow-sm" data-assistant-id="{{ assistant.id }}">
                        <div class="row g-3">
                            {# Link wrapper covers first 3 columns (col-md-10) and contains an inner row #}
                    <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="directory-item-link-wrapper col-md-10 row g-3 me-0 text-decoration-none text-body">
                        {# Column 1: Logo (col-md-2 within link row) - With Fallback Logic #}
                        <div class="col-md-2 d-flex justify-content-center align-items-start pt-1">
                             {% with logo_url=assistant.get_logo_url %}
                                {% if logo_url %}
                                    <img src="{{ logo_url }}" alt="{{ assistant.name }} logo" class="rounded" style="height: 64px; width: auto; max-width: 100%; object-fit: contain; display: block; margin: auto;">
                                {% else %}
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 64px; width: 75px;">
                                         <i class="bi bi-robot text-muted fs-3"></i>
                                    </div>
                                {% endif %}
                            {% endwith %}
                        </div>
                        {# Column 2: Name, Type, Tags/Categories (col-md-3 within link row) #}
                        <div class="col-md-3">
                            <h6 class="mb-1 fs-6 fw-semibold">{{ assistant.name }}</h6> {# Removed inner link #}
                            <span class="badge bg-primary bg-opacity-10 text-primary mb-2" style="font-size: 0.75em;">{{ assistant.get_assistant_type_display }}</span>
                            {% if assistant.listing %}
                                <div class="mt-1">
                                    {% for category in assistant.listing.categories %}
                                        <span class="badge bg-secondary tag-badge">{{ category }}</span>
                                    {% endfor %}
                                    {% for tag in assistant.listing.tags %}
                                        <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Column 3: Description (col-md-7 within link row) #}
                        <div class="col-md-7">
                            <p class="text-muted mb-0 item-description">
                                {{ assistant.description|default:"No description available."|truncatewords:50 }}
                            </p>
                        </div>
                    </a> {# End of clickable area link #}

                    {# Column 4: Rating & Actions (col-md-2) - Outside the link #}
                    <div class="col-md-2 d-flex flex-column align-items-end justify-content-start">
                        {# Rating Display Container with ID #}
                        <div class="rating-display-container mb-1 w-100 d-flex justify-content-end" id="rating-display-assistant-{{ assistant.id }}">
                            {% if assistant.listing %}
                                {% render_stars assistant.listing.avg_rating assistant.listing.total_ratings %}
                            {% else %}
                                <div class="star-rating mb-1"><span class="rating-count ms-1">(No ratings)</span></div>
                            {% endif %}
                        </div>
                        {# Favorite Button (Adjusted style like list view) #}
                        {% if user.is_authenticated %}
                            <button
                                class="like-button btn btn-sm p-0 ms-2 {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                data-item-id="{{ assistant.id }}"
                                data-item-type="assistant"
                                title="{% if assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}"
                                style="background: none; border: none; cursor: pointer; line-height: 1; margin-bottom: 0.5rem; z-index: 5;"> {# Added margin/z-index like list #}
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16" style="pointer-events: none;">
                                    <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314"/>
                                </svg>
                            </button>
                        {% endif %}
                        {# Rate Button (Copied from assistant_list.html) #}
                        {% if user.is_authenticated %}
                        <div class="w-100 d-flex justify-content-end mb-1">
                            <button type="button"
                                    class="btn btn-sm rate-assistant-btn" style="background-color: #252638; border-color: #252638; color: #ffffff;"
                                    data-bs-toggle="modal"
                                    data-bs-target="#ratingModal"
                                    data-assistant-id="{{ assistant.id }}"
                                    data-assistant-name="{{ assistant.name|escapejs }}"
                                    style="z-index: 5;">
                                <i class="bi bi-star me-1"></i> Rate
                            </button>
                        </div>
                        <div class="w-100 d-flex justify-content-end">
                            <span class="rating-update-message small text-success mt-1" id="rating-msg-assistant-{{ assistant.id }}" style="display: none;"></span> {# Added message span #}
                        </div>
                        {% endif %}
                        {# Removed Chat link as it's redundant with the main link #}
                            </div> {# End Actions Column #}
                        </div> {# /row #}
                    </div> {# /list-group-item #}
                    {% endfor %} {# End inner assistant loop #}
                </div> {# /list-group for this folder #}
            {% endfor %} {# End outer folder loop #}
        </div> {# /company-assistants-list #}

        {# Pagination was removed from the view context, so remove include here #}
        {# {% include "pagination.html" with page_obj=page_obj %} #}

    {% else %}
        <p class="text-muted text-center p-3">
            {% if search_query %}
                No public assistants found matching your search criteria
                {% if selected_folder_id == 'unassigned' %}in the Unassigned group{% elif selected_folder_id %}in this folder{% endif %}.
            {% elif selected_folder_id %}
                No public assistants found in this folder.
            {% else %}
                This company has no public assistants available.
            {% endif %}
        </p>
    {% endif %}
</div>

{# Rating Modal Structure (Copied from assistant_list.html) #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Select your rating for <strong id="modalAssistantName">this assistant</strong>:</p> {# Ensure ID matches JS #}
        <div class="modal-stars text-center mb-3" style="font-size: 2rem;">
            {% for i_int in "12345" %}
            <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                <i class="bi bi-star"></i>
            </button>
            {% endfor %}
        </div>
        <div id="modalErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{# Folder Options Modal (Copied from assistant_list.html) #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Add to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName">this item</strong> to favorites:</p>

        {# Option 1: Save without folder #}
        <div class="d-grid mb-3">
            <button type="button" class="btn btn-outline-primary text-start" id="saveNoFolderBtn">
                <i class="bi bi-bookmark-heart me-2"></i>Save (Uncategorized)
            </button>
        </div>

        <hr>

        {# Option 2: Add to existing folder #}
        <div id="existingFoldersSection" class="mb-3" style="display: none;">
            <label for="selectFolder" class="form-label small mb-1">Add to existing folder:</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                <select class="form-select" id="selectFolder">
                    <option selected disabled value="">Choose folder...</option>
                    {# Options will be populated by JS #}
                </select>
                <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                    <i class="bi bi-plus-lg"></i> Add
                </button>
            </div>
        </div>

        {# Option 3: Create new folder #}
        <div>
            <label for="newFolderName" class="form-label small mb-1">Or create a new folder:</label>
            <div class="input-group">
                 <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                <button class="btn" type="button" id="createFolderAndSaveBtn" disabled style="background-color: #252638; border-color: #252638; color: #ffffff;">
                   <i class="bi bi-check-lg"></i> Create & Save
                </button>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="text-danger small mt-3" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}

{# --- START: Company QR Code Modal --- #}
<div class="modal fade" id="companyQrCodeModal" tabindex="-1" aria-labelledby="companyQrCodeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-sm modal-dialog-centered"> {# Smaller modal #}
    <div class="modal-content">
      <div class="modal-header">
         <h5 class="modal-title" id="companyQrCodeModalLabel">Scan Company QR Code</h5> {# Updated title #}
         <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
       </div>
       <div class="modal-body text-center">
         {# Ensure the img tag has the correct ID and empty src initially #}
         <img src="" id="companyQrCodeImage" class="img-fluid mb-2" alt="QR Code for Company Profile" style="max-width: 200px; height: auto;"> {# Updated ID and alt text #}
         {# Add download link below the image #}
         <a href="#" id="companyQrCodeDownloadLink" class="btn btn-sm mt-2" download="company_qr_code.png" style="background-color: #252638; border-color: #252638; color: #ffffff;"> {# Updated ID and download name #}
             <i class="bi bi-download me-1"></i> Download QR Code
         </a>
         <p class="mt-2 small">Scan this code to view the company profile.</p> {# Updated text #}
       </div>
     </div>
  </div>
</div>
{# --- END: Company QR Code Modal --- #}

{# --- START: Company Rating Modal --- #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate <span id="modalCompanyName">this company</span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="ratingForm">
          {% csrf_token %}
          <p class="mb-3">How would you rate your experience with <strong id="modalCompanyNameInner">this company</strong>?</p>

          <div class="modal-stars text-center mb-4">
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="1">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="2">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="3">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="4">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="5">
              <i class="bi bi-star text-secondary"></i>
            </button>
          </div>

          <div id="modalErrorMsg" class="alert alert-danger" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>
{# --- END: Company Rating Modal --- #}

{% endblock %}

{% block extra_js %}
{# Copied and adapted from templates/directory/assistant_list.html #}
<script>
// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    // Fallback for meta tag if needed, though form input is standard in Django
    if (!csrfInput) {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) return csrfMeta.getAttribute('content');
    }
    return csrfInput ? csrfInput.value : null;
}

// --- Favorite Button & Folder Modal Logic ---
function handleFavoriteButtons() {
    // Target all containers that might have like buttons
    const listContainer = document.getElementById('company-assistants-list');
    const companyLikeButton = document.querySelector('.card-header .like-button'); // Company header like button
    const featuredContainer = document.querySelector('.featured-section'); // Featured section container

    const folderModalElement = document.getElementById('folderOptionsModal');
    let folderModal = null;
    if (folderModalElement) {
        try {
            folderModal = new bootstrap.Modal(folderModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal for folders:", e);
        }
    } else {
        console.error("Folder options modal element not found.");
        // Don't return here, like buttons might still work without modal
    }

    // Handle company like button if it exists
    if (companyLikeButton) {
        companyLikeButton.addEventListener('click', async (event) => {
            event.preventDefault();
            event.stopPropagation();

            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                console.error("CSRF token not found!");
                alert("Action failed. Please refresh.");
                return;
            }

            const itemId = companyLikeButton.dataset.itemId;
            const itemType = companyLikeButton.dataset.itemType; // Should be 'company' here
            const url = "{% url 'directory:toggle_saved_item' %}";

            // Store for potential modal use
            currentModalItemId = itemId;
            currentModalItemType = itemType;

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
                });

                const data = await response.json();

                if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

                if (data.status === 'success' && data.action === 'unfavorited') {
                    // Item was unfavorited successfully
                    updateHeartIcon(companyLikeButton, false);
                } else if (data.status === 'options') {
                    // Item is not saved, show folder options modal
                    if (folderModal) { // Check if modal exists and was initialized
                        populateAndShowFolderModal(data);
                    } else {
                        // Fallback: If modal doesn't exist, just mark as liked (basic functionality)
                        console.warn("Folder modal not found, performing basic like.");
                        updateHeartIcon(companyLikeButton, true); // Assume it was liked if options were offered
                    }
                } else if (data.status === 'success' && data.action === 'favorited') {
                    // Handle case where toggle immediately favorites (no modal needed)
                    updateHeartIcon(companyLikeButton, true);
                } else {
                    // Unexpected success response
                    console.error('Unexpected response from toggle_saved_item:', data);
                    alert('An unexpected error occurred.');
                }

            } catch (error) {
                console.error('Error handling favorite click:', error);
                alert(`An error occurred: ${error.message}`);
            }
        });
    }

    // Handle featured section like buttons if the container exists
    if (featuredContainer) {
        featuredContainer.addEventListener('click', async (event) => {
            const button = event.target.closest('.like-button');
            if (!button) return; // Ignore clicks not on the like button

            event.preventDefault();
            event.stopPropagation();

            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                console.error("CSRF token not found!");
                alert("Action failed. Please refresh.");
                return;
            }

            const itemId = button.dataset.itemId;
            const itemType = button.dataset.itemType;
            const url = "{% url 'directory:toggle_saved_item' %}";

            // Store for potential modal use
            currentModalItemId = itemId;
            currentModalItemType = itemType;

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
                });

                const data = await response.json();

                if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

                if (data.status === 'success' && data.action === 'unfavorited') {
                    // Item was unfavorited successfully
                    updateHeartIcon(button, false);
                } else if (data.status === 'options') {
                    // Item is not saved, show folder options modal
                    if (folderModal) { // Check if modal exists and was initialized
                        populateAndShowFolderModal(data);
                    } else {
                        // Fallback: If modal doesn't exist, just mark as liked (basic functionality)
                        console.warn("Folder modal not found, performing basic like.");
                        updateHeartIcon(button, true); // Assume it was liked if options were offered
                    }
                } else if (data.status === 'success' && data.action === 'favorited') {
                    // Handle case where toggle immediately favorites (no modal needed)
                    updateHeartIcon(button, true);
                } else {
                    // Unexpected success response
                    console.error('Unexpected response from toggle_saved_item:', data);
                    alert('An unexpected error occurred.');
                }

            } catch (error) {
                console.error('Error handling favorite click:', error);
                alert(`An error occurred: ${error.message}`);
            }
        });
    }

    if (!listContainer) {
        console.log("Company assistants list container (#company-assistants-list) not found.");
        // Don't return here, we might still have the company like button or featured section
    }

    // Store current item details for modal actions
    let currentModalItemId = null;
    let currentModalItemType = null;

    // --- Main Click Handler (Like Button using Event Delegation) ---
    listContainer.addEventListener('click', async (event) => {
        const button = event.target.closest('.like-button');
        if (!button) return; // Ignore clicks not on the like button

        event.preventDefault();
        event.stopPropagation(); // Prevent triggering other links if button is inside one

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            console.error("CSRF token not found!");
            alert("Action failed. Please refresh.");
            return;
        }

        const itemId = button.dataset.itemId;
        const itemType = button.dataset.itemType; // Should be 'assistant' here
        const url = "{% url 'directory:toggle_saved_item' %}";

        // Store for potential modal use
        currentModalItemId = itemId;
        currentModalItemType = itemType;

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
            });

            const data = await response.json();

            if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

            if (data.status === 'success' && data.action === 'unfavorited') {
                // Item was unfavorited successfully
                updateHeartIcon(button, false);
            } else if (data.status === 'options') {
                // Item is not saved, show folder options modal
                if (folderModal) { // Check if modal exists and was initialized
                    populateAndShowFolderModal(data);
                } else {
                    // Fallback: If modal doesn't exist, just mark as liked (basic functionality)
                    console.warn("Folder modal not found, performing basic like.");
                    updateHeartIcon(button, true); // Assume it was liked if options were offered
                }
            } else if (data.status === 'success' && data.action === 'favorited') {
                 // Handle case where toggle immediately favorites (no modal needed)
                 updateHeartIcon(button, true);
            }
             else {
                // Unexpected success response
                console.error('Unexpected response from toggle_saved_item:', data);
                alert('An unexpected error occurred.');
            }

        } catch (error) {
            console.error('Error handling favorite click:', error);
            alert(`An error occurred: ${error.message}`);
        }
    });

    // --- Helper: Update Heart Icon ---
    function updateHeartIcon(button, isSaved) {
        if (!button) return;

        // Update button classes
        if (isSaved) {
            button.classList.remove('text-secondary');
            button.classList.add('text-danger');
            button.title = 'Remove from Favorites';
        } else {
            button.classList.remove('text-danger');
            button.classList.add('text-secondary');
            button.title = 'Add to Favorites';
        }

        // Check for Bootstrap icon (i.bi)
        const bootstrapIcon = button.querySelector('i.bi');
        if (bootstrapIcon) {
            if (isSaved) {
                bootstrapIcon.classList.remove('bi-heart');
                bootstrapIcon.classList.add('bi-heart-fill');
            } else {
                bootstrapIcon.classList.remove('bi-heart-fill');
                bootstrapIcon.classList.add('bi-heart');
            }
        }

        // Check for SVG icon (used in company list)
        const svgIcon = button.querySelector('svg.bi');
        if (svgIcon) {
            // SVG already has the heart-fill path, we just change the color via the button's class
            // No need to modify the SVG itself
        }
    }

    // --- Helper: Populate and Show Folder Modal ---
    function populateAndShowFolderModal(data) {
        if (!folderModalElement || !folderModal) return;

        // Set item name
        const itemNameElement = folderModalElement.querySelector('#modalFolderNameItemName');
        if (itemNameElement) itemNameElement.textContent = data.item_name || 'this item';

        // Populate existing folders dropdown
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const existingFoldersSection = folderModalElement.querySelector('#existingFoldersSection');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');

        selectFolder.innerHTML = '<option selected disabled value="">Choose folder...</option>'; // Reset options
        if (data.folders && data.folders.length > 0) {
            data.folders.forEach(folder => {
                const option = document.createElement('option');
                option.value = folder.id;
                option.textContent = folder.name;
                selectFolder.appendChild(option);
            });
            existingFoldersSection.style.display = 'block';
            addToFolderBtn.disabled = true; // Disable until a folder is selected
        } else {
            existingFoldersSection.style.display = 'none';
        }

        // Reset other fields
        folderModalElement.querySelector('#newFolderName').value = '';
        folderModalElement.querySelector('#createFolderAndSaveBtn').disabled = true;
        folderModalElement.querySelector('#folderModalErrorMsg').style.display = 'none';
        folderModalElement.querySelector('#folderModalErrorMsg').textContent = '';

        // Show the modal
        folderModal.show();
    }

    // --- Folder Modal Event Listeners ---
    if (folderModalElement) {
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');
        const newFolderNameInput = folderModalElement.querySelector('#newFolderName');
        const createFolderBtn = folderModalElement.querySelector('#createFolderAndSaveBtn');
        const saveNoFolderBtn = folderModalElement.querySelector('#saveNoFolderBtn');
        const errorMsgElement = folderModalElement.querySelector('#folderModalErrorMsg');

        // Enable/disable "Add" button based on selection
        selectFolder.addEventListener('change', () => {
            addToFolderBtn.disabled = !selectFolder.value;
        });

        // Enable/disable "Create & Save" button based on input
        newFolderNameInput.addEventListener('input', () => {
            createFolderBtn.disabled = !newFolderNameInput.value.trim();
        });

        // Action: Save without folder
        saveNoFolderBtn.addEventListener('click', async () => {
            await handleSaveFolderAction("{% url 'directory:save_item_no_folder' %}", {});
        });

        // Action: Add to existing folder
        addToFolderBtn.addEventListener('click', async () => {
            const folderId = selectFolder.value;
            if (!folderId) return;
            await handleSaveFolderAction("{% url 'directory:add_item_to_folder' %}", { folder_id: folderId });
        });

        // Action: Create folder and save
        createFolderBtn.addEventListener('click', async () => {
            const folderName = newFolderNameInput.value.trim();
            if (!folderName) return;
            await handleSaveFolderAction("{% url 'directory:create_folder_and_save' %}", { folder_name: folderName });
        });

        // Generic function to handle the save actions
        async function handleSaveFolderAction(url, additionalParams) {
            // Use the globally stored item ID and type from the initial click
            if (!currentModalItemId || !currentModalItemType) {
                showFolderModalError("Item details missing. Please close and retry.");
                return;
            }
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                 showFolderModalError("CSRF token missing. Please refresh.");
                 return;
            }

            showFolderModalError(''); // Clear previous errors

            const bodyParams = new URLSearchParams({
                'item_id': currentModalItemId,
                'item_type': currentModalItemType,
                ...additionalParams
            });

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });
                const data = await response.json();

                if (!response.ok || data.status !== 'success') {
                    throw new Error(data.message || `HTTP error ${response.status}`);
                }

                // Success! Update the heart icon on the page and close modal
                // Find the button in any of our containers
                let originalButton = null;

                // Check in the company header
                if (companyLikeButton &&
                    companyLikeButton.dataset.itemId === currentModalItemId &&
                    companyLikeButton.dataset.itemType === currentModalItemType) {
                    originalButton = companyLikeButton;
                }

                // Check in the assistants list
                if (!originalButton && listContainer) {
                    originalButton = listContainer.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                }

                // Check in the featured section
                if (!originalButton && featuredContainer) {
                    originalButton = featuredContainer.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                }

                if (originalButton) {
                    updateHeartIcon(originalButton, true);
                } else {
                    console.warn(`Could not find original like button for item ${currentModalItemType} ${currentModalItemId}`);
                }

                if (folderModal) folderModal.hide();

            } catch (error) {
                console.error(`Error saving favorite via modal (${url}):`, error);
                showFolderModalError(`Error: ${error.message}`);
            }
        }

        function showFolderModalError(message) {
            errorMsgElement.textContent = message;
            errorMsgElement.style.display = message ? 'block' : 'none';
        }
    } else {
        console.log("Folder modal element not found, modal actions disabled.");
    }
}
// --- End Favorite Button & Folder Modal Logic ---


// --- Function to handle Share Buttons ---
function handleShareButtons() {
    // Use updated IDs for company page
    const qrCodeBtn = document.getElementById('company-qr-code-btn'); // May not exist if no QR code
    const copyUrlBtn = document.getElementById('copy-company-url-btn');
    const qrCodeModalElement = document.getElementById('companyQrCodeModal');
    const qrCodeImage = document.getElementById('companyQrCodeImage');
    const qrCodeDownloadLink = document.getElementById('companyQrCodeDownloadLink'); // Get the download link

    // QR Code Modal Logic (only if button and modal exist)
    if (qrCodeBtn && qrCodeModalElement && qrCodeImage && qrCodeDownloadLink) { // Check for download link too
        qrCodeModalElement.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget; // Button that triggered the modal
            if (button) {
                const qrUrl = button.getAttribute('data-qr-url');
                if (qrUrl) {
                    qrCodeImage.src = qrUrl; // Set image source
                    qrCodeDownloadLink.href = qrUrl; // Set download link href
                } else {
                    console.error("Company QR Code URL not found on button.");
                    qrCodeImage.src = ""; // Clear image if URL is missing
                    qrCodeDownloadLink.href = "#"; // Clear download link
                }
            }
        });
    } else {
        // Log error only if the button exists but modal/image/link doesn't
        if (qrCodeBtn && (!qrCodeModalElement || !qrCodeImage || !qrCodeDownloadLink)) {
            console.error("Company QR Code button exists, but modal, image, or download link element is missing.");
        }
    }

    // Copy URL Button Logic
    if (copyUrlBtn) {
        console.log("Copy Company URL button found:", copyUrlBtn); // Debug log
        copyUrlBtn.addEventListener('click', function() {
            const urlToCopy = window.location.href; // This correctly gets the current page URL
            navigator.clipboard.writeText(urlToCopy).then(() => {
                // Success feedback: Change icon temporarily
                const originalIconHTML = copyUrlBtn.innerHTML; // Store original icon HTML
                copyUrlBtn.innerHTML = '<i class="bi bi-check-lg text-success"></i> Copied!'; // Check icon + text
                copyUrlBtn.disabled = true; // Briefly disable

                setTimeout(() => {
                    copyUrlBtn.innerHTML = originalIconHTML; // Restore original icon/text
                    copyUrlBtn.disabled = false; // Re-enable
                }, 1500); // Restore after 1.5 seconds
            }).catch(err => {
                console.error('Failed to copy Company URL: ', err);
                // Optional: Add error feedback to the user
                const originalIconHTML = copyUrlBtn.innerHTML;
                copyUrlBtn.innerHTML = '<i class="bi bi-x-lg text-danger"></i> Failed'; // Error icon + text
                copyUrlBtn.disabled = true;
                setTimeout(() => {
                    copyUrlBtn.innerHTML = originalIconHTML;
                    copyUrlBtn.disabled = false;
                }, 2000);
            });
        });
    } else {
         console.error("Copy Company URL button not found.");
    }
}
// --- End Function to handle Share Buttons ---


// --- Modal Rating Logic (Adapted for Assistants on this page) ---
function handleRatingModal() {
    const ratingModalElement = document.getElementById('ratingModal');
    if (!ratingModalElement) {
        console.error("Rating modal element not found.");
        return; // Cannot proceed without the modal
    }

    let ratingModal = bootstrap.Modal.getInstance(ratingModalElement);
    if (!ratingModal) {
        try {
            ratingModal = new bootstrap.Modal(ratingModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal for ratings:", e);
            return; // Cannot proceed
        }
    }

    const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
    const modalAssistantName = ratingModalElement.querySelector('#modalAssistantName'); // Ensure this ID exists in the modal HTML
    const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
    const modalSubmitBtn = ratingModalElement.querySelector('#submitRatingBtn');
    const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
    let currentAssistantId = null;
    let selectedRating = 0;
    let currentItemType = ''; // Track if modal is for assistant or company

    // 1. Populate modal when triggered (using Bootstrap event)
    ratingModalElement.addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget; // Button that triggered the modal
        // *** Check if the button is specifically a rate-assistant-btn ***
        if (button && button.classList.contains('rate-assistant-btn')) {
            // Populate for Assistant
            currentItemType = 'assistant'; // Set type
            currentAssistantId = button.getAttribute('data-assistant-id');
            const assistantName = button.getAttribute('data-assistant-name');

            if (modalTitle) modalTitle.textContent = `Rate ${assistantName || 'Assistant'}`;
            if (modalAssistantName) modalAssistantName.textContent = assistantName || 'this assistant';
            ratingModalElement.dataset.itemId = currentAssistantId; // Store ID
            ratingModalElement.dataset.itemType = currentItemType; // Store type

        } else {
             // Clear state if triggered by something else
             currentAssistantId = null;
             currentItemType = '';
             ratingModalElement.dataset.itemType = ''; // Clear type marker
             console.log("Rating modal opened, but not by an assistant rate button.");
             // Optionally disable submit button or show a message if needed
             if (modalSubmitBtn) modalSubmitBtn.disabled = true;
             return; // Stop further processing for non-assistant triggers
        }


        // Reset modal state (Common for all types if modal is reused)
        selectedRating = 0;
        if (modalErrorMsg) {
            modalErrorMsg.textContent = '';
            modalErrorMsg.style.display = 'none';
        }
        if (modalSubmitBtn) modalSubmitBtn.disabled = true;

        // Reset stars visual state
        if (modalStarsContainer) {
            const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
            stars.forEach(star => {
                star.classList.remove('selected', 'text-warning');
                star.classList.add('text-secondary');
                const icon = star.querySelector('i');
                if (icon) {
                    icon.classList.remove('bi-star-fill');
                    icon.classList.add('bi-star');
                }
            });
        }
    });

    // 2. Handle star selection
    if (modalStarsContainer) {
        modalStarsContainer.addEventListener('click', function(event) {
            const starButton = event.target.closest('.modal-star-btn');
            if (!starButton) return;

            // Only act if the modal is currently set for an assistant
            if (ratingModalElement.dataset.itemType !== 'assistant') return;

            selectedRating = parseInt(starButton.dataset.ratingValue);
            if (modalSubmitBtn) modalSubmitBtn.disabled = false;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';

            const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
            stars.forEach(star => {
                const starValue = parseInt(star.dataset.ratingValue);
                const icon = star.querySelector('i');
                if (icon) {
                    if (starValue <= selectedRating) {
                        star.classList.add('selected', 'text-warning');
                        star.classList.remove('text-secondary');
                        icon.classList.remove('bi-star');
                        icon.classList.add('bi-star-fill');
                    } else {
                        star.classList.remove('selected', 'text-warning');
                        star.classList.add('text-secondary');
                        icon.classList.remove('bi-star-fill');
                        icon.classList.add('bi-star');
                    }
                }
            });
        });
    } else {
         console.error("Modal stars container not found.");
    }


    // 3. Handle rating submission
    if (modalSubmitBtn) {
        modalSubmitBtn.addEventListener('click', async function() {
            const type = ratingModalElement.dataset.itemType;
            const id = ratingModalElement.dataset.itemId; // Use generic itemId

            // Only proceed if the modal was opened for an assistant
            if (type !== 'assistant' || selectedRating === 0 || !id) {
                 if (modalErrorMsg && type === 'assistant') {
                    modalErrorMsg.textContent = 'Please select a rating (1-5 stars).';
                    modalErrorMsg.style.display = 'block';
                 } else if (type !== 'assistant') {
                     console.log("Submit clicked, but modal not configured for assistant rating.");
                 }
                return;
            }

            const csrfTokenLocal = getCsrfToken();
            // Construct the URL with the assistant ID
            const url = `/directory/rate-assistant/${id}/`;

            modalSubmitBtn.disabled = true;
            modalSubmitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';

            try {
                const bodyParams = new URLSearchParams();
                bodyParams.append('assistant_id', id); // Send correct param name
                bodyParams.append('rating', selectedRating);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfTokenLocal,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });

                const data = await response.json();

                if (!response.ok || data.status !== 'success') {
                    throw new Error(data.message || `HTTP error ${response.status}`);
                }

                if (ratingModal) ratingModal.hide();

                // Update the rating display on the page
                const starsHtml = data.rendered_stars_html;
                // *** Find the correct display container using the assistant ID ***
                const displayContainer = document.getElementById(`rating-display-assistant-${id}`);

                let updateSuccess = false;
                if (displayContainer && starsHtml) {
                    // Replace the content or outerHTML depending on what render_stars returns
                    displayContainer.innerHTML = starsHtml; // Assuming it returns inner HTML
                    updateSuccess = true;
                } else {
                     console.error(`[Rating] Could not find container #rating-display-assistant-${id} or received empty stars HTML.`);
                }

                if (updateSuccess) {
                    // Update the button state
                    const rateButton = document.querySelector(`.rate-assistant-btn[data-assistant-id="${id}"]`);
                    // *** Find the message span using the assistant ID ***
                    const msgSpan = document.getElementById(`rating-msg-assistant-${id}`);
                    if (rateButton) {
                         rateButton.disabled = true;
                         rateButton.innerHTML = '<i class="bi bi-check-lg"></i> Rated';
                    }
                    if (msgSpan) {
                         msgSpan.textContent = 'Thanks!';
                         msgSpan.style.display = 'inline';
                    }
                } else {
                    // Fallback message if update failed
                    const msgSpan = document.getElementById(`rating-msg-assistant-${id}`);
                     if (msgSpan) {
                         msgSpan.textContent = 'Rated (refresh?)';
                         msgSpan.classList.remove('text-success');
                         msgSpan.classList.add('text-warning');
                         msgSpan.style.display = 'inline';
                     }
                }

            } catch (error) {
                console.error('Error submitting rating via modal:', error);
                 if (modalErrorMsg) {
                    modalErrorMsg.textContent = `Error: ${error.message}`;
                    modalErrorMsg.style.display = 'block';
                 }
            } finally {
                modalSubmitBtn.disabled = false;
                modalSubmitBtn.innerHTML = 'Submit Rating';
                // Clear modal state
                ratingModalElement.dataset.itemType = '';
                ratingModalElement.dataset.itemId = '';
                currentAssistantId = null;
                currentItemType = '';
            }
        });
    } else {
         console.error("Modal submit button not found.");
    }
}
// --- End Modal Rating Logic ---

// --- Company Rating Modal Logic ---
function handleCompanyRatingModal() {
    const ratingModalElement = document.getElementById('ratingModal');
    if (!ratingModalElement) {
        console.log("Company rating modal not found on this page.");
        return;
    }

    // Initialize Bootstrap modal if needed
    let ratingModal = bootstrap.Modal.getInstance(ratingModalElement);
    if (!ratingModal) {
        try {
            ratingModal = new bootstrap.Modal(ratingModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal for company rating:", e);
        }
    }

    const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
    const submitRatingBtn = ratingModalElement.querySelector('#submitRatingBtn');
    const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
    let ratingCompanyId = null;
    let selectedRating = 0;

    // Set up modal when it's shown
    ratingModalElement.addEventListener('show.bs.modal', function (event) {
        try {
            const button = event.relatedTarget;
            if (!button) {
                console.warn('Modal opened without a relatedTarget button');
                return;
            }

            ratingCompanyId = button.getAttribute('data-company-id');
            const companyName = button.getAttribute('data-company-name');
            const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
            const modalCompanyName = ratingModalElement.querySelector('#modalCompanyName');
            const modalCompanyNameInner = ratingModalElement.querySelector('#modalCompanyNameInner');

            console.log('Rating modal opened for company:', companyName, 'ID:', ratingCompanyId);

            if (modalTitle) modalTitle.innerHTML = `Rate <span id="modalCompanyName">${companyName}</span>`;
            if (modalCompanyName) modalCompanyName.textContent = companyName;
            if (modalCompanyNameInner) modalCompanyNameInner.textContent = companyName;

            selectedRating = 0;
            if (submitRatingBtn) submitRatingBtn.disabled = true;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';
            if (modalStarsContainer) {
                const starButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                if (starButtons && starButtons.length > 0) {
                    starButtons.forEach(btn => {
                        btn.classList.remove('active');
                        const starIcon = btn.querySelector('i');
                        if (starIcon) {
                            starIcon.classList.remove('bi-star-fill', 'text-warning');
                            starIcon.classList.add('bi-star', 'text-secondary');
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Error in modal show event:', error);
        }
    });

    // Handle star clicks
    if (modalStarsContainer) {
        modalStarsContainer.addEventListener('click', function (event) {
            try {
                const starButton = event.target.closest('.modal-star-btn');
                if (!starButton) return;

                const ratingValue = starButton.getAttribute('data-rating-value');
                if (!ratingValue) {
                    console.warn('Star button clicked but no rating value found');
                    return;
                }

                selectedRating = parseInt(ratingValue);
                console.log('Selected rating:', selectedRating);

                if (submitRatingBtn) submitRatingBtn.disabled = false;
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                // Update star appearance
                const allStarButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                if (allStarButtons && allStarButtons.length > 0) {
                    allStarButtons.forEach(btn => {
                        const starIcon = btn.querySelector('i');
                        if (!starIcon) return;

                        const btnValueAttr = btn.getAttribute('data-rating-value');
                        if (!btnValueAttr) return;

                        const btnValue = parseInt(btnValueAttr);

                        if (btnValue <= selectedRating) {
                            starIcon.classList.remove('bi-star', 'text-secondary');
                            starIcon.classList.add('bi-star-fill', 'text-warning');
                            btn.classList.add('active');
                        } else {
                            starIcon.classList.remove('bi-star-fill', 'text-warning');
                            starIcon.classList.add('bi-star', 'text-secondary');
                            btn.classList.remove('active');
                        }
                    });
                }
            } catch (error) {
                console.error('Error handling star click:', error);
            }
        });
    }

    // Handle submit button click
    if (submitRatingBtn) {
        submitRatingBtn.addEventListener('click', function() {
            try {
                if (!selectedRating || !ratingCompanyId) {
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = 'Please select a rating.';
                        modalErrorMsg.style.display = 'block';
                    }
                    return;
                }

                const csrfToken = getCsrfToken();
                if (!csrfToken) {
                    console.error('CSRF token not found!');
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = 'CSRF token not found. Please refresh the page.';
                        modalErrorMsg.style.display = 'block';
                    }
                    return;
                }

                // Save original button text for restoration
                const originalButtonText = submitRatingBtn.innerHTML;
                submitRatingBtn.disabled = true;
                submitRatingBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

                // Use the URL for rating companies
                const rateUrl = "{% url 'directory:rate_company' %}";

                console.log('Submitting company rating to:', rateUrl, 'Company ID:', ratingCompanyId, 'Rating:', selectedRating);

                const formData = new URLSearchParams();
                formData.append('company_id', ratingCompanyId);
                formData.append('rating', selectedRating);

                // Use fetch API to submit the rating
                fetch(rateUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Rating response:', data);

                    if (data.status === 'success') {
                        // Update the rating display
                        const ratingDisplay = document.getElementById(`rating-display-${ratingCompanyId}`);
                        if (ratingDisplay && data.rendered_stars_html) {
                            ratingDisplay.innerHTML = data.rendered_stars_html;
                            console.log('Updated rating display for company ID:', ratingCompanyId);
                        }

                        // Close the modal
                        if (ratingModal) {
                            ratingModal.hide();
                        }

                        // Show success message in the UI instead of an alert
                        const rateButton = document.querySelector(`button[data-company-id="${ratingCompanyId}"]`);
                        if (rateButton) {
                            rateButton.innerHTML = '<i class="bi bi-check-lg"></i> Rated';
                            rateButton.disabled = true;
                            setTimeout(() => {
                                rateButton.innerHTML = '<i class="bi bi-star me-1"></i>Rate this company';
                                rateButton.disabled = false;
                            }, 2000);
                        }
                    } else {
                        if (modalErrorMsg) {
                            modalErrorMsg.textContent = data.message || 'Failed to submit rating.';
                            modalErrorMsg.style.display = 'block';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error submitting rating:', error);
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = error.message || 'An unexpected error occurred.';
                        modalErrorMsg.style.display = 'block';
                    }
                })
                .finally(() => {
                    // Always re-enable the button and restore its text
                    if (submitRatingBtn) {
                        submitRatingBtn.disabled = false;
                        submitRatingBtn.innerHTML = originalButtonText || 'Submit Rating';
                    }
                });
            } catch (error) {
                console.error('Error in submit button click handler:', error);
                if (modalErrorMsg) {
                    modalErrorMsg.textContent = error.message || 'An unexpected error occurred.';
                    modalErrorMsg.style.display = 'block';
                }
            }
        });
    }
}
// --- End Company Rating Modal Logic ---

// Initialize handlers when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded, initializing handlers for company detail page.");
    handleFavoriteButtons(); // Initialize favorite button handler
    handleRatingModal();   // Initialize modal rating handler for assistants
    handleCompanyRatingModal(); // Initialize modal rating handler for company
    // Ensure handleShareButtons is called after other initializations
    handleShareButtons();
});
</script>
{% endblock %}

</final_file_content>

IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.


<environment_details>
# VSCode Visible Files
templates/accounts/public_company_detail.html

# VSCode Open Tabs
directory/apps.py
content/apps.py
company_assistant/__init__.py
templates/accounts/password_reset.html
templates/accounts/password_reset_done.html
templates/accounts/password_reset_confirm.html
templates/accounts/password_reset_complete.html
templates/accounts/password_change.html
templates/accounts/password_change_done.html
accounts/tests/test_models.py
accounts/tests/test_views.py
accounts/tests/test_forms.py
accounts/tests/test_signals.py
accounts/tests/__init__.py
accounts/management/commands/README.md
accounts/management/commands/toggle_debug.py
accounts/management/commands/test_setup.py
accounts/templatetags/__init__.py
accounts/tests/test_templatetags.py
content/urls.py
README.md
.gitignore
accounts/apps.py
content/models.py
.env.example
content/views.py
content/admin.py
templates/contact.html
templates/demo.html
templates/features.html
templates/security.html
templates/careers.html
templates/cookies.html
templates/accounts/company_create.html
templates/accounts/user_settings.html
templates/accounts/email/team_invitation.html
templates/accounts/email/team_invitation.txt
accounts/management/commands/manage_invitations.py
accounts/tests/test_commands.py
templates/accounts/password_reset_subject.txt
templates/accounts/password_reset_email.html
templates/accounts/login.html
templates/accounts/register.html
templates/home.html
templates/errors/404.html
templates/errors/403.html
templates/errors/500.html
accounts/signals.py
assistants/apps.py
templates/search.html
test_openai.py
templates/assistants/assistant_form.html
templates/assistants/assistant_train.html
templates/assistants/assistant_usage.html
templates/assistants/assistant_analytics.html
assistants/urls.py
templates/assistants/assistant_history.html
templates/base/pagination.html
templates/pagination.html
templates/directory/assistant_directory_list.html
directory/management/commands/sync_listings.py
directory/templatetags/directory_tags.py
templates/assistants/assistant_list.html
directory/templatetags/__init__.py
templates/directory/partials/render_stars_partial.html
directory/templatetags/rating_tags.py
directory/admin.py
templates/accounts/company_detail.html
directory/urls.py
accounts/utils.py
accounts/admin.py
accounts/migrations/0005_seed_initial_roles.py
accounts/migrations/0006_combined_rbac_setup.py
assistants/migrations/0003_assistantaccesstoken_assistantfolder_and_more.py
accounts/urls.py
company_assistant/views.py
company_assistant/urls.py
templates/accounts/company_team.html
accounts/templatetags/account_tags.py
templates/accounts/tags/activity_item.html
templates/accounts/company_switch.html
../cs/grweb4.py
assistants/signals.py
templates/assistants/assistant_detail.html
../../AppData/Local/Temp/temp_image_1743375134464.png
templates/assistants/base.html
templates/assistants/partials/chat_response.html
assistants/llm_utils.py
templates/base/layout.html
static/css/style.css
assistants/models.py
assistants/forms.py
templates/assistants/assistant_wizard_form.html
assistants/views.py
directory/models.py
accounts/forms.py
accounts/views.py
templates/accounts/company_settings.html
directory/views.py
templates/directory/company_list.html
templates/directory/partials/favorite_company_item.html
templates/directory/my_favorites.html
templates/directory/assistant_list.html
templates/directory/partials/favorite_assistant_item.html
templates/accounts/public_company_detail.html
templates/assistants/assistant_chat.html
accounts/templatetags/permission_tags.py
templates/accounts/dashboard.html
accounts/models.py
requirements.txt
company_assistant/settings.py
.env
manage.py
company_assistant/wsgi.py
company_assistant/asgi.py
templates/accounts/tags/company_member_card.html
assistants/admin.py
accounts/management/commands/__init__.py
accounts/management/__init__.py
../../AppData/Local/Temp/temp_image_1742878610084.png
../../AppData/Local/Temp/temp_image_1742836842125.png

# Current Time
4/1/2025, 10:54:55 AM (Africa/Kampala, UTC+3:00)

# Current Mode
ACT MODE
</environment_details>
