# Generated migration to fix mutable default values in JSONField

from django.db import migrations, models


def default_dict():
    """Return an empty dict for JSONField default."""
    return {}


def default_list():
    """Return an empty list for JSONField default."""
    return []


def fix_shared_data(apps, schema_editor):
    """
    Fix any existing shared data by ensuring each instance has its own copy.
    This migration addresses the mutable default issue where all instances
    were sharing the same dict/list objects.
    """
    Assistant = apps.get_model('assistants', 'Assistant')
    CommunityContext = apps.get_model('assistants', 'CommunityContext')
    NavigationItem = apps.get_model('assistants', 'NavigationItem')
    
    # Fix Assistant instances
    for assistant in Assistant.objects.all():
        # Ensure website_data is a proper dict for each instance
        if assistant.website_data is None:
            assistant.website_data = {}
        elif not isinstance(assistant.website_data, dict):
            assistant.website_data = {}
        
        # Ensure saved_suggestions is a proper list for each instance
        if assistant.saved_suggestions is None:
            assistant.saved_suggestions = []
        elif not isinstance(assistant.saved_suggestions, list):
            assistant.saved_suggestions = []
        
        assistant.save(update_fields=['website_data', 'saved_suggestions'])
    
    # Fix CommunityContext instances
    for context in CommunityContext.objects.all():
        # Ensure keywords is a proper list for each instance
        if context.keywords is None:
            context.keywords = []
        elif not isinstance(context.keywords, list):
            context.keywords = []
        
        context.save(update_fields=['keywords'])
    
    # Fix NavigationItem instances
    for nav_item in NavigationItem.objects.all():
        # Ensure gallery is a proper list for each instance
        if nav_item.gallery is None:
            nav_item.gallery = []
        elif not isinstance(nav_item.gallery, list):
            nav_item.gallery = []
        
        nav_item.save(update_fields=['gallery'])


def reverse_fix_shared_data(apps, schema_editor):
    """
    Reverse migration - no action needed as we're just ensuring data integrity.
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('assistants', '0001_initial'),
    ]

    operations = [
        # First, run the data migration to fix existing data
        migrations.RunPython(fix_shared_data, reverse_fix_shared_data),
        
        # Then update the field definitions to use callable defaults
        migrations.AlterField(
            model_name='assistant',
            name='website_data',
            field=models.JSONField(blank=True, default=default_dict, help_text='Structured website data for customer care assistants (content keyed by NavigationItem unique_id)'),
        ),
        migrations.AlterField(
            model_name='assistant',
            name='saved_suggestions',
            field=models.JSONField(blank=True, default=default_list, help_text='List of suggested questions saved from the Analyze & Suggest tab.'),
        ),
        migrations.AlterField(
            model_name='communitycontext',
            name='keywords',
            field=models.JSONField(blank=True, default=default_list, help_text='List of keywords to help categorize this context'),
        ),
        migrations.AlterField(
            model_name='navigationitem',
            name='gallery',
            field=models.JSONField(blank=True, default=default_list, help_text='Gallery images for this navigation item.'),
        ),
    ]
