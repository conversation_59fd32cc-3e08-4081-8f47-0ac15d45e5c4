#!/usr/bin/env python3
"""
Simple test script to verify Unicode handling works correctly.
This simulates the type of data that was causing the original error.
"""

import os
import sys
import json

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)

def test_unicode_printing():
    """Test printing Unicode characters like the original error."""
    
    # Simulate the POST data that was causing the error
    test_data = {
        'title': 'Test with smart quotes',
        'content': 'This has a right single quotation mark: \u2019',
        'description': 'Hello 世界 🌍',
        'emoji_test': '🚀 🎉 ✨',
        'mixed': 'Regular text with "smart quotes" and émojis 🎯'
    }
    
    print("\n=== Testing Unicode Output ===")
    
    # Test 1: Direct printing
    try:
        for key, value in test_data.items():
            print(f"{key}: {value}")
        print("✅ Direct printing successful")
    except UnicodeEncodeError as e:
        print(f"❌ Direct printing failed: {e}")
        return False
    
    # Test 2: JSON serialization (like our fix)
    try:
        json_output = json.dumps(test_data, indent=2, ensure_ascii=False)
        print("\n=== JSON Output ===")
        print(json_output)
        print("✅ JSON serialization successful")
    except Exception as e:
        print(f"❌ JSON serialization failed: {e}")
        return False
    
    # Test 3: repr() fallback (like our fix)
    try:
        print("\n=== Repr Output ===")
        for key, value in test_data.items():
            print(f"{key}: {repr(value)}")
        print("✅ Repr output successful")
    except Exception as e:
        print(f"❌ Repr output failed: {e}")
        return False
    
    return True

def test_django_request_simulation():
    """Simulate Django request processing with Unicode data."""
    
    try:
        from django.test import RequestFactory
        from django.http import QueryDict
        
        # Create a mock POST request with Unicode data
        factory = RequestFactory()
        
        # Simulate POST data with Unicode characters
        post_data = QueryDict(mutable=True)
        post_data['title'] = 'Test with smart quotes \u2019'
        post_data['content'] = 'Hello 世界 🌍'
        post_data['description'] = 'Émojis and special chars: 🚀 ✨'
        
        request = factory.post('/test/', post_data)
        
        print("\n=== Django Request Simulation ===")
        print("POST data processing:")
        
        # Test our improved printing method
        processed_data = {}
        for key, value in request.POST.items():
            if isinstance(value, str):
                processed_data[key] = value.encode('utf-8', errors='replace').decode('utf-8')
            else:
                processed_data[key] = str(value)
        
        print(json.dumps(processed_data, indent=2, ensure_ascii=False))
        print("✅ Django request simulation successful")
        return True
        
    except Exception as e:
        print(f"❌ Django request simulation failed: {e}")
        return False

if __name__ == '__main__':
    print("Testing Unicode handling after encoding fix...")
    print(f"Python version: {sys.version}")
    print(f"Default encoding: {sys.getdefaultencoding()}")
    print(f"Stdout encoding: {getattr(sys.stdout, 'encoding', 'unknown')}")
    
    # Run tests
    test1_passed = test_unicode_printing()
    test2_passed = test_django_request_simulation()
    
    if test1_passed and test2_passed:
        print("\n🎉 All Unicode tests passed! The encoding fix is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the encoding configuration.")
        sys.exit(1)
