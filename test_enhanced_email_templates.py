#!/usr/bin/env python
"""
Test script to showcase the enhanced email templates.
Sends sample emails using all the new enhanced templates.
"""

import os
import sys
from datetime import datetime, timedelta

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    
    from django.contrib.auth import get_user_model
    from accounts.email_utils import (
        send_welcome_email,
        send_notification_email,
        send_enhanced_email,
        send_html_email
    )
    
    User = get_user_model()
    
    print("🎨 Enhanced Email Templates Showcase")
    print("=" * 60)
    
    # Get recipient email
    recipient = input("Enter email address to test enhanced templates (or press <NAME_EMAIL>): ").strip()
    if not recipient:
        recipient = '<EMAIL>'
    
    print(f"\n📧 Sending enhanced email templates to: {recipient}")
    
    # Create a test user object for templates
    test_user = type('TestUser', (), {
        'username': 'testuser',
        'email': recipient,
        'get_full_name': lambda: 'Test User',
        'date_joined': datetime.now(),
    })()
    
    # Test 1: Welcome Email
    print("\n1️⃣ Sending Welcome Email...")
    try:
        success = send_welcome_email(test_user)
        if success:
            print("✅ Welcome email sent successfully!")
        else:
            print("❌ Welcome email failed")
    except Exception as e:
        print(f"❌ Error sending welcome email: {e}")
    
    # Test 2: Success Notification
    print("\n2️⃣ Sending Success Notification...")
    try:
        notification_data = {
            'title': 'Account Verification Complete',
            'message': 'Your account has been successfully verified and is now active.',
            'content': 'You can now access all features of the 24seven platform.',
            'type': 'success',
            'icon': '🎉',
            'subtitle': 'Welcome to the team!',
            'action_url': 'https://24seven.site/dashboard/',
            'action_text': 'Go to Dashboard',
            'details': {
                'items': [
                    'Full access to AI assistants',
                    'Team collaboration tools',
                    'Knowledge base access',
                    'Premium support'
                ]
            },
            'timestamp': datetime.now(),
        }
        
        success = send_notification_email(test_user, notification_data)
        if success:
            print("✅ Success notification sent successfully!")
        else:
            print("❌ Success notification failed")
    except Exception as e:
        print(f"❌ Error sending success notification: {e}")
    
    # Test 3: Warning Notification
    print("\n3️⃣ Sending Warning Notification...")
    try:
        notification_data = {
            'title': 'Account Security Alert',
            'message': 'We detected unusual activity on your account.',
            'content': 'Multiple failed login attempts were detected from an unknown location.',
            'type': 'warning',
            'icon': '⚠️',
            'subtitle': 'Please review your account security',
            'action_url': 'https://24seven.site/security/',
            'action_text': 'Review Security',
            'details': {
                'items': [
                    'Location: Unknown (IP: ***********)',
                    'Time: 15 minutes ago',
                    'Attempts: 5 failed logins',
                    'Status: Account temporarily locked'
                ]
            },
            'timestamp': datetime.now(),
            'related_items': [
                {
                    'title': 'Change Password',
                    'url': 'https://24seven.site/change-password/',
                    'description': 'Update your account password'
                },
                {
                    'title': 'Security Settings',
                    'url': 'https://24seven.site/security/',
                    'description': 'Review and update security preferences'
                }
            ]
        }
        
        success = send_notification_email(test_user, notification_data)
        if success:
            print("✅ Warning notification sent successfully!")
        else:
            print("❌ Warning notification failed")
    except Exception as e:
        print(f"❌ Error sending warning notification: {e}")
    
    # Test 4: Error Notification
    print("\n4️⃣ Sending Error Notification...")
    try:
        notification_data = {
            'title': 'Payment Processing Failed',
            'message': 'We were unable to process your payment for the premium subscription.',
            'content': 'Your subscription will be downgraded to the free plan in 3 days unless payment is resolved.',
            'type': 'error',
            'icon': '💳',
            'subtitle': 'Action required to maintain service',
            'action_url': 'https://24seven.site/billing/',
            'action_text': 'Update Payment Method',
            'details': {
                'items': [
                    'Card ending in ****1234',
                    'Error: Card expired',
                    'Amount: $29.99/month',
                    'Next attempt: Tomorrow'
                ]
            },
            'timestamp': datetime.now(),
        }
        
        success = send_notification_email(test_user, notification_data)
        if success:
            print("✅ Error notification sent successfully!")
        else:
            print("❌ Error notification failed")
    except Exception as e:
        print(f"❌ Error sending error notification: {e}")
    
    # Test 5: Custom Enhanced Email
    print("\n5️⃣ Sending Custom Enhanced Email...")
    try:
        custom_content = """
        <div class="success-box">
            <h4 style="margin: 0 0 16px 0; color: #2f855a;">🚀 New Feature Available</h4>
            <p style="margin: 0; font-size: 16px;">
                We've just released an exciting new feature that will revolutionize how you work with AI assistants!
            </p>
        </div>
        
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 24px; border-radius: 12px; margin: 24px 0;">
            <h3 style="margin: 0 0 16px 0; font-size: 20px;">✨ Smart Context Memory</h3>
            <p style="margin: 0; opacity: 0.9; font-size: 16px;">
                Your AI assistants now remember context across conversations, making them even more helpful and personalized.
            </p>
        </div>
        
        <div class="info-box">
            <h4 style="margin: 0 0 16px 0; color: #2b6cb0;">🎯 Key Benefits</h4>
            <ul style="margin: 0; padding-left: 20px;">
                <li>Seamless conversation continuity</li>
                <li>Personalized responses based on your history</li>
                <li>Improved accuracy and relevance</li>
                <li>Better understanding of your preferences</li>
            </ul>
        </div>
        """
        
        success = send_enhanced_email(
            to_email=recipient,
            subject='Exciting New Feature: Smart Context Memory',
            email_type='info',
            heading='New Feature Release',
            subtitle='Enhanced AI capabilities now available',
            icon='🧠',
            content=custom_content,
            button_url='https://24seven.site/features/context-memory/',
            button_text='Explore New Feature'
        )
        
        if success:
            print("✅ Custom enhanced email sent successfully!")
        else:
            print("❌ Custom enhanced email failed")
    except Exception as e:
        print(f"❌ Error sending custom enhanced email: {e}")
    
    # Test 6: Team Invitation Preview
    print("\n6️⃣ Sending Team Invitation Preview...")
    try:
        # Create mock invitation data
        invitation_html = """
        <p style="font-size: 18px; margin-bottom: 24px;">
            <strong>Hello there!</strong>
        </p>

        <p style="font-size: 16px; margin-bottom: 24px;">
            <strong>John Smith</strong> has invited you to join <strong>Acme Corporation</strong> on 24seven, our AI-powered collaboration platform.
        </p>

        <div class="info-box">
            <h4 style="margin: 0 0 12px 0; color: #2b6cb0;">💬 Personal Message from John Smith</h4>
            <p style="margin: 0; font-style: italic; font-size: 16px; color: #4a5568;">
                "We'd love to have you join our team! Your expertise would be a great addition to our AI-powered workflow."
            </p>
        </div>

        <div class="button-container">
            <a href="https://24seven.site/invitations/accept/sample/" class="button" style="font-size: 18px; padding: 18px 36px;">
                🎯 Accept Invitation
            </a>
        </div>

        <div class="success-box">
            <h4 style="margin: 0 0 16px 0; color: #2f855a;">✨ What You'll Get Access To</h4>
            <div style="display: grid; gap: 12px;">
                <div style="display: flex; align-items: center; padding: 8px 0;">
                    <span style="font-size: 20px; margin-right: 12px;">🤖</span>
                    <div>
                        <strong>AI-Powered Assistants</strong><br>
                        <small style="color: #718096;">Smart AI tools that understand your team's context</small>
                    </div>
                </div>
                <div style="display: flex; align-items: center; padding: 8px 0;">
                    <span style="font-size: 20px; margin-right: 12px;">📚</span>
                    <div>
                        <strong>Shared Knowledge Base</strong><br>
                        <small style="color: #718096;">Centralized repository for team knowledge</small>
                    </div>
                </div>
            </div>
        </div>
        """
        
        context = {
            'email_title': 'Team Invitation - Acme Corporation on 24seven',
            'email_icon': '🎉',
            'email_heading': "You've Been Invited!",
            'email_subtitle': 'Join Acme Corporation on 24seven',
            'email_content': invitation_html,
        }
        
        success = send_html_email(
            to_email=recipient,
            subject='Team Invitation Preview - Acme Corporation',
            template_html='accounts/email/base_email.html',
            context=context
        )
        
        if success:
            print("✅ Team invitation preview sent successfully!")
        else:
            print("❌ Team invitation preview failed")
    except Exception as e:
        print(f"❌ Error sending team invitation preview: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ENHANCED EMAIL TEMPLATES SHOWCASE COMPLETE")
    print("=" * 60)
    
    print(f"📧 All enhanced email templates sent to: {recipient}")
    print("\n✨ Enhanced Templates Showcased:")
    print("   1. Welcome Email - Modern onboarding experience")
    print("   2. Success Notification - Celebration and confirmation")
    print("   3. Warning Notification - Security alerts and warnings")
    print("   4. Error Notification - Critical issues and actions")
    print("   5. Custom Enhanced Email - Feature announcements")
    print("   6. Team Invitation Preview - Professional invitations")
    
    print("\n🎨 Design Features Highlighted:")
    print("   ✅ Modern gradient backgrounds")
    print("   ✅ Professional typography and spacing")
    print("   ✅ Responsive design for all devices")
    print("   ✅ Consistent branding and colors")
    print("   ✅ Interactive buttons with hover effects")
    print("   ✅ Information boxes with visual hierarchy")
    print("   ✅ Security-focused design elements")
    print("   ✅ Dark mode support")
    print("   ✅ Accessibility considerations")
    
    print("\n📋 Check your email inbox to see the enhanced designs!")
    print("   (Also check spam/junk folder if needed)")
    
    print("\n🚀 Your email templates are now professional, modern, and engaging!")

except Exception as e:
    print(f"❌ Error running enhanced email template showcase: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
