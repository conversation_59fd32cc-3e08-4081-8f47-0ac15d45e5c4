#!/usr/bin/env python
"""
Test script to verify real approval login behavior.
This creates a real approval token and URL that you can click in a browser.
"""

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.auth_utils import store_signin_approval
from accounts.email_utils import send_signin_approval_email
from django.urls import reverse

User = get_user_model()

print("🔧 Creating Real Approval Link for Browser Testing")
print("=" * 60)

# Create test user
user, created = User.objects.get_or_create(
    username='realtest', 
    defaults={'email': '<EMAIL>', 'is_active': True}
)

if created:
    print(f"✅ Created new test user: {user.username}")
else:
    print(f"✅ Using existing test user: {user.username}")

# Generate approval token and URL
print("\n🔐 Generating approval token...")
token = store_signin_approval(user, 24)
approval_url = f"http://127.0.0.1:8000{reverse('accounts:approve_signin', kwargs={'token': token})}"

print(f"✅ Token generated: {token}")
print(f"✅ Approval URL: {approval_url}")

# Send email with the approval link
print("\n📧 Sending approval email...")
success = send_signin_approval_email(user, approval_url, 24)

if success:
    print("✅ Approval email sent successfully!")
else:
    print("❌ Failed to send approval email")

print("\n" + "=" * 60)
print("🧪 REAL BROWSER TEST INSTRUCTIONS")
print("=" * 60)

print(f"1. 📧 Check email inbox: {user.email}")
print("2. 🔗 Or click this URL directly in your browser:")
print(f"   {approval_url}")
print("3. 🔍 After clicking, check if you're logged in by:")
print("   - Looking for user menu in top-right corner")
print("   - Checking if you see dashboard/company options")
print("   - Visiting http://127.0.0.1:8000/accounts/dashboard/")

print("\n📊 Expected Behavior:")
print("✅ Should automatically log you in as:", user.username)
print("✅ Should redirect to home page")
print("✅ Should show success message")
print("✅ Should see user menu in header")

print("\n🐛 If Login Fails:")
print("❌ You'll see the approval page but won't be logged in")
print("❌ You'll need to manually login again")
print("❌ Check the Django server logs for error messages")

print("\n📝 Debug Information:")
print(f"User ID: {user.id}")
print(f"Username: {user.username}")
print(f"Email: {user.email}")
print(f"Is Active: {user.is_active}")
print(f"Token: {token}")

print("\n🔧 Server Logs:")
print("Watch the Django development server console for log messages")
print("Look for messages like:")
print("- 'User [username] logged in successfully via approval'")
print("- 'User [username] is authenticated after login'")
print("- Any error messages about login failures")

print("\n" + "=" * 60)
print("🚀 CLICK THE APPROVAL URL TO TEST!")
print("=" * 60)
print(f"\n🔗 {approval_url}")
print("\n📧 Or check your email at: <EMAIL>")

print("\n💡 Tip: Open this URL in an incognito/private browser window")
print("   to ensure you start from a logged-out state.")

print("\n" + "=" * 60)
