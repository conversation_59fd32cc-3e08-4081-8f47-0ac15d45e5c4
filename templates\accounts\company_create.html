{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Create {% if form.entity_type.value == 'community' %}Community{% else %}Company{% endif %} - 24seven{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold mb-3">Create Your {% if form.entity_type.value == 'community' %}Community{% else %}Workspace{% endif %}</h1>
                <p class="lead text-muted">
                    {% if form.entity_type.value == 'community' %}
                        Set up your community to start sharing knowledge
                    {% else %}
                        Set up your workspace to start collaborating
                    {% endif %}
                </p>
            </div>

            <!-- Progress Steps -->
            <div class="progress mb-4" style="height: 4px;">
                <div class="progress-bar" role="progressbar" style="width: 0%;"
                     data-current-step="1" data-total-steps="5"></div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <form method="post" id="companyForm" class="needs-validation" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Step 1: Basic Information -->
                        <div class="step" data-step="1">
                            <h3 class="h5 mb-4">Workspace Information</h3>

                            <div class="mb-3">
                                <label for="{{ form.entity_type.id_for_label }}" class="form-label">
                                    Workspace Type <span class="text-danger">*</span>
                                </label>
                                {% render_field form.entity_type class="form-select form-select-lg mb-3" %}
                                {% if form.entity_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.entity_type.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <strong>Company:</strong> For businesses and organizations with teams<br>
                                    <strong>Community:</strong> For community-driven AI assistants with user contributions
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <span id="entity-name-label">Name</span> <span class="text-danger">*</span>
                                </label>
                                {% render_field form.name class="form-control form-control-lg" placeholder="Enter name" %}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.name.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.mission.id_for_label }}" class="form-label">
                                    <span id="mission-label">Mission</span>
                                </label>
                                {% render_field form.mission class="form-control" placeholder="Brief description of the mission" rows="3" %}
                                <div class="form-text" id="mission-help">
                                    This helps define the purpose of your workspace.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.description.id_for_label }}" class="form-label">
                                    Public Description
                                </label>
                                {% render_field form.description class="form-control" placeholder="Description for public directory" rows="4" %}
                                <div class="form-text">
                                    This description will be shown in the public directory (optional).
                                </div>
                            </div>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.industry.id_for_label }}" class="form-label company-label">
                                        Industry
                                    </label>
                                    <div class="dropdown-container industry-dropdown-container">
                                        {% render_field form.industry class="form-control" placeholder="Select or type industry" %}
                                        <input type="hidden" name="industry_value" id="industry_value">
                                        <div class="dropdown-list"></div>
                                    </div>
                                    <div class="form-text">
                                        {{ form.industry.help_text }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.categories.id_for_label }}" class="form-label company-label">
                                        Categories
                                    </label>
                                    <div class="dropdown-container category-dropdown-container">
                                        {% render_field form.categories class="form-control" placeholder="Select or type categories" %}
                                        <input type="hidden" name="categories_value" id="categories_value">
                                        <div class="dropdown-list"></div>
                                    </div>
                                    <div class="form-text">
                                        {{ form.categories.help_text }}
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.size.id_for_label }}" class="form-label company-label">
                                    Company Size
                                </label>
                                {% render_field form.size class="form-control" placeholder="e.g., 1-10, 11-50" %}
                                <div class="form-text">
                                    {{ form.size.help_text }}
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Contact Details -->
                        <div class="step d-none" data-step="2">
                            <h3 class="h5 mb-4">Contact Information</h3>

                            <div class="mb-3">
                                <label for="{{ form.website.id_for_label }}" class="form-label company-label">
                                    Company Website
                                </label>
                                {% render_field form.website class="form-control" placeholder="https://example.com" %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.contact_email.id_for_label }}" class="form-label company-label">
                                    Contact Email
                                </label>
                                {% render_field form.contact_email class="form-control" placeholder="<EMAIL>" %}
                                <div class="form-text">
                                    This email will be visible to your team members.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.contact_phone.id_for_label }}" class="form-label company-label">
                                    Contact Phone <span class="text-danger">*</span>
                                </label>
                                {% render_field form.contact_phone class="form-control" placeholder="+1234567890" required="required" %}
                                <div class="form-text">
                                    {{ form.contact_phone.help_text }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.timezone.id_for_label }}" class="form-label company-label">
                                    Timezone <span class="text-danger">*</span>
                                </label>
                                {% render_field form.timezone class="form-select" required="required" %}
                                <div class="form-text">
                                    {{ form.timezone.help_text }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.language.id_for_label }}" class="form-label company-label">
                                    Language <span class="text-danger">*</span>
                                </label>
                                {% render_field form.language class="form-select" required="required" %}
                                <div class="form-text">
                                    {{ form.language.help_text }}
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Address Information -->
                        <div class="step d-none" data-step="3">
                            <h3 class="h5 mb-4">Address Information</h3>

                            <div class="mb-3">
                                <label for="{{ form.address_line1.id_for_label }}" class="form-label company-label">
                                    Street Address
                                </label>
                                {% render_field form.address_line1 class="form-control" placeholder="123 Main Street" %}
                                <div class="form-text">
                                    {{ form.address_line1.help_text }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.address_line2.id_for_label }}" class="form-label company-label">
                                    Address Line 2
                                </label>
                                {% render_field form.address_line2 class="form-control" placeholder="Apartment, suite, etc." %}
                                <div class="form-text">
                                    {{ form.address_line2.help_text }}
                                </div>
                            </div>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.city.id_for_label }}" class="form-label company-label">
                                        City
                                    </label>
                                    {% render_field form.city class="form-control" placeholder="New York" %}
                                    <div class="form-text">
                                        {{ form.city.help_text }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.postal_code.id_for_label }}" class="form-label company-label">
                                        Postal Code
                                    </label>
                                    {% render_field form.postal_code class="form-control" placeholder="10001" %}
                                    <div class="form-text">
                                        {{ form.postal_code.help_text }}
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.country.id_for_label }}" class="form-label company-label">
                                    Country
                                </label>
                                {% render_field form.country class="form-control" placeholder="United States" %}
                                <div class="form-text">
                                    {{ form.country.help_text }}
                                </div>
                            </div>
                        </div>

                        <!-- Step 4: Company Details -->
                        <div class="step d-none" data-step="4">
                            <h3 class="h5 mb-4">Company Details</h3>

                            <div class="mb-3">
                                <label for="{{ form.logo.id_for_label }}" class="form-label company-label">
                                    Company Logo
                                </label>
                                {% render_field form.logo class="form-control" %}
                                <div class="form-text">
                                    {{ form.logo.help_text }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.founded.id_for_label }}" class="form-label company-label">
                                    Year Founded
                                </label>
                                {% render_field form.founded class="form-control" placeholder="2020" %}
                                <div class="form-text">
                                    {{ form.founded.help_text }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.linkedin.id_for_label }}" class="form-label company-label">
                                    LinkedIn Profile
                                </label>
                                {% render_field form.linkedin class="form-control" placeholder="https://linkedin.com/company/yourcompany" %}
                                <div class="form-text">
                                    {{ form.linkedin.help_text }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.twitter.id_for_label }}" class="form-label company-label">
                                    Twitter Profile
                                </label>
                                {% render_field form.twitter class="form-control" placeholder="https://twitter.com/yourcompany" %}
                                <div class="form-text">
                                    {{ form.twitter.help_text }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.facebook.id_for_label }}" class="form-label company-label">
                                    Facebook Page
                                </label>
                                {% render_field form.facebook class="form-control" placeholder="https://facebook.com/yourcompany" %}
                                <div class="form-text">
                                    {{ form.facebook.help_text }}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.custom_domain.id_for_label }}" class="form-label company-label">
                                    Custom Domain
                                </label>
                                {% render_field form.custom_domain class="form-control" placeholder="yourcompany.com" %}
                                <div class="form-text">
                                    {{ form.custom_domain.help_text }}
                                </div>
                            </div>
                        </div>

                        <!-- Step 5: Settings & Preferences -->
                        <div class="step d-none" data-step="5">
                            <h3 class="h5 mb-4">Preferences</h3>

                            <!-- Timezone and language fields moved to step 2 -->

                            <div class="form-check form-switch mb-3">
                                {% render_field form.list_in_directory class="form-check-input" %}
                                <label class="form-check-label" for="{{ form.list_in_directory.id_for_label }}">
                                    List in Public Directory
                                </label>
                                <div class="form-text">
                                    Make your company visible in our public directory.
                                </div>
                            </div>

                            <div class="mb-4 company-only" id="categories-section">
                                <label class="form-label company-label">Directory Categories</label>
                                <div class="form-text mb-2">
                                    Select categories that best describe your company for the directory listing.
                                </div>

                                <div class="categories-container">
                                    <!-- Render with widget_tweaks -->
                                    {% render_field form.categories class="form-select" multiple="multiple" %}
                                    {% if form.categories.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.categories.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="bi bi-info-circle me-2"></i>
                                    What's Next?
                                </h6>
                                <p class="small mb-0">
                                    After creating your company, you'll be able to:
                                </p>
                                <ul class="small mb-0">
                                    <li>Invite team members</li>
                                    <li>Set up AI assistants</li>
                                    <li>Upload company content</li>
                                    <li>Configure advanced settings</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-light prev-step" style="display: none;">
                                <i class="bi bi-arrow-left me-2"></i>
                                Back
                            </button>
                            <div class="ms-auto">
                                <button type="button" class="btn btn-primary next-step">
                                    Next
                                    <i class="bi bi-arrow-right ms-2"></i>
                                </button>
                                <button type="submit" class="btn btn-success submit-step" style="display: none;">
                                    <i class="bi bi-check2 me-2"></i>
                                    Create {% if form.entity_type.value == 'community' %}Community{% else %}Company{% endif %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Text -->
            <div class="text-center mt-4">
                <p class="text-muted small">
                    Need help? <a href="{{ site_config.contact_url|default:'/contact/' }}">Contact our support team</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.step {
    transition: opacity 0.3s ease-in-out;
}
.progress-bar {
    transition: width 0.3s ease-in-out;
}
.categories-container {
    max-height: 200px;
    padding: 10px;
    border-radius: 5px;
}
.categories-container select {
    max-height: 200px;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
}
.form-check {
    margin-bottom: 8px;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/category-dropdowns.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('companyForm');
    const steps = document.querySelectorAll('.step');
    const progressBar = document.querySelector('.progress-bar');
    const prevBtn = document.querySelector('.prev-step');
    const nextBtn = document.querySelector('.next-step');
    const submitBtn = document.querySelector('.submit-step');
    const totalSteps = steps.length;
    let currentStep = 1;

    // Entity type selector
    const entityTypeSelect = document.getElementById('{{ form.entity_type.id_for_label }}');
    const entityNameLabel = document.getElementById('entity-name-label');
    const missionLabel = document.getElementById('mission-label');
    const missionHelp = document.getElementById('mission-help');

    // Update labels and UI based on entity type
    function updateLabels() {
        const entityType = entityTypeSelect.value;
        if (entityType === 'company') {
            // Update labels for company
            entityNameLabel.textContent = 'Company Name';
            missionLabel.textContent = 'Company Mission';
            missionHelp.textContent = 'This helps your team understand the company\'s purpose.';

            // Update other UI elements for company
            document.querySelectorAll('.company-label').forEach(el => {
                if (el.dataset.originalText) {
                    el.textContent = el.dataset.originalText;
                }
            });

            // Update submit button text
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-check2 me-2"></i>Create Company';
            }

            // Show company-specific fields
            document.querySelectorAll('.company-only').forEach(el => {
                el.style.display = '';
            });

            // Hide community-specific fields
            document.querySelectorAll('.community-only').forEach(el => {
                el.style.display = 'none';
            });
        } else if (entityType === 'community') {
            // Update labels for community
            entityNameLabel.textContent = 'Community Name';
            missionLabel.textContent = 'Community Purpose';
            missionHelp.textContent = 'This helps users understand the community\'s focus.';

            // Update other UI elements for community
            document.querySelectorAll('.company-label').forEach(el => {
                if (!el.dataset.originalText) {
                    el.dataset.originalText = el.textContent;
                }
                el.textContent = el.textContent.replace('Company', 'Community');
            });

            // Update submit button text
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-check2 me-2"></i>Create Community';
            }

            // Hide company-specific fields
            document.querySelectorAll('.company-only').forEach(el => {
                el.style.display = 'none';
            });

            // Show community-specific fields
            document.querySelectorAll('.community-only').forEach(el => {
                el.style.display = '';
            });
        }
    }

    // Initial update
    updateLabels();

    // Listen for changes
    entityTypeSelect.addEventListener('change', updateLabels);

    function updateProgress() {
        const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
        progressBar.style.width = `${progress}%`;
    }

    function showStep(stepNumber) {
        // Close any open dropdowns before changing steps
        closeAllDropdowns();

        steps.forEach(step => {
            step.classList.add('d-none');
            if (step.dataset.step == stepNumber) {
                step.classList.remove('d-none');
            }
        });

        // Update buttons
        prevBtn.style.display = stepNumber > 1 ? 'block' : 'none';
        nextBtn.style.display = stepNumber < totalSteps ? 'block' : 'none';
        submitBtn.style.display = stepNumber == totalSteps ? 'block' : 'none';

        currentStep = stepNumber;
        updateProgress();
    }

    // Helper function to close all dropdowns
    function closeAllDropdowns() {
        document.querySelectorAll('.dropdown-list').forEach(list => {
            list.style.display = 'none';
            list.classList.remove('show');
        });
    }

    function validateStep(stepNumber) {
        const step = document.querySelector(`[data-step="${stepNumber}"]`);
        const inputs = step.querySelectorAll('input, select, textarea');
        let isValid = true;

        inputs.forEach(input => {
            // Skip hidden inputs and dropdown lists
            if (input.type === 'hidden' || input.closest('.dropdown-list')) {
                return;
            }

            // Check if the input is required or has the required attribute
            const isRequired = input.hasAttribute('required') || input.required === true;

            // Skip validation for non-required empty fields
            if (!isRequired && !input.value.trim()) {
                return;
            }

            // Validate required fields
            if (isRequired && !input.value.trim()) {
                isValid = false;
                input.classList.add('is-invalid');

                // Add error message if it doesn't exist
                let errorDiv = null;
                const nextElement = input.nextElementSibling;
                if (nextElement && nextElement.classList.contains('invalid-feedback')) {
                    errorDiv = nextElement;
                }

                if (!errorDiv) {
                    const newErrorDiv = document.createElement('div');
                    newErrorDiv.className = 'invalid-feedback d-block';
                    newErrorDiv.textContent = 'This field is required.';
                    input.parentNode.insertBefore(newErrorDiv, input.nextElementSibling);
                }
            } else {
                // Additional validation for specific field types
                let fieldValid = true;

                // Email validation
                if (input.type === 'email' && input.value.trim()) {
                    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailPattern.test(input.value.trim())) {
                        fieldValid = false;
                        const errorMessage = 'Please enter a valid email address.';
                        showFieldError(input, errorMessage);
                    }
                }

                // URL validation
                if (input.type === 'url' && input.value.trim()) {
                    try {
                        new URL(input.value.trim());
                    } catch (e) {
                        fieldValid = false;
                        const errorMessage = 'Please enter a valid URL.';
                        showFieldError(input, errorMessage);
                    }
                }

                // Phone validation for contact_phone field
                if (input.id === '{{ form.contact_phone.id_for_label }}' && input.value.trim()) {
                    // Simple validation - at least 7 digits
                    const phoneDigits = input.value.replace(/\D/g, '');
                    if (phoneDigits.length < 7) {
                        fieldValid = false;
                        const errorMessage = 'Please enter a valid phone number.';
                        showFieldError(input, errorMessage);
                    }
                }

                if (fieldValid) {
                    input.classList.remove('is-invalid');

                    // Remove error message if it exists
                    const nextElement = input.nextElementSibling;
                    if (nextElement && nextElement.classList.contains('invalid-feedback')) {
                        nextElement.remove();
                    }
                } else {
                    isValid = false;
                }
            }
        });

        return isValid;
    }

    function showFieldError(input, message) {
        input.classList.add('is-invalid');

        // Remove existing error message if any
        const nextElement = input.nextElementSibling;
        if (nextElement && nextElement.classList.contains('invalid-feedback')) {
            nextElement.remove();
        }

        // Add new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback d-block';
        errorDiv.textContent = message;
        input.parentNode.insertBefore(errorDiv, input.nextElementSibling);
    }

    nextBtn.addEventListener('click', () => {
        if (validateStep(currentStep)) {
            showStep(currentStep + 1);
        }
    });

    prevBtn.addEventListener('click', () => {
        showStep(currentStep - 1);
    });

    // Form validation
    form.addEventListener('submit', function(e) {
        e.preventDefault(); // Always prevent default first to handle validation

        // Close any open dropdowns
        closeAllDropdowns();

        // Validate current step
        if (!validateStep(currentStep)) {
            return false;
        }

        // If we're not on the last step, move to the next step
        if (currentStep < totalSteps) {
            showStep(currentStep + 1);
            return false;
        }

        // Validate all steps before final submission
        let allValid = true;
        for (let i = 1; i <= totalSteps; i++) {
            // Store current step
            const tempCurrentStep = currentStep;
            // Temporarily switch to step i for validation
            currentStep = i;
            if (!validateStep(i)) {
                allValid = false;
                // Show the first invalid step
                if (tempCurrentStep !== i) {
                    showStep(i);
                    break;
                }
            }
            // Restore current step
            currentStep = tempCurrentStep;
        }

        if (allValid) {
            // Add a loading state to the submit button
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Creating...';

            // Submit the form
            form.submit();
        }
    });

    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });

    // Initialize first step
    showStep(1);
});
</script>
{% endblock %}
