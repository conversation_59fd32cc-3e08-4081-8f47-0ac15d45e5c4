{% extends "base/layout.html" %}
{% load static account_tags rating_tags %} {# Load rating_tags #}

{% block title %}NUP Constituencies Directory{% endblock %}

{% block body_class %}nup-section-white company-directory-page{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/directory.css' %}">
<link rel="stylesheet" href="{% static 'css/featured-carousel.css' %}">
<link rel="stylesheet" href="{% static 'css/featured-home-style-carousel.css' %}">
<link rel="stylesheet" href="{% static 'css/home-carousel-mobile-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/enhanced-tier-badges.css' %}">
<link rel="stylesheet" href="{% static 'css/unified-cards-responsive.css' %}">
<!-- <link rel="stylesheet" href="{% static 'css/unified-cards-dark-mode.css' %}"> -->
<!-- <link rel="stylesheet" href="{% static 'css/directory-cards-dark.css' %}"> -->
<link rel="stylesheet" href="{% static 'css/company-list-tablet-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/company-list-landscape-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/tablet-mobile-contact-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/mobile-style-for-tablet.css' %}">
<link rel="stylesheet" href="{% static 'css/green-to-nup-override.css' %}">
<link rel="stylesheet" href="{% static 'css/blue-to-nup-override.css' %}">
<link rel="stylesheet" href="{% static 'css/improved-candidate-cards.css' %}">
<link rel="stylesheet" href="{% static 'css/clean-card-hover-fix.css' %}">
<link rel="stylesheet" href="{% static 'css/candidate-hover-fix.css' %}">
<style>
    /* Override like button styling - no circles, transparent background */
    .like-button,
    .like-button:hover,
    .like-button:active,
    .like-button:focus,
    .like-button.text-danger,
    .like-button.text-secondary,
    .featured-carousel-item .like-button,
    .btn-like,
    .btn-favorite,
    .favorite-button {
        background: transparent !important;
        background-color: transparent !important;
        border: none !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        width: auto !important;
        height: auto !important;
        padding: 4px !important;
    }

    /* Apply directory theme styling to featured section */
    .featured-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    /* CLEAR VISIBLE CARD BOUNDARIES - Override inline styles */
    .featured-carousel-item .featured-item-wrapper,
    .featured-carousel-items .featured-carousel-item .featured-item-wrapper {
        /* Solid visible background to define card area */
        background: linear-gradient(145deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(240, 240, 240, 0.9) 50%,
            rgba(220, 220, 220, 0.85) 100%) !important;
        background-color: rgba(255, 255, 255, 0.9) !important;

        /* VERY VISIBLE SOLID BORDER - Clear card boundaries */
        border: 4px solid #007bff !important;
        border-top: 4px solid #66b3ff !important;
        border-left: 4px solid #66b3ff !important;
        border-right: 4px solid #0056b3 !important;
        border-bottom: 4px solid #0056b3 !important;

        /* Enhanced 3D Shadow - override inline styles */
        box-shadow:
            /* Main shadow */
            0 30px 60px rgba(0, 0, 0, 0.2),
            /* Inner highlight */
            inset 0 2px 0 rgba(255, 255, 255, 0.9),
            /* Inner shadow for depth */
            inset 0 -2px 0 rgba(0, 0, 0, 0.15),
            /* Side highlights */
            inset 2px 0 0 rgba(255, 255, 255, 0.5),
            inset -2px 0 0 rgba(0, 0, 0, 0.1),
            /* Outer glow */
            0 0 25px rgba(255, 255, 255, 0.15),
            /* Additional depth */
            0 15px 30px rgba(0, 0, 0, 0.1) !important;

        /* Enhanced backdrop blur */
        backdrop-filter: blur(25px) saturate(200%) !important;
        -webkit-backdrop-filter: blur(25px) saturate(200%) !important;

        /* 3D Transform */
        transform: perspective(1200px) rotateX(3deg) translateZ(5px) !important;
        transform-style: preserve-3d !important;

        /* Additional glass properties */
        position: relative !important;
        overflow: visible !important;
    }

    /* Add a pseudo-element for extra glass effect */
    .featured-carousel-item .featured-item-wrapper::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            transparent 50%,
            rgba(0, 0, 0, 0.05) 100%);
        border-radius: inherit;
        pointer-events: none;
        z-index: 1;
    }

    .featured-carousel-item .featured-item-wrapper:hover,
    .featured-carousel-items .featured-carousel-item .featured-item-wrapper:hover {
        /* Enhanced hover effect - override inline styles */
        background: linear-gradient(145deg,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0.25) 50%,
            rgba(255, 255, 255, 0.15) 100%) !important;
        background-color: transparent !important;

        /* Brighter border on hover */
        border-top: 3px solid rgba(255, 255, 255, 0.9) !important;
        border-left: 3px solid rgba(255, 255, 255, 0.9) !important;
        border-right: 3px solid rgba(255, 255, 255, 0.25) !important;
        border-bottom: 3px solid rgba(255, 255, 255, 0.25) !important;

        /* Enhanced shadow on hover */
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.25),
            inset 0 3px 0 rgba(255, 255, 255, 1),
            inset 0 -3px 0 rgba(0, 0, 0, 0.2),
            inset 3px 0 0 rgba(255, 255, 255, 0.7),
            inset -3px 0 0 rgba(0, 0, 0, 0.15),
            0 0 35px rgba(255, 255, 255, 0.25),
            0 20px 40px rgba(0, 0, 0, 0.15) !important;

        /* 3D hover transform */
        transform: perspective(1200px) rotateX(6deg) translateY(-12px) translateZ(15px) scale(1.03) !important;
    }

    /* DARK MODE - CLEAR VISIBLE CARD BOUNDARIES */
    [data-theme="dark"] .featured-carousel-item .featured-item-wrapper,
    [data-theme="dark"] .featured-carousel-items .featured-carousel-item .featured-item-wrapper {
        /* Solid dark background to define card area */
        background: linear-gradient(145deg,
            rgba(60, 60, 60, 0.95) 0%,
            rgba(45, 45, 45, 0.9) 50%,
            rgba(30, 30, 30, 0.85) 100%) !important;
        background-color: rgba(50, 50, 50, 0.9) !important;

        /* VERY VISIBLE SOLID BORDER FOR DARK MODE */
        border: 4px solid #00d4ff !important;
        border-top: 4px solid #66e0ff !important;
        border-left: 4px solid #66e0ff !important;
        border-right: 4px solid #0099cc !important;
        border-bottom: 4px solid #0099cc !important;

        /* Dark mode shadows - override inline styles */
        box-shadow:
            0 30px 60px rgba(0, 0, 0, 0.6),
            inset 0 2px 0 rgba(255, 255, 255, 0.4),
            inset 0 -2px 0 rgba(0, 0, 0, 0.4),
            inset 2px 0 0 rgba(255, 255, 255, 0.25),
            inset -2px 0 0 rgba(0, 0, 0, 0.3),
            0 0 25px rgba(255, 255, 255, 0.08),
            0 15px 30px rgba(0, 0, 0, 0.3) !important;
    }

    [data-theme="dark"] .featured-carousel-item .featured-item-wrapper:hover,
    [data-theme="dark"] .featured-carousel-items .featured-carousel-item .featured-item-wrapper:hover {
        /* Dark hover effect - override inline styles */
        background: linear-gradient(145deg,
            rgba(255, 255, 255, 0.18) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(0, 0, 0, 0.1) 100%) !important;
        background-color: transparent !important;

        /* Brighter dark border on hover */
        border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
        border-left: 3px solid rgba(255, 255, 255, 0.7) !important;
        border-right: 3px solid rgba(0, 0, 0, 0.5) !important;
        border-bottom: 3px solid rgba(0, 0, 0, 0.5) !important;

        /* Enhanced dark shadow on hover */
        box-shadow:
            0 40px 80px rgba(0, 0, 0, 0.7),
            inset 0 3px 0 rgba(255, 255, 255, 0.5),
            inset 0 -3px 0 rgba(0, 0, 0, 0.4),
            inset 3px 0 0 rgba(255, 255, 255, 0.4),
            inset -3px 0 0 rgba(0, 0, 0, 0.3),
            0 0 35px rgba(255, 255, 255, 0.15),
            0 20px 40px rgba(0, 0, 0, 0.4) !important;
    }

    /* Enhanced logo container glass effect - override inline styles */
    .featured-carousel-item .logo-container,
    .featured-carousel-items .featured-carousel-item .logo-container {
        /* Glass border for logo container - override inline styles */
        border: 2px solid rgba(255, 255, 255, 0.5) !important;
        border-top: 2px solid rgba(255, 255, 255, 0.8) !important;
        border-left: 2px solid rgba(255, 255, 255, 0.8) !important;
        border-right: 2px solid rgba(255, 255, 255, 0.2) !important;
        border-bottom: 2px solid rgba(255, 255, 255, 0.2) !important;

        /* Logo container shadow - override inline styles */
        box-shadow:
            0 12px 24px rgba(0, 0, 0, 0.15),
            inset 0 2px 0 rgba(255, 255, 255, 0.7),
            inset 2px 0 0 rgba(255, 255, 255, 0.4),
            inset 0 -1px 0 rgba(0, 0, 0, 0.1),
            0 0 15px rgba(255, 255, 255, 0.1) !important;

        backdrop-filter: blur(15px) saturate(150%) !important;
        -webkit-backdrop-filter: blur(15px) saturate(150%) !important;

        /* Override any background color */
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    /* Dark mode logo container - override inline styles */
    [data-theme="dark"] .featured-carousel-item .logo-container,
    [data-theme="dark"] .featured-carousel-items .featured-carousel-item .logo-container {
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
        border-top: 2px solid rgba(255, 255, 255, 0.5) !important;
        border-left: 2px solid rgba(255, 255, 255, 0.5) !important;
        border-right: 2px solid rgba(0, 0, 0, 0.3) !important;
        border-bottom: 2px solid rgba(0, 0, 0, 0.3) !important;

        box-shadow:
            0 12px 24px rgba(0, 0, 0, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3),
            inset 2px 0 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2),
            0 0 15px rgba(255, 255, 255, 0.05) !important;

        /* Override any background color for dark mode */
        background-color: rgba(255, 255, 255, 0.05) !important;
    }

    .tier-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .list-group-item {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
        overflow: hidden;
        height: auto;
        min-height: 160px;
        margin-bottom: 1rem;
    }

    .list-group-item:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .filter-form {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    /* Logo container styles are now in directory.css */

    .logo-placeholder {
        font-size: 2.5rem;
    }

    .tier-badge {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 0.7em;
        padding: 0.25em 0.5em;
        margin: 0.5rem;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .tier-gold {
        background-color: #ffc107;
        color: #212529;
    }

    .tier-silver {
        background-color: #adb5bd;
        color: #212529;
    }

    .tier-bronze {
        background-color: #cd7f32;
        color: #fff;
    }

    .featured-carousel .carousel-item {
        background-color: #fff;
        border-radius: 0.5rem;
        padding: 1.5rem;
    }

    .carousel-indicators {
        position: relative;
        margin-top: 1rem;
        margin-bottom: 0;
    }

    .carousel-indicators button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #dee2e6;
    }

    .carousel-indicators button.active {
        background-color: #0d6efd;
    }
</style>
{% endblock %}

{% block head_extra %}
<style>
    /* Rating stars styling */
    .star-rating .stars .bi-star-fill {
        color: gold;
    }
    .star-rating .stars .bi-star {
        color: #ccc;
    }
    .modal-stars .modal-star-btn.active i {
        color: gold !important;
    }

    /* Make stars more clickable */
    .modal-stars .modal-star-btn {
        cursor: pointer;
        padding: 10px !important;
        margin: 0 5px;
        transition: transform 0.2s;
    }
    .modal-stars .modal-star-btn:hover {
        transform: scale(1.2);
    }
    .modal-stars .modal-star-btn i {
        font-size: 1.5em;
    }

    /* Featured carousel rating styling */
    .featured-carousel-item .rating-display-container {
        margin-top: 5px;
        text-align: center;
    }
    .featured-carousel-item .star-rating {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    .featured-carousel-item .star-rating .stars {
        display: inline-flex;
    }
    .featured-carousel-item .star-rating .stars i {
        font-size: 1rem;
        margin: 0 1px;
    }

    /* Logo container styles are now in directory.css */

    /* Ensure consistent card sizing */
    .list-group-item {
        height: auto;
        min-height: 180px;
        overflow: hidden;
    }
</style>
{% csrf_token %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="nup-section-red">
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">NUP Constituencies Directory</h1>
                <div class="nup-accent-bar"></div>
                <p class="lead mb-0">Explore National Unity Platform constituencies and connect with local representatives</p>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<section class="nup-section-white">
<div class="container mt-4 mb-5 company-directory-container"
     data-hide-standard-tier-companies="{{ hide_standard_tier_companies|yesno:'True,False' }}">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2 mb-0">NUP Constituencies Directory</h1>
        <a href="{% url 'accounts:company_create' %}" class="btn btn-nup-purple">
            <i class="bi bi-plus-circle me-2"></i> Register Constituency
        </a>
    </div>

    {# Filter Form #}
    <form method="get" action="{% url 'directory:company_list' %}" class="filter-form">
        <h5 class="mb-3"><i class="bi bi-funnel-fill me-2 text-nup-red"></i>Find Constituencies</h5>

        <div class="row g-3">
            <div class="col-md-6"> {# Name/Description Filter #}
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" name="q_name" id="q_name_comp" class="form-control" placeholder="Search by name or description..." value="{{ q_name|default:'' }}">
                </div>
            </div>
            <div class="col-md-3"> {# Industry Filter #}
                <div class="dropdown-container industry-dropdown-container">
                    <input type="text" name="q_industry" id="q_industry_comp" class="form-control industry-dropdown" placeholder="Filter by industry..." value="{{ q_industry|default:'' }}">
                    <input type="hidden" name="industry_value" id="industry_value">
                    <div class="dropdown-list"></div>
                </div>
            </div>
            <div class="col-md-3"> {# Category Filter #}
                <div class="dropdown-container category-dropdown-container">
                    <input type="text" name="q_category" id="q_category_comp" class="form-control category-dropdown" placeholder="Filter by category..." value="{{ q_category|default:'' }}">
                    <input type="hidden" name="categories_value" id="categories_value">
                    <div class="dropdown-list"></div>
                </div>
            </div>
            <div class="col-12 d-flex">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search me-1"></i> Search
                </button>
                {% if q_name or q_industry or q_category or q_tag %}
                <a href="{% url 'directory:company_list' %}" class="btn btn-outline-secondary ms-2">
                    <i class="bi bi-x-circle me-1"></i> Clear Search
                </a>
                {% endif %}
            </div>
        </div>
    </form>
    {# End Filter Form #}

    {# Display Active Filters - Only show filters from search bar #}
    {% if q_name or q_industry or q_category or q_tag %}
    <div class="alert alert-light border mb-4 mt-2">
        <div class="d-flex align-items-center">
            <i class="bi bi-funnel me-2 text-primary"></i>
            <span class="fw-medium">Filtering by:</span>
            <div class="ms-2">
                {% if q_name %}<span class="badge bg-primary me-1">Name/Desc: {{ q_name }}</span>{% endif %}
                {% if q_industry %}<span class="badge bg-info text-dark me-1">Industry: {{ q_industry }}</span>{% endif %}
                {% if q_category %}<span class="badge bg-secondary me-1">Category: {{ q_category }}</span>{% endif %}
                {% if q_tag %}<span class="badge bg-light text-dark border me-1">Tag: {{ q_tag }}</span>{% endif %}
            </div>
        </div>
    </div>
    {% endif %}
    {# End Display Active Filters #}



    {# --- Featured Companies Section (Assistants Style Carousel) --- #}
    <div class="featured-section mt-4">
        <h2><i class="bi bi-star-fill me-2"></i>Featured Constituencies</h2>

        {% if featured_company_listings|length > 0 %}
            <!-- Featured Companies Carousel - Home Page Style -->
            <div class="company-logo-carousel-container featured-companies-carousel">
                <div class="company-logo-carousel">
                    {# Display real featured companies #}
                    {% for listing in featured_company_listings %}
                        <div class="company-logo-item featured-company-item" data-company-id="{{ listing.company.id }}">
                            <a href="{% url 'accounts:public_company_detail' slug=listing.company.slug %}" title="{{ listing.company.name }}" class="text-center">
                                {% if listing.company.info.logo %}
                                    <div class="logo-container home-carousel-logo">
                                        <img src="{{ listing.company.info.logo.url }}"
                                             alt="{{ listing.company.name }} Logo"
                                             class="company-logo"
                                             onerror="this.onerror=null; this.src='/static/img/default-company-logo.svg';">
                                    </div>
                                {% else %}
                                    <div class="company-logo-placeholder home-carousel-placeholder">
                                        <i class="bi bi-building"></i>
                                        <span>{{ listing.company.name }}</span>
                                    </div>
                                {% endif %}
                                <div class="company-info">
                                    <h5 class="company-name">{{ listing.company.name }}</h5>
                                    {% if listing.description %}
                                        <p class="company-description">{{ listing.description|truncatechars:50|safe }}</p>
                                    {% endif %}
                                    <div class="rating-display-container" id="rating-display-company-{{ listing.company.id }}">
                                        {% if listing.avg_rating and listing.avg_rating > 0 %}
                                            {% render_stars listing.avg_rating listing.total_ratings %}
                                        {% else %}
                                            <span class="text-muted fst-italic small">(No ratings yet)</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </a>
                            <!-- Like Button positioned absolutely -->
                            {% if user.is_authenticated and listing.company.id %}
                                <button class="like-button btn btn-icon {% if listing.company.id in saved_company_ids %}text-danger{% else %}text-secondary{% endif %}"
                                        data-item-id="{{ listing.company.id }}"
                                        data-item-type="company"
                                        title="{% if listing.company.id in saved_company_ids %}Unlike{% else %}Like{% endif %}">
                                    <i class="bi {% if listing.company.id in saved_company_ids %}bi-heart-fill{% else %}bi-heart{% endif %}"></i>
                                </button>
                            {% endif %}
                        </div>
                    {% endfor %}

                    {# Duplicate the real featured companies for continuous scrolling effect #}
                    {% if featured_company_listings|length >= 3 %}
                        {% for listing in featured_company_listings %}
                            <div class="company-logo-item featured-company-item" data-company-id="{{ listing.company.id }}">
                                <a href="{% url 'accounts:public_company_detail' slug=listing.company.slug %}" title="{{ listing.company.name }}" class="text-center">
                                    {% if listing.company.info.logo %}
                                        <div class="logo-container home-carousel-logo">
                                            <img src="{{ listing.company.info.logo.url }}"
                                                 alt="{{ listing.company.name }} Logo"
                                                 class="company-logo"
                                                 onerror="this.onerror=null; this.src='/static/img/default-company-logo.svg';">
                                        </div>
                                    {% else %}
                                        <div class="company-logo-placeholder home-carousel-placeholder">
                                            <i class="bi bi-building"></i>
                                            <span>{{ listing.company.name }}</span>
                                        </div>
                                    {% endif %}
                                    <div class="company-info">
                                        <h5 class="company-name">{{ listing.company.name }}</h5>
                                        {% if listing.description %}
                                            <p class="company-description">{{ listing.description|truncatechars:50|safe }}</p>
                                        {% endif %}
                                        <div class="rating-display-container" id="rating-display-company-dup-{{ listing.company.id }}">
                                            {% if listing.avg_rating and listing.avg_rating > 0 %}
                                                {% render_stars listing.avg_rating listing.total_ratings %}
                                            {% else %}
                                                <span class="text-muted fst-italic small">(No ratings yet)</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </a>
                                <!-- Like Button positioned absolutely -->
                                {% if user.is_authenticated and listing.company.id %}
                                    <button class="like-button btn btn-icon {% if listing.company.id in saved_company_ids %}text-danger{% else %}text-secondary{% endif %}"
                                            data-item-id="{{ listing.company.id }}"
                                            data-item-type="company"
                                            title="{% if listing.company.id in saved_company_ids %}Unlike{% else %}Like{% endif %}">
                                        <i class="bi {% if listing.company.id in saved_company_ids %}bi-heart-fill{% else %}bi-heart{% endif %}"></i>
                                    </button>
                                {% endif %}
                            </div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                No featured companies yet. When companies are marked as featured, they will appear here.
            </div>
        {% endif %}
    </div>
    {# --- End Featured Companies Section --- #}

    {# --- Main Company List Section --- #}
    <div class="mt-5">
        <h2 class="h3 mb-4 fw-bold">
            {% if sort_by == 'tier' %}
                <i class="bi bi-layers-fill me-2 text-nup-red"></i>Constituencies by Region
            {% else %}
                <i class="bi bi-geo-alt me-2 text-nup-red"></i>All Constituencies
            {% endif %}
        </h2>

        {% if page_obj.object_list %}
            <div class="list-group" id="company-list-container"> {# Container for event delegation, added ID #}
                {% if sort_by == 'tier' %}
                    {# Logic to display tier headers dynamically #}
                    {% regroup page_obj.object_list by company.get_tier_display as tier_groups %}

                    {% for group in tier_groups %}
                        {# Determine Tier Heading based on grouper (which is the display name) #}
                        {% with tier_display_name=group.grouper %}
                            <div class="tier-section {% if tier_display_name == 'Gold' %}gold{% elif tier_display_name == 'Silver' %}silver{% elif tier_display_name == 'Bronze' %}bronze{% else %}standard{% endif %}"
                                 {% if tier_display_name == 'Standard' and hide_standard_tier_companies %}style="display: none;"{% endif %}>
                                <h3>
                                    {% if tier_display_name == 'Gold' %}
                                        <i class="bi bi-trophy-fill me-2 text-warning"></i>Gold Tier
                                    {% elif tier_display_name == 'Silver' %}
                                        <i class="bi bi-award-fill me-2 text-secondary"></i>Silver Tier
                                    {% elif tier_display_name == 'Bronze' %}
                                        <i class="bi bi-award me-2" style="color: #cd7f32;"></i>Bronze Tier
                                    {% elif tier_display_name == 'Standard' %}
                                        <i class="bi bi-person-badge me-2 text-muted"></i>Standard Tier
                                    {% else %}
                                        <i class="bi bi-building me-2"></i>Other Companies {# Fallback #}
                                    {% endif %}
                                </h3>

                                {# Loop through items in this tier group #}
                                <div class="list-group company-cards-container">
                                    {% for listing in group.list %}
                                        {% include "directory/partials/company_card.html" with listing=listing saved_company_ids=saved_company_ids display_context='tier' %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endwith %}
                    {% endfor %}

                {% else %}
                    {# Default display if not sorting by tier #}
                    <div class="list-group company-cards-container">
                        {% for listing in page_obj.object_list %}
                            {% include "directory/partials/company_card.html" with listing=listing saved_company_ids=saved_company_ids display_context='tier' %}
                        {% endfor %}
                    </div>
                {% endif %}
            </div> {# /list-group #}

            <div class="mt-5">
                {% include "pagination_with_items_per_page.html" with page_obj=page_obj %} {# Use enhanced pagination with items per page #}
            </div>
            {% csrf_token %} {# Keep CSRF token if needed for other actions on the page #}

        {% else %}
            <div class="alert alert-info shadow-sm">
                <div class="d-flex align-items-center">
                    <i class="bi bi-info-circle-fill me-3 fs-4"></i>
                    <div>
                        <h5 class="mb-1">No Results Found</h5>
                        <p class="mb-0">No companies found matching your criteria. Try adjusting your filters.</p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
    {# --- End Main Company List Section --- #}

</div> {# /container #}

{# Rating Modal Structure (Enhanced) #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="ratingModalLabel">Rate Company</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-4">How would you rate <strong id="modalItemName" class="text-primary">this company</strong>?</p>
        <div class="modal-stars text-center mb-4" style="font-size: 2.5rem;">
            {% for i_int in "12345" %}
            <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                <i class="bi bi-star"></i>
            </button>
            {% endfor %}
        </div>
        <div class="text-center text-muted small mb-3">Click on a star to select your rating</div>
        <div id="modalErrorMsg" class="alert alert-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" id="cancelRatingModalBtn" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i> Cancel
        </button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>
            <i class="bi bi-check-circle me-1"></i> Submit Rating
        </button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{# Folder Options Modal (Enhanced) #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="folderOptionsModalLabel">
            <i class="bi bi-heart-fill text-danger me-2"></i>Add to Favorites
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName" class="text-primary">this item</strong> to your favorites:</p>

        {# Option 1: Save without folder #}
        <div class="card mb-3 border-primary">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-bookmark-heart-fill me-2 text-primary"></i>Quick Save</h6>
                <p class="card-text small text-muted mb-2">Save without organizing into a folder</p>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary" id="saveNoFolderBtn">
                        <i class="bi bi-bookmark-heart me-2"></i>Save to Favorites
                    </button>
                </div>
            </div>
        </div>

        {# Option 2: Add to existing folder #}
        <div id="existingFoldersSection" class="card mb-3" style="display: none;">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-folder-fill me-2 text-warning"></i>Add to Folder</h6>
                <p class="card-text small text-muted mb-2">Add to one of your existing folders</p>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                    <select class="form-select" id="selectFolder">
                        <option selected disabled value="">Choose folder...</option>
                        {# Options will be populated by JS #}
                    </select>
                    <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                        <i class="bi bi-plus-lg"></i> Add
                    </button>
                </div>
            </div>
        </div>

        {# Option 3: Create new folder #}
        <div class="card">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-folder-plus me-2 text-success"></i>Create New Folder</h6>
                <p class="card-text small text-muted mb-2">Create a new folder and add this item to it</p>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                    <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                    <button class="btn btn-success" type="button" id="createFolderAndSaveBtn" disabled>
                        <i class="bi bi-check-lg"></i> Create
                    </button>
                </div>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="alert alert-danger small mt-3" style="display: none;"></div>

        {# Add Cancel Button #}
        <div class="d-grid mt-3">
            <button type="button" class="btn btn-outline-secondary" id="cancelFolderModalBtn" data-bs-dismiss="modal">
                <i class="bi bi-x-circle me-2"></i>Cancel
            </button>
        </div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/http-protocol-fix.js' %}"></script>
<script src="{% static 'js/featured-carousel.js' %}"></script>
<script src="{% static 'js/image-fallback-handler.js' %}"></script>
<script>
// Home page style carousel functionality
document.addEventListener('DOMContentLoaded', function() {
    // Company logo carousel hover effect (like home page)
    const carousel = document.querySelector('.company-logo-carousel');
    if (carousel) {
        const carouselContainer = document.querySelector('.company-logo-carousel-container');

        // Pause animation on hover
        carouselContainer.addEventListener('mouseenter', () => {
            carousel.style.animationPlayState = 'paused';
        });

        // Resume animation when mouse leaves
        carouselContainer.addEventListener('mouseleave', () => {
            carousel.style.animationPlayState = 'running';
        });
    }
});
</script>
<script>
// Global image handling functions
function handleImageError(img) {
    console.error('[ImageError] Failed to load:', img.src);

    // Prevent infinite loop
    if (img.dataset.errorHandled) {
        console.warn('[ImageError] Already handled error for:', img.src);
        return;
    }

    img.dataset.errorHandled = 'true';

    // Get fallback source
    const fallbackSrc = img.dataset.fallbackSrc || '{% static "img/default-company-logo.svg" %}';

    // Apply fallback
    img.src = fallbackSrc;
    img.classList.add('fallback-image');

    console.log('[ImageError] Applied fallback:', fallbackSrc);
}

function handleImageLoad(img) {
    console.log('[ImageLoad] Successfully loaded:', img.src);

    // Ensure image is visible
    img.style.display = 'block';
    img.style.visibility = 'visible';
    img.style.opacity = '1';

    // Remove any error handling flags
    delete img.dataset.errorHandled;
}

// Enhanced image display fix
document.addEventListener('DOMContentLoaded', function() {
    console.log('[ImageFix] Starting enhanced image display fix');

    // Find all logo images
    const logoImages = document.querySelectorAll('.company-logo-img, .featured-company-logo-img, .logo-container img');
    console.log('[ImageFix] Found', logoImages.length, 'logo images');

    logoImages.forEach((img, index) => {
        console.log(`[ImageFix] Processing image ${index}:`, {
            src: img.src,
            complete: img.complete,
            naturalWidth: img.naturalWidth,
            naturalHeight: img.naturalHeight
        });

        // Force display properties
        img.style.display = 'block';
        img.style.visibility = 'visible';
        img.style.opacity = '1';
        img.style.maxWidth = '100%';
        img.style.maxHeight = '100%';
        img.style.objectFit = 'contain';
        img.style.background = 'transparent';

        // Set up fallback if not already set
        if (!img.dataset.fallbackSrc) {
            img.dataset.fallbackSrc = '{% static "img/default-company-logo.svg" %}';
        }

        // Add event listeners if not already added
        if (!img.dataset.listenersAdded) {
            img.addEventListener('error', function() {
                handleImageError(this);
            });

            img.addEventListener('load', function() {
                handleImageLoad(this);
            });

            img.dataset.listenersAdded = 'true';
        }

        // Check if image is already broken
        if (img.complete && img.naturalWidth === 0) {
            console.error(`[ImageFix] Image ${index} already broken:`, img.src);
            handleImageError(img);
        } else if (img.complete && img.naturalWidth > 0) {
            console.log(`[ImageFix] Image ${index} already loaded:`, img.src);
            handleImageLoad(img);
        }
    });

    // Set up a timer to check again after a delay
    setTimeout(() => {
        console.log('[ImageFix] Final check after delay');
        logoImages.forEach((img, index) => {
            if (img.complete && img.naturalWidth === 0 && !img.dataset.errorHandled) {
                console.error(`[ImageFix] Image ${index} still broken after delay:`, img.src);
                handleImageError(img);
            }
        });
    }, 2000);

    // Set up mutation observer for dynamically added images
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const newImages = node.querySelectorAll ? node.querySelectorAll('.company-logo-img, .featured-company-logo-img, .logo-container img') : [];
                    newImages.forEach(img => {
                        console.log('[ImageFix] New image detected:', img.src);
                        // Apply the same fixes to new images
                        if (!img.dataset.fallbackSrc) {
                            img.dataset.fallbackSrc = '{% static "img/default-company-logo.svg" %}';
                        }
                        if (!img.dataset.listenersAdded) {
                            img.addEventListener('error', function() { handleImageError(this); });
                            img.addEventListener('load', function() { handleImageLoad(this); });
                            img.dataset.listenersAdded = 'true';
                        }
                    });
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
</script>
<script>
// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (!csrfInput) {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) return csrfMeta.getAttribute('content');
    }
    return csrfInput ? csrfInput.value : null;
}

// Helper function to clean up modal backdrops
function cleanupModalBackdrop() {
    console.log('[DEBUG] Cleaning up modal backdrop');
    // Remove any lingering backdrop
    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
        console.log('[DEBUG] Removing modal backdrop');
        backdrop.remove();
    }
    // Ensure body doesn't have modal-open class
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
}

// --- Favorite Button & Folder Modal Logic ---
function handleFavoriteButtons() {
    console.log('[DEBUG] Favorites: Initializing favorite buttons handler');

    const listContainer = document.getElementById('company-list-container'); // Main list container
    console.log('[DEBUG] Favorites: List container found?', !!listContainer);

    const featuredCarousel = document.querySelector('.featured-carousel-items'); // Featured carousel container
    console.log('[DEBUG] Favorites: Featured carousel found?', !!featuredCarousel);

    const folderModalElement = document.getElementById('folderOptionsModal');
    console.log('[DEBUG] Favorites: Folder modal element found?', !!folderModalElement);

    let folderModal = null;
    if (folderModalElement) {
        folderModal = new bootstrap.Modal(folderModalElement);
        console.log('[DEBUG] Favorites: Bootstrap modal initialized');
    } else {
        console.error("Folder options modal element not found.");
        return; // Cannot proceed without the modal
    }

    // Check if either container exists
    if (!listContainer && !featuredCarousel) {
        console.log("[DEBUG] Favorites: Neither company list container nor featured carousel found.");
        return;
    }

    // Log the like buttons found in each container
    if (listContainer) {
        const listLikeButtons = listContainer.querySelectorAll('.like-button');
        console.log('[DEBUG] Favorites: Found', listLikeButtons.length, 'like buttons in list container');
    }

    if (featuredCarousel) {
        const carouselLikeButtons = featuredCarousel.querySelectorAll('.like-button');
        console.log('[DEBUG] Favorites: Found', carouselLikeButtons.length, 'like buttons in featured carousel');
    }

    // Store current item details for modal actions
    let currentModalItemId = null;
    let currentModalItemType = null;

    // --- Helper function to handle like button clicks ---
    async function handleLikeButtonClick(button, event) {
        console.log('[DEBUG] Favorites: handleLikeButtonClick called');

        if (!button) {
            console.error('[DEBUG] Favorites: No button provided to handleLikeButtonClick');
            return; // Ignore if no button
        }

        console.log('[DEBUG] Favorites: Button data:', {
            id: button.dataset.itemId,
            type: button.dataset.itemType,
            classes: button.className
        });

        event.preventDefault();
        event.stopPropagation();

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            console.error("[DEBUG] Favorites: CSRF token not found!");
            alert("Action failed. Please refresh.");
            return;
        }

        console.log('[DEBUG] Favorites: CSRF token found');

        const itemId = button.dataset.itemId;
        const itemType = button.dataset.itemType;
        const url = "{% url 'directory:toggle_saved_item' %}";

        console.log('[DEBUG] Favorites: Preparing to toggle item', itemId, 'of type', itemType);

        // Store for potential modal use
        currentModalItemId = itemId;
        currentModalItemType = itemType;

        try {
            console.log('[DEBUG] Favorites: Sending fetch request to', url);

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
            });

            console.log('[DEBUG] Favorites: Response status:', response.status);

            const data = await response.json();
            console.log('[DEBUG] Favorites: Response data:', data);

            if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

            if (data.status === 'success' && data.action === 'unfavorited') {
                console.log('[DEBUG] Favorites: Item unfavorited successfully');
                // Item was unfavorited successfully
                updateHeartIcon(button, false);
            } else if (data.status === 'options') {
                console.log('[DEBUG] Favorites: Showing folder options modal');
                // Item is not saved, show folder options modal
                populateAndShowFolderModal(data);
            } else if (data.status === 'success' && data.action === 'favorited') {
                console.log('[DEBUG] Favorites: Item favorited successfully');
                // Handle case where toggle immediately favorites (no modal needed)
                updateHeartIcon(button, true);
            } else {
                // Unexpected success response
                console.error('[DEBUG] Favorites: Unexpected response from toggle_saved_item:', data);
                alert('An unexpected error occurred.');
            }

        } catch (error) {
            console.error('[DEBUG] Favorites: Error handling favorite click:', error);
            alert(`An error occurred: ${error.message}`);
        }
    }

    // --- Main Click Handler for List Container (Like Button) ---
    if (listContainer) {
        console.log('[DEBUG] Favorites: Adding click handler to list container');
        listContainer.addEventListener('click', async (event) => {
            console.log('[DEBUG] Favorites: List container click detected');
            const button = event.target.closest('.like-button');
            if (!button) {
                console.log('[DEBUG] Favorites: Click was not on a like button');
                return; // Ignore clicks not on the like button
            }
            console.log('[DEBUG] Favorites: Like button clicked in list container');
            await handleLikeButtonClick(button, event);
        });
    }

    // --- Featured Carousel Click Handler (Like Button) ---
    if (featuredCarousel) {
        console.log('[DEBUG] Favorites: Adding click handler to featured carousel');
        featuredCarousel.addEventListener('click', async (event) => {
            console.log('[DEBUG] Favorites: Featured carousel click detected');
            const button = event.target.closest('.like-button');
            if (!button) {
                console.log('[DEBUG] Favorites: Click was not on a like button');
                return; // Ignore clicks not on the like button
            }
            console.log('[DEBUG] Favorites: Like button clicked in featured carousel');
            await handleLikeButtonClick(button, event);
        });
    }

    // --- Helper: Update Heart Icon ---
    function updateHeartIcon(button, isSaved) {
        console.log('[DEBUG] Favorites: updateHeartIcon called with isSaved =', isSaved);

        if (!button) {
            console.error('[DEBUG] Favorites: No button provided to updateHeartIcon');
            return;
        }

        console.log('[DEBUG] Favorites: Button before update:', {
            classes: button.className,
            title: button.title
        });

        const icon = button.querySelector('i');
        if (icon) {
            if (isSaved) {
                // Item is saved - show filled heart with red color
                icon.classList.remove('bi-heart');
                icon.classList.add('bi-heart-fill', 'pulse');
                button.classList.remove('text-secondary');
                button.classList.add('text-danger');
                button.title = 'Unlike';
                console.log('[DEBUG] Favorites: Updated button to liked state (text-danger)');

                // Remove pulse class after animation
                setTimeout(() => {
                    icon.classList.remove('pulse');
                }, 600);
            } else {
                // Item is not saved - show outline heart
                icon.classList.remove('bi-heart-fill', 'pulse');
                icon.classList.add('bi-heart');
                button.classList.remove('text-danger');
                button.classList.add('text-secondary');
                button.title = 'Like';
                console.log('[DEBUG] Favorites: Updated button to unliked state (text-secondary)');
            }
        }

        console.log('[DEBUG] Favorites: Button after update:', {
            classes: button.className,
            title: button.title
        });
    }

    // --- Helper: Populate and Show Folder Modal ---
    function populateAndShowFolderModal(data) {
        if (!folderModalElement || !folderModal) return;

        // Set item name
        const itemNameElement = folderModalElement.querySelector('#modalFolderNameItemName');
        if (itemNameElement) itemNameElement.textContent = data.item_name || 'this item';

        // Populate existing folders dropdown
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const existingFoldersSection = folderModalElement.querySelector('#existingFoldersSection');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');

        selectFolder.innerHTML = '<option selected disabled value="">Choose folder...</option>'; // Reset options
        if (data.folders && data.folders.length > 0) {
            data.folders.forEach(folder => {
                const option = document.createElement('option');
                option.value = folder.id;
                option.textContent = folder.name;
                selectFolder.appendChild(option);
            });
            existingFoldersSection.style.display = 'block';
            addToFolderBtn.disabled = true; // Disable until a folder is selected
        } else {
            existingFoldersSection.style.display = 'none';
        }

        // Reset other fields
        folderModalElement.querySelector('#newFolderName').value = '';
        folderModalElement.querySelector('#createFolderAndSaveBtn').disabled = true;
        folderModalElement.querySelector('#folderModalErrorMsg').style.display = 'none';
        folderModalElement.querySelector('#folderModalErrorMsg').textContent = '';

        // Show the modal
        folderModal.show();
    }

    // --- Folder Modal Event Listeners ---
    if (folderModalElement) {
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');
        const newFolderNameInput = folderModalElement.querySelector('#newFolderName');
        const createFolderBtn = folderModalElement.querySelector('#createFolderAndSaveBtn');
        const saveNoFolderBtn = folderModalElement.querySelector('#saveNoFolderBtn');
        const cancelBtn = folderModalElement.querySelector('#cancelFolderModalBtn');
        const errorMsgElement = folderModalElement.querySelector('#folderModalErrorMsg');

        // Add event listener for cancel button
        if (cancelBtn) {
            cancelBtn.addEventListener('click', function() {
                // Explicitly hide the modal and clean up
                folderModal.hide();
                cleanupModalBackdrop();
            });
        }

        // Add event listener for modal hidden event to ensure backdrop is removed
        folderModalElement.addEventListener('hidden.bs.modal', function() {
            cleanupModalBackdrop();
        });

        // Enable/disable "Add" button based on selection
        selectFolder.addEventListener('change', () => {
            addToFolderBtn.disabled = !selectFolder.value;
        });

        // Enable/disable "Create & Save" button based on input
        newFolderNameInput.addEventListener('input', () => {
            createFolderBtn.disabled = !newFolderNameInput.value.trim();
        });

        // Action: Save without folder
        saveNoFolderBtn.addEventListener('click', async () => {
            await handleSaveFolderAction("{% url 'directory:save_item_no_folder' %}", {});
        });

        // Action: Add to existing folder
        addToFolderBtn.addEventListener('click', async () => {
            const folderId = selectFolder.value;
            if (!folderId) return;
            await handleSaveFolderAction("{% url 'directory:add_item_to_folder' %}", { folder_id: folderId });
        });

        // Action: Create folder and save
        createFolderBtn.addEventListener('click', async () => {
            const folderName = newFolderNameInput.value.trim();
            if (!folderName) return;
            await handleSaveFolderAction("{% url 'directory:create_folder_and_save' %}", { folder_name: folderName });
        });

        // Generic function to handle the save actions
        async function handleSaveFolderAction(url, additionalParams) {
            if (!currentModalItemId || !currentModalItemType) {
                showFolderModalError("Item details missing. Please close and retry.");
                return;
            }
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                 showFolderModalError("CSRF token missing. Please refresh.");
                 return;
            }

            showFolderModalError(''); // Clear previous errors

            const bodyParams = new URLSearchParams({
                'item_id': currentModalItemId,
                'item_type': currentModalItemType,
                ...additionalParams
            });

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });
                const data = await response.json();

                if (!response.ok || data.status !== 'success') {
                    throw new Error(data.message || `HTTP error ${response.status}`);
                }

                // Success! Update the heart icon on the page and close modal
                // Find the button in any of our containers
                let originalButton = null;

                // Check in the main list container
                if (listContainer) {
                    originalButton = listContainer.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                }

                // Check in the featured carousel if not found
                if (!originalButton && featuredCarousel) {
                    originalButton = featuredCarousel.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                }

                if (originalButton) {
                    updateHeartIcon(originalButton, true);
                } else {
                    console.warn(`Could not find original like button for item ${currentModalItemType} ${currentModalItemId}`);
                }

                folderModal.hide();

            } catch (error) {
                console.error(`Error saving favorite via modal (${url}):`, error);
                showFolderModalError(`Error: ${error.message}`);
            }
        }

        function showFolderModalError(message) {
            errorMsgElement.textContent = message;
            errorMsgElement.style.display = message ? 'block' : 'none';
        }
    }
}

// Initialize favorite buttons when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('[DEBUG] Favorites: DOMContentLoaded event fired');
    console.log('[DEBUG] Favorites: Calling handleFavoriteButtons()');
    handleFavoriteButtons();
    console.log('[DEBUG] Favorites: handleFavoriteButtons() completed');
});
// --- End Favorite Button & Folder Modal Logic ---


// --- Modal Rating Logic (for Companies) ---
function handleCompanyRatingModal() {
    const ratingModalElement = document.getElementById('ratingModal');
    if (!ratingModalElement) {
        console.error("Rating modal element not found.");
        return;
    }

    let ratingModal = bootstrap.Modal.getInstance(ratingModalElement);
    if (!ratingModal) {
        try {
            ratingModal = new bootstrap.Modal(ratingModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal:", e);
            return;
        }
    }

    const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
    const modalItemName = ratingModalElement.querySelector('#modalItemName'); // Use generic ID
    const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
    const modalSubmitBtn = ratingModalElement.querySelector('#submitRatingBtn');
    const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
    let currentCompanyId = null;
    let selectedRating = 0;

    // Add event listener for cancel button
    const cancelRatingBtn = ratingModalElement.querySelector('#cancelRatingModalBtn');
    if (cancelRatingBtn) {
        cancelRatingBtn.addEventListener('click', function() {
            // Explicitly hide the modal and clean up
            ratingModal.hide();
            cleanupModalBackdrop();
        });
    }

    // Add event listener for modal hidden event to ensure backdrop is removed
    ratingModalElement.addEventListener('hidden.bs.modal', function() {
        cleanupModalBackdrop();
    });

    // 1. Populate modal when triggered (using Bootstrap event)
    ratingModalElement.addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;

        if (button && button.classList.contains('rate-company-btn')) {
            currentCompanyId = button.getAttribute('data-company-id');
            const companyName = button.getAttribute('data-company-name');

            if (modalTitle) modalTitle.textContent = `Rate ${companyName || 'Company'}`;
            if (modalItemName) modalItemName.textContent = companyName || 'this company';
            ratingModalElement.dataset.itemId = currentCompanyId;
            ratingModalElement.dataset.itemType = 'company';

            selectedRating = 0;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';
            if (modalSubmitBtn) modalSubmitBtn.disabled = true;

            if (modalStarsContainer) {
                const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
                stars.forEach(star => {
                    star.classList.remove('selected', 'text-warning');
                    star.classList.add('text-secondary');
                    const icon = star.querySelector('i');
                    if (icon) {
                        icon.classList.remove('bi-star-fill');
                        icon.classList.add('bi-star');
                    }
                });
            }
        } else {
            // Clear if triggered by something else
            ratingModalElement.dataset.itemType = '';
            currentCompanyId = null;
        }
    });

    // 2. Handle star selection
    if (modalStarsContainer) {
        modalStarsContainer.addEventListener('click', function(event) {
            const starButton = event.target.closest('.modal-star-btn');
            if (!starButton) return;

            selectedRating = parseInt(starButton.dataset.ratingValue);
            if (modalSubmitBtn) modalSubmitBtn.disabled = false;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';

            const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
            stars.forEach(star => {
                const starValue = parseInt(star.dataset.ratingValue);
                const icon = star.querySelector('i');
                if (icon) {
                    if (starValue <= selectedRating) {
                        star.classList.add('selected', 'text-warning');
                        star.classList.remove('text-secondary');
                        icon.classList.remove('bi-star');
                        icon.classList.add('bi-star-fill');
                    } else {
                        star.classList.remove('selected', 'text-warning');
                        star.classList.add('text-secondary');
                        icon.classList.remove('bi-star-fill');
                        icon.classList.add('bi-star');
                    }
                }
            });
        });
    } else {
         console.error("Modal stars container not found.");
    }

    // 3. Handle rating submission
    if (modalSubmitBtn) {
        modalSubmitBtn.addEventListener('click', async function() {
            const itemType = ratingModalElement.dataset.itemType;
            const id = ratingModalElement.dataset.itemId;

            if (itemType !== 'company' || selectedRating === 0 || !id) {
                 if (modalErrorMsg && itemType === 'company') {
                    modalErrorMsg.textContent = 'Please select a rating (1-5 stars).';
                    modalErrorMsg.style.display = 'block';
                 } else if (itemType !== 'company') {
                     console.log("Submit clicked, but modal not configured for company rating.");
                 }
                return;
            }

            const csrfTokenLocal = getCsrfToken();
            const url = "{% url 'directory:rate_company' %}";

            modalSubmitBtn.disabled = true;
            modalSubmitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';

            try {
                const bodyParams = new URLSearchParams();
                bodyParams.append('company_id', id);
                bodyParams.append('rating', selectedRating);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfTokenLocal,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });

                const data = await response.json();

                if (!response.ok || data.status !== 'success') {
                    throw new Error(data.message || `HTTP error ${response.status}`);
                }

                // Clear any error messages before hiding the modal
                if (modalErrorMsg) {
                    modalErrorMsg.style.display = 'none';
                    modalErrorMsg.textContent = '';
                }

                ratingModal.hide();

                const starsHtml = data.rendered_stars_html;

                // Look for all possible containers to update (main list and carousel)
                let containersToUpdate = [
                    document.querySelector(`[data-company-id="${id}"] .rating-display-container`),
                    document.getElementById(`rating-display-company-${id}`),
                    document.getElementById(`rating-display-company-dup-${id}`)
                ].filter(Boolean); // Filter out null/undefined

                const noRatingPlaceholders = [
                    document.getElementById(`no-rating-placeholder-${id}`),
                    document.getElementById(`no-rating-placeholder-dup-${id}`)
                ].filter(Boolean);

                // If no containers found but placeholders exist, use those
                if (containersToUpdate.length === 0 && noRatingPlaceholders.length > 0) {
                    containersToUpdate = noRatingPlaceholders;
                }

                let updateSuccess = false;
                if (containersToUpdate.length > 0 && starsHtml) {
                    // Update all found containers
                    containersToUpdate.forEach(container => {
                        container.outerHTML = starsHtml;
                    });
                    updateSuccess = true;
                    console.log(`[Rating] Updated ${containersToUpdate.length} rating containers for company ${id}`);
                } else {
                    console.error("[Rating] Could not find containers or received empty stars HTML for company.");
                }

                if (updateSuccess) {
                    const rateButton = document.querySelector(`.rate-company-btn[data-company-id="${id}"]`);
                    const msgSpan = document.getElementById(`rating-msg-company-${id}`);
                    if (rateButton) {
                         rateButton.disabled = true;
                         rateButton.innerHTML = '<i class="bi bi-check-lg"></i> Rated';
                    }
                    if (msgSpan) {
                         msgSpan.textContent = 'Thanks!';
                         msgSpan.style.display = 'inline';
                    }
                } else {
                    const msgSpan = document.getElementById(`rating-msg-company-${id}`);
                     if (msgSpan) {
                         msgSpan.textContent = 'Rated (refresh?)';
                         msgSpan.classList.remove('text-success');
                         msgSpan.classList.add('text-warning');
                         msgSpan.style.display = 'inline';
                     }
                }

            } catch (error) {
                console.error('Error submitting company rating via modal:', error);
                 if (modalErrorMsg) {
                    modalErrorMsg.textContent = `Error: ${error.message}`;
                    modalErrorMsg.style.display = 'block';
                 }
            } finally {
                modalSubmitBtn.disabled = false;
                modalSubmitBtn.innerHTML = 'Submit Rating';
                ratingModalElement.dataset.itemType = '';
                ratingModalElement.dataset.itemId = '';
                currentCompanyId = null;
            }
        });
    } else {
         console.error("Modal submit button not found.");
    }
}
// --- End Modal Rating Logic ---

// Function to check all images on the page
function checkAllImages() {
    console.log('[DEBUG] Running image status check...');
    const allImages = document.querySelectorAll('img');
    console.log(`[DEBUG] Found ${allImages.length} images on the page`);

    let loadedCount = 0;
    let errorCount = 0;
    let pendingCount = 0;

    allImages.forEach((img, index) => {
        // Check if image is already loaded or has error
        if (img.complete) {
            if (img.naturalWidth === 0) {
                errorCount++;
                console.error(`[DEBUG] Image #${index} failed to load:`, {
                    src: img.src,
                    alt: img.alt,
                    parent: img.parentNode.className
                });
            } else {
                loadedCount++;
                console.log(`[DEBUG] Image #${index} loaded successfully:`, {
                    src: img.src,
                    alt: img.alt,
                    dimensions: `${img.naturalWidth}x${img.naturalHeight}`
                });
            }
        } else {
            pendingCount++;
            console.log(`[DEBUG] Image #${index} is still loading:`, {
                src: img.src,
                alt: img.alt
            });

            // Add event listeners for pending images
            img.addEventListener('load', function() {
                console.log(`[DEBUG] Image #${index} loaded:`, {
                    src: this.src,
                    alt: this.alt,
                    dimensions: `${this.naturalWidth}x${this.naturalHeight}`
                });
            });

            img.addEventListener('error', function() {
                console.error(`[DEBUG] Image #${index} failed to load:`, {
                    src: this.src,
                    alt: this.alt
                });
            });
        }
    });

    console.log('[DEBUG] Image status summary:', {
        total: allImages.length,
        loaded: loadedCount,
        error: errorCount,
        pending: pendingCount
    });
}

// Initialize handlers when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("[DEBUG] DOM loaded, initializing company favorite button handlers.");

    // Clean up any lingering modal backdrops from previous page loads
    cleanupModalBackdrop();

    handleFavoriteButtons();
    handleCompanyRatingModal(); // Initialize company rating modal handler

    // Run image check after a short delay to allow images to load
    setTimeout(checkAllImages, 1000);

    // Debug carousel star ratings
    setTimeout(function() {
        console.log("[DEBUG] Checking carousel star ratings...");
        const carouselItems = document.querySelectorAll('.featured-carousel-item');
        carouselItems.forEach((item, index) => {
            const ratingContainer = item.querySelector('.rating-display-container');
            const starRating = item.querySelector('.star-rating');
            const stars = item.querySelectorAll('.star-rating .stars i');

            console.log(`[DEBUG] Carousel item ${index}:`, {
                hasRatingContainer: !!ratingContainer,
                hasStarRating: !!starRating,
                starsCount: stars.length,
                containerId: ratingContainer ? ratingContainer.id : 'none'
            });

            // Fix any styling issues with stars
            if (starRating) {
                starRating.style.display = 'inline-flex';
                starRating.style.alignItems = 'center';
                starRating.style.justifyContent = 'center';

                const starsContainer = starRating.querySelector('.stars');
                if (starsContainer) {
                    starsContainer.style.display = 'inline-flex';

                    const starIcons = starsContainer.querySelectorAll('i');
                    starIcons.forEach(icon => {
                        icon.style.fontSize = '1rem';
                        icon.style.margin = '0 1px';

                        if (icon.classList.contains('bi-star-fill')) {
                            icon.style.color = 'gold';
                        } else {
                            icon.style.color = '#ccc';
                        }
                    });
                }
            }
        });
    }, 2000);

    // Add a global click handler to clean up any lingering modal backdrops
    document.addEventListener('click', function(event) {
        // Check if click is outside any modal content
        if (!event.target.closest('.modal-content') && document.querySelector('.modal-backdrop')) {
            // Check if there are any open modals
            const openModals = document.querySelectorAll('.modal.show');
            if (openModals.length === 0) {
                // No open modals but backdrop exists - clean it up
                cleanupModalBackdrop();
            }
        }
    });
});
</script>
{% endblock %}
</section>

