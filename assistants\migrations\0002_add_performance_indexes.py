# Generated migration for performance optimization indexes

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assistants', '0001_initial'),  # Adjust based on your latest migration
    ]

    operations = [
        # Add indexes for Assistant model
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_company_type ON assistants_assistant(company_id, assistant_type);",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_company_type;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_active_public ON assistants_assistant(is_active, is_public);",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_active_public;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_created_at ON assistants_assistant(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_created_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_name_search ON assistants_assistant USING gin(to_tsvector('english', name));",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_name_search;"
        ),
        
        # Add indexes for Interaction model
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_interaction_assistant_user ON assistants_interaction(assistant_id, user_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_interaction_assistant_user;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_interaction_created_at ON assistants_interaction(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_interaction_created_at;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_interaction_assistant_created ON assistants_interaction(assistant_id, created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_interaction_assistant_created;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_interaction_rating ON assistants_interaction(rating) WHERE rating IS NOT NULL;",
            reverse_sql="DROP INDEX IF EXISTS idx_interaction_rating;"
        ),
        
        # Add indexes for CommunityContext model (if exists)
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_context_assistant ON assistants_communitycontext(assistant_id, is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_context_assistant;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_community_context_created ON assistants_communitycontext(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_community_context_created;"
        ),
        
        # Add indexes for AssistantFolder model (if exists)
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_folder_company ON assistants_assistantfolder(company_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_folder_company;"
        ),
        
        # Add composite indexes for common query patterns
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_company_active_public ON assistants_assistant(company_id, is_active, is_public);",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_company_active_public;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_type_active_public ON assistants_assistant(assistant_type, is_active, is_public);",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_type_active_public;"
        ),
        
        # Add partial indexes for better performance on filtered queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_active_only ON assistants_assistant(id) WHERE is_active = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_active_only;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_public_only ON assistants_assistant(id) WHERE is_public = true;",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_public_only;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_assistant_community_type ON assistants_assistant(id) WHERE assistant_type = 'community';",
            reverse_sql="DROP INDEX IF EXISTS idx_assistant_community_type;"
        ),
        
        # Add indexes for foreign key relationships that might not have them
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_interaction_user_id ON assistants_interaction(user_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_interaction_user_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_interaction_assistant_id ON assistants_interaction(assistant_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_interaction_assistant_id;"
        ),
        
        # Add indexes for content search if content models exist
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_content_company_type ON content_content(company_id, content_type);",
            reverse_sql="DROP INDEX IF EXISTS idx_content_company_type;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_content_search ON content_content USING gin(to_tsvector('english', title || ' ' || body));",
            reverse_sql="DROP INDEX IF EXISTS idx_content_search;"
        ),
        
        # Add indexes for accounts models
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_company_active_featured ON accounts_company(is_active, is_featured);",
            reverse_sql="DROP INDEX IF EXISTS idx_company_active_featured;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_company_info_directory ON accounts_companyinformation(list_in_directory);",
            reverse_sql="DROP INDEX IF EXISTS idx_company_info_directory;"
        ),
        
        # Add indexes for membership queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_membership_user_company ON accounts_membership(user_id, company_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_membership_user_company;"
        ),
        
        # Add indexes for directory models if they exist
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_saved_item_user_type ON directory_saveditem(user_id, item_type);",
            reverse_sql="DROP INDEX IF EXISTS idx_saved_item_user_type;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_saved_item_created ON directory_saveditem(created_at DESC);",
            reverse_sql="DROP INDEX IF EXISTS idx_saved_item_created;"
        ),
    ]
