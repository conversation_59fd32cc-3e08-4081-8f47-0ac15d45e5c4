{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Our Platform - NUP{% endblock %}

{% block meta_description %}
Discover how the National Unity Platform connects citizens with their representatives and builds stronger communities across Uganda.
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="nup-section-red">
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">Our Platform</h1>
                <p class="lead mb-0">Connecting citizens with representatives and building stronger communities across Uganda.</p>
            </div>
        </div>
    </div>
</section>

<!-- Main Features Section -->
<section class="nup-section-white">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="h2 fw-bold mb-4">Platform Features</h2>
                <div class="nup-accent-bar"></div>
                <p class="lead text-muted">
                    Our platform provides comprehensive tools to connect citizens with their representatives and strengthen democratic participation across Uganda.
                </p>
            </div>
        </div>

        <div class="row g-4 mb-5">
            <!-- Custom AI Assistants -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-primary bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-robot"></i>
                        </div>
                        <h3 class="h5 mb-2">Custom AI Assistants</h3>
                        <p class="text-muted mb-0">
                            Create tailored AI assistants trained on your company's specific data and knowledge. Each assistant can be customized with unique personalities, expertise areas, and capabilities.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Knowledge Management -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-success bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-database"></i>
                        </div>
                        <h3 class="h5 mb-2">Knowledge Management</h3>
                        <p class="text-muted mb-0">
                            Upload documents, connect data sources, and build comprehensive knowledge bases that power your assistants with accurate, up-to-date information.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Team Collaboration -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-info bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-people"></i>
                        </div>
                        <h3 class="h5 mb-2">Team Collaboration</h3>
                        <p class="text-muted mb-0">
                            Invite team members, assign roles, and collaborate on assistant development. Role-based permissions ensure the right people have access to the right tools.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Multi-Channel Deployment -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-warning bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-broadcast"></i>
                        </div>
                        <h3 class="h5 mb-2">Multi-Channel Deployment</h3>
                        <p class="text-muted mb-0">
                            Deploy your assistants across multiple channels including web, mobile, Slack, Microsoft Teams, and more. Reach your users wherever they are.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Analytics & Insights -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-danger bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <h3 class="h5 mb-2">Analytics & Insights</h3>
                        <p class="text-muted mb-0">
                            Track assistant performance, user engagement, and conversation quality. Gain insights to continuously improve your AI assistants.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Enterprise Security -->
            <div class="col-md-6 col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon bg-secondary bg-gradient text-white rounded-3 mb-3">
                            <i class="bi bi-shield-lock"></i>
                        </div>
                        <h3 class="h5 mb-2">Enterprise Security</h3>
                        <p class="text-muted mb-0">
                            Enterprise-grade security with data encryption, access controls, and compliance features to keep your information safe and secure.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Assistant Capabilities Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="h2 fw-bold mb-4">Assistant Capabilities</h2>
                <p class="lead text-muted">
                    Our AI assistants come with powerful capabilities that can be customized to meet your specific needs.
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-sm bg-primary text-white rounded-circle">
                            <i class="bi bi-chat-dots"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-2">Natural Conversations</h3>
                        <p class="text-muted mb-0">
                            Engage in natural, human-like conversations with context awareness and memory of previous interactions.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-sm bg-primary text-white rounded-circle">
                            <i class="bi bi-translate"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-2">Multilingual Support</h3>
                        <p class="text-muted mb-0">
                            Communicate with users in multiple languages, breaking down language barriers for global teams.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-sm bg-primary text-white rounded-circle">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-2">Document Analysis</h3>
                        <p class="text-muted mb-0">
                            Extract insights, summarize content, and answer questions based on uploaded documents and files.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-sm bg-primary text-white rounded-circle">
                            <i class="bi bi-code-square"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-2">Code Generation</h3>
                        <p class="text-muted mb-0">
                            Generate code snippets, debug issues, and provide programming assistance across multiple languages.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-sm bg-primary text-white rounded-circle">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-2">Task Automation</h3>
                        <p class="text-muted mb-0">
                            Automate routine tasks, schedule meetings, set reminders, and manage workflows to save time.
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <div class="feature-icon-sm bg-primary text-white rounded-circle">
                            <i class="bi bi-image"></i>
                        </div>
                    </div>
                    <div class="ms-3">
                        <h3 class="h5 mb-2">Image Understanding</h3>
                        <p class="text-muted mb-0">
                            Analyze and interpret images, extract text from visuals, and provide context-aware responses.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-gradient position-relative cta-section">
    <div class="container">
        <div class="p-5 text-center bg-white rounded-3 shadow-sm">
            <h2 class="display-6 fw-bold mb-4">Ready to Experience These Features?</h2>
            <p class="lead mb-4">
                Start building your custom AI assistants today and transform how your business operates.
            </p>
            <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
                <a href="{% url 'accounts:register' %}" class="btn btn-primary btn-lg px-4 me-sm-3">
                    Start Free Trial
                </a>
                <a href="{{ site_config.contact_url|default:'/contact/' }}" class="btn btn-outline-secondary btn-lg px-4">
                    Contact Sales
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    .feature-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .feature-icon-sm {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
    }

    .bg-gradient {
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    }

    .card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add scroll reveal animation
        const animateOnScroll = () => {
            document.querySelectorAll('.card, .d-flex').forEach(element => {
                const rect = element.getBoundingClientRect();
                if (rect.top < window.innerHeight - 100) {
                    element.style.opacity = '1';
                    element.style.transform = element.classList.contains('card') ? 'translateY(0)' : 'translateX(0)';
                }
            });
        };

        // Initial styles
        document.querySelectorAll('.card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease-out';
        });

        document.querySelectorAll('.d-flex').forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = index % 2 === 0 ? 'translateX(-20px)' : 'translateX(20px)';
            element.style.transition = 'all 0.6s ease-out';
        });

        // Listen for scroll
        window.addEventListener('scroll', animateOnScroll);
        animateOnScroll(); // Initial check
    });
</script>
{% endblock %}
