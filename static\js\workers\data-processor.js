/**
 * Web Worker for Heavy Data Processing
 * Offloads CPU-intensive tasks from the main thread
 */

class DataProcessor {
    constructor() {
        this.cache = new Map();
        this.processors = {
            'filter-assistants': this.filterAssistants.bind(this),
            'search-assistants': this.searchAssistants.bind(this),
            'sort-data': this.sortData.bind(this),
            'aggregate-stats': this.aggregateStats.bind(this),
            'process-interactions': this.processInteractions.bind(this),
            'generate-insights': this.generateInsights.bind(this),
            'compress-data': this.compressData.bind(this),
            'decompress-data': this.decompressData.bind(this)
        };
        
        this.init();
    }
    
    init() {
        self.addEventListener('message', this.handleMessage.bind(this));
        console.log('🔧 Data Processor Worker initialized');
    }
    
    handleMessage(event) {
        const { id, type, data, options = {} } = event.data;
        
        try {
            const startTime = performance.now();
            
            if (this.processors[type]) {
                const result = this.processors[type](data, options);
                const duration = performance.now() - startTime;
                
                this.postMessage({
                    id,
                    success: true,
                    result,
                    duration,
                    type
                });
                
                // Log performance for optimization
                if (duration > 100) {
                    console.warn(`🐌 Slow processing: ${type} took ${duration.toFixed(2)}ms`);
                }
            } else {
                throw new Error(`Unknown processor type: ${type}`);
            }
        } catch (error) {
            this.postMessage({
                id,
                success: false,
                error: error.message,
                type
            });
        }
    }
    
    postMessage(data) {
        self.postMessage(data);
    }
    
    // Assistant Filtering
    filterAssistants(assistants, options) {
        const {
            search = '',
            category = '',
            company = '',
            isPublic = null,
            isActive = true,
            minRating = 0,
            sortBy = 'name',
            sortOrder = 'asc'
        } = options;
        
        let filtered = assistants.filter(assistant => {
            // Basic filters
            if (isActive !== null && assistant.is_active !== isActive) return false;
            if (isPublic !== null && assistant.is_public !== isPublic) return false;
            if (assistant.average_rating < minRating) return false;
            
            // Text search
            if (search) {
                const searchLower = search.toLowerCase();
                const searchableText = [
                    assistant.name,
                    assistant.description,
                    assistant.category,
                    assistant.company_name
                ].join(' ').toLowerCase();
                
                if (!searchableText.includes(searchLower)) return false;
            }
            
            // Category filter
            if (category && assistant.category !== category) return false;
            
            // Company filter
            if (company && assistant.company_id !== company) return false;
            
            return true;
        });
        
        // Sorting
        filtered = this.sortData(filtered, { sortBy, sortOrder });
        
        return {
            assistants: filtered,
            total: filtered.length,
            filters: options
        };
    }
    
    // Advanced Search
    searchAssistants(assistants, options) {
        const { query, fuzzyMatch = true, weights = {} } = options;
        
        if (!query) return assistants;
        
        const defaultWeights = {
            name: 3,
            description: 2,
            category: 1,
            tags: 2
        };
        
        const searchWeights = { ...defaultWeights, ...weights };
        const queryLower = query.toLowerCase();
        const queryWords = queryLower.split(/\s+/).filter(word => word.length > 0);
        
        const scoredResults = assistants.map(assistant => {
            let score = 0;
            
            // Exact name match gets highest score
            if (assistant.name.toLowerCase() === queryLower) {
                score += 100;
            }
            
            // Calculate weighted scores
            Object.entries(searchWeights).forEach(([field, weight]) => {
                const fieldValue = (assistant[field] || '').toLowerCase();
                
                // Exact phrase match
                if (fieldValue.includes(queryLower)) {
                    score += weight * 10;
                }
                
                // Individual word matches
                queryWords.forEach(word => {
                    if (fieldValue.includes(word)) {
                        score += weight;
                    }
                    
                    // Fuzzy matching for typos
                    if (fuzzyMatch && this.fuzzyMatch(word, fieldValue)) {
                        score += weight * 0.5;
                    }
                });
            });
            
            return { ...assistant, searchScore: score };
        });
        
        // Filter out zero scores and sort by relevance
        return scoredResults
            .filter(item => item.searchScore > 0)
            .sort((a, b) => b.searchScore - a.searchScore);
    }
    
    // Fuzzy matching for typos
    fuzzyMatch(word, text, threshold = 0.8) {
        const words = text.split(/\s+/);
        
        return words.some(textWord => {
            if (textWord.length < 3 || word.length < 3) return false;
            
            const similarity = this.calculateSimilarity(word, textWord);
            return similarity >= threshold;
        });
    }
    
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }
    
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    // Data Sorting
    sortData(data, options) {
        const { sortBy, sortOrder = 'asc' } = options;
        
        if (!sortBy) return data;
        
        return [...data].sort((a, b) => {
            let aVal = this.getNestedValue(a, sortBy);
            let bVal = this.getNestedValue(b, sortBy);
            
            // Handle different data types
            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }
            
            if (aVal === bVal) return 0;
            
            const comparison = aVal < bVal ? -1 : 1;
            return sortOrder === 'desc' ? -comparison : comparison;
        });
    }
    
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : '';
        }, obj);
    }
    
    // Statistics Aggregation
    aggregateStats(data, options) {
        const { groupBy, metrics = ['count'] } = options;
        
        if (!groupBy) {
            return this.calculateMetrics(data, metrics);
        }
        
        // Group data
        const groups = data.reduce((acc, item) => {
            const key = this.getNestedValue(item, groupBy);
            if (!acc[key]) acc[key] = [];
            acc[key].push(item);
            return acc;
        }, {});
        
        // Calculate metrics for each group
        const result = {};
        Object.entries(groups).forEach(([key, items]) => {
            result[key] = this.calculateMetrics(items, metrics);
        });
        
        return result;
    }
    
    calculateMetrics(data, metrics) {
        const result = {};
        
        metrics.forEach(metric => {
            switch (metric) {
                case 'count':
                    result.count = data.length;
                    break;
                    
                case 'average_rating':
                    const ratings = data.map(item => item.average_rating || 0);
                    result.average_rating = ratings.length > 0 
                        ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length 
                        : 0;
                    break;
                    
                case 'total_interactions':
                    result.total_interactions = data.reduce((sum, item) => 
                        sum + (item.total_interactions || 0), 0);
                    break;
                    
                case 'active_percentage':
                    const activeCount = data.filter(item => item.is_active).length;
                    result.active_percentage = data.length > 0 
                        ? (activeCount / data.length) * 100 
                        : 0;
                    break;
            }
        });
        
        return result;
    }
    
    // Interaction Processing
    processInteractions(interactions, options) {
        const { timeRange = '30d', aggregateBy = 'day' } = options;
        
        // Filter by time range
        const cutoffDate = this.getTimeRangeCutoff(timeRange);
        const filtered = interactions.filter(interaction => 
            new Date(interaction.created_at) >= cutoffDate
        );
        
        // Aggregate by time period
        const aggregated = this.aggregateByTime(filtered, aggregateBy);
        
        return {
            total: filtered.length,
            timeRange,
            aggregated,
            metrics: this.calculateInteractionMetrics(filtered)
        };
    }
    
    getTimeRangeCutoff(timeRange) {
        const now = new Date();
        const match = timeRange.match(/(\d+)([dwmy])/);
        
        if (!match) return new Date(0);
        
        const [, amount, unit] = match;
        const value = parseInt(amount);
        
        switch (unit) {
            case 'd': return new Date(now.getTime() - value * 24 * 60 * 60 * 1000);
            case 'w': return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000);
            case 'm': return new Date(now.getTime() - value * 30 * 24 * 60 * 60 * 1000);
            case 'y': return new Date(now.getTime() - value * 365 * 24 * 60 * 60 * 1000);
            default: return new Date(0);
        }
    }
    
    aggregateByTime(interactions, period) {
        const groups = {};
        
        interactions.forEach(interaction => {
            const date = new Date(interaction.created_at);
            const key = this.getTimePeriodKey(date, period);
            
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(interaction);
        });
        
        return Object.entries(groups).map(([key, items]) => ({
            period: key,
            count: items.length,
            averageRating: items.reduce((sum, item) => sum + (item.rating || 0), 0) / items.length,
            averageDuration: items.reduce((sum, item) => sum + (item.duration || 0), 0) / items.length
        }));
    }
    
    getTimePeriodKey(date, period) {
        switch (period) {
            case 'hour':
                return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}-${date.getHours()}`;
            case 'day':
                return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
            case 'week':
                const weekStart = new Date(date);
                weekStart.setDate(date.getDate() - date.getDay());
                return `${weekStart.getFullYear()}-W${Math.ceil(weekStart.getDate() / 7)}`;
            case 'month':
                return `${date.getFullYear()}-${date.getMonth() + 1}`;
            default:
                return date.toISOString().split('T')[0];
        }
    }
    
    calculateInteractionMetrics(interactions) {
        if (interactions.length === 0) {
            return {
                averageRating: 0,
                averageDuration: 0,
                totalTokens: 0,
                successRate: 0
            };
        }
        
        const totalRating = interactions.reduce((sum, item) => sum + (item.rating || 0), 0);
        const totalDuration = interactions.reduce((sum, item) => sum + (item.duration || 0), 0);
        const totalTokens = interactions.reduce((sum, item) => sum + (item.token_count || 0), 0);
        const successfulInteractions = interactions.filter(item => !item.error).length;
        
        return {
            averageRating: totalRating / interactions.length,
            averageDuration: totalDuration / interactions.length,
            totalTokens,
            successRate: (successfulInteractions / interactions.length) * 100
        };
    }
    
    // Data Compression
    compressData(data) {
        try {
            const jsonString = JSON.stringify(data);
            // Simple compression using repeated pattern replacement
            let compressed = jsonString;
            
            // Replace common patterns
            const patterns = [
                ['"id":', '§1'],
                ['"name":', '§2'],
                ['"description":', '§3'],
                ['"created_at":', '§4'],
                ['"updated_at":', '§5'],
                ['true', '§t'],
                ['false', '§f'],
                ['null', '§n']
            ];
            
            patterns.forEach(([pattern, replacement]) => {
                compressed = compressed.replace(new RegExp(pattern, 'g'), replacement);
            });
            
            return {
                compressed,
                originalSize: jsonString.length,
                compressedSize: compressed.length,
                ratio: compressed.length / jsonString.length
            };
        } catch (error) {
            throw new Error(`Compression failed: ${error.message}`);
        }
    }
    
    // Data Decompression
    decompressData(compressedData) {
        try {
            let decompressed = compressedData.compressed || compressedData;
            
            // Restore patterns
            const patterns = [
                ['§1', '"id":'],
                ['§2', '"name":'],
                ['§3', '"description":'],
                ['§4', '"created_at":'],
                ['§5', '"updated_at":'],
                ['§t', 'true'],
                ['§f', 'false'],
                ['§n', 'null']
            ];
            
            patterns.forEach(([replacement, pattern]) => {
                decompressed = decompressed.replace(new RegExp(replacement, 'g'), pattern);
            });
            
            return JSON.parse(decompressed);
        } catch (error) {
            throw new Error(`Decompression failed: ${error.message}`);
        }
    }
    
    // Generate Insights
    generateInsights(data, options) {
        const insights = [];
        
        // Performance insights
        if (data.interactions) {
            const avgDuration = data.interactions.reduce((sum, i) => sum + (i.duration || 0), 0) / data.interactions.length;
            if (avgDuration > 5000) {
                insights.push({
                    type: 'performance',
                    severity: 'warning',
                    message: `Average response time is ${(avgDuration / 1000).toFixed(1)}s - consider optimization`,
                    metric: avgDuration
                });
            }
        }
        
        // Usage insights
        if (data.assistants) {
            const inactiveCount = data.assistants.filter(a => !a.is_active).length;
            const inactivePercentage = (inactiveCount / data.assistants.length) * 100;
            
            if (inactivePercentage > 30) {
                insights.push({
                    type: 'usage',
                    severity: 'info',
                    message: `${inactivePercentage.toFixed(1)}% of assistants are inactive`,
                    metric: inactivePercentage
                });
            }
        }
        
        return insights;
    }
}

// Initialize the worker
new DataProcessor();
