"""
Management command to clean up expired and orphaned sessions.
"""
from django.core.management.base import BaseCommand
from django.contrib.sessions.models import Session
from django.utils import timezone
from django.contrib.auth import get_user_model
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


class Command(BaseCommand):
    help = 'Clean up expired and orphaned sessions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )
        parser.add_argument(
            '--all-expired',
            action='store_true',
            help='Clean up all expired sessions',
        )
        parser.add_argument(
            '--orphaned',
            action='store_true',
            help='Clean up sessions for users that no longer exist',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        clean_expired = options['all_expired']
        clean_orphaned = options['orphaned']

        if not clean_expired and not clean_orphaned:
            # Default: clean both
            clean_expired = True
            clean_orphaned = True

        total_cleaned = 0

        if clean_expired:
            total_cleaned += self.clean_expired_sessions(dry_run)

        if clean_orphaned:
            total_cleaned += self.clean_orphaned_sessions(dry_run)

        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'DRY RUN: Would have cleaned up {total_cleaned} sessions'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully cleaned up {total_cleaned} sessions'
                )
            )

    def clean_expired_sessions(self, dry_run=False):
        """Clean up expired sessions."""
        expired_sessions = Session.objects.filter(
            expire_date__lt=timezone.now()
        )

        count = expired_sessions.count()

        if count > 0:
            self.stdout.write(f'Found {count} expired sessions')

            if not dry_run:
                expired_sessions.delete()
                logger.info(f'Cleaned up {count} expired sessions')
        else:
            self.stdout.write('No expired sessions found')

        return count

    def clean_orphaned_sessions(self, dry_run=False):
        """Clean up sessions for users that no longer exist."""
        orphaned_sessions = []

        # Get all active sessions
        active_sessions = Session.objects.filter(
            expire_date__gte=timezone.now()
        )

        self.stdout.write(f'Checking {active_sessions.count()} active sessions for orphaned data')

        for session in active_sessions:
            is_corrupted = False
            try:
                session_data = session.get_decoded()
                if not session_data:
                    # Empty session data
                    is_corrupted = True
                else:
                    user_id = session_data.get('_auth_user_id')

                    if user_id:
                        # Check if user still exists
                        try:
                            User.objects.get(id=user_id)
                        except User.DoesNotExist:
                            orphaned_sessions.append(session)
                            self.stdout.write(f'Found orphaned session for deleted user ID: {user_id}')

            except Exception as e:
                # Session data is corrupted
                is_corrupted = True
                self.stdout.write(f'Found corrupted session: {session.session_key} - {str(e)}')

            if is_corrupted:
                orphaned_sessions.append(session)

        count = len(orphaned_sessions)

        if count > 0:
            self.stdout.write(f'Found {count} orphaned sessions')

            if not dry_run:
                for session in orphaned_sessions:
                    session.delete()
                logger.info(f'Cleaned up {count} orphaned sessions')
        else:
            self.stdout.write('No orphaned sessions found')

        return count
