/**
 * Unified Header JavaScript
 * Handles functionality for the consolidated header component
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize header functionality
    initializeUnifiedHeader();
    
    // Initialize company actions if present
    initializeCompanyActions();
    
    // Initialize share functionality
    initializeShareButton();
    
    // Initialize like button functionality
    initializeLikeButtons();
});

/**
 * Initialize unified header functionality
 */
function initializeUnifiedHeader() {
    // Handle dropdown accessibility
    const dropdownToggles = document.querySelectorAll('.unified-header .dropdown-toggle');
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    
    // Handle mobile menu close on item click
    const navItems = document.querySelectorAll('.unified-header .nav-link:not(.dropdown-toggle)');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // Close mobile menu if open
            const navbarCollapse = document.getElementById('unifiedNavContent');
            if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                const navbarToggler = document.querySelector('.unified-header .navbar-toggler');
                if (navbarToggler) {
                    navbarToggler.click();
                }
            }
        });
    });
    
    // Handle scroll behavior for header
    let lastScrollTop = 0;
    const header = document.querySelector('.unified-header');
    
    if (header) {
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Add/remove scrolled class for styling
            if (scrollTop > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            
            lastScrollTop = scrollTop;
        });
    }
}

/**
 * Initialize company actions functionality
 */
function initializeCompanyActions() {
    // QR Code button functionality
    const qrButtons = document.querySelectorAll('[data-bs-target="#companyQrCodeModal"]');
    qrButtons.forEach(button => {
        button.addEventListener('click', function() {
            const qrUrl = this.getAttribute('data-qr-url');
            const modal = document.getElementById('companyQrCodeModal');
            if (modal && qrUrl) {
                const img = modal.querySelector('img');
                if (img) {
                    img.src = qrUrl;
                }
            }
        });
    });
    
    // Rating button functionality
    const ratingButtons = document.querySelectorAll('[data-bs-target="#ratingModal"]');
    ratingButtons.forEach(button => {
        button.addEventListener('click', function() {
            const companyId = this.getAttribute('data-company-id');
            const companyName = this.getAttribute('data-company-name');
            const modal = document.getElementById('ratingModal');
            
            if (modal && companyId) {
                // Update modal with company information
                const modalTitle = modal.querySelector('.modal-title');
                if (modalTitle) {
                    modalTitle.textContent = `Rate ${companyName}`;
                }
                
                // Set company ID in form if present
                const companyIdInput = modal.querySelector('input[name="company_id"]');
                if (companyIdInput) {
                    companyIdInput.value = companyId;
                }
            }
        });
    });
}

/**
 * Initialize share button functionality
 */
function initializeShareButton() {
    const shareButton = document.getElementById('copy-company-url-btn');
    if (shareButton) {
        shareButton.addEventListener('click', function() {
            if (navigator.share) {
                // Use Web Share API if available
                const companyName = document.querySelector('.company-name')?.textContent || 'Company';
                navigator.share({
                    title: companyName,
                    text: `Check out ${companyName}!`,
                    url: window.location.href
                })
                .then(() => console.log('Share successful'))
                .catch((error) => console.log('Error sharing:', error));
            } else {
                // Fallback: copy to clipboard
                const tempInput = document.createElement('input');
                tempInput.value = window.location.href;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                
                // Show feedback
                showToast('URL copied to clipboard!', 'success');
            }
        });
    }
}

/**
 * Initialize like button functionality
 */
function initializeLikeButtons() {
    const likeButtons = document.querySelectorAll('.unified-header .like-button');
    likeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const itemId = this.getAttribute('data-item-id');
            const itemType = this.getAttribute('data-item-type');
            
            if (!itemId || !itemType) return;
            
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
            if (!csrfToken) {
                console.error('CSRF token not found');
                return;
            }
            
            // Toggle like status
            fetch('/directory/toggle-favorite/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    item_id: itemId,
                    item_type: itemType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update button appearance
                    const icon = this.querySelector('i');
                    if (data.is_favorited) {
                        this.classList.remove('text-secondary');
                        this.classList.add('text-danger');
                        icon.classList.remove('bi-heart');
                        icon.classList.add('bi-heart-fill');
                        this.setAttribute('title', 'Remove from Favorites');
                    } else {
                        this.classList.remove('text-danger');
                        this.classList.add('text-secondary');
                        icon.classList.remove('bi-heart-fill');
                        icon.classList.add('bi-heart');
                        this.setAttribute('title', 'Add to Favorites');
                    }
                    
                    // Show feedback
                    const message = data.is_favorited ? 'Added to favorites!' : 'Removed from favorites!';
                    showToast(message, 'success');
                } else {
                    showToast('Error updating favorites', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error updating favorites', 'error');
            });
        });
    });
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'primary'} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    // Add to toast container or create one
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
    });
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

/**
 * Handle responsive behavior
 */
function handleResponsiveBehavior() {
    const header = document.querySelector('.unified-header');
    if (!header) return;
    
    function updateHeaderForScreenSize() {
        const isMobile = window.innerWidth < 992;
        
        if (isMobile) {
            header.classList.add('mobile-view');
        } else {
            header.classList.remove('mobile-view');
        }
    }
    
    // Initial check
    updateHeaderForScreenSize();
    
    // Listen for resize events
    window.addEventListener('resize', updateHeaderForScreenSize);
}

// Initialize responsive behavior
document.addEventListener('DOMContentLoaded', handleResponsiveBehavior);
