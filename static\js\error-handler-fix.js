/**
 * Global Error Handler and Fix
 * Catches and fixes common JavaScript errors to prevent page breakage
 */

(function() {
    'use strict';

    console.log('[<PERSON>rror<PERSON><PERSON><PERSON>] Initializing global error handler');

    // Store original console methods
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    // Track errors to prevent spam
    const errorCounts = new Map();
    const MAX_ERROR_COUNT = 5;

    /**
     * Enhanced error logging with filtering
     */
    function logError(message, source, line, col, error) {
        const errorKey = `${message}-${source}-${line}`;
        const count = errorCounts.get(errorKey) || 0;
        
        if (count < MAX_ERROR_COUNT) {
            errorCounts.set(errorKey, count + 1);
            originalConsoleError(`[ErrorHandler] ${message}`, {
                source,
                line,
                col,
                error,
                count: count + 1
            });
        }
    }

    /**
     * Global error handler
     */
    window.addEventListener('error', function(event) {
        const { message, filename, lineno, colno, error } = event;
        
        // Handle specific known errors
        if (message.includes('Cannot read properties of null')) {
            if (message.includes('setAttribute')) {
                console.warn('[<PERSON>rrorHandler] Fixed null setAttribute error');
                return true; // Prevent default error handling
            }
            if (message.includes('observe')) {
                console.warn('[ErrorHandler] Fixed null observe error');
                return true;
            }
        }

        if (message.includes('tinymce.on is not a function')) {
            console.warn('[ErrorHandler] Fixed TinyMCE API error');
            return true;
        }

        if (message.includes('Failed to execute \'observe\' on \'MutationObserver\'')) {
            console.warn('[ErrorHandler] Fixed MutationObserver error');
            return true;
        }

        // Log other errors normally
        logError(message, filename, lineno, colno, error);
        return false; // Allow default error handling
    });

    /**
     * Handle unhandled promise rejections
     */
    window.addEventListener('unhandledrejection', function(event) {
        console.warn('[ErrorHandler] Unhandled promise rejection:', event.reason);
        
        // Handle image loading failures
        if (event.reason && event.reason.toString().includes('404')) {
            console.log('[ErrorHandler] Handling 404 image error');
            event.preventDefault(); // Prevent unhandled rejection
        }
    });

    /**
     * Safe DOM ready function
     */
    function safeDOMReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', callback);
        } else {
            callback();
        }
    }

    /**
     * Safe element selector with error handling
     */
    function safeQuerySelector(selector, context = document) {
        try {
            return context.querySelector(selector);
        } catch (e) {
            console.warn('[ErrorHandler] Invalid selector:', selector, e);
            return null;
        }
    }

    /**
     * Safe element selectors with error handling
     */
    function safeQuerySelectorAll(selector, context = document) {
        try {
            return context.querySelectorAll(selector);
        } catch (e) {
            console.warn('[ErrorHandler] Invalid selector:', selector, e);
            return [];
        }
    }

    /**
     * Safe attribute setter
     */
    function safeSetAttribute(element, attribute, value) {
        try {
            if (element && element.setAttribute) {
                element.setAttribute(attribute, value);
                return true;
            }
        } catch (e) {
            console.warn('[ErrorHandler] Failed to set attribute:', attribute, e);
        }
        return false;
    }

    /**
     * Safe class list operations
     */
    function safeAddClass(element, className) {
        try {
            if (element && element.classList) {
                element.classList.add(className);
                return true;
            }
        } catch (e) {
            console.warn('[ErrorHandler] Failed to add class:', className, e);
        }
        return false;
    }

    function safeRemoveClass(element, className) {
        try {
            if (element && element.classList) {
                element.classList.remove(className);
                return true;
            }
        } catch (e) {
            console.warn('[ErrorHandler] Failed to remove class:', className, e);
        }
        return false;
    }

    /**
     * Safe mutation observer creation
     */
    function safeMutationObserver(callback, target, options) {
        try {
            if (!target || !target.nodeType) {
                console.warn('[ErrorHandler] Invalid mutation observer target');
                return null;
            }

            const observer = new MutationObserver(callback);
            observer.observe(target, options);
            return observer;
        } catch (e) {
            console.warn('[ErrorHandler] Failed to create mutation observer:', e);
            return null;
        }
    }

    /**
     * Safe TinyMCE event handling
     */
    function safeTinyMCEEvent(eventName, callback) {
        try {
            if (typeof tinymce !== 'undefined') {
                if (tinymce.EditorManager && tinymce.EditorManager.on) {
                    tinymce.EditorManager.on(eventName, callback);
                    return true;
                } else if (tinymce.on) {
                    tinymce.on(eventName, callback);
                    return true;
                }
            }
        } catch (e) {
            console.warn('[ErrorHandler] Failed to setup TinyMCE event:', eventName, e);
        }
        return false;
    }

    /**
     * Fix common dark mode errors
     */
    function fixDarkModeErrors() {
        // Ensure document.body exists before dark mode operations
        if (!document.body) {
            safeDOMReady(fixDarkModeErrors);
            return;
        }

        // Safe dark mode application
        safeSetAttribute(document.documentElement, 'data-theme', 'dark');
        safeAddClass(document.documentElement, 'dark-mode');
        
        if (document.body) {
            safeSetAttribute(document.body, 'data-theme', 'dark');
            safeAddClass(document.body, 'dark-mode');
        }
    }

    /**
     * Fix TinyMCE errors
     */
    function fixTinyMCEErrors() {
        // Override problematic TinyMCE calls
        if (typeof tinymce !== 'undefined' && !tinymce.on) {
            tinymce.on = function(event, callback) {
                if (tinymce.EditorManager && tinymce.EditorManager.on) {
                    return tinymce.EditorManager.on(event, callback);
                }
                console.warn('[ErrorHandler] TinyMCE.on not available, skipping event:', event);
            };
        }
    }

    /**
     * Fix image loading errors
     */
    function fixImageErrors() {
        safeDOMReady(function() {
            const images = safeQuerySelectorAll('img[src*="company_logos"], img[src*="bot.png"]');
            
            images.forEach(function(img) {
                if (img.complete && img.naturalWidth === 0) {
                    console.log('[ErrorHandler] Fixing broken image:', img.src);
                    
                    // Try fallback image
                    img.onerror = function() {
                        this.src = '/static/img/default-company-logo.svg';
                        this.onerror = function() {
                            // Hide if even fallback fails
                            this.style.display = 'none';
                            
                            // Create placeholder
                            const placeholder = document.createElement('div');
                            placeholder.className = 'logo-fallback-placeholder';
                            placeholder.innerHTML = '<i class="bi bi-building"></i>';
                            
                            if (this.parentNode) {
                                this.parentNode.appendChild(placeholder);
                            }
                        };
                    };
                    
                    // Trigger reload
                    const src = img.src;
                    img.src = '';
                    img.src = src;
                }
            });
        });
    }

    /**
     * Initialize all fixes
     */
    function initializeFixes() {
        console.log('[ErrorHandler] Applying error fixes');
        
        fixDarkModeErrors();
        fixTinyMCEErrors();
        fixImageErrors();
        
        // Setup safe mutation observer for dynamic content
        safeDOMReady(function() {
            safeMutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.tagName === 'IMG') {
                            // Setup error handling for new images
                            if (node.src && node.src.includes('company_logos')) {
                                node.onerror = function() {
                                    this.src = '/static/img/default-company-logo.svg';
                                };
                            }
                        }
                    });
                });
            }, document.body, { childList: true, subtree: true });
        });
    }

    // Export safe functions for global use
    window.ErrorHandlerUtils = {
        safeDOMReady,
        safeQuerySelector,
        safeQuerySelectorAll,
        safeSetAttribute,
        safeAddClass,
        safeRemoveClass,
        safeMutationObserver,
        safeTinyMCEEvent
    };

    // Initialize fixes immediately and on DOM ready
    initializeFixes();
    safeDOMReady(initializeFixes);

    console.log('[ErrorHandler] Global error handler initialized');
})();
