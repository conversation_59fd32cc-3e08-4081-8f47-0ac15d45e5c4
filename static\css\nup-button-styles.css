/**
 * NUP Button Styles CSS
 * Bold white text with NUP color hover effects
 * Transitions between #cf2e2e (NUP Red) and #252638 (NUP Dark Accent)
 */

/* ===== UNIVERSAL BUTTON STYLING ===== */
.btn, button, 
input[type="button"], input[type="submit"], input[type="reset"],
.button, [role="button"] {
    font-weight: 700 !important;
    color: #ffffff !important;
    transition: all 0.3s ease !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 6px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    display: inline-block !important;
    text-align: center !important;
    vertical-align: middle !important;
    user-select: none !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* ===== PRIMARY BUTTONS (NUP Red to Dark Accent) ===== */
.btn-primary, 
.btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-outline-light):not(.btn-light):not(.btn-warning) {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.2) !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active,
.btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-outline-light):not(.btn-light):not(.btn-warning):hover,
.btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-outline-light):not(.btn-light):not(.btn-warning):focus,
.btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-outline-light):not(.btn-light):not(.btn-warning):active {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

/* ===== SECONDARY BUTTONS (Dark Accent to NUP Red) ===== */
.btn-secondary {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    box-shadow: 0 2px 4px rgba(37, 38, 56, 0.2) !important;
}

.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== OUTLINE BUTTONS ===== */
.btn-outline-primary {
    border: 2px solid #cf2e2e !important;
    color: #cf2e2e !important;
    background-color: transparent !important;
    font-weight: 700 !important;
}

.btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

.btn-outline-secondary {
    border: 2px solid #252638 !important;
    color: #252638 !important;
    background-color: transparent !important;
    font-weight: 700 !important;
}

.btn-outline-secondary:hover, .btn-outline-secondary:focus, .btn-outline-secondary:active {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

/* ===== LIGHT BUTTONS ===== */
.btn-light {
    background-color: #f8f9fa !important;
    border-color: #f8f9fa !important;
    color: #333333 !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(248, 249, 250, 0.2) !important;
}

.btn-light:hover, .btn-light:focus, .btn-light:active {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== SUCCESS BUTTONS ===== */
.btn-success {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.2) !important;
}

.btn-success:hover, .btn-success:focus, .btn-success:active {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

/* ===== DANGER BUTTONS ===== */
.btn-danger {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.2) !important;
}

.btn-danger:hover, .btn-danger:focus, .btn-danger:active {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

/* ===== WARNING BUTTONS (NUP Gold) ===== */
.btn-warning {
    background-color: #f7bd00 !important;
    border-color: #f7bd00 !important;
    color: #333333 !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(247, 189, 0, 0.2) !important;
}

.btn-warning:hover, .btn-warning:focus, .btn-warning:active {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== INFO BUTTONS ===== */
.btn-info {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(37, 38, 56, 0.2) !important;
}

.btn-info:hover, .btn-info:focus, .btn-info:active {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== DARK BUTTONS ===== */
.btn-dark {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(37, 38, 56, 0.2) !important;
}

.btn-dark:hover, .btn-dark:focus, .btn-dark:active {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== BUTTON STATES ===== */
.btn:focus, .btn:active, .btn.active {
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
    outline: none !important;
}

.btn:disabled, .btn.disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    pointer-events: none !important;
}

/* ===== BUTTON SIZES ===== */
.btn-sm {
    padding: 8px 16px !important;
    font-size: 12px !important;
}

.btn-lg {
    padding: 16px 32px !important;
    font-size: 16px !important;
}

/* ===== BUTTON GROUPS ===== */
.btn-group .btn {
    border-radius: 0 !important;
}

.btn-group .btn:first-child {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
}

.btn-group .btn:last-child {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;
}

/* ===== SPECIAL BUTTON CLASSES ===== */
.btn-block {
    width: 100% !important;
    display: block !important;
}

.btn-link {
    background-color: transparent !important;
    border: none !important;
    color: #cf2e2e !important;
    text-decoration: underline !important;
    font-weight: 400 !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    padding: 0 !important;
    box-shadow: none !important;
}

.btn-link:hover, .btn-link:focus, .btn-link:active {
    color: #252638 !important;
    background-color: transparent !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== FORM SUBMIT BUTTONS ===== */
input[type="submit"], input[type="button"], input[type="reset"] {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

input[type="submit"]:hover, input[type="button"]:hover, input[type="reset"]:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

/* ===== RESPONSIVE BUTTON ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .btn {
        padding: 10px 20px !important;
        font-size: 13px !important;
    }
    
    .btn-sm {
        padding: 6px 12px !important;
        font-size: 11px !important;
    }
    
    .btn-lg {
        padding: 14px 28px !important;
        font-size: 15px !important;
    }
}

/* ===== ENSURE BUTTON TEXT IS ALWAYS VISIBLE ===== */
.btn *, button *, input[type="button"] *, input[type="submit"] *, input[type="reset"] * {
    color: inherit !important;
}

/* ===== AGGRESSIVE BUTTON TEXT STYLING ===== */
/* Force bold white text on ALL buttons with maximum specificity */
.btn, .btn *,
button, button *,
input[type="button"], input[type="button"] *,
input[type="submit"], input[type="submit"] *,
input[type="reset"], input[type="reset"] *,
.button, .button *,
[role="button"], [role="button"] *,
a.btn, a.btn *,
span.btn, span.btn *,
div.btn, div.btn * {
    font-weight: 700 !important;
    color: #ffffff !important;
    text-shadow: none !important;
}

/* Override any text color that might be applied */
.btn:not(.btn-light):not(.btn-warning),
.btn:not(.btn-light):not(.btn-warning) *,
button:not(.btn-light):not(.btn-warning),
button:not(.btn-light):not(.btn-warning) *,
input[type="button"]:not(.btn-light):not(.btn-warning),
input[type="submit"]:not(.btn-light):not(.btn-warning),
input[type="reset"]:not(.btn-light):not(.btn-warning) {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* Special handling for light and warning buttons */
.btn-light, .btn-light * {
    color: #333333 !important;
    font-weight: 700 !important;
}

.btn-warning, .btn-warning * {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* Override on hover states */
.btn:hover, .btn:hover *,
.btn:focus, .btn:focus *,
.btn:active, .btn:active *,
button:hover, button:hover *,
button:focus, button:focus *,
button:active, button:active * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* Light and warning button hover states */
.btn-light:hover, .btn-light:hover *,
.btn-warning:hover, .btn-warning:hover * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* Override any computed styles */
html .btn,
html .btn *,
html button,
html button *,
body .btn,
body .btn *,
body button,
body button * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

/* Exception for light buttons before hover */
html .btn-light,
html .btn-light *,
body .btn-light,
body .btn-light *,
html .btn-warning,
html .btn-warning *,
body .btn-warning,
body .btn-warning * {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* Nuclear option for button text */
[class*="btn"] {
    font-weight: 700 !important;
    color: #ffffff !important;
}

[class*="btn"] * {
    font-weight: 700 !important;
    color: #ffffff !important;
}

/* Exception for light variants */
[class*="btn-light"] {
    color: #333333 !important;
    font-weight: 700 !important;
}

[class*="btn-light"] * {
    color: #333333 !important;
    font-weight: 700 !important;
}

[class*="btn-warning"] {
    color: #333333 !important;
    font-weight: 700 !important;
}

[class*="btn-warning"] * {
    color: #333333 !important;
    font-weight: 700 !important;
}
