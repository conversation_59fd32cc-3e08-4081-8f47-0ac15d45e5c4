"""
Test script to verify session management functionality.
Run this with: python manage.py shell < test_session_management.py
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from django.test import RequestFactory
from django.utils import timezone
from accounts.unified_session_manager import UnifiedSessionManager

User = get_user_model()

def test_session_management():
    """Test the unified session management system."""
    print("Testing Unified Session Management System")
    print("=" * 50)
    
    # Create a test user if it doesn't exist
    try:
        user = User.objects.get(username='test_session_user')
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='test_session_user',
            email='<EMAIL>',
            password='testpass123'
        )
        print(f"Created test user: {user.username}")
    
    # Test getting user sessions
    print(f"\n1. Testing get_user_sessions for user: {user.username}")
    sessions = UnifiedSessionManager.get_user_sessions(user)
    print(f"   Found {len(sessions)} active sessions for user")
    
    # Test session cleanup
    print(f"\n2. Testing cleanup_user_cache")
    UnifiedSessionManager.cleanup_user_cache(user)
    print("   Cache cleanup completed")
    
    # Test expired session cleanup
    print(f"\n3. Testing cleanup_expired_sessions")
    cleaned_count = UnifiedSessionManager.cleanup_expired_sessions()
    print(f"   Cleaned up {cleaned_count} expired sessions")
    
    # Test logout all sessions
    print(f"\n4. Testing logout_all_sessions")
    terminated_count = UnifiedSessionManager.logout_all_sessions(user)
    print(f"   Terminated {terminated_count} sessions for user")
    
    # Verify sessions are cleaned up
    sessions_after = UnifiedSessionManager.get_user_sessions(user)
    print(f"   Sessions after cleanup: {len(sessions_after)}")
    
    print(f"\n5. Session Management Test Summary:")
    print(f"   - User sessions before cleanup: {len(sessions)}")
    print(f"   - User sessions after cleanup: {len(sessions_after)}")
    print(f"   - Expired sessions cleaned: {cleaned_count}")
    print(f"   - User sessions terminated: {terminated_count}")
    
    print("\nSession management test completed successfully!")

if __name__ == "__main__":
    test_session_management()
