"""
WSGI configuration for cPanel with Passenger
"""
import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

# Set the Django settings module to use production settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

# Import the WSGI application
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()
