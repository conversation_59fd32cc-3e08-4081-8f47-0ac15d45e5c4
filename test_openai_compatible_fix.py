#!/usr/bin/env python
"""
Test script to verify OpenAI compatible models are working properly after fixes.
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from assistants.models import Assistant
from accounts.models import Company, User
from django.core.exceptions import ValidationError

def test_openai_compatible_validation():
    """Test validation for OpenAI compatible models."""
    print("Testing OpenAI compatible model validation...")
    
    # Create a test company and user if they don't exist
    try:
        user = User.objects.get(username='test_user')
    except User.DoesNotExist:
        user = User.objects.create_user(
            username='test_user',
            email='<EMAIL>',
            password='testpass123'
        )
    
    try:
        company = Company.objects.get(name='Test Company')
    except Company.DoesNotExist:
        company = Company.objects.create(
            name='Test Company',
            owner=user
        )
    
    # Test 1: Create assistant with missing required fields
    print("\n1. Testing validation with missing fields...")
    assistant = Assistant(
        name='Test OpenAI Compatible Assistant',
        company=company,
        model='openai-compatible',
        # Missing api_key, base_url, custom_model_name
    )
    
    try:
        assistant.validate_openai_compatible_config()
        print("❌ Validation should have failed but didn't")
        return False
    except ValidationError as e:
        print(f"✅ Validation correctly failed: {e}")
    
    # Test 2: Create assistant with invalid URL
    print("\n2. Testing validation with invalid URL...")
    assistant.api_key = "test-key"
    assistant.base_url = "not-a-valid-url"
    assistant.custom_model_name = "test-model"
    
    try:
        assistant.validate_openai_compatible_config()
        print("❌ Validation should have failed for invalid URL but didn't")
        return False
    except ValidationError as e:
        print(f"✅ Validation correctly failed for invalid URL: {e}")
    
    # Test 3: Create assistant with valid configuration
    print("\n3. Testing validation with valid configuration...")
    assistant.base_url = "https://api.example.com/v1"
    
    try:
        assistant.validate_openai_compatible_config()
        print("✅ Validation passed for valid configuration")
    except ValidationError as e:
        print(f"❌ Validation failed unexpectedly: {e}")
        return False
    
    return True

def test_openai_compatible_functions():
    """Test the OpenAI compatible functions."""
    print("\nTesting OpenAI compatible functions...")
    
    # Test importing the functions
    try:
        from assistants.llm_utils import _generate_openai_compatible_response
        from assistants.llm_utils_optimized import _generate_openai_compatible_response_optimized
        print("✅ Successfully imported OpenAI compatible functions")
    except ImportError as e:
        print(f"❌ Failed to import functions: {e}")
        return False
    
    # Create a mock assistant for testing
    class MockAssistant:
        def __init__(self):
            self.api_key = "test-key"
            self.base_url = "https://api.example.com/v1"
            self.custom_model_name = "test-model"
    
    assistant = MockAssistant()
    messages = [{"role": "user", "content": "Hello"}]
    
    # Test validation in regular function
    print("\n1. Testing regular function validation...")
    try:
        _generate_openai_compatible_response(messages, assistant, 0.7, 100)
        print("❌ Function should have failed (no actual API) but didn't")
    except ValueError as e:
        if "Failed to connect" in str(e) or "Authentication failed" in str(e):
            print("✅ Function correctly handled connection/auth error")
        else:
            print(f"✅ Function correctly validated and failed: {e}")
    except Exception as e:
        print(f"✅ Function failed as expected (no real API): {e}")
    
    # Test validation in optimized function
    print("\n2. Testing optimized function validation...")
    try:
        _generate_openai_compatible_response_optimized(messages, assistant, 0.7, 100)
        print("❌ Function should have failed (no actual API) but didn't")
    except ValueError as e:
        if "Failed to connect" in str(e) or "Authentication failed" in str(e):
            print("✅ Optimized function correctly handled connection/auth error")
        else:
            print(f"✅ Optimized function correctly validated and failed: {e}")
    except Exception as e:
        print(f"✅ Optimized function failed as expected (no real API): {e}")
    
    # Test with missing fields
    print("\n3. Testing with missing API key...")
    assistant.api_key = ""
    
    try:
        _generate_openai_compatible_response(messages, assistant, 0.7, 100)
        print("❌ Function should have failed for missing API key")
        return False
    except ValueError as e:
        if "API key is required" in str(e):
            print("✅ Regular function correctly validated missing API key")
        else:
            print(f"❌ Unexpected error: {e}")
            return False
    
    try:
        _generate_openai_compatible_response_optimized(messages, assistant, 0.7, 100)
        print("❌ Optimized function should have failed for missing API key")
        return False
    except ValueError as e:
        if "API key is required" in str(e):
            print("✅ Optimized function correctly validated missing API key")
        else:
            print(f"❌ Unexpected error: {e}")
            return False
    
    return True

def main():
    """Run all tests."""
    print("🔧 Testing OpenAI Compatible Models Fix")
    print("=" * 50)
    
    results = []
    
    # Test validation
    results.append(test_openai_compatible_validation())
    
    # Test functions
    results.append(test_openai_compatible_functions())
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    if all(results):
        print("✅ All tests passed! OpenAI compatible models are working properly.")
        return True
    else:
        print("❌ Some tests failed. Please check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
