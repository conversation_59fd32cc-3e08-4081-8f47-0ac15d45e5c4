// Function to safely escape HTML
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&gt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// Function to add context links to message with enhanced styling
function addContextLinks(message, contexts) {
    let messageWithContext = message;

    if (contexts && contexts.length > 0) {
        // Add context links section with improved styling
        messageWithContext += `
            <div class="context-links">
                <div class="context-links-header">
                    <i class="bi bi-info-circle me-1"></i>
                    <small>Sources used:</small>
                </div>
                <div class="context-links-container">`;

        contexts.forEach((context, index) => {
            const contextData = {
                id: context.id,
                title: escapeHtml(context.title),
                created_by: escapeHtml(context.created_by),
                created_at: escapeHtml(context.created_at)
            };

            messageWithContext += `
                <a href="#"
                   class="context-link"
                   data-bs-toggle="modal"
                   data-bs-target="#contextModal"
                   data-context-id="${contextData.id}"
                   data-context-title="${contextData.title}"
                   data-context-created-by="${contextData.created_by}"
                   data-context-created-at="${contextData.created_at}">
                    <i class="bi bi-file-text me-1"></i>${contextData.title}
                </a>${index < contexts.length - 1 ? ' • ' : ''}`;
        });

        messageWithContext += `
                </div>
            </div>`;
    }

    return messageWithContext;
}

// Initialize context modal handler when document is ready
document.addEventListener('DOMContentLoaded', function() {
    const contextModal = document.getElementById('contextModal');
    if (contextModal) {
        contextModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const contextId = button.getAttribute('data-context-id');
            const title = button.getAttribute('data-context-title');
            const createdBy = button.getAttribute('data-context-created-by');
            const createdAt = button.getAttribute('data-context-created-at');

            showContext(button, contextId, title, createdBy, createdAt);
        });
    }
});

// Function to show context in modal with enhanced presentation
function showContext(button, contextId, title, createdBy, createdAt) {
    const contextModal = document.getElementById('contextModal');
    if (!contextModal) return;

    // Update modal title and metadata
    const modalTitle = contextModal.querySelector('.modal-title');
    const modalMetadata = contextModal.querySelector('.context-metadata');
    if (modalTitle) modalTitle.textContent = title;
    if (modalMetadata) {
        modalMetadata.innerHTML = `
            <small class="text-muted">
                <i class="bi bi-person me-1"></i>${createdBy} •
                <i class="bi bi-calendar me-1"></i>${createdAt}
            </small>`;
    }

    // Show loading state with improved styling
    const modalBody = contextModal.querySelector('.modal-body');
    if (modalBody) {
        modalBody.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-3">Loading content...</p>
            </div>`;
    }

    // Get the company and assistant IDs from meta tags
    const companyId = document.querySelector('meta[name="company-id"]').content;
    const assistantId = document.querySelector('meta[name="assistant-id"]').content;

    // Fetch context content
    fetch(`/assistant/company/${companyId}/assistants/${assistantId}/context/${contextId}/`, {
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.content) {
            // Update the modal body with the context content
            modalBody.innerHTML = `<div class="context-content">${data.content}</div>`;

            // Update usage and upvote counts
            const usageCount = contextModal.querySelector('#context-usage');
            const upvoteCount = contextModal.querySelector('#context-upvotes');
            const answerUpvoteCount = contextModal.querySelector('#context-answer-upvotes');
            const answerUpvoteBadge = contextModal.querySelector('#context-answer-upvotes-badge');

            if (usageCount) usageCount.textContent = data.times_used || 0;
            if (upvoteCount) upvoteCount.textContent = data.direct_upvote_count || 0;

            // Handle answer upvotes
            if (answerUpvoteCount && data.answer_upvote_count > 0) {
                answerUpvoteCount.textContent = data.answer_upvote_count;
                if (answerUpvoteBadge) answerUpvoteBadge.style.display = 'inline-flex';
            } else if (answerUpvoteBadge) {
                answerUpvoteBadge.style.display = 'none';
            }

            // Set up the upvote button
            const upvoteBtn = contextModal.querySelector('#upvote-context-btn');
            if (upvoteBtn) {
                upvoteBtn.setAttribute('data-context-id', contextId);
                upvoteBtn.setAttribute('data-url', data.upvote_url);

                // Update button appearance based on upvote status
                if (data.is_upvoted) {
                    upvoteBtn.classList.remove('btn-outline-success');
                    upvoteBtn.classList.add('btn-success');
                    upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up-fill me-1"></i> Upvoted';
                } else {
                    upvoteBtn.classList.remove('btn-success');
                    upvoteBtn.classList.add('btn-outline-success');
                    upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up me-1"></i> Upvote This Knowledge';
                }

                // Add click event listener for upvoting
                upvoteBtn.onclick = function() {
                    const url = this.getAttribute('data-url');
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                    fetch(url, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Update the upvote counts
                            if (upvoteCount) upvoteCount.textContent = data.upvote_count;

                            // If we have answer upvotes, make sure they're still displayed
                            const answerUpvoteCount = contextModal.querySelector('#context-answer-upvotes');
                            const answerUpvoteBadge = contextModal.querySelector('#context-answer-upvotes-badge');
                            if (answerUpvoteCount && answerUpvoteBadge && parseInt(answerUpvoteCount.textContent) > 0) {
                                answerUpvoteBadge.style.display = 'inline-flex';
                            }

                            // Update the button appearance
                            if (data.upvote_status === 'added') {
                                upvoteBtn.classList.remove('btn-outline-success');
                                upvoteBtn.classList.add('btn-success');
                                upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up-fill me-1"></i> Upvoted';
                            } else {
                                upvoteBtn.classList.remove('btn-success');
                                upvoteBtn.classList.add('btn-outline-success');
                                upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up me-1"></i> Upvote This Knowledge';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error upvoting context:', error);
                    });
                };
            }
        } else {
            modalBody.innerHTML = '<p class="text-danger">Error loading content</p>';
        }
    })
    .catch(error => {
        modalBody.innerHTML = '<p class="text-danger">Error loading content</p>';
        console.error('Error:', error);
    });
}

// Main chat functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get the initial display area
    const initialDisplayArea = document.getElementById('initial-display-area');

    // Initialize Bootstrap modal events
    const contextModal = document.getElementById('contextModal');
    if (contextModal) {
        contextModal.addEventListener('show.bs.modal', function(event) {
            showContext(event.target);
        });
    }

    const chatForm = document.getElementById('chat-form');
    const messageInput = document.getElementById('message-input');
    const chatBox = document.getElementById('chat-box');
    const sendButton = document.getElementById('send-button');
    const resetButton = document.getElementById('reset-chat-btn');

    // Get assistant ID and user info for session storage key
    const assistantId = document.querySelector('meta[name="assistant-id"]')?.content || 'default';
    const userId = document.querySelector('meta[name="user-id"]')?.content || 'anonymous';
    const sessionStorageKey = `chatHistory_${userId}_${assistantId}`;

    // Initialize message history from session storage or empty array
    let messageHistory = [];
    try {
        const storedHistory = sessionStorage.getItem(sessionStorageKey);
        if (storedHistory) {
            const parsedHistory = JSON.parse(storedHistory);

            // Validate that this history belongs to the current user
            const historyUserId = parsedHistory.userId || 'anonymous';
            if (historyUserId === userId) {
                // Clean the loaded history to ensure it only has supported properties
                messageHistory = (parsedHistory.messages || parsedHistory).map(msg => {
                    // Only include role and content properties
                    return {
                        role: msg.role,
                        content: msg.content,
                        timestamp: msg.timestamp || new Date().toISOString()
                    };
                });

                console.log('Loaded message history from session storage:', messageHistory);

                // If we have message history, hide the initial display area
                if (messageHistory.length > 0 && initialDisplayArea) {
                    initialDisplayArea.style.display = 'none';

                    // Restore the chat messages from history
                    messageHistory.forEach(msg => {
                        if (msg.role === 'user') {
                            addMessage(escapeHtml(msg.content), true);
                        } else if (msg.role === 'assistant') {
                            addMessage(msg.content, false);
                        }
                    });
                }
            } else {
                // Clear history if it belongs to a different user
                console.log('Clearing history for different user');
                sessionStorage.removeItem(sessionStorageKey);
                messageHistory = [];
            }
        }
    } catch (error) {
        console.error('Error loading message history from session storage:', error);
        // Clear potentially corrupted storage
        sessionStorage.removeItem(sessionStorageKey);
    }

    function addMessage(message, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;

        // Add avatar to the message
        let avatarHTML = '';
        if (isUser) {
            // User avatar - using a default icon since we don't have a user avatar image
            avatarHTML = '<i class="bi bi-person-circle rounded-circle me-2" style="font-size: 40px; color: #6c757d;"></i>';
        } else {
            // Get assistant avatar from the initial display area
            const assistantAvatar = document.querySelector('.assistant-profile-pic');
            if (assistantAvatar && assistantAvatar.src) {
                // Clone the avatar image with performance optimizations
                avatarHTML = `<img src="${assistantAvatar.src}" alt="Assistant" class="rounded-circle me-2" style="width: 40px; height: 40px;" loading="lazy" decoding="async">`;
            } else {
                // Fallback to default avatar icon
                avatarHTML = '<i class="bi bi-robot rounded-circle me-2" style="font-size: 40px; color: #6c757d;"></i>';
            }
        }

        // Wrap the message content in a span with tinymce-content class if it's not already wrapped
        if (!message.includes('class="message-content tinymce-content"') && !message.includes('class="tinymce-content message-content"')) {
            if (message.includes('class="message-content"')) {
                // Replace existing message-content class with both classes
                message = message.replace('class="message-content"', 'class="message-content tinymce-content"');
            } else {
                // Wrap the content in a span with both classes
                message = `<span class="message-content tinymce-content">${message}</span>`;
            }
        }

        // Add avatar and message content
        messageDiv.innerHTML = avatarHTML + message;

        // Add the message to the chat box
        chatBox.appendChild(messageDiv);
        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // Function to handle assistant messages (exported for external use)
    window.handleAssistantMessage = function(data) {
        try {
            // Check if there's an error in the response
            if (data.status === 'error') {
                // Display error message with reset button
                addMessage(`<div class="message-content tinymce-content text-danger">
                    <p><i class="bi bi-exclamation-triangle-fill me-2"></i>${data.error || 'An error occurred'}</p>
                    <p class="mt-2">Please try <button class="btn btn-sm btn-outline-primary" onclick="resetChat()">resetting the conversation</button> or try again later.</p>
                </div>`, false);

                // Clear message history to prevent further errors
                messageHistory = [];

                // Clear session storage
                try {
                    sessionStorage.removeItem(sessionStorageKey);
                    console.log('Cleared message history from session storage due to error');
                } catch (error) {
                    console.error('Error clearing message history from session storage:', error);
                }
                return;
            }

            // Process response content
            let processedContent = data.content;

            // Add context links if available
            if (data.used_contexts) {
                processedContent = addContextLinks(processedContent, data.used_contexts);
            }

            // Display the message
            addMessage(processedContent, false);
        } catch (error) {
            console.error('Error handling assistant message:', error);
            addMessage(`<div class="message-content tinymce-content text-danger">
                <p><i class="bi bi-exclamation-triangle-fill me-2"></i>Error displaying message</p>
                <p class="mt-2">Please try <button class="btn btn-sm btn-outline-primary" onclick="resetChat()">resetting the conversation</button> or try again later.</p>
            </div>`, false);

            // Clear message history to prevent further errors
            messageHistory = [];

            // Clear session storage
            try {
                sessionStorage.removeItem(sessionStorageKey);
                console.log('Cleared message history from session storage due to error');
            } catch (storageError) {
                console.error('Error clearing message history from session storage:', storageError);
            }
        }
    };

    function sendMessage(message) {
        // Hide the initial display area when user sends a message
        const initialDisplayArea = document.getElementById('initial-display-area');
        if (initialDisplayArea) {
            // Force hide with !important to override any other styles
            initialDisplayArea.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important;';

            // Also hide all children
            const children = initialDisplayArea.querySelectorAll('*');
            children.forEach(child => {
                child.style.cssText = 'display: none !important; visibility: hidden !important; opacity: 0 !important;';
            });

            // Remove from DOM flow
            initialDisplayArea.setAttribute('aria-hidden', 'true');

            console.log('Message sent: Initial display area hidden');
        }

        // Show user message
        addMessage(escapeHtml(message), true);

        // Clear input
        messageInput.value = '';

        // Add enhanced thinking indicator
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'message assistant-message thinking';
        loadingDiv.innerHTML = `
            <div class="thinking-indicator">
                <span class="thinking-text">Thinking</span>
                <div class="thinking-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        chatBox.appendChild(loadingDiv);
        chatBox.scrollTop = chatBox.scrollHeight;

        // Save the user message to history immediately - only include supported properties
        messageHistory.push({
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
        });

        // Clean message history to ensure compatibility with all APIs
        const cleanedHistory = messageHistory.map(msg => {
            // Only include role and content properties
            return {
                role: msg.role,
                content: msg.content,
                timestamp: msg.timestamp || new Date().toISOString()
            };
        });

        // Save updated history to session storage with user validation
        try {
            const historyData = {
                userId: userId,
                assistantId: assistantId,
                messages: cleanedHistory,
                lastUpdated: new Date().toISOString()
            };
            sessionStorage.setItem(sessionStorageKey, JSON.stringify(historyData));
        } catch (error) {
            console.error('Error saving message history to session storage:', error);
        }

        // Update the messageHistory with the cleaned version
        messageHistory = cleanedHistory;

        // Get path components from current URL
        const pathParts = window.location.pathname.split('/');
        let companyId, assistantId, slug, useSlugUrl = false;

        if (pathParts.includes('company')) {
            // Format: /company/{company_id}/assistants/{assistant_id}/chat/
            companyId = pathParts[pathParts.indexOf('company') + 1];
            assistantId = pathParts[pathParts.indexOf('assistants') + 1];
            console.log('DEBUG: Using company/assistant ID URL format');
        } else if (pathParts.includes('assistant')) {
            // Format: /assistant/{slug}/chat/
            // Need to get IDs from the page for API calls
            companyId = document.querySelector('meta[name="company-id"]').content;
            assistantId = document.querySelector('meta[name="assistant-id"]').content;

            // Get the slug from the URL
            const assistantIndex = pathParts.indexOf('assistant');
            if (assistantIndex >= 0 && assistantIndex + 1 < pathParts.length) {
                slug = pathParts[assistantIndex + 1];
                useSlugUrl = true;
                console.log(`DEBUG: Using slug URL format, slug=${slug}`);
            }
        }

        // Prepare request data
        const data = {
            message: message,
            history: messageHistory,
            use_community_context: true // Always use community context for context links feature
        };

        // Determine which URL to use
        let apiUrl;
        if (useSlugUrl && slug) {
            apiUrl = `/assistant/interact/${slug}/`;
            console.log('DEBUG: Using slug-based URL for interaction:', apiUrl);
        } else {
            apiUrl = `/assistant/company/${companyId}/assistants/${assistantId}/interact/`;
            console.log('DEBUG: Using ID-based URL for interaction:', apiUrl);
        }

        // Log the current URL format for debugging
        console.log('DEBUG: Current page URL:', window.location.pathname);

        // Send message to server
        console.log('DEBUG: Sending message to server', {
            url: apiUrl,
            data: data,
            companyId: companyId,
            assistantId: assistantId,
            slug: slug,
            useSlugUrl: useSlugUrl
        });

        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            console.log('DEBUG: Response status:', response.status);
            if (!response.ok) {
                console.error('DEBUG: Response not OK:', response);
                return response.text().then(text => {
                    console.error('DEBUG: Error response text:', text);
                    // Handle 500 error more gracefully
                    if (response.status === 500) {
                        return {
                            status: 'error',
                            error: 'Server error occurred. Please try resetting the conversation.',
                            content: 'Sorry, I encountered an error. Please try resetting the conversation or try again later.'
                        };
                    }
                    throw new Error(`Server error: ${response.status} - ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            // Remove loading indicator
            loadingDiv.remove();

            console.log('DEBUG: Received response data:', data);

            if (data.status === 'error') {
                throw new Error(data.error || 'Error processing message');
            }

            // Add only the assistant response to history (user message was already added)
            // Only include role and content properties for compatibility with all APIs
            messageHistory.push({
                role: 'assistant',
                content: data.content,
                timestamp: new Date().toISOString()
            });

            // Clean message history to ensure compatibility with all APIs
            const cleanedHistory = messageHistory.map(msg => {
                // Only include role and content properties
                return {
                    role: msg.role,
                    content: msg.content,
                    timestamp: msg.timestamp || new Date().toISOString()
                };
            });

            // Update the messageHistory with the cleaned version
            messageHistory = cleanedHistory;

            // Save updated history to session storage with user validation
            try {
                const historyData = {
                    userId: userId,
                    assistantId: assistantId,
                    messages: cleanedHistory,
                    lastUpdated: new Date().toISOString()
                };
                sessionStorage.setItem(sessionStorageKey, JSON.stringify(historyData));
                console.log('Saved message history to session storage:', historyData);
            } catch (error) {
                console.error('Error saving message history to session storage:', error);
            }

            // Use handleAssistantMessage to process and display the response
            handleAssistantMessage(data);
        })
        .catch(error => {
            loadingDiv.remove();
            const errorMessage = error.message || 'Error sending message';
            addMessage(`<div class="message-content tinymce-content text-danger">
                <p><i class="bi bi-exclamation-triangle-fill me-2"></i>${errorMessage}</p>
                <p class="mt-2">Please try <button class="btn btn-sm btn-outline-primary" onclick="resetChat()">resetting the conversation</button> or try again later.</p>
            </div>`, false);
            console.error('DEBUG: Error in fetch:', error);

            // Clear message history to prevent further errors
            messageHistory = [];

            // Clear session storage
            try {
                sessionStorage.removeItem(sessionStorageKey);
                console.log('Cleared message history from session storage due to error');
            } catch (storageError) {
                console.error('Error clearing message history from session storage:', storageError);
            }
        });
    }

    // Form submit handler
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const message = messageInput.value.trim();
        if (message) {
            sendMessage(message);
        }
    });

    // Send button click handler
    sendButton.addEventListener('click', function() {
        const message = messageInput.value.trim();
        if (message) {
            sendMessage(message);
        }
    });

    // Reset button handler
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            // Use the global resetChat function
            resetChat();
        });
    }

    // Enter key handler
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const message = messageInput.value.trim();
            if (message) {
                sendMessage(message);
            }
        }
    });
});

// Export showContext for use in other scripts
window.showContext = showContext;

// Reset chat function - exported to global scope for error handling
window.resetChat = function() {
    // Clear message history from session storage
    const assistantId = document.querySelector('meta[name="assistant-id"]')?.content || 'default';
    const userId = document.querySelector('meta[name="user-id"]')?.content || 'anonymous';
    const sessionStorageKey = `chatHistory_${userId}_${assistantId}`;
    sessionStorage.removeItem(sessionStorageKey);

    // Clear all messages from the chat box
    const chatBox = document.getElementById('chat-box');
    if (chatBox) {
        // Remove all message elements except the initial greeting
        const messages = chatBox.querySelectorAll('.message:not(.initial-greeting)');
        messages.forEach(message => message.remove());

        // Show the welcome message if it exists
        if (typeof window.showWelcomeMessage === 'function') {
            window.showWelcomeMessage();
        } else {
            // Fallback if the function isn't available
            const initialDisplayArea = document.getElementById('initial-display-area');
            if (initialDisplayArea) {
                initialDisplayArea.style.cssText = 'display: block !important; visibility: visible !important; opacity: 1 !important; height: auto !important; overflow: visible !important; margin-bottom: 1rem !important; padding: 0 !important;';
            }
        }
    } else {
        // If we can't find the chat box, just reload the page
        window.location.reload();
    }

    // Clear the input field
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.value = '';
    }

    console.log('Chat reset: History cleared and welcome message restored');
};
