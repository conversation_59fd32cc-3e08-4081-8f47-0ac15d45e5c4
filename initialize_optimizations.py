#!/usr/bin/env python
"""
Initialize all performance optimizations for the Django AI assistant platform.
This script sets up advanced data structures, caching, async processing, and more.
"""

import os
import sys
import django
import logging
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Set up Django environment with performance optimizations
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
os.environ['PRODUCTION'] = 'True'  # Enable production optimizations
django.setup()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OptimizationInitializer:
    """Initializes all performance optimizations."""

    def __init__(self):
        self.initialized_components = []
        self.errors = []

    def initialize_all(self):
        """Initialize all optimization components."""
        logger.info("Starting performance optimization initialization...")

        # Initialize components in order of dependency
        self.initialize_advanced_data_structures()
        self.initialize_advanced_caching()
        self.initialize_memory_management()
        self.initialize_async_processing()
        self.initialize_database_optimizations()
        self.initialize_llm_optimizations()

        # Report results
        self.report_initialization_results()

    def initialize_advanced_data_structures(self):
        """Initialize advanced data structures."""
        try:
            logger.info("Initializing advanced data structures...")

            from assistants.advanced_data_structures import (
                initialize_data_structures,
                assistant_search_trie,
                response_cache,
                query_bloom_filter
            )

            # Initialize with existing data
            initialize_data_structures()

            # Test the structures
            stats = {
                'trie_size': assistant_search_trie.size,
                'cache_stats': response_cache.get_stats(),
                'bloom_filter_items': query_bloom_filter.item_count
            }

            logger.info(f"Advanced data structures initialized: {stats}")
            self.initialized_components.append("Advanced Data Structures")

        except Exception as e:
            error_msg = f"Failed to initialize advanced data structures: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)

    def initialize_advanced_caching(self):
        """Initialize advanced multi-level caching."""
        try:
            logger.info("Initializing advanced caching system...")

            from assistants.advanced_caching import (
                multi_level_cache,
                cache_warmer,
                warm_cache_async
            )

            # Test cache functionality
            test_key = "test_cache_key"
            test_value = {"test": "data", "timestamp": "2024"}

            multi_level_cache.set(test_key, test_value, ttl=60)
            retrieved_value = multi_level_cache.get(test_key)

            if retrieved_value == test_value:
                logger.info("Advanced caching system working correctly")

                # Start cache warming
                warm_cache_async()

                cache_stats = multi_level_cache.get_stats()
                logger.info(f"Cache statistics: {cache_stats}")

                self.initialized_components.append("Advanced Caching")
            else:
                raise Exception("Cache test failed")

        except Exception as e:
            error_msg = f"Failed to initialize advanced caching: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)

    def initialize_memory_management(self):
        """Initialize memory management system."""
        try:
            logger.info("Initializing memory management...")

            from assistants.memory_manager import (
                start_memory_monitoring,
                get_memory_stats,
                gc_optimizer
            )

            # Start memory monitoring
            start_memory_monitoring()

            # Optimize garbage collection
            gc_optimizer.optimize_gc()

            # Get initial memory stats
            memory_stats = get_memory_stats()
            logger.info(f"Memory management initialized. Stats: {memory_stats}")

            self.initialized_components.append("Memory Management")

        except Exception as e:
            error_msg = f"Failed to initialize memory management: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)

    def initialize_async_processing(self):
        """Initialize asynchronous processing system."""
        try:
            logger.info("Initializing async processing...")

            from assistants.async_processors import (
                start_async_processing,
                get_async_stats,
                event_bus
            )

            # Start async processing
            start_async_processing()

            # Test event bus
            test_events_received = []

            def test_handler(data):
                test_events_received.append(data)

            event_bus.subscribe("test_event", test_handler)
            event_bus.publish("test_event", {"test": "data"})

            # Give it a moment to process
            import time
            time.sleep(0.1)

            if test_events_received:
                logger.info("Event bus working correctly")

            async_stats = get_async_stats()
            logger.info(f"Async processing initialized. Stats: {async_stats}")

            self.initialized_components.append("Async Processing")

        except Exception as e:
            error_msg = f"Failed to initialize async processing: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)

    def initialize_database_optimizations(self):
        """Initialize database optimizations."""
        try:
            logger.info("Initializing database optimizations...")

            from assistants.advanced_queries import (
                create_assistant_stats_view,
                get_performance_stats
            )

            # Create materialized views
            try:
                create_assistant_stats_view()
                logger.info("Materialized views created successfully")
            except Exception as e:
                logger.warning(f"Could not create materialized views: {e}")

            # Get performance stats
            perf_stats = get_performance_stats()
            logger.info(f"Database optimization stats: {perf_stats}")

            self.initialized_components.append("Database Optimizations")

        except Exception as e:
            error_msg = f"Failed to initialize database optimizations: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)

    def initialize_llm_optimizations(self):
        """Initialize LLM optimizations."""
        try:
            logger.info("Initializing LLM optimizations...")

            from assistants.llm_utils_advanced import (
                streaming_processor,
                parallel_processor,
                batch_processor
            )

            # Test components are working
            logger.info("LLM optimization components loaded successfully")

            # Get processor stats
            processor_stats = parallel_processor.get_stats()
            logger.info(f"LLM processor stats: {processor_stats}")

            self.initialized_components.append("LLM Optimizations")

        except Exception as e:
            error_msg = f"Failed to initialize LLM optimizations: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)

    def report_initialization_results(self):
        """Report the results of initialization."""
        logger.info("=" * 60)
        logger.info("PERFORMANCE OPTIMIZATION INITIALIZATION COMPLETE")
        logger.info("=" * 60)

        logger.info(f"Successfully initialized {len(self.initialized_components)} components:")
        for component in self.initialized_components:
            logger.info(f"  ✅ {component}")

        if self.errors:
            logger.warning(f"Encountered {len(self.errors)} errors:")
            for error in self.errors:
                logger.warning(f"  ❌ {error}")

        logger.info("=" * 60)

        # Performance recommendations
        self.show_performance_recommendations()

    def show_performance_recommendations(self):
        """Show performance optimization recommendations."""
        logger.info("PERFORMANCE RECOMMENDATIONS:")
        logger.info("1. Monitor cache hit rates in logs/performance.log")
        logger.info("2. Check memory usage regularly with get_memory_stats()")
        logger.info("3. Use async processing for heavy operations")
        logger.info("4. Monitor database query performance")
        logger.info("5. Enable Django Debug Toolbar in development to verify optimizations")
        logger.info("6. Run load tests to measure performance improvements")

        # Show next steps
        logger.info("\nNEXT STEPS:")
        logger.info("1. Update your Django settings to use 'company_assistant.performance_settings'")
        logger.info("2. Run migrations: python manage.py migrate")
        logger.info("3. Test the application with the optimizations enabled")
        logger.info("4. Monitor performance metrics and adjust settings as needed")


def main():
    """Main function to run the initialization."""
    try:
        initializer = OptimizationInitializer()
        initializer.initialize_all()

        # Create a status file to indicate successful initialization
        status_file = project_dir / "optimization_status.txt"
        with open(status_file, 'w') as f:
            f.write("Performance optimizations initialized successfully\n")
            f.write(f"Components: {', '.join(initializer.initialized_components)}\n")
            f.write(f"Errors: {len(initializer.errors)}\n")

        logger.info(f"Status written to {status_file}")

        return len(initializer.errors) == 0

    except Exception as e:
        logger.error(f"Critical error during initialization: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
