/**
 * Blue to NUP Color Override
 * Changes all blue buttons and elements to NUP party red color (#cf2e2e)
 */

/* ===== PRIMARY BUTTONS ===== */
.btn-primary,
.btn-primary:not(:disabled):not(.disabled),
.btn-primary.active,
.btn-primary:active,
.show > .btn-primary.dropdown-toggle {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.2) !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary.focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-primary.dropdown-toggle:focus {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== INFO BUTTONS ===== */
.btn-info,
.btn-info:not(:disabled):not(.disabled),
.btn-info.active,
.btn-info:active,
.show > .btn-info.dropdown-toggle {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 700 !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.2) !important;
}

.btn-info:hover,
.btn-info:focus,
.btn-info.focus,
.btn-info:not(:disabled):not(.disabled):active:focus,
.btn-info:not(:disabled):not(.disabled).active:focus,
.show > .btn-info.dropdown-toggle:focus {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== PRIMARY BACKGROUNDS ===== */
.bg-primary {
    background-color: #cf2e2e !important;
}

.text-bg-primary {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== INFO BACKGROUNDS ===== */
.bg-info {
    background-color: #cf2e2e !important;
}

.text-bg-info {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== PRIMARY TEXT ===== */
.text-primary {
    color: #cf2e2e !important;
}

.link-primary {
    color: #cf2e2e !important;
}

.link-primary:hover,
.link-primary:focus {
    color: #b82626 !important;
}

/* ===== INFO TEXT ===== */
.text-info {
    color: #cf2e2e !important;
}

.link-info {
    color: #cf2e2e !important;
}

.link-info:hover,
.link-info:focus {
    color: #b82626 !important;
}

/* ===== PRIMARY BADGES ===== */
.badge.bg-primary,
.badge.bg-primary.tag-badge,
span.badge.bg-primary {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.3) !important;
    border: none !important;
    letter-spacing: 0.02em !important;
}

/* ===== INFO BADGES ===== */
.badge.bg-info,
.badge.bg-info.tag-badge,
span.badge.bg-info {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important;
    box-shadow: 0 2px 4px rgba(207, 46, 46, 0.3) !important;
    border: none !important;
    letter-spacing: 0.02em !important;
}

/* ===== PRIMARY ALERTS ===== */
.alert-primary {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    color: #cf2e2e !important;
}

.alert-primary .alert-link {
    color: #b82626 !important;
}

/* ===== INFO ALERTS ===== */
.alert-info {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    color: #cf2e2e !important;
}

.alert-info .alert-link {
    color: #b82626 !important;
}

/* ===== PRIMARY BORDERS ===== */
.border-primary {
    border-color: #cf2e2e !important;
}

/* ===== INFO BORDERS ===== */
.border-info {
    border-color: #cf2e2e !important;
}

/* ===== PRIMARY OUTLINE BUTTONS ===== */
.btn-outline-primary {
    color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    background-color: transparent !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary.focus,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== INFO OUTLINE BUTTONS ===== */
.btn-outline-info {
    color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    background-color: transparent !important;
}

.btn-outline-info:hover,
.btn-outline-info:focus,
.btn-outline-info.focus,
.btn-outline-info:not(:disabled):not(.disabled):active,
.btn-outline-info:not(:disabled):not(.disabled).active,
.show > .btn-outline-info.dropdown-toggle {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== PROGRESS BARS ===== */
.progress-bar.bg-primary {
    background-color: #cf2e2e !important;
}

.progress-bar.bg-info {
    background-color: #cf2e2e !important;
}

/* ===== LIST GROUP ITEMS ===== */
.list-group-item-primary {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    color: #cf2e2e !important;
}

.list-group-item-primary.list-group-item-action:hover,
.list-group-item-primary.list-group-item-action:focus {
    background-color: rgba(207, 46, 46, 0.15) !important;
    color: #cf2e2e !important;
}

.list-group-item-info {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
    color: #cf2e2e !important;
}

.list-group-item-info.list-group-item-action:hover,
.list-group-item-info.list-group-item-action:focus {
    background-color: rgba(207, 46, 46, 0.15) !important;
    color: #cf2e2e !important;
}

/* ===== TABLE VARIANTS ===== */
.table-primary {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
}

.table-info {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: rgba(207, 46, 46, 0.2) !important;
}

/* ===== SPECIFIC BLUE ELEMENTS ===== */
/* Override any hardcoded blue colors */
[style*="background-color: #007bff"],
[style*="background-color: #0d6efd"],
[style*="background-color: blue"],
[style*="background: #007bff"],
[style*="background: #0d6efd"],
[style*="background: blue"] {
    background-color: #cf2e2e !important;
    background: #cf2e2e !important;
}

[style*="color: #007bff"],
[style*="color: #0d6efd"],
[style*="color: blue"] {
    color: #cf2e2e !important;
}

[style*="border-color: #007bff"],
[style*="border-color: #0d6efd"],
[style*="border-color: blue"] {
    border-color: #cf2e2e !important;
}

/* ===== CSS VARIABLES OVERRIDE ===== */
:root {
    --bs-primary: #cf2e2e !important;
    --bs-primary-rgb: 207, 46, 46 !important;
    --bs-info: #cf2e2e !important;
    --bs-info-rgb: 207, 46, 46 !important;
    --primary: #cf2e2e !important;
    --info: #cf2e2e !important;
}
