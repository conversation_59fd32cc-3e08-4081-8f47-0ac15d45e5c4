# 🚀 cPanel Deployment Checklist for 24seven.site

## ✅ **Pre-Deployment Updates Completed**

The following updates have been automatically applied to your project:

### **Files Updated:**
- ✅ `passenger_wsgi.py` - Configured for cPanel production
- ✅ `company_assistant/settings.py` - Updated ALLOWED_HOSTS and CSRF_TRUSTED_ORIGINS
- ✅ `accounts/email_utils.py` - Production domain fallback configured
- ✅ Django Site framework - Set to 24seven.site
- ✅ `.env.cpanel` - Created with production environment variables

### **Configuration Applied:**
- ✅ Domain: 24seven.site
- ✅ ALLOWED_HOSTS: Updated with production domain
- ✅ CSRF_TRUSTED_ORIGINS: Updated with HTTPS and HTTP variants
- ✅ Environment detection: Configured for cPanel
- ✅ Email utilities: Production domain ready

---

## 📤 **cPanel Deployment Steps**

### **STEP 1: Upload Files**
Upload these files to your cPanel hosting account:

**Required Files:**
- `passenger_wsgi.py` (root directory)
- `.htaccess` (root directory)
- All Django project files
- `requirements.txt`

**File Structure on cPanel:**
```
/public_html/
├── passenger_wsgi.py
├── .htaccess
├── company_assistant/
├── accounts/
├── assistants/
├── content/
├── directory/
├── site_settings/
├── superadmin/
├── templates/
├── static/
├── media/
└── manage.py
```

### **STEP 2: Set Environment Variables in cPanel**

In your cPanel Python App settings, add these environment variables:

**Required Environment Variables:**
```
CPANEL_ENV=True
PRODUCTION=True
DEBUG=False
SECRET_KEY=your-actual-secret-key-here
DB_NAME=your-actual-database-name
DB_USER=your-actual-database-user
DB_PASSWORD=your-actual-database-password
DB_HOST=localhost
DB_PORT=5432
EMAIL_HOST=mail.24seven.site
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-actual-email-password
```

### **STEP 3: Database Setup**

1. **Create PostgreSQL Database in cPanel:**
   - Go to cPanel → PostgreSQL Databases
   - Create database: `your_database_name`
   - Create user: `your_database_user`
   - Assign user to database with ALL PRIVILEGES

2. **Update Environment Variables:**
   - Set DB_NAME to your actual database name
   - Set DB_USER to your actual database user
   - Set DB_PASSWORD to your actual database password

### **STEP 4: Install Dependencies**

In cPanel terminal or SSH:
```bash
pip install -r requirements.txt
```

### **STEP 5: Run Django Setup Commands**

```bash
# Run database migrations
python manage.py migrate

# Create Django Site table if needed
python manage.py shell -c "from django.contrib.sites.models import Site; Site.objects.get_or_create(pk=1, defaults={'domain': '24seven.site', 'name': '24seven Platform'})"

# Collect static files
python manage.py collectstatic --noinput

# Create superuser
python manage.py createsuperuser
```

### **STEP 6: Configure Email Settings**

Update these in your cPanel environment variables:
```
EMAIL_HOST=mail.24seven.site
EMAIL_PORT=465
EMAIL_USE_SSL=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-actual-email-password
DEFAULT_FROM_EMAIL=24seven Platform <<EMAIL>>
```

### **STEP 7: Test Your Application**

1. **Visit your website:** https://24seven.site
2. **Test admin panel:** https://24seven.site/admin/
3. **Test email functionality:** Try password reset or sign-in approval
4. **Check error logs** in cPanel if issues occur

---

## 🔧 **Troubleshooting**

### **Common Issues:**

**1. Application Not Loading:**
- Check cPanel error logs
- Verify passenger_wsgi.py is in root directory
- Ensure all environment variables are set
- Check file permissions (755 for directories, 644 for files)

**2. Database Connection Errors:**
- Verify database credentials in environment variables
- Ensure database user has proper privileges
- Check if database exists

**3. Static Files Not Loading:**
- Run `python manage.py collectstatic --noinput`
- Check .htaccess file is uploaded
- Verify static file paths in settings

**4. Email Not Working:**
- Verify email settings in environment variables
- Test email configuration in cPanel
- Check if email account exists

**5. 500 Internal Server Error:**
- Check cPanel error logs
- Verify all dependencies are installed
- Ensure DEBUG=False in production
- Check file permissions

### **Log Locations:**
- cPanel Error Logs: `~/logs/error_log`
- Application Logs: Check cPanel Python App logs
- Django Logs: Configured in settings.py

---

## 🎉 **Post-Deployment Verification**

After deployment, verify these features work:

- [ ] Homepage loads correctly
- [ ] Admin panel accessible
- [ ] User registration/login
- [ ] Email sending (password reset, sign-in approval)
- [ ] File uploads (if applicable)
- [ ] All main application features

---

## 📞 **Support**

If you encounter issues:

1. Check the troubleshooting section above
2. Review cPanel error logs
3. Verify all environment variables are set correctly
4. Ensure all files are uploaded properly

**Your project is now ready for cPanel deployment! 🚀**
