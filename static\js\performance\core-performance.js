/**
 * Core Performance Utilities
 * Advanced frontend optimization system for maximum performance
 */

class PerformanceManager {
    constructor() {
        this.metrics = new Map();
        this.observers = new Map();
        this.cache = new Map();
        this.config = {
            enableLazyLoading: true,
            enableServiceWorker: true,
            enableVirtualScrolling: true,
            enableImageOptimization: true,
            enableCriticalCSS: true,
            cacheStrategy: 'aggressive',
            performanceThreshold: {
                fcp: 1500,
                lcp: 2500,
                fid: 100,
                cls: 0.1
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupPerformanceObserver();
        this.setupIntersectionObserver();
        this.setupMutationObserver();
        this.preloadCriticalResources();
        this.initServiceWorker();
        this.optimizeImages();
        this.setupVirtualScrolling();
        
        console.log('🚀 Performance Manager initialized');
    }
    
    // Performance Monitoring
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // Core Web Vitals
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordMetric(entry.name, entry.value, entry.entryType);
                    
                    // Alert on poor performance
                    if (this.isPerformancePoor(entry)) {
                        this.handlePoorPerformance(entry);
                    }
                }
            });
            
            observer.observe({ entryTypes: ['measure', 'navigation', 'paint', 'largest-contentful-paint'] });
            this.observers.set('performance', observer);
        }
    }
    
    recordMetric(name, value, type) {
        const timestamp = Date.now();
        const metric = { name, value, type, timestamp };
        
        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }
        
        this.metrics.get(name).push(metric);
        
        // Keep only last 100 metrics per type
        if (this.metrics.get(name).length > 100) {
            this.metrics.get(name).shift();
        }
        
        // Send to analytics if configured
        this.sendToAnalytics(metric);
    }
    
    isPerformancePoor(entry) {
        const thresholds = this.config.performanceThreshold;
        
        switch (entry.name) {
            case 'first-contentful-paint':
                return entry.value > thresholds.fcp;
            case 'largest-contentful-paint':
                return entry.value > thresholds.lcp;
            case 'first-input-delay':
                return entry.value > thresholds.fid;
            case 'cumulative-layout-shift':
                return entry.value > thresholds.cls;
            default:
                return false;
        }
    }
    
    handlePoorPerformance(entry) {
        console.warn(`⚠️ Poor performance detected: ${entry.name} = ${entry.value}ms`);
        
        // Trigger optimization strategies
        this.optimizeForPoorPerformance(entry.name);
    }
    
    optimizeForPoorPerformance(metricName) {
        switch (metricName) {
            case 'first-contentful-paint':
                this.optimizeFCP();
                break;
            case 'largest-contentful-paint':
                this.optimizeLCP();
                break;
            case 'first-input-delay':
                this.optimizeFID();
                break;
            case 'cumulative-layout-shift':
                this.optimizeCLS();
                break;
        }
    }
    
    // Lazy Loading System
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadElement(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });
            
            this.observers.set('intersection', observer);
            this.observeLazyElements();
        }
    }
    
    observeLazyElements() {
        const lazyElements = document.querySelectorAll('[data-lazy]');
        const observer = this.observers.get('intersection');
        
        lazyElements.forEach(element => {
            observer.observe(element);
        });
    }
    
    loadElement(element) {
        const src = element.dataset.src;
        const type = element.dataset.lazy;
        
        switch (type) {
            case 'image':
                this.loadLazyImage(element, src);
                break;
            case 'component':
                this.loadLazyComponent(element, src);
                break;
            case 'iframe':
                this.loadLazyIframe(element, src);
                break;
            default:
                element.src = src;
        }
        
        element.removeAttribute('data-lazy');
        element.classList.add('loaded');
    }
    
    loadLazyImage(img, src) {
        // Create a new image to preload
        const newImg = new Image();
        
        newImg.onload = () => {
            img.src = src;
            img.classList.add('fade-in');
        };
        
        newImg.onerror = () => {
            img.src = '/static/images/placeholder.webp';
            img.classList.add('error');
        };
        
        newImg.src = src;
    }
    
    async loadLazyComponent(element, componentPath) {
        try {
            const module = await import(componentPath);
            const component = module.default || module;
            
            if (typeof component === 'function') {
                const result = component(element);
                if (result instanceof Promise) {
                    await result;
                }
            }
            
            element.classList.add('component-loaded');
        } catch (error) {
            console.error(`Failed to load component: ${componentPath}`, error);
            element.classList.add('component-error');
        }
    }
    
    loadLazyIframe(iframe, src) {
        iframe.src = src;
        iframe.onload = () => {
            iframe.classList.add('iframe-loaded');
        };
    }
    
    // Image Optimization
    optimizeImages() {
        const images = document.querySelectorAll('img[data-optimize]');
        
        images.forEach(img => {
            this.optimizeImage(img);
        });
    }
    
    optimizeImage(img) {
        const originalSrc = img.src || img.dataset.src;
        const optimizedSrc = this.getOptimizedImageSrc(originalSrc, img);
        
        // Use WebP if supported
        if (this.supportsWebP()) {
            img.src = optimizedSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp');
        } else {
            img.src = optimizedSrc;
        }
        
        // Add responsive attributes
        this.addResponsiveAttributes(img);
    }
    
    getOptimizedImageSrc(src, img) {
        const width = img.offsetWidth || img.dataset.width;
        const height = img.offsetHeight || img.dataset.height;
        const quality = img.dataset.quality || 85;
        
        // Add optimization parameters
        const url = new URL(src, window.location.origin);
        url.searchParams.set('w', width);
        url.searchParams.set('h', height);
        url.searchParams.set('q', quality);
        url.searchParams.set('auto', 'format,compress');
        
        return url.toString();
    }
    
    supportsWebP() {
        if (this.cache.has('webp-support')) {
            return this.cache.get('webp-support');
        }
        
        const canvas = document.createElement('canvas');
        canvas.width = 1;
        canvas.height = 1;
        const support = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        
        this.cache.set('webp-support', support);
        return support;
    }
    
    addResponsiveAttributes(img) {
        if (!img.hasAttribute('loading')) {
            img.setAttribute('loading', 'lazy');
        }
        
        if (!img.hasAttribute('decoding')) {
            img.setAttribute('decoding', 'async');
        }
    }
    
    // Service Worker
    async initServiceWorker() {
        if ('serviceWorker' in navigator && this.config.enableServiceWorker) {
            try {
                const registration = await navigator.serviceWorker.register('/static/js/sw.js');
                console.log('✅ Service Worker registered:', registration);
                
                // Listen for updates
                registration.addEventListener('updatefound', () => {
                    console.log('🔄 Service Worker update found');
                });
                
            } catch (error) {
                console.error('❌ Service Worker registration failed:', error);
            }
        }
    }
    
    // Virtual Scrolling
    setupVirtualScrolling() {
        const virtualLists = document.querySelectorAll('[data-virtual-scroll]');
        
        virtualLists.forEach(list => {
            this.initVirtualScrolling(list);
        });
    }
    
    initVirtualScrolling(container) {
        const itemHeight = parseInt(container.dataset.itemHeight) || 50;
        const bufferSize = parseInt(container.dataset.bufferSize) || 5;
        const totalItems = parseInt(container.dataset.totalItems) || 0;
        
        const virtualScroller = new VirtualScroller(container, {
            itemHeight,
            bufferSize,
            totalItems,
            renderItem: (index) => this.renderVirtualItem(container, index)
        });
        
        container.virtualScroller = virtualScroller;
    }
    
    renderVirtualItem(container, index) {
        const template = container.querySelector('[data-item-template]');
        if (!template) return null;
        
        const item = template.cloneNode(true);
        item.removeAttribute('data-item-template');
        item.style.display = 'block';
        
        // Populate item data
        const dataSource = container.dataset.dataSource;
        if (dataSource && window[dataSource]) {
            const data = window[dataSource][index];
            this.populateItemData(item, data);
        }
        
        return item;
    }
    
    populateItemData(item, data) {
        const bindings = item.querySelectorAll('[data-bind]');
        
        bindings.forEach(element => {
            const property = element.dataset.bind;
            if (data && data[property] !== undefined) {
                if (element.tagName === 'IMG') {
                    element.src = data[property];
                } else {
                    element.textContent = data[property];
                }
            }
        });
    }
    
    // Critical Resource Preloading
    preloadCriticalResources() {
        const criticalResources = [
            { href: '/static/css/critical.css', as: 'style' },
            { href: '/static/js/critical.js', as: 'script' },
            { href: '/static/fonts/main.woff2', as: 'font', type: 'font/woff2', crossorigin: 'anonymous' }
        ];
        
        criticalResources.forEach(resource => {
            this.preloadResource(resource);
        });
    }
    
    preloadResource(resource) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        
        if (resource.type) link.type = resource.type;
        if (resource.crossorigin) link.crossOrigin = resource.crossorigin;
        
        document.head.appendChild(link);
    }
    
    // Performance Optimization Strategies
    optimizeFCP() {
        // Inline critical CSS
        this.inlineCriticalCSS();
        
        // Preload key resources
        this.preloadCriticalResources();
        
        // Remove render-blocking resources
        this.deferNonCriticalCSS();
    }
    
    optimizeLCP() {
        // Optimize largest contentful element
        const lcpElement = this.findLCPElement();
        if (lcpElement) {
            this.optimizeLCPElement(lcpElement);
        }
    }
    
    optimizeFID() {
        // Break up long tasks
        this.breakUpLongTasks();
        
        // Use web workers for heavy computations
        this.offloadToWebWorkers();
    }
    
    optimizeCLS() {
        // Set explicit dimensions for images and videos
        this.setExplicitDimensions();
        
        // Preload fonts
        this.preloadFonts();
    }
    
    // Utility Methods
    sendToAnalytics(metric) {
        // Send to your analytics service
        if (window.gtag) {
            window.gtag('event', 'performance_metric', {
                metric_name: metric.name,
                metric_value: metric.value,
                metric_type: metric.type
            });
        }
    }
    
    getMetrics() {
        return Object.fromEntries(this.metrics);
    }
    
    clearMetrics() {
        this.metrics.clear();
    }
    
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.metrics.clear();
        this.cache.clear();
    }
}

// Virtual Scroller Implementation
class VirtualScroller {
    constructor(container, options) {
        this.container = container;
        this.options = {
            itemHeight: 50,
            bufferSize: 5,
            totalItems: 0,
            renderItem: () => null,
            ...options
        };
        
        this.scrollTop = 0;
        this.visibleItems = new Map();
        this.init();
    }
    
    init() {
        this.setupContainer();
        this.bindEvents();
        this.render();
    }
    
    setupContainer() {
        this.container.style.overflowY = 'auto';
        this.container.style.position = 'relative';
        
        // Create spacer element
        this.spacer = document.createElement('div');
        this.spacer.style.height = `${this.options.totalItems * this.options.itemHeight}px`;
        this.container.appendChild(this.spacer);
    }
    
    bindEvents() {
        this.container.addEventListener('scroll', () => {
            this.scrollTop = this.container.scrollTop;
            this.render();
        });
    }
    
    render() {
        const containerHeight = this.container.clientHeight;
        const startIndex = Math.floor(this.scrollTop / this.options.itemHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(containerHeight / this.options.itemHeight) + this.options.bufferSize,
            this.options.totalItems
        );
        
        // Remove items outside visible range
        this.visibleItems.forEach((item, index) => {
            if (index < startIndex || index > endIndex) {
                item.remove();
                this.visibleItems.delete(index);
            }
        });
        
        // Add items in visible range
        for (let i = startIndex; i <= endIndex; i++) {
            if (!this.visibleItems.has(i)) {
                const item = this.options.renderItem(i);
                if (item) {
                    item.style.position = 'absolute';
                    item.style.top = `${i * this.options.itemHeight}px`;
                    item.style.width = '100%';
                    item.style.height = `${this.options.itemHeight}px`;
                    
                    this.container.appendChild(item);
                    this.visibleItems.set(i, item);
                }
            }
        }
    }
}

// Initialize Performance Manager
const performanceManager = new PerformanceManager();

// Export for global access
window.PerformanceManager = PerformanceManager;
window.performanceManager = performanceManager;
