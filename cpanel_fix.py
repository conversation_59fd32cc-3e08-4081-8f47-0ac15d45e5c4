#!/usr/bin/env python
"""
cPanel-specific fix script for Django deployment.
Run this script on your cPanel server via SSH.
"""

import os
import sys
import django

def setup_cpanel_environment():
    """Setup cPanel environment variables."""
    os.environ['CPANEL_ENV'] = 'True'
    os.environ['PRODUCTION'] = 'True'
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
    django.setup()

def create_cache_directories():
    """Create necessary cache directories for file-based caching."""
    print("📁 Creating cache directories...")
    
    directories = [
        'cache',
        'logs', 
        'session',
        'media',
        'staticfiles',
        'tmp'
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            os.chmod(directory, 0o755)
            print(f"✅ Created: {directory}/")
        except Exception as e:
            print(f"⚠️  Warning: Could not create {directory}/: {e}")

def test_cache_configuration():
    """Test the current cache configuration."""
    print("🧪 Testing cache configuration...")
    
    try:
        from django.core.cache import cache
        from django.conf import settings
        
        # Show current cache backend
        cache_backend = settings.CACHES['default']['BACKEND']
        print(f"📋 Cache Backend: {cache_backend}")
        
        if 'filebased' in cache_backend.lower():
            cache_location = settings.CACHES['default']['LOCATION']
            print(f"📍 Cache Location: {cache_location}")
            print("✅ Using file-based cache (recommended for cPanel)")
        elif 'db' in cache_backend.lower():
            print("⚠️  Using database cache (may cause issues)")
        else:
            print(f"📋 Cache type: {cache_backend}")
        
        # Test cache operations
        cache.set('test_key', 'test_value', 30)
        test_value = cache.get('test_key')
        
        if test_value == 'test_value':
            print("✅ Cache test successful!")
            cache.delete('test_key')
            return True
        else:
            print("❌ Cache test failed!")
            return False
            
    except Exception as e:
        print(f"❌ Cache test error: {e}")
        return False

def run_essential_commands():
    """Run essential Django management commands."""
    from django.core.management import execute_from_command_line
    
    commands = [
        (['manage.py', 'migrate', '--noinput'], "Running migrations"),
        (['manage.py', 'collectstatic', '--noinput'], "Collecting static files"),
    ]
    
    for command, description in commands:
        print(f"🔧 {description}...")
        try:
            execute_from_command_line(command)
            print(f"✅ {description} completed!")
        except Exception as e:
            print(f"⚠️  {description} warning: {e}")

def main():
    """Main cPanel fix function."""
    print("🚀 cPanel Django Deployment Fix")
    print("=" * 40)
    
    # Setup cPanel environment
    setup_cpanel_environment()
    
    # Create necessary directories
    create_cache_directories()
    
    # Test cache configuration
    cache_works = test_cache_configuration()
    
    # Run essential commands
    run_essential_commands()
    
    print("\n" + "=" * 40)
    if cache_works:
        print("🎉 cPanel fix completed successfully!")
        print("\n✅ Your Django app should now work without cache errors.")
        print("\n📋 Next steps:")
        print("1. Restart your cPanel application")
        print("2. Test your website")
        print("3. Monitor for any remaining errors")
    else:
        print("⚠️  cPanel fix completed with warnings.")
        print("\n📋 If you still get cache errors:")
        print("1. Check file permissions in cache/ directory")
        print("2. Ensure your cPanel account has write access")
        print("3. Contact your hosting provider if issues persist")

if __name__ == '__main__':
    main()
