"""
Tests for the context preloader functionality.
"""

from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from unittest.mock import patch, MagicMock

from accounts.models import Company, CompanyInfo
from assistants.models import Assistant
from assistants.context_preloader import ContextPreloader, context_preloader

User = get_user_model()


class ContextPreloaderTestCase(TestCase):
    """Test cases for context preloader functionality."""

    def setUp(self):
        """Set up test data."""
        # Clear cache before each test
        cache.clear()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test company
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )
        
        # Create company info
        self.company_info = CompanyInfo.objects.create(
            company=self.company,
            mission='Test mission',
            founded=2020
        )
        
        # Create test assistant
        self.assistant = Assistant.objects.create(
            name='Test Assistant',
            description='Test description',
            company=self.company,
            created_by=self.user,
            model='gpt-3.5-turbo',
            assistant_type=Assistant.TYPE_SUPPORT,
            is_active=True,
            is_public=True
        )
        
        # Create context preloader instance
        self.preloader = ContextPreloader()

    def tearDown(self):
        """Clean up after each test."""
        cache.clear()

    def test_preloader_initialization(self):
        """Test that context preloader initializes correctly."""
        self.assertTrue(self.preloader.enabled)
        self.assertEqual(self.preloader.preload_cache_ttl, 1800)

    def test_preload_assistant_info(self):
        """Test preloading basic assistant information."""
        assistant_info = self.preloader._preload_assistant_info(self.assistant)
        
        self.assertEqual(assistant_info['id'], self.assistant.id)
        self.assertEqual(assistant_info['name'], self.assistant.name)
        self.assertEqual(assistant_info['description'], self.assistant.description)
        self.assertEqual(assistant_info['assistant_type'], self.assistant.assistant_type)
        self.assertEqual(assistant_info['model'], self.assistant.model)
        self.assertEqual(assistant_info['company_name'], self.company.name)
        self.assertEqual(assistant_info['company_mission'], self.company_info.mission)

    def test_preload_system_context(self):
        """Test preloading system context."""
        with patch('assistants.context_preloader._get_system_context_cached') as mock_context:
            mock_context.return_value = 'Test system context'
            
            system_context = self.preloader._preload_system_context(self.assistant)
            
            self.assertEqual(system_context, 'Test system context')
            mock_context.assert_called_once_with(self.assistant)

    def test_preload_navigation_context_support_assistant(self):
        """Test preloading navigation context for support assistant."""
        # Create mock navigation items
        with patch.object(self.assistant, 'navigation_items') as mock_nav:
            mock_item = MagicMock()
            mock_item.id = 1
            mock_item.title = 'Test Section'
            mock_item.content = 'Test content'
            mock_item.url = 'http://test.com'
            mock_item.order = 1
            mock_item.unique_id = 'test_section'
            
            mock_nav.filter.return_value.order_by.return_value = [mock_item]
            
            nav_context = self.preloader._preload_navigation_context(self.assistant)
            
            self.assertIn('items', nav_context)
            self.assertEqual(nav_context['count'], 1)
            self.assertEqual(nav_context['items'][0]['title'], 'Test Section')

    def test_preload_navigation_context_non_support_assistant(self):
        """Test preloading navigation context for non-support assistant."""
        # Change assistant type
        self.assistant.assistant_type = Assistant.TYPE_COMMUNITY
        
        nav_context = self.preloader._preload_navigation_context(self.assistant)
        
        self.assertEqual(nav_context, {})

    def test_preload_community_context_community_assistant(self):
        """Test preloading community context for community assistant."""
        # Change assistant type
        self.assistant.assistant_type = Assistant.TYPE_COMMUNITY
        
        with patch.object(self.assistant, 'communitycontext_set') as mock_contexts:
            mock_context = MagicMock()
            mock_context.id = 1
            mock_context.title = 'Test Context'
            mock_context.text_content = 'Test content for community'
            mock_context.created_by.username = 'testuser'
            mock_context.created_at.isoformat.return_value = '2023-01-01T00:00:00'
            mock_context.times_used = 5
            
            mock_contexts.all.return_value = [mock_context]
            
            community_context = self.preloader._preload_community_context(self.assistant)
            
            self.assertIn('contexts', community_context)
            self.assertEqual(community_context['count'], 1)
            self.assertEqual(community_context['contexts'][0]['title'], 'Test Context')

    def test_preload_community_context_non_community_assistant(self):
        """Test preloading community context for non-community assistant."""
        community_context = self.preloader._preload_community_context(self.assistant)
        
        self.assertEqual(community_context, {})

    def test_preload_website_data(self):
        """Test preloading website data."""
        # Set website data
        self.assistant.website_data = {
            'navigation_items': [{'id': 1, 'title': 'Test'}],
            'company_info': {'name': 'Test Company'},
            'contact_info': {'email': '<EMAIL>'}
        }
        
        website_data = self.preloader._preload_website_data(self.assistant)
        
        self.assertTrue(website_data['has_data'])
        self.assertEqual(len(website_data['navigation_items']), 1)
        self.assertEqual(website_data['company_info']['name'], 'Test Company')

    def test_preload_assistant_context_full(self):
        """Test full context preloading."""
        context_data = self.preloader.preload_assistant_context(self.assistant.id, self.user)
        
        self.assertIn('assistant_info', context_data)
        self.assertIn('system_context', context_data)
        self.assertIn('navigation_context', context_data)
        self.assertIn('community_context', context_data)
        self.assertIn('website_data', context_data)
        self.assertIn('preloaded_at', context_data)

    def test_preload_assistant_context_caching(self):
        """Test that context preloading uses caching."""
        # First call should preload
        context_data1 = self.preloader.preload_assistant_context(self.assistant.id, self.user)
        
        # Second call should use cache
        context_data2 = self.preloader.preload_assistant_context(self.assistant.id, self.user)
        
        # Should be the same data
        self.assertEqual(context_data1['preloaded_at'], context_data2['preloaded_at'])

    def test_get_preloaded_context(self):
        """Test getting preloaded context."""
        # Initially should be None
        context = self.preloader.get_preloaded_context(self.assistant.id, self.user)
        self.assertIsNone(context)
        
        # After preloading should return data
        self.preloader.preload_assistant_context(self.assistant.id, self.user)
        context = self.preloader.get_preloaded_context(self.assistant.id, self.user)
        self.assertIsNotNone(context)

    def test_invalidate_assistant_context(self):
        """Test invalidating assistant context."""
        # Preload context
        self.preloader.preload_assistant_context(self.assistant.id, self.user)
        
        # Verify it's cached
        context = self.preloader.get_preloaded_context(self.assistant.id, self.user)
        self.assertIsNotNone(context)
        
        # Invalidate
        self.preloader.invalidate_assistant_context(self.assistant.id)
        
        # Should be None after invalidation
        context = self.preloader.get_preloaded_context(self.assistant.id, self.user)
        self.assertIsNone(context)

    @override_settings(ENABLE_CONTEXT_PRELOADING=False)
    def test_preloader_disabled(self):
        """Test that preloader respects the disabled setting."""
        disabled_preloader = ContextPreloader()
        
        context_data = disabled_preloader.preload_assistant_context(self.assistant.id, self.user)
        
        self.assertEqual(context_data, {})

    def test_global_preloader_instance(self):
        """Test that global preloader instance works."""
        context_data = context_preloader.preload_assistant_context(self.assistant.id, self.user)
        
        self.assertIn('assistant_info', context_data)
        self.assertIn('preloaded_at', context_data)

    def test_preload_nonexistent_assistant(self):
        """Test preloading context for non-existent assistant."""
        context_data = self.preloader.preload_assistant_context(99999, self.user)
        
        self.assertEqual(context_data, {})
