/**
 * Homepage Authentication State Fix
 * Ensures the header properly recognizes authentication state on the homepage
 */

class HomepageAuthFix {
    constructor() {
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.fixAuthenticationState());
        } else {
            this.fixAuthenticationState();
        }
    }

    fixAuthenticationState() {
        try {
            // Get authentication state from multiple sources
            const authState = this.getAuthenticationState();

            // Update header if authentication state is detected
            if (authState.isAuthenticated) {
                this.updateHeaderForAuthenticatedUser(authState);
            } else {
                this.updateHeaderForAnonymousUser();
            }

            // Set up periodic checks to ensure state remains consistent
            this.setupPeriodicChecks();

        } catch (error) {
            console.warn('Homepage auth fix error:', error);
        }
    }

    getAuthenticationState() {
        // Check multiple sources for authentication state
        const sources = {
            bodyData: document.body.dataset.authenticated === 'true',
            metaTag: document.querySelector('meta[name="user-authenticated"]')?.content === 'true',
            userIdMeta: !!document.querySelector('meta[name="user-id"]')?.content,
            usernameMeta: !!document.querySelector('meta[name="user-username"]')?.content
        };

        // Get user information
        const userId = document.querySelector('meta[name="user-id"]')?.content || '';
        const username = document.querySelector('meta[name="user-username"]')?.content || '';

        // Determine if user is authenticated based on any source
        const isAuthenticated = Object.values(sources).some(state => state === true);

        return {
            isAuthenticated,
            userId,
            username,
            sources
        };
    }

    updateHeaderForAuthenticatedUser(authState) {
        console.log('Homepage: Updating header for authenticated user:', authState.username);

        // Ensure body has correct authentication state
        document.body.dataset.authenticated = 'true';

        // Update any authentication-dependent elements in the header
        const header = document.querySelector('.unified-header');
        if (header) {
            header.classList.add('authenticated');
            header.classList.remove('anonymous');
        }

        // Force refresh of any dynamic content that depends on auth state
        this.refreshAuthDependentContent();
    }

    updateHeaderForAnonymousUser() {
        console.log('Homepage: Updating header for anonymous user');

        // Ensure body has correct authentication state
        document.body.dataset.authenticated = 'false';

        // Update header classes
        const header = document.querySelector('.unified-header');
        if (header) {
            header.classList.add('anonymous');
            header.classList.remove('authenticated');
        }
    }

    refreshAuthDependentContent() {
        // Trigger any events that might need to update based on auth state
        const authEvent = new CustomEvent('authStateUpdated', {
            detail: {
                authenticated: true,
                source: 'homepage-auth-fix'
            }
        });
        document.dispatchEvent(authEvent);
    }

    setupPeriodicChecks() {
        // Check authentication state every 30 seconds to catch any changes
        setInterval(() => {
            this.validateAuthenticationState();
        }, 30000);

        // Also check when the page becomes visible (user switches back to tab)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                setTimeout(() => {
                    this.validateAuthenticationState();
                }, 1000);
            }
        });

        // Check when user interacts with the page
        ['click', 'keydown', 'touchstart'].forEach(event => {
            document.addEventListener(event, () => {
                // Debounced check - only once per 10 seconds
                if (!this.lastInteractionCheck || Date.now() - this.lastInteractionCheck > 10000) {
                    this.lastInteractionCheck = Date.now();
                    setTimeout(() => {
                        this.validateAuthenticationState();
                    }, 500);
                }
            }, { passive: true, once: false });
        });
    }

    async validateAuthenticationState() {
        try {
            // Get current state
            const currentState = this.getAuthenticationState();
            const bodyState = document.body.dataset.authenticated === 'true';

            // If there's a mismatch, fix it
            if (currentState.isAuthenticated !== bodyState) {
                console.log('Homepage: Authentication state mismatch detected, fixing...');

                if (currentState.isAuthenticated) {
                    this.updateHeaderForAuthenticatedUser(currentState);
                } else {
                    this.updateHeaderForAnonymousUser();
                }
            }

            // Periodically validate with server (every 5 minutes)
            if (!this.lastServerCheck || Date.now() - this.lastServerCheck > 300000) {
                this.lastServerCheck = Date.now();
                await this.validateWithServer();
            }
        } catch (error) {
            console.warn('Homepage auth validation error:', error);
        }
    }

    async validateWithServer() {
        try {
            const response = await fetch('/accounts/api/check-session-status/', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': this.getCSRFToken()
                },
                credentials: 'same-origin'
            });

            if (response.ok) {
                const data = await response.json();
                const serverAuthState = data.authenticated;
                const clientAuthState = document.body.dataset.authenticated === 'true';

                if (serverAuthState !== clientAuthState) {
                    console.log('Homepage: Server/client auth state mismatch, refreshing page...');
                    // Refresh the page to get the correct state
                    window.location.reload();
                }
            }
        } catch (error) {
            console.warn('Homepage server auth validation error:', error);
        }
    }

    getCSRFToken() {
        // Try to get CSRF token from various sources
        let token = null;

        // From meta tag
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        if (metaTag) {
            token = metaTag.getAttribute('content');
        }

        // From cookie
        if (!token) {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    token = value;
                    break;
                }
            }
        }

        return token;
    }

    // Static method to manually trigger auth state check
    static checkAuthState() {
        const instance = window.homepageAuthFix;
        if (instance) {
            instance.validateAuthenticationState();
        }
    }
}

// Initialize the fix when the script loads
if (typeof window !== 'undefined') {
    window.homepageAuthFix = new HomepageAuthFix();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HomepageAuthFix;
}
