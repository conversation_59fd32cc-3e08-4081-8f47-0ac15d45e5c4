#!/usr/bin/env python3
"""
Debug Image Display Issues
Quick script to test image paths and availability
"""

import os
import sys
import django
from pathlib import Path

# Add the project directory to Python path
sys.path.append(str(Path(__file__).parent))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from accounts.models import Company, CompanyInformation
from django.conf import settings
from django.templatetags.static import static
from django.urls import reverse

def test_image_paths():
    """Test various image paths"""
    print("=== Testing Image Paths ===")
    
    # Test static files
    static_images = [
        'img/default-company-logo.svg',
        'img/logos/company-1.svg',
        'img/logos/company-2.svg',
        'img/logos/company-3.svg',
    ]
    
    for img_path in static_images:
        full_path = os.path.join(settings.STATIC_ROOT or 'static', img_path)
        local_path = os.path.join('static', img_path)
        
        if os.path.exists(local_path):
            print(f"✓ Static image exists: {img_path}")
        else:
            print(f"✗ Static image missing: {img_path}")
    
    print()

def test_company_logos():
    """Test company logo availability"""
    print("=== Testing Company Logos ===")
    
    companies = Company.objects.all()[:5]
    
    for company in companies:
        print(f"\nCompany: {company.name}")
        
        # Check if company has info
        if hasattr(company, 'info') and company.info:
            print(f"  ✓ Has CompanyInformation")
            
            # Check logo
            if company.info.logo:
                logo_path = company.info.logo.path
                logo_url = company.info.logo.url
                
                print(f"  Logo URL: {logo_url}")
                print(f"  Logo Path: {logo_path}")
                
                if os.path.exists(logo_path):
                    file_size = os.path.getsize(logo_path)
                    print(f"  ✓ Logo file exists ({file_size} bytes)")
                else:
                    print(f"  ✗ Logo file missing")
            else:
                print(f"  - No logo assigned")
        else:
            print(f"  ✗ No CompanyInformation")

def test_template_logic():
    """Test template logic conditions"""
    print("\n=== Testing Template Logic ===")
    
    companies = Company.objects.all()[:3]
    
    for company in companies:
        print(f"\nCompany: {company.name}")
        
        # Test the exact conditions used in templates
        has_info = hasattr(company, 'info') and company.info
        has_logo = has_info and company.info.logo
        has_logo_url = has_logo and company.info.logo.url
        
        print(f"  has_info: {has_info}")
        print(f"  has_logo: {has_logo}")
        print(f"  has_logo_url: {has_logo_url}")
        
        if has_logo_url:
            print(f"  → Will show: {company.info.logo.url}")
        else:
            print(f"  → Will show: default-company-logo.svg")

def main():
    """Main function"""
    print("Image Debug Script")
    print("=" * 50)
    
    test_image_paths()
    test_company_logos()
    test_template_logic()
    
    print("\n=== Recommendations ===")
    print("1. Check browser developer console for image loading errors")
    print("2. Verify media URL configuration in Django settings")
    print("3. Ensure static files are being served correctly")
    print("4. Test image URLs directly in browser:")
    print("   - http://localhost:8000/static/img/default-company-logo.svg")
    print("   - http://localhost:8000/media/company_logos/bot.png")
    
    print("\nDebug completed!")

if __name__ == '__main__':
    main()
