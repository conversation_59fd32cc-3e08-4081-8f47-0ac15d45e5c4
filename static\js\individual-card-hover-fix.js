/**
 * Individual Card Hover Fix JavaScript
 * Prevents JavaScript from adding hover effects to multiple cards at once
 * Ensures only individual directory cards respond to hover
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== REMOVE PROBLEMATIC EVENT LISTENERS =====
    
    // Function to remove all existing hover event listeners from cards
    function removeProblematicHoverListeners() {
        // Get all cards that might have problematic listeners
        const allCards = document.querySelectorAll('.card, .directory-card, .company-card');
        
        allCards.forEach(card => {
            // Clone the element to remove all event listeners
            const newCard = card.cloneNode(true);
            card.parentNode.replaceChild(newCard, card);
        });
    }
    
    // ===== PREVENT SHADOW CLASS ADDITIONS =====
    
    // Override any functions that might add shadow classes to multiple cards
    const originalAddEventListener = Element.prototype.addEventListener;
    Element.prototype.addEventListener = function(type, listener, options) {
        // If it's a mouseenter/mouseleave on a card, check if it's problematic
        if ((type === 'mouseenter' || type === 'mouseleave') && 
            (this.classList.contains('card') || this.classList.contains('directory-card'))) {
            
            // Only allow hover effects on individual directory cards, not all cards
            if (this.classList.contains('directory-card') && 
                this.classList.contains('list-group-item')) {
                // This is a legitimate directory card, allow the listener
                return originalAddEventListener.call(this, type, listener, options);
            } else {
                // This might be a problematic listener, don't add it
                console.log('Blocked problematic card hover listener on:', this);
                return;
            }
        }
        
        // For all other events, proceed normally
        return originalAddEventListener.call(this, type, listener, options);
    };
    
    // ===== CLEAN UP EXISTING PROBLEMATIC EFFECTS =====
    
    // Remove any existing shadow classes that might have been added
    function cleanupShadowClasses() {
        const allCards = document.querySelectorAll('.card, .directory-card');
        allCards.forEach(card => {
            card.classList.remove('shadow', 'shadow-sm', 'shadow-lg');
            // Reset any inline styles that might have been added by JavaScript
            if (!card.classList.contains('directory-card') || 
                !card.classList.contains('list-group-item')) {
                card.style.transform = '';
                card.style.boxShadow = '';
                card.style.transition = '';
            }
        });
    }
    
    // ===== SETUP PROPER INDIVIDUAL CARD HOVER =====
    
    // Add proper hover effects only to individual directory cards
    function setupIndividualCardHover() {
        const directoryCards = document.querySelectorAll('.directory-card.list-group-item');
        
        directoryCards.forEach(card => {
            // Remove any existing listeners first
            const newCard = card.cloneNode(true);
            card.parentNode.replaceChild(newCard, card);
            
            // Add proper individual hover effect
            newCard.addEventListener('mouseenter', function() {
                // Only affect this specific card
                this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.05)';
                this.style.borderColor = 'rgba(207, 46, 46, 0.2)';
                this.style.zIndex = '2';
                this.style.transition = 'box-shadow 0.2s ease, border-color 0.2s ease';
            });
            
            newCard.addEventListener('mouseleave', function() {
                // Reset this specific card
                this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04)';
                this.style.borderColor = '#e0e0e0';
                this.style.zIndex = '1';
            });
        });
    }
    
    // ===== PREVENT CONTAINER HOVER EFFECTS =====
    
    // Ensure parent containers don't trigger hover effects on children
    function preventContainerHoverEffects() {
        const containers = document.querySelectorAll('.tier-section, .list-group, .company-cards-container');
        
        containers.forEach(container => {
            container.addEventListener('mouseenter', function(e) {
                // Stop the event from affecting child cards
                e.stopPropagation();
            });
            
            container.addEventListener('mouseleave', function(e) {
                // Stop the event from affecting child cards
                e.stopPropagation();
            });
        });
    }
    
    // ===== MUTATION OBSERVER TO HANDLE DYNAMIC CONTENT =====
    
    // Watch for new cards being added and apply fixes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        // Check if the added node is a card or contains cards
                        if (node.classList && (node.classList.contains('card') || 
                            node.classList.contains('directory-card'))) {
                            cleanupShadowClasses();
                            setupIndividualCardHover();
                        }
                        
                        // Check for cards within the added node
                        const cards = node.querySelectorAll && node.querySelectorAll('.card, .directory-card');
                        if (cards && cards.length > 0) {
                            cleanupShadowClasses();
                            setupIndividualCardHover();
                        }
                    }
                });
            }
        });
    });
    
    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // ===== INITIALIZE FIXES =====
    
    // Run initial cleanup and setup
    setTimeout(() => {
        cleanupShadowClasses();
        setupIndividualCardHover();
        preventContainerHoverEffects();
    }, 100);
    
    // Run again after a longer delay to catch any late-loading content
    setTimeout(() => {
        cleanupShadowClasses();
        setupIndividualCardHover();
        preventContainerHoverEffects();
    }, 1000);
    
    // ===== PERIODIC CLEANUP =====
    
    // Periodically clean up any problematic effects that might get re-added
    setInterval(() => {
        cleanupShadowClasses();
    }, 5000);
    
});

// ===== OVERRIDE PROBLEMATIC FUNCTIONS =====

// Override any global functions that might add problematic hover effects
window.addEventListener('load', function() {
    // If there are any global card hover functions, override them here
    if (window.initCardHoverEffects) {
        window.initCardHoverEffects = function() {
            // Do nothing - prevent problematic hover effects
            console.log('Blocked initCardHoverEffects');
        };
    }
    
    if (window.addCardHoverListeners) {
        window.addCardHoverListeners = function() {
            // Do nothing - prevent problematic hover effects
            console.log('Blocked addCardHoverListeners');
        };
    }
});
