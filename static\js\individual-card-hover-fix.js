/**
 * Individual Card Hover Fix JavaScript
 * Lightweight fix to prevent multiple card hover effects
 */

document.addEventListener('DOMContentLoaded', function() {

    // ===== SIMPLE CLEANUP APPROACH =====

    // Function to clean up problematic shadow classes
    function cleanupShadowClasses() {
        try {
            const allCards = document.querySelectorAll('.card:not(.directory-card), .directory-card:not(.list-group-item)');
            allCards.forEach(card => {
                card.classList.remove('shadow', 'shadow-sm', 'shadow-lg');
                // Reset inline styles that might cause issues
                if (card.style.transform) card.style.transform = '';
                if (card.style.boxShadow) card.style.boxShadow = '';
            });
        } catch (e) {
            console.log('Error in cleanupShadowClasses:', e);
        }
    }

    // ===== PREVENT CONTAINER HOVER EFFECTS =====

    // Simple approach to prevent container hover effects
    function preventContainerHoverEffects() {
        try {
            const containers = document.querySelectorAll('.tier-section, .list-group, .company-cards-container');

            containers.forEach(container => {
                // Remove any existing hover styles
                container.style.background = 'transparent';
                container.style.backgroundColor = 'transparent';
                container.style.transform = 'none';
                container.style.boxShadow = 'none';
            });
        } catch (e) {
            console.log('Error in preventContainerHoverEffects:', e);
        }
    }

    // ===== INITIALIZE FIXES =====

    // Run cleanup once after page loads
    setTimeout(() => {
        cleanupShadowClasses();
        preventContainerHoverEffects();
    }, 500);

});
