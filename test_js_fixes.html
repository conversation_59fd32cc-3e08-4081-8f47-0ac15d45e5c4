<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Fixes Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="static/css/company-logo-fallback.css" rel="stylesheet">
    <style>
        body {
            background-color: #121212;
            color: #ffffff;
            padding: 20px;
        }
        .test-section {
            background: #1e1e1e;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .logo-container {
            width: 120px;
            height: 120px;
            border: 1px solid #444;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px;
            background: #2d2d2d;
        }
        .company-logo {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JavaScript Fixes Test Page</h1>
        <p>This page tests the JavaScript error fixes and company logo fallback system.</p>

        <div class="test-section">
            <h2>1. Dark Mode Error Fixes Test</h2>
            <p>Testing if dark mode scripts run without errors:</p>
            <div id="dark-mode-status" class="alert alert-info">Testing...</div>
        </div>

        <div class="test-section">
            <h2>2. Company Logo Fallback Test</h2>
            <p>Testing company logo fallback system with broken images:</p>
            <div class="row">
                <div class="col-md-3">
                    <div class="logo-container">
                        <img src="/media/company_logos/non-existent-1.svg" alt="Test Company 1 Logo" class="company-logo">
                    </div>
                    <p class="text-center small">Non-existent image 1</p>
                </div>
                <div class="col-md-3">
                    <div class="logo-container">
                        <img src="/media/company_logos/non-existent-2.png" alt="Test Company 2 Logo" class="company-logo">
                    </div>
                    <p class="text-center small">Non-existent image 2</p>
                </div>
                <div class="col-md-3">
                    <div class="logo-container">
                        <img src="/static/img/default-company-logo.svg" alt="Default Logo" class="company-logo">
                    </div>
                    <p class="text-center small">Default logo (should work)</p>
                </div>
                <div class="col-md-3">
                    <div class="logo-container">
                        <img src="/static/img/logos/company-1.svg" alt="Company 1 Logo" class="company-logo">
                    </div>
                    <p class="text-center small">Fallback logo (should work)</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>3. TinyMCE Error Fixes Test</h2>
            <p>Testing TinyMCE error handling:</p>
            <div id="tinymce-status" class="alert alert-info">Testing...</div>
        </div>

        <div class="test-section">
            <h2>4. Mutation Observer Error Fixes Test</h2>
            <p>Testing mutation observer error handling:</p>
            <div id="observer-status" class="alert alert-info">Testing...</div>
            <button id="add-content-btn" class="btn btn-primary">Add Dynamic Content</button>
        </div>

        <div class="test-section">
            <h2>5. Console Output</h2>
            <p>JavaScript console output (errors and logs):</p>
            <div id="console-output" class="console-output">
                <div>Console output will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Load the fixed JavaScript files -->
    <script src="static/js/error-handler-fix.js"></script>
    <script src="static/js/dark-mode-performance-fix.js"></script>
    <script src="static/js/force-dark-mode.js"></script>
    <script src="static/js/filter-background-fix.js"></script>
    <script src="static/js/company-logo-fallback.js"></script>
    <script src="static/js/tinymce-responsive-handler.js"></script>

    <script>
        // Capture console output
        (function() {
            const consoleOutput = document.getElementById('console-output');
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;

            function addToConsole(type, message) {
                const div = document.createElement('div');
                div.style.color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#ffa500' : '#0f0';
                div.textContent = `[${type.toUpperCase()}] ${message}`;
                consoleOutput.appendChild(div);
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
            }

            console.log = function(...args) {
                addToConsole('log', args.join(' '));
                originalLog.apply(console, args);
            };

            console.error = function(...args) {
                addToConsole('error', args.join(' '));
                originalError.apply(console, args);
            };

            console.warn = function(...args) {
                addToConsole('warn', args.join(' '));
                originalWarn.apply(console, args);
            };
        })();

        // Test functions
        function testDarkMode() {
            const status = document.getElementById('dark-mode-status');
            try {
                // Test if dark mode functions exist and work
                if (typeof window.applyGlobalDarkMode === 'function') {
                    window.applyGlobalDarkMode();
                    status.className = 'alert alert-success';
                    status.textContent = '✓ Dark mode functions working correctly';
                } else {
                    status.className = 'alert alert-warning';
                    status.textContent = '⚠ Dark mode functions not found';
                }
            } catch (error) {
                status.className = 'alert alert-danger';
                status.textContent = '✗ Dark mode error: ' + error.message;
            }
        }

        function testTinyMCE() {
            const status = document.getElementById('tinymce-status');
            try {
                // Test TinyMCE error handling
                if (typeof tinymce !== 'undefined') {
                    status.className = 'alert alert-success';
                    status.textContent = '✓ TinyMCE loaded successfully';
                } else {
                    status.className = 'alert alert-info';
                    status.textContent = 'ℹ TinyMCE not loaded (expected on this test page)';
                }
            } catch (error) {
                status.className = 'alert alert-danger';
                status.textContent = '✗ TinyMCE error: ' + error.message;
            }
        }

        function testMutationObserver() {
            const status = document.getElementById('observer-status');
            try {
                // Test mutation observer creation
                const observer = new MutationObserver(() => {});
                if (document.body) {
                    observer.observe(document.body, { childList: true });
                    observer.disconnect();
                    status.className = 'alert alert-success';
                    status.textContent = '✓ Mutation observer working correctly';
                } else {
                    status.className = 'alert alert-danger';
                    status.textContent = '✗ Document body not available';
                }
            } catch (error) {
                status.className = 'alert alert-danger';
                status.textContent = '✗ Mutation observer error: ' + error.message;
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded, running tests...');
            
            setTimeout(() => {
                testDarkMode();
                testTinyMCE();
                testMutationObserver();
            }, 1000);

            // Add dynamic content test
            document.getElementById('add-content-btn').addEventListener('click', function() {
                const container = document.querySelector('.test-section:nth-child(4)');
                const newImg = document.createElement('img');
                newImg.src = '/media/company_logos/dynamic-test.svg';
                newImg.alt = 'Dynamic Test Logo';
                newImg.className = 'company-logo';
                newImg.style.width = '100px';
                newImg.style.height = '100px';
                newImg.style.border = '1px solid #444';
                newImg.style.margin = '10px';
                
                container.appendChild(newImg);
                console.log('Added dynamic image element');
            });
        });

        // Test error handling
        window.addEventListener('error', function(event) {
            console.error('Global error caught:', event.message, 'at', event.filename + ':' + event.lineno);
        });

        console.log('Test page script loaded');
    </script>
</body>
</html>
