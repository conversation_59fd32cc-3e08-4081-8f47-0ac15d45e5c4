"""
cPanel-optimized LLM utilities to prevent resource spikes.
Implements aggressive timeouts, caching, and resource management.
"""

import time
import hashlib
import logging
from typing import Dict, List, Optional
from django.core.cache import cache
from django.conf import settings
import openai
import anthropic
from groq import Groq

logger = logging.getLogger(__name__)

# Aggressive timeouts for cPanel
CPANEL_TIMEOUT = 10  # 10 seconds max for LLM calls
CACHE_TIMEOUT = 1800  # 30 minutes cache
MAX_RETRIES = 1  # Only 1 retry for cPanel


class CPanelLLMManager:
    """
    LLM manager optimized for cPanel shared hosting.
    Prevents resource spikes and hanging processes.
    """

    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        self.groq_client = None
        self._init_clients()

    def _init_clients(self):
        """Initialize LLM clients with aggressive timeouts."""
        try:
            # OpenAI client with timeout
            if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
                self.openai_client = openai.OpenAI(
                    api_key=settings.OPENAI_API_KEY,
                    timeout=CPANEL_TIMEOUT,
                    max_retries=MAX_RETRIES
                )

            # Anthropic client with timeout
            if hasattr(settings, 'ANTHROPIC_API_KEY') and settings.ANTHROPIC_API_KEY:
                self.anthropic_client = anthropic.Anthropic(
                    api_key=settings.ANTHROPIC_API_KEY,
                    timeout=CPANEL_TIMEOUT,
                    max_retries=MAX_RETRIES
                )

            # Groq client with timeout
            if hasattr(settings, 'GROQ_API_KEY') and settings.GROQ_API_KEY:
                self.groq_client = Groq(
                    api_key=settings.GROQ_API_KEY,
                    timeout=CPANEL_TIMEOUT,
                    max_retries=MAX_RETRIES
                )

        except Exception as e:
            logger.error(f"Error initializing LLM clients: {e}")

    def generate_response(self, assistant, messages: List[Dict], user_message: str) -> Dict:
        """
        Generate LLM response with aggressive cPanel optimizations.
        """
        # Generate cache key
        cache_key = self._generate_cache_key(assistant, messages, user_message)

        # Try cache first
        cached_response = cache.get(cache_key)
        if cached_response:
            logger.info("Returning cached LLM response")
            return cached_response

        # Prepare for LLM call with timeout protection
        start_time = time.time()

        try:
            # Determine model and client
            model_name = assistant.model
            response_content = ""

            if model_name.startswith('gpt'):
                response_content = self._call_openai(messages, model_name, assistant)
            elif model_name.startswith('llama'):
                response_content = self._call_groq(messages, model_name, assistant)
            elif model_name.startswith('claude'):
                response_content = self._call_anthropic(messages, model_name, assistant)
            elif model_name == 'openai-compatible':
                response_content = self._call_openai_compatible(messages, model_name, assistant)
            else:
                # Fallback to simple response
                response_content = "I'm currently experiencing high load. Please try again in a moment."

            # Check if we exceeded timeout
            elapsed = time.time() - start_time
            if elapsed > CPANEL_TIMEOUT:
                logger.warning(f"LLM call exceeded timeout: {elapsed:.2f}s")
                response_content = "Response took too long. Please try a simpler question."

            # Prepare response
            response = {
                'content': response_content,
                'model': model_name,
                'cached': False,
                'duration': elapsed
            }

            # Cache successful responses
            if response_content and not response_content.startswith("I'm currently experiencing"):
                cache.set(cache_key, response, CACHE_TIMEOUT)

            return response

        except Exception as e:
            logger.error(f"LLM generation error: {e}")
            return {
                'content': "I'm currently experiencing technical difficulties. Please try again later.",
                'model': model_name,
                'cached': False,
                'error': str(e),
                'duration': time.time() - start_time
            }

    def _call_openai(self, messages: List[Dict], model: str, assistant) -> str:
        """Call OpenAI API with cPanel optimizations."""
        if not self.openai_client:
            raise Exception("OpenAI client not initialized")

        response = self.openai_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=min(assistant.temperature, 0.7),  # Limit temperature
            max_tokens=min(assistant.max_tokens, 500),    # Limit tokens for cPanel
            timeout=CPANEL_TIMEOUT
        )

        return response.choices[0].message.content.strip()

    def _call_groq(self, messages: List[Dict], model: str, assistant) -> str:
        """Call Groq API with cPanel optimizations."""
        if not self.groq_client:
            raise Exception("Groq client not initialized")

        response = self.groq_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=min(assistant.temperature, 0.7),  # Limit temperature
            max_tokens=min(assistant.max_tokens, 500),    # Limit tokens for cPanel
            timeout=CPANEL_TIMEOUT
        )

        return response.choices[0].message.content.strip()

    def _call_anthropic(self, messages: List[Dict], model: str, assistant) -> str:
        """Call Anthropic API with cPanel optimizations."""
        if not self.anthropic_client:
            raise Exception("Anthropic client not initialized")

        # Convert messages for Anthropic format
        system_message = ""
        user_messages = []

        for msg in messages:
            if msg['role'] == 'system':
                system_message = msg['content']
            else:
                user_messages.append(msg)

        response = self.anthropic_client.messages.create(
            model=model,
            system=system_message,
            messages=user_messages,
            temperature=min(assistant.temperature, 0.7),  # Limit temperature
            max_tokens=min(assistant.max_tokens, 500),    # Limit tokens for cPanel
            timeout=CPANEL_TIMEOUT
        )

        return response.content[0].text.strip()

    def _call_openai_compatible(self, messages: List[Dict], model: str, assistant) -> str:
        """Call OpenAI Compatible API with cPanel optimizations."""
        # Validate required fields for OpenAI compatible models
        if not assistant.api_key or not assistant.api_key.strip():
            raise Exception("API key is required for OpenAI Compatible model")

        if not assistant.base_url or not assistant.base_url.strip():
            raise Exception("Base URL is required for OpenAI Compatible model")

        if not assistant.custom_model_name or not assistant.custom_model_name.strip():
            raise Exception("Model name is required for OpenAI Compatible model")

        try:
            # Create custom client with cPanel optimizations
            custom_client = openai.OpenAI(
                api_key=assistant.api_key.strip(),
                base_url=assistant.base_url.strip(),
                timeout=CPANEL_TIMEOUT,
                max_retries=MAX_RETRIES
            )

            # Make API call with cPanel optimizations
            response = custom_client.chat.completions.create(
                model=assistant.custom_model_name.strip(),
                messages=messages,
                temperature=min(assistant.temperature, 0.7),  # Limit temperature for cPanel
                max_tokens=min(assistant.max_tokens, 500),    # Limit tokens for cPanel
                timeout=CPANEL_TIMEOUT
            )

            # Validate response
            if not response.choices or not response.choices[0].message.content:
                raise Exception("Empty response received from OpenAI Compatible API")

            return response.choices[0].message.content.strip()

        except openai.APIConnectionError as e:
            logger.error(f"Connection error with OpenAI Compatible API: {e}")
            raise Exception(f"Failed to connect to OpenAI Compatible API: {e}")
        except openai.AuthenticationError as e:
            logger.error(f"Authentication error with OpenAI Compatible API: {e}")
            raise Exception(f"Authentication failed with OpenAI Compatible API. Please check your API key: {e}")
        except openai.RateLimitError as e:
            logger.error(f"Rate limit error with OpenAI Compatible API: {e}")
            raise Exception(f"Rate limit exceeded for OpenAI Compatible API: {e}")
        except openai.APIError as e:
            logger.error(f"API error with OpenAI Compatible API: {e}")
            raise Exception(f"OpenAI Compatible API error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error with OpenAI Compatible API: {e}")
            raise Exception(f"Unexpected error calling OpenAI Compatible API: {e}")

    def _generate_cache_key(self, assistant, messages: List[Dict], user_message: str) -> str:
        """Generate cache key for LLM response."""
        # Create hash from assistant ID, model, and message content
        content = f"{assistant.id}:{assistant.model}:{user_message}"

        # Add last few messages for context
        if len(messages) > 1:
            context = "".join([msg.get('content', '')[-100:] for msg in messages[-3:]])
            content += f":{context}"

        # Generate hash
        cache_key = hashlib.md5(content.encode()).hexdigest()
        return f"llm_cpanel:{cache_key}"

    def generate_suggestions(self, assistant, conversation_history: str) -> List[str]:
        """Generate suggested questions with cPanel optimizations."""
        cache_key = f"suggestions:{assistant.id}:{hashlib.md5(conversation_history.encode()).hexdigest()}"

        # Try cache first
        cached_suggestions = cache.get(cache_key)
        if cached_suggestions:
            return cached_suggestions

        # Simple fallback suggestions for cPanel
        default_suggestions = [
            "Can you help me with this?",
            "Tell me more about this topic",
            "What are the key points?",
            "How does this work?"
        ]

        try:
            # Only try LLM if we have a fast model
            if assistant.model.startswith('llama') and self.groq_client:
                prompt = f"Based on this conversation, suggest 3 brief follow-up questions:\n{conversation_history[-500:]}"

                response = self.groq_client.chat.completions.create(
                    model=assistant.model,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.7,
                    max_tokens=100,  # Very limited for suggestions
                    timeout=5  # Very short timeout for suggestions
                )

                suggestions_text = response.choices[0].message.content.strip()
                suggestions = [s.strip() for s in suggestions_text.split('\n') if s.strip()]

                if len(suggestions) >= 3:
                    # Cache successful suggestions
                    cache.set(cache_key, suggestions[:4], 600)  # 10 minutes
                    return suggestions[:4]

        except Exception as e:
            logger.warning(f"Error generating suggestions: {e}")

        # Return default suggestions
        cache.set(cache_key, default_suggestions, 300)  # 5 minutes
        return default_suggestions


# Global instance for cPanel
cpanel_llm_manager = CPanelLLMManager()


def generate_cpanel_optimized_response(assistant, messages: List[Dict], user_message: str) -> Dict:
    """
    Main function for generating LLM responses in cPanel environment.
    """
    return cpanel_llm_manager.generate_response(assistant, messages, user_message)


def generate_cpanel_suggestions(assistant, conversation_history: str) -> List[str]:
    """
    Generate suggestions optimized for cPanel.
    """
    return cpanel_llm_manager.generate_suggestions(assistant, conversation_history)
