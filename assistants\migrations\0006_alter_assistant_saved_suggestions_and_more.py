# Generated by Django 4.2.21 on 2025-05-26 07:56

import assistants.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("assistants", "0005_merge_20250525_2318"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="assistant",
            name="saved_suggestions",
            field=models.J<PERSON><PERSON>ield(
                blank=True,
                default=assistants.models.default_list,
                help_text="List of suggested questions saved from the Analyze & Suggest tab.",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="assistant",
            name="website_data",
            field=models.J<PERSON><PERSON>ield(
                blank=True,
                default=assistants.models.default_dict,
                help_text="Structured website data for customer care assistants (content keyed by NavigationItem unique_id)",
            ),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="communitycontext",
            name="keywords",
            field=models.JSONField(
                blank=True,
                default=assistants.models.default_list,
                help_text="List of keywords to help categorize this context",
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="navigationitem",
            name="gallery",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                blank=True,
                default=assistants.models.default_list,
                help_text="Gallery images for this navigation item.",
            ),
        ),
    ]
