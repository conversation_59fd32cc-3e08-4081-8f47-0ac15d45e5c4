{% autoescape off %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{% block email_title %}24seven{% endblock %}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* Header */
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            text-align: center;
            padding: 40px 30px;
            position: relative;
        }

        .email-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .email-header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: -0.5px;
            position: relative;
            z-index: 1;
        }

        .email-header .subtitle {
            margin: 8px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .email-header .icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
            position: relative;
            z-index: 1;
        }

        /* Content */
        .email-content {
            padding: 40px 30px;
            background-color: #ffffff;
        }

        .email-content h2 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 20px 0;
            line-height: 1.3;
        }

        .email-content h3 {
            color: #34495e;
            font-size: 20px;
            font-weight: 600;
            margin: 24px 0 16px 0;
            line-height: 1.3;
        }

        .email-content p {
            font-size: 16px;
            margin: 0 0 16px 0;
            color: #4a5568;
            line-height: 1.6;
        }

        .email-content ul {
            margin: 16px 0;
            padding-left: 20px;
        }

        .email-content li {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 8px;
            line-height: 1.5;
        }

        /* Buttons */
        .button-container {
            text-align: center;
            margin: 32px 0;
        }

        .button {
            display: inline-block;
            padding: 16px 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .button-secondary {
            background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
            box-shadow: 0 4px 15px rgba(113, 128, 150, 0.4);
        }

        /* Info boxes */
        .info-box {
            background: linear-gradient(135deg, #ebf8ff 0%, #e6fffa 100%);
            border-left: 4px solid #3182ce;
            padding: 20px;
            border-radius: 8px;
            margin: 24px 0;
        }

        .warning-box {
            background: linear-gradient(135deg, #fffbeb 0%, #fef5e7 100%);
            border-left: 4px solid #d69e2e;
            padding: 20px;
            border-radius: 8px;
            margin: 24px 0;
        }

        .success-box {
            background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
            border-left: 4px solid #38a169;
            padding: 20px;
            border-radius: 8px;
            margin: 24px 0;
        }

        .error-box {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            border-left: 4px solid #e53e3e;
            padding: 20px;
            border-radius: 8px;
            margin: 24px 0;
        }

        /* Security info */
        .security-info {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }

        .security-info h4 {
            color: #2d3748;
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 16px 0;
            display: flex;
            align-items: center;
        }

        .security-info ul {
            margin: 0;
            padding-left: 20px;
        }

        .security-info li {
            margin-bottom: 8px;
            color: #4a5568;
        }

        /* Footer */
        .email-footer {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: #e2e8f0;
            padding: 40px 30px;
            text-align: center;
        }

        .email-footer h3 {
            color: #ffffff;
            font-size: 24px;
            font-weight: 700;
            margin: 0 0 8px 0;
        }

        .email-footer .tagline {
            color: #a0aec0;
            font-size: 14px;
            margin: 0 0 24px 0;
            font-style: italic;
        }

        .email-footer p {
            font-size: 14px;
            margin: 8px 0;
            color: #cbd5e0;
        }

        .email-footer a {
            color: #81e6d9;
            text-decoration: none;
        }

        .email-footer a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 12px;
            }

            .email-header {
                padding: 30px 20px;
            }

            .email-header h1 {
                font-size: 28px;
            }

            .email-content {
                padding: 30px 20px;
            }

            .email-footer {
                padding: 30px 20px;
            }

            .button {
                padding: 14px 24px;
                font-size: 15px;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1a202c;
            }

            .email-content {
                background-color: #1a202c;
            }

            .email-content h2,
            .email-content h3 {
                color: #f7fafc;
            }

            .email-content p,
            .email-content li {
                color: #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <div style="background-color: #f8fafc; padding: 20px 0;">
        <div class="email-container">
            <!-- Header -->
            <div class="email-header">
                {% block email_header %}
                <div class="icon">{% block email_icon %}📧{% endblock %}</div>
                <h1>{% block email_heading %}24seven{% endblock %}</h1>
                <div class="subtitle">{% block email_subtitle %}AI-Powered Collaboration Platform{% endblock %}</div>
                {% endblock %}
            </div>

            <!-- Content -->
            <div class="email-content">
                {% block email_content %}
                <p>This is the base email template content.</p>
                {% endblock %}
            </div>

            <!-- Footer -->
            <div class="email-footer">
                {% block email_footer %}
                <h3>24seven</h3>
                <div class="tagline">Secure AI-Powered Collaboration</div>
                <p>
                    <strong>Thanks,<br>The 24seven Team</strong>
                </p>
                <p>
                    <a href="{{ site_url|default:'https://24seven.site' }}">{{ site_url|default:'24seven.site' }}</a>
                </p>
                <p style="font-size: 12px; margin-top: 20px; opacity: 0.8;">
                    This is an automated message, please do not reply to this email.<br>
                    If you need assistance, please visit our <a href="{{ site_config.support_url|default:site_config.contact_url|default:'/contact/' }}">support center</a>.
                </p>
                {% endblock %}
            </div>
        </div>
    </div>
</body>
</html>
{% endautoescape %}
