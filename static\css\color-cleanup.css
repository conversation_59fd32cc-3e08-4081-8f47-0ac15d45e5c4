/**
 * Color Cleanup CSS
 * Removes all blue/purple colors and replaces with #252638
 * Fixes white text visibility on white backgrounds
 */

/* ===== REMOVE ALL BLUE/PURPLE COLORS ===== */
:root {
    /* Override all blue/purple CSS variables */
    --blue: #cf2e2e !important;
    --purple: #252638 !important;
    --indigo: #252638 !important;
    --navy: #252638 !important;
    --primary-blue: #cf2e2e !important;
    --secondary-blue: #252638 !important;
    --accent-blue: #cf2e2e !important;
    --info: #252638 !important;
    --primary: #cf2e2e !important;
    
    /* Bootstrap color overrides */
    --bs-blue: #cf2e2e !important;
    --bs-purple: #252638 !important;
    --bs-indigo: #252638 !important;
    --bs-primary: #cf2e2e !important;
    --bs-info: #252638 !important;
    
    /* Custom blue/purple variables */
    --vibrant-blue: #cf2e2e !important;
    --vibrant-blue-gradient: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
    --vibrant-blue-light: #f7f7f7 !important;
}

/* ===== REPLACE BLUE/PURPLE BACKGROUNDS ===== */
.bg-blue, .bg-primary, .bg-info,
[class*="bg-blue"], [class*="bg-purple"], [class*="bg-indigo"],
[style*="background-color: blue"], [style*="background: blue"],
[style*="background-color: #0066ff"], [style*="background: #0066ff"],
[style*="background-color: #007bff"], [style*="background: #007bff"],
[style*="background-color: #6f42c1"], [style*="background: #6f42c1"],
[style*="background-color: #5a67d8"], [style*="background: #5a67d8"],
[style*="background-color: #4299e1"], [style*="background: #4299e1"],
[style*="background-color: #3182ce"], [style*="background: #3182ce"],
[style*="background-color: #2b6cb0"], [style*="background: #2b6cb0"] {
    background-color: #252638 !important;
    background: #252638 !important;
    color: #ffffff !important;
}

/* ===== REPLACE BLUE/PURPLE TEXT COLORS ===== */
.text-blue, .text-primary, .text-info,
[class*="text-blue"], [class*="text-purple"], [class*="text-indigo"],
[style*="color: blue"], [style*="color: #0066ff"],
[style*="color: #007bff"], [style*="color: #6f42c1"],
[style*="color: #5a67d8"], [style*="color: #4299e1"],
[style*="color: #3182ce"], [style*="color: #2b6cb0"] {
    color: #cf2e2e !important;
}

/* ===== REPLACE BLUE/PURPLE BORDERS ===== */
.border-blue, .border-primary, .border-info,
[class*="border-blue"], [class*="border-purple"], [class*="border-indigo"],
[style*="border-color: blue"], [style*="border-color: #0066ff"],
[style*="border-color: #007bff"], [style*="border-color: #6f42c1"] {
    border-color: #252638 !important;
}

/* ===== FIX BUTTON COLORS ===== */
.btn-primary, .btn-info, .btn-blue {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.btn-primary:hover, .btn-info:hover, .btn-blue:hover {
    background-color: #b82626 !important;
    border-color: #b82626 !important;
    color: #ffffff !important;
}

.btn-outline-primary, .btn-outline-info, .btn-outline-blue {
    background-color: transparent !important;
    border-color: #cf2e2e !important;
    color: #cf2e2e !important;
}

.btn-outline-primary:hover, .btn-outline-info:hover, .btn-outline-blue:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== FIX WHITE TEXT VISIBILITY ===== */
/* Make white text dark on white/light backgrounds */
.text-white, .text-light {
    color: #242424 !important;
}

/* Specific fixes for white backgrounds */
.bg-white .text-white,
.bg-light .text-white,
.bg-white .text-light,
.bg-light .text-light,
[style*="background-color: white"] .text-white,
[style*="background-color: #fff"] .text-white,
[style*="background-color: #ffffff"] .text-white,
[style*="background: white"] .text-white,
[style*="background: #fff"] .text-white,
[style*="background: #ffffff"] .text-white {
    color: #242424 !important;
}

/* Fix white text in cards on white backgrounds */
.card .text-white,
.card-body .text-white,
.card-header .text-white,
.card-footer .text-white {
    color: #242424 !important;
}

/* Fix white text in containers */
.container .text-white,
.container-fluid .text-white,
.row .text-white,
.col .text-white,
[class*="col-"] .text-white {
    color: #242424 !important;
}

/* ===== FIX LINK COLORS ===== */
a.text-white, a.text-light {
    color: #cf2e2e !important;
}

a.text-white:hover, a.text-light:hover {
    color: #b82626 !important;
}

/* ===== FIX NAVBAR COLORS ===== */
.navbar-light .navbar-brand,
.navbar-light .navbar-nav .nav-link {
    color: #242424 !important;
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus {
    color: #cf2e2e !important;
}

/* ===== FIX ALERT COLORS ===== */
.alert-primary, .alert-info {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: #cf2e2e !important;
    color: #cf2e2e !important;
}

/* ===== FIX BADGE COLORS ===== */
.badge.bg-primary, .badge.bg-info {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== FIX PROGRESS BAR COLORS ===== */
.progress-bar, .progress-bar-primary {
    background-color: #cf2e2e !important;
}

/* ===== FIX TABLE COLORS ===== */
.table-primary {
    background-color: rgba(207, 46, 46, 0.1) !important;
    border-color: #cf2e2e !important;
}

/* ===== FIX FORM COLORS ===== */
.form-control:focus {
    border-color: #cf2e2e !important;
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
}

.form-check-input:checked {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
}

/* ===== FIX DROPDOWN COLORS ===== */
.dropdown-item:hover,
.dropdown-item:focus {
    background-color: #f7f7f7 !important;
    color: #242424 !important;
}

.dropdown-item.active {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== FIX PAGINATION COLORS ===== */
.page-link {
    color: #cf2e2e !important;
}

.page-link:hover {
    color: #b82626 !important;
    background-color: #f7f7f7 !important;
    border-color: #cf2e2e !important;
}

.page-item.active .page-link {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== FIX MODAL COLORS ===== */
.modal-header {
    border-bottom-color: #e5e5e5 !important;
}

.modal-footer {
    border-top-color: #e5e5e5 !important;
}

/* ===== FIX TOOLTIP COLORS ===== */
.tooltip-inner {
    background-color: #252638 !important;
    color: #ffffff !important;
}

/* ===== FIX POPOVER COLORS ===== */
.popover {
    border-color: #e5e5e5 !important;
}

.popover-header {
    background-color: #f7f7f7 !important;
    border-bottom-color: #e5e5e5 !important;
    color: #242424 !important;
}

/* ===== SPECIFIC OVERRIDES FOR COMMON PATTERNS ===== */
/* Override any remaining blue gradients */
[style*="linear-gradient"][style*="blue"],
[style*="linear-gradient"][style*="#0066ff"],
[style*="linear-gradient"][style*="#007bff"] {
    background: linear-gradient(135deg, #cf2e2e 0%, #b82626 100%) !important;
}

/* Override box shadows with blue colors */
[style*="box-shadow"][style*="blue"],
[style*="box-shadow"][style*="#0066ff"],
[style*="box-shadow"][style*="#007bff"] {
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* ===== ENSURE DARK FOOTER ===== */
footer, .footer, .main-footer {
    background-color: #252638 !important;
    color: #ffffff !important;
}

footer .text-white, .footer .text-white, .main-footer .text-white {
    color: #ffffff !important;
}
