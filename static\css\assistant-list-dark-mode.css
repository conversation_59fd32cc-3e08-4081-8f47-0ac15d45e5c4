/**
 * Dark Mode Styling for Assistant Lists
 * Applies to all assistant list pages (private, public, community)
 */

/* Common dark mode styles for assistant lists */
html[data-theme="dark"] .container,
[data-theme="dark"] .container,
body[data-theme="dark"] .container {
  color: #ffffff !important;
}

/* Filter form in dark mode */
html[data-theme="dark"] .filter-form,
[data-theme="dark"] .filter-form,
body[data-theme="dark"] .filter-form {
  background-color: #1e1e1e !important;
  border-color: #333333 !important;
  color: #ffffff !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Input group in dark mode */
html[data-theme="dark"] .filter-form .input-group,
[data-theme="dark"] .filter-form .input-group,
body[data-theme="dark"] .filter-form .input-group {
  background-color: transparent !important;
  border: none !important;
}

/* Input group text in dark mode */
html[data-theme="dark"] .filter-form .input-group-text,
[data-theme="dark"] .filter-form .input-group-text,
body[data-theme="dark"] .filter-form .input-group-text {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

/* Search input in dark mode */
html[data-theme="dark"] .filter-form .form-control,
[data-theme="dark"] .filter-form .form-control,
body[data-theme="dark"] .filter-form .form-control {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

/* Search button in dark mode */
html[data-theme="dark"] .filter-form .btn-primary,
[data-theme="dark"] .filter-form .btn-primary,
body[data-theme="dark"] .filter-form .btn-primary {
  background-color: #0077ff !important;
  border-color: #0066dd !important;
  color: #ffffff !important;
}

/* Clear search button in dark mode */
html[data-theme="dark"] .filter-form .btn-outline-secondary,
[data-theme="dark"] .filter-form .btn-outline-secondary,
body[data-theme="dark"] .filter-form .btn-outline-secondary {
  border-color: #444444 !important;
  color: #ffffff !important;
}

html[data-theme="dark"] .filter-form .btn-outline-secondary:hover,
[data-theme="dark"] .filter-form .btn-outline-secondary:hover,
body[data-theme="dark"] .filter-form .btn-outline-secondary:hover {
  background-color: #333333 !important;
}

/* Input fields in dark mode */
html[data-theme="dark"] .form-control,
[data-theme="dark"] .form-control,
body[data-theme="dark"] .form-control {
  background-color: #333333 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

/* Input group text in dark mode */
html[data-theme="dark"] .input-group-text,
[data-theme="dark"] .input-group-text,
body[data-theme="dark"] .input-group-text {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

/* Select dropdown in dark mode */
html[data-theme="dark"] .form-select,
[data-theme="dark"] .form-select,
body[data-theme="dark"] .form-select {
  background-color: #333333 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
}

/* Tier sections in dark mode */
html[data-theme="dark"] .tier-section,
[data-theme="dark"] .tier-section,
body[data-theme="dark"] .tier-section {
  background-color: #1e1e1e !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

/* Featured section in dark mode */
html[data-theme="dark"] .featured-section,
[data-theme="dark"] .featured-section,
body[data-theme="dark"] .featured-section {
  background-color: #1e1e1e !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

/* List group items (assistant cards) in dark mode */
html[data-theme="dark"] .list-group-item,
[data-theme="dark"] .list-group-item,
body[data-theme="dark"] .list-group-item {
  background-color: #252525 !important;
  border-color: #333333 !important;
  color: #ffffff !important;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2) !important;
}

/* List group item hover in dark mode */
html[data-theme="dark"] .list-group-item:hover,
[data-theme="dark"] .list-group-item:hover,
body[data-theme="dark"] .list-group-item:hover {
  background-color: #2a2a2a !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3) !important;
}

/* Logo container in dark mode */
html[data-theme="dark"] .logo-container,
[data-theme="dark"] .logo-container,
body[data-theme="dark"] .logo-container {
  background-color: #333333 !important;
  border-color: #444444 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Logo placeholder in dark mode */
html[data-theme="dark"] .logo-placeholder,
[data-theme="dark"] .logo-placeholder,
body[data-theme="dark"] .logo-placeholder {
  background-color: rgba(30, 30, 30, 0.5) !important;
  color: #3b7dd8 !important;
}

/* Links in dark mode */
html[data-theme="dark"] a:not(.btn),
[data-theme="dark"] a:not(.btn),
body[data-theme="dark"] a:not(.btn) {
  color: #3b7dd8 !important;
}

html[data-theme="dark"] a:not(.btn):hover,
[data-theme="dark"] a:not(.btn):hover,
body[data-theme="dark"] a:not(.btn):hover {
  color: #5a9aef !important;
}

/* Text muted in dark mode */
html[data-theme="dark"] .text-muted,
[data-theme="dark"] .text-muted,
body[data-theme="dark"] .text-muted {
  color: #aaaaaa !important;
}

/* Buttons in dark mode */
html[data-theme="dark"] .btn-outline-secondary,
[data-theme="dark"] .btn-outline-secondary,
body[data-theme="dark"] .btn-outline-secondary {
  color: #ffffff !important;
  border-color: #444444 !important;
}

html[data-theme="dark"] .btn-outline-secondary:hover,
[data-theme="dark"] .btn-outline-secondary:hover,
body[data-theme="dark"] .btn-outline-secondary:hover {
  background-color: #333333 !important;
  color: #ffffff !important;
}

/* Alert in dark mode */
html[data-theme="dark"] .alert-light,
[data-theme="dark"] .alert-light,
body[data-theme="dark"] .alert-light {
  background-color: #252525 !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

html[data-theme="dark"] .alert-info,
[data-theme="dark"] .alert-info,
body[data-theme="dark"] .alert-info {
  background-color: #1a3a57 !important;
  border-color: #2a5a87 !important;
  color: #ffffff !important;
}

/* Badges in dark mode */
html[data-theme="dark"] .badge.bg-light,
[data-theme="dark"] .badge.bg-light,
body[data-theme="dark"] .badge.bg-light {
  background-color: #333333 !important;
  color: #ffffff !important;
}

html[data-theme="dark"] .badge.bg-secondary,
[data-theme="dark"] .badge.bg-secondary,
body[data-theme="dark"] .badge.bg-secondary {
  background-color: #555555 !important;
  color: #ffffff !important;
}

/* Featured carousel in dark mode */
html[data-theme="dark"] .featured-carousel-container,
[data-theme="dark"] .featured-carousel-container,
body[data-theme="dark"] .featured-carousel-container {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
}

html[data-theme="dark"] .featured-item-wrapper,
[data-theme="dark"] .featured-item-wrapper,
body[data-theme="dark"] .featured-item-wrapper {
  background-color: #252525 !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3) !important;
}

html[data-theme="dark"] .featured-item-wrapper:hover,
[data-theme="dark"] .featured-item-wrapper:hover,
body[data-theme="dark"] .featured-item-wrapper:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4) !important;
}

/* Featured assistant cards - exact copy from company list styling */
html[data-theme="dark"] .featured-assistant-item,
[data-theme="dark"] .featured-assistant-item,
body[data-theme="dark"] .featured-assistant-item {
  /* Solid dark background to define card area - exact copy from company list */
  background: linear-gradient(145deg,
      rgba(60, 60, 60, 0.95) 0%,
      rgba(45, 45, 45, 0.9) 50%,
      rgba(30, 30, 30, 0.85) 100%) !important;
  background-color: rgba(50, 50, 50, 0.9) !important;

  /* Enhanced glass border effect - exact copy from company list */
  border: 3px solid rgba(255, 255, 255, 0.2) !important;
  border-top: 3px solid rgba(255, 255, 255, 0.4) !important;
  border-left: 3px solid rgba(255, 255, 255, 0.4) !important;
  border-right: 3px solid rgba(0, 0, 0, 0.2) !important;
  border-bottom: 3px solid rgba(0, 0, 0, 0.2) !important;

  /* Enhanced shadow for depth - exact copy from company list */
  box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.5),
      inset 0 3px 0 rgba(255, 255, 255, 0.3),
      inset 3px 0 0 rgba(255, 255, 255, 0.2),
      inset 0 -2px 0 rgba(0, 0, 0, 0.2),
      0 0 20px rgba(255, 255, 255, 0.05) !important;

  /* Glass effect backdrop filter - exact copy from company list */
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
}

html[data-theme="dark"] .featured-assistant-item:hover,
[data-theme="dark"] .featured-assistant-item:hover,
body[data-theme="dark"] .featured-assistant-item:hover {
  /* Enhanced hover effect - exact copy from company list */
  background: linear-gradient(145deg,
      rgba(70, 70, 70, 0.95) 0%,
      rgba(55, 55, 55, 0.9) 50%,
      rgba(40, 40, 40, 0.85) 100%) !important;
  background-color: rgba(60, 60, 60, 0.9) !important;

  /* Enhanced hover border - exact copy from company list */
  border-top: 3px solid rgba(255, 255, 255, 0.6) !important;
  border-left: 3px solid rgba(255, 255, 255, 0.6) !important;
  border-right: 3px solid rgba(0, 0, 0, 0.3) !important;
  border-bottom: 3px solid rgba(0, 0, 0, 0.3) !important;

  /* Enhanced hover shadow - exact copy from company list */
  box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.6),
      inset 0 4px 0 rgba(255, 255, 255, 0.4),
      inset 4px 0 0 rgba(255, 255, 255, 0.3),
      inset 0 -3px 0 rgba(0, 0, 0, 0.3),
      0 0 25px rgba(255, 255, 255, 0.1) !important;
}

html[data-theme="dark"] .featured-carousel-item .logo-container,
[data-theme="dark"] .featured-carousel-item .logo-container,
body[data-theme="dark"] .featured-carousel-item .logo-container {
  background-color: #333333 !important;
  border-color: #444444 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
}

html[data-theme="dark"] .featured-carousel-item .item-info h5,
[data-theme="dark"] .featured-carousel-item .item-info h5,
body[data-theme="dark"] .featured-carousel-item .item-info h5 {
  color: #3b7dd8 !important;
}

html[data-theme="dark"] .featured-carousel-item .item-info p,
[data-theme="dark"] .featured-carousel-item .item-info p,
body[data-theme="dark"] .featured-carousel-item .item-info p {
  color: #cccccc !important;
}

/* Like button in dark mode */
html[data-theme="dark"] .like-button,
[data-theme="dark"] .like-button,
body[data-theme="dark"] .like-button {
  background-color: #333333 !important;
  border-color: #444444 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Modals in dark mode */
html[data-theme="dark"] .modal-content,
[data-theme="dark"] .modal-content,
body[data-theme="dark"] .modal-content {
  background-color: #252525 !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

html[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-header,
body[data-theme="dark"] .modal-header {
  border-color: #333333 !important;
}

html[data-theme="dark"] .modal-footer,
[data-theme="dark"] .modal-footer,
body[data-theme="dark"] .modal-footer {
  border-color: #333333 !important;
}

/* Folder filter buttons in dark mode */
html[data-theme="dark"] .folder-filter-buttons,
[data-theme="dark"] .folder-filter-buttons,
body[data-theme="dark"] .folder-filter-buttons {
  border-color: #333333 !important;
}

html[data-theme="dark"] .btn-link,
[data-theme="dark"] .btn-link,
body[data-theme="dark"] .btn-link {
  color: #3b7dd8 !important;
}

html[data-theme="dark"] .btn-link.active,
[data-theme="dark"] .btn-link.active,
body[data-theme="dark"] .btn-link.active {
  color: #5a9aef !important;
  text-decoration: underline !important;
}

/* Dropdown menu in dark mode */
html[data-theme="dark"] .dropdown-menu,
[data-theme="dark"] .dropdown-menu,
body[data-theme="dark"] .dropdown-menu {
  background-color: #252525 !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

html[data-theme="dark"] .dropdown-item,
[data-theme="dark"] .dropdown-item,
body[data-theme="dark"] .dropdown-item {
  color: #ffffff !important;
}

html[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:hover,
body[data-theme="dark"] .dropdown-item:hover {
  background-color: #333333 !important;
}

html[data-theme="dark"] .dropdown-divider,
[data-theme="dark"] .dropdown-divider,
body[data-theme="dark"] .dropdown-divider {
  border-color: #444444 !important;
}

/* Rating stars in dark mode */
html[data-theme="dark"] .star-rating .stars .bi-star,
[data-theme="dark"] .star-rating .stars .bi-star,
body[data-theme="dark"] .star-rating .stars .bi-star {
  color: #555555 !important;
}

/* Background color for body in dark mode */
html[data-theme="dark"] body.bg-light,
[data-theme="dark"] body.bg-light,
body[data-theme="dark"] body.bg-light {
  background-color: #121212 !important;
}
