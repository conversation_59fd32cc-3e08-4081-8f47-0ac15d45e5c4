#!/usr/bin/env python
"""
Test script to validate performance optimizations are working correctly.
Run this after enabling optimizations to verify everything is functioning.
"""

import os
import sys
import time
import django
from pathlib import Path

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.core.cache import cache
from django.db import connection
from django.test.utils import override_settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class OptimizationTester:
    """Test suite for performance optimizations."""
    
    def __init__(self):
        self.test_results = []
        self.errors = []
    
    def run_all_tests(self):
        """Run all optimization tests."""
        logger.info("🚀 Starting Performance Optimization Tests")
        logger.info("=" * 60)
        
        # Test each component
        self.test_cache_system()
        self.test_llm_cache()
        self.test_optimized_queries()
        self.test_memory_optimization()
        self.test_database_indexes()
        self.test_view_integration()
        
        # Display results
        self.display_results()
    
    def test_cache_system(self):
        """Test basic cache functionality."""
        logger.info("🔧 Testing Cache System...")
        
        try:
            # Test basic cache operations
            test_key = "optimization_test"
            test_value = {"test": "data", "timestamp": time.time()}
            
            # Set cache
            cache.set(test_key, test_value, 60)
            
            # Get cache
            cached_value = cache.get(test_key)
            
            if cached_value == test_value:
                self.test_results.append("✅ Basic cache operations working")
            else:
                self.errors.append("❌ Basic cache operations failed")
            
            # Test cache deletion
            cache.delete(test_key)
            if cache.get(test_key) is None:
                self.test_results.append("✅ Cache deletion working")
            else:
                self.errors.append("❌ Cache deletion failed")
                
        except Exception as e:
            self.errors.append(f"❌ Cache system error: {e}")
    
    def test_llm_cache(self):
        """Test LLM caching system."""
        logger.info("🤖 Testing LLM Cache System...")
        
        try:
            from assistants.llm_cache import LLMCache, get_cached_llm_response, cache_llm_response
            
            # Test cache key generation
            messages = [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Hello, how are you?"}
            ]
            
            cache_key = LLMCache._generate_cache_key(1, messages, 0.7, 1000)
            if cache_key and cache_key.startswith('llm_cache:'):
                self.test_results.append("✅ LLM cache key generation working")
            else:
                self.errors.append("❌ LLM cache key generation failed")
            
            # Test message normalization
            normalized = LLMCache._normalize_messages(messages)
            if len(normalized) == 2 and normalized[0]['role'] == 'system':
                self.test_results.append("✅ LLM message normalization working")
            else:
                self.errors.append("❌ LLM message normalization failed")
            
            # Test compression
            test_data = {"content": "Test response", "token_count": 10}
            compressed = LLMCache._compress_data(test_data)
            decompressed = LLMCache._decompress_data(compressed)
            
            if decompressed == test_data:
                self.test_results.append("✅ LLM data compression working")
            else:
                self.errors.append("❌ LLM data compression failed")
            
            # Test cache stats
            stats = LLMCache.get_cache_stats()
            if 'cache_backend' in stats:
                self.test_results.append("✅ LLM cache stats working")
            else:
                self.errors.append("❌ LLM cache stats failed")
                
        except ImportError as e:
            self.errors.append(f"❌ LLM cache import failed: {e}")
        except Exception as e:
            self.errors.append(f"❌ LLM cache error: {e}")
    
    def test_optimized_queries(self):
        """Test optimized database queries."""
        logger.info("🗃️ Testing Optimized Queries...")
        
        try:
            from assistants.optimized_queries import (
                OptimizedAssistantQueries, 
                OptimizedInteractionQueries,
                get_optimized_company_assistants
            )
            
            # Test import success
            self.test_results.append("✅ Optimized queries import successful")
            
            # Test query class methods exist
            methods_to_check = [
                'get_company_assistants_optimized',
                'get_assistant_with_stats',
                'get_community_assistants_optimized'
            ]
            
            for method in methods_to_check:
                if hasattr(OptimizedAssistantQueries, method):
                    self.test_results.append(f"✅ {method} method available")
                else:
                    self.errors.append(f"❌ {method} method missing")
            
            # Test convenience functions
            if callable(get_optimized_company_assistants):
                self.test_results.append("✅ Convenience functions available")
            else:
                self.errors.append("❌ Convenience functions missing")
                
        except ImportError as e:
            self.errors.append(f"❌ Optimized queries import failed: {e}")
        except Exception as e:
            self.errors.append(f"❌ Optimized queries error: {e}")
    
    def test_memory_optimization(self):
        """Test memory optimization components."""
        logger.info("💾 Testing Memory Optimization...")
        
        try:
            from assistants.memory_optimized import (
                LazyQuerysetIterator,
                CompressedDataCache,
                MemoryEfficientPaginator,
                CircularBuffer
            )
            
            # Test circular buffer
            buffer = CircularBuffer(maxsize=5)
            for i in range(10):
                buffer.append(f"item_{i}")
            
            if len(buffer) == 5:
                self.test_results.append("✅ Circular buffer working correctly")
            else:
                self.errors.append("❌ Circular buffer size limit failed")
            
            # Test compressed cache
            test_data = {"large_data": "x" * 1000, "numbers": list(range(100))}
            compressed = CompressedDataCache.compress_data(test_data)
            decompressed = CompressedDataCache.decompress_data(compressed)
            
            if decompressed == test_data and len(compressed) < len(str(test_data)):
                self.test_results.append("✅ Compressed data cache working")
            else:
                self.errors.append("❌ Compressed data cache failed")
            
            self.test_results.append("✅ Memory optimization components available")
            
        except ImportError as e:
            self.errors.append(f"❌ Memory optimization import failed: {e}")
        except Exception as e:
            self.errors.append(f"❌ Memory optimization error: {e}")
    
    def test_database_indexes(self):
        """Test database indexes are created."""
        logger.info("📊 Testing Database Indexes...")
        
        try:
            with connection.cursor() as cursor:
                # Check if some key indexes exist (PostgreSQL/SQLite compatible)
                index_checks = [
                    "SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_assistant%'",
                    "SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_interaction%'"
                ]
                
                indexes_found = 0
                for check in index_checks:
                    try:
                        cursor.execute(check)
                        results = cursor.fetchall()
                        indexes_found += len(results)
                    except Exception:
                        # Might be PostgreSQL or other DB
                        pass
                
                if indexes_found > 0:
                    self.test_results.append(f"✅ Found {indexes_found} performance indexes")
                else:
                    # Try alternative check for PostgreSQL
                    try:
                        cursor.execute("""
                            SELECT indexname FROM pg_indexes 
                            WHERE indexname LIKE 'idx_assistant%' OR indexname LIKE 'idx_interaction%'
                        """)
                        pg_indexes = cursor.fetchall()
                        if pg_indexes:
                            self.test_results.append(f"✅ Found {len(pg_indexes)} PostgreSQL indexes")
                        else:
                            self.test_results.append("⚠️ Database indexes check inconclusive")
                    except Exception:
                        self.test_results.append("⚠️ Database indexes check inconclusive (may need migration)")
                        
        except Exception as e:
            self.errors.append(f"❌ Database index check error: {e}")
    
    def test_view_integration(self):
        """Test view integration with optimized functions."""
        logger.info("🌐 Testing View Integration...")
        
        try:
            from assistants.llm_utils_optimized import generate_assistant_response_optimized
            
            # Test function is callable
            if callable(generate_assistant_response_optimized):
                self.test_results.append("✅ Optimized LLM function available")
            else:
                self.errors.append("❌ Optimized LLM function not callable")
            
            # Check if views.py contains optimized imports
            views_file = project_dir / 'assistants' / 'views.py'
            if views_file.exists():
                content = views_file.read_text()
                if 'generate_assistant_response_optimized' in content:
                    self.test_results.append("✅ Views updated with optimized functions")
                else:
                    self.errors.append("❌ Views not updated with optimized functions")
            else:
                self.errors.append("❌ Views file not found")
                
        except ImportError as e:
            self.errors.append(f"❌ View integration import failed: {e}")
        except Exception as e:
            self.errors.append(f"❌ View integration error: {e}")
    
    def display_results(self):
        """Display test results."""
        logger.info("\n" + "=" * 60)
        logger.info("🎯 OPTIMIZATION TEST RESULTS")
        logger.info("=" * 60)
        
        if self.test_results:
            logger.info("✅ SUCCESSFUL TESTS:")
            for result in self.test_results:
                logger.info(f"  {result}")
        
        if self.errors:
            logger.info("\n❌ FAILED TESTS:")
            for error in self.errors:
                logger.info(f"  {error}")
        
        # Summary
        total_tests = len(self.test_results) + len(self.errors)
        success_rate = (len(self.test_results) / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"\n📊 SUMMARY:")
        logger.info(f"  Total Tests: {total_tests}")
        logger.info(f"  Successful: {len(self.test_results)}")
        logger.info(f"  Failed: {len(self.errors)}")
        logger.info(f"  Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            logger.info("\n🎉 OPTIMIZATION STATUS: EXCELLENT ✅")
            logger.info("Your optimizations are working correctly!")
        elif success_rate >= 60:
            logger.info("\n⚠️ OPTIMIZATION STATUS: GOOD ⚠️")
            logger.info("Most optimizations working, some issues to address.")
        else:
            logger.info("\n❌ OPTIMIZATION STATUS: NEEDS ATTENTION ❌")
            logger.info("Several issues found, please review and fix.")
        
        logger.info("\n" + "=" * 60)
        
        return len(self.errors) == 0


def main():
    """Main function to run optimization tests."""
    logger.info("Starting Performance Optimization Validation...")
    
    tester = OptimizationTester()
    success = tester.run_all_tests()
    
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
