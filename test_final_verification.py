#!/usr/bin/env python
"""
Final verification test for company creation and settings data persistence.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from accounts.models import Company, CompanyInformation
import uuid

def test_final_verification():
    """Final verification of the fix"""
    print("🎯 FINAL VERIFICATION TEST")
    print("=" * 50)
    
    # Create test user
    username = f"final_test_{uuid.uuid4().hex[:6]}"
    user = User.objects.create_user(
        username=username,
        email=f'{username}@test.com',
        password='testpass123'
    )
    print(f"✅ Created user: {user.username}")
    
    # Create Django test client and log in
    client = Client()
    client.login(username=username, password='testpass123')
    
    # Test data
    form_data = {
        'name': f'Final Test Company {uuid.uuid4().hex[:4]}',
        'entity_type': 'company',
        'mission': 'Final verification mission',
        'website': 'https://final-test.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '******-FINAL',
        'timezone': 'UTC',
        'language': 'en',
        'industry': 'Final Testing',
        'city': 'Final City',
        'list_in_directory': True
    }
    
    try:
        # Step 1: Create company
        print("\n📝 Creating company...")
        create_url = reverse('accounts:company_create')
        response = client.post(create_url, form_data)
        
        if response.status_code != 302:
            print(f"❌ Company creation failed. Status: {response.status_code}")
            return False
        
        # Step 2: Find and verify company
        print("🔍 Verifying company data...")
        company = Company.objects.filter(owner=user).latest('created_at')
        company_info = CompanyInformation.objects.get(company=company)
        
        # Check key fields
        checks = [
            ('mission', company_info.mission, form_data['mission']),
            ('website', company_info.website, form_data['website']),
            ('contact_email', company_info.contact_email, form_data['contact_email']),
            ('industry', company_info.industry, form_data['industry']),
            ('city', company_info.city, form_data['city']),
        ]
        
        all_good = True
        for field, saved, expected in checks:
            if saved == expected:
                print(f"  ✅ {field}: '{saved}'")
            else:
                print(f"  ❌ {field}: '{saved}' (expected: '{expected}')")
                all_good = False
        
        # Step 3: Test settings page access
        print("\n⚙️ Testing settings page...")
        settings_url = reverse('accounts:company_settings', kwargs={'company_id': company.id})
        response = client.get(settings_url)
        
        if response.status_code != 200:
            print(f"❌ Settings page not accessible. Status: {response.status_code}")
            all_good = False
        else:
            print("✅ Settings page accessible")
        
        # Step 4: Verify data persistence after settings access
        print("🔍 Verifying data persistence...")
        company_info.refresh_from_db()
        
        for field, _, expected in checks:
            saved = getattr(company_info, field)
            if saved == expected:
                print(f"  ✅ {field}: Still preserved")
            else:
                print(f"  ❌ {field}: Lost! Now '{saved}' (was '{expected}')")
                all_good = False
        
        # Clean up
        company.delete()
        user.delete()
        
        if all_good:
            print("\n🎉 FINAL VERIFICATION PASSED!")
            print("✅ Company creation data saves correctly")
            print("✅ Settings form can find and load saved data")
            print("✅ Data persists after accessing settings")
            return True
        else:
            print("\n❌ FINAL VERIFICATION FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

if __name__ == "__main__":
    success = test_final_verification()
    sys.exit(0 if success else 1)
