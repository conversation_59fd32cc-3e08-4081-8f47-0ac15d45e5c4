"""
Optimized signals for cPanel environments.
Reduces database queries and improves performance.
"""

import logging
from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from django.contrib.auth.models import Group, Permission
from django.core.cache import cache
from django.db import transaction
from guardian.shortcuts import assign_perm
from .models import Company, CompanyInformation
from directory.models import CompanyListing

logger = logging.getLogger(__name__)

# Cache timeout for signal-related operations
SIGNAL_CACHE_TIMEOUT = 300  # 5 minutes


@receiver(post_save, sender=Company)
def optimized_company_post_save(sender, instance, created, **kwargs):
    """
    Optimized company post-save signal with reduced database queries.
    """
    # Skip if this is an update operation that doesn't need signal processing
    if not created and not _should_process_company_update(instance):
        return
    
    # Use transaction to batch operations
    with transaction.atomic():
        if created:
            _assign_company_permissions_optimized(instance)
        
        # Update company listing efficiently
        _update_company_listing_optimized(instance)
        
        # Generate QR code asynchronously if needed
        if _should_generate_qr_code(instance, created):
            _schedule_qr_generation(instance)


def _should_process_company_update(instance):
    """
    Check if company update requires signal processing.
    """
    # Cache the previous state to compare
    cache_key = f"company_state:{instance.id}"
    previous_state = cache.get(cache_key)
    
    current_state = {
        'name': instance.name,
        'is_public': instance.is_public,
        'is_active': instance.is_active,
        'slug': instance.slug,
    }
    
    # Cache current state
    cache.set(cache_key, current_state, SIGNAL_CACHE_TIMEOUT)
    
    # If no previous state, process the update
    if previous_state is None:
        return True
    
    # Check if relevant fields changed
    return previous_state != current_state


def _assign_company_permissions_optimized(instance):
    """
    Assign permissions to company with optimized queries.
    """
    if not instance.owner:
        logger.warning(f"Company '{instance.name}' created without owner")
        return
    
    try:
        # Get permissions and groups in bulk
        permissions_cache_key = "company_permissions_bulk"
        groups_cache_key = "company_groups_bulk"
        
        permissions = cache.get(permissions_cache_key)
        groups = cache.get(groups_cache_key)
        
        if permissions is None:
            permissions = list(Permission.objects.filter(
                codename__in=[
                    'view_company', 'change_company', 'delete_company',
                    'manage_company_assistants'
                ]
            ).select_related('content_type'))
            cache.set(permissions_cache_key, permissions, 3600)  # 1 hour
        
        if groups is None:
            groups = {
                group.name: group for group in Group.objects.filter(
                    name__in=['Company Administrators', 'Company Members']
                )
            }
            cache.set(groups_cache_key, groups, 3600)  # 1 hour
        
        # Assign owner permissions
        owner_perms = ['view_company', 'change_company', 'delete_company']
        for perm in permissions:
            if perm.codename in owner_perms:
                assign_perm(perm, instance.owner, instance)
        
        # Assign group permissions
        admin_group = groups.get('Company Administrators')
        member_group = groups.get('Company Members')
        
        if admin_group:
            for perm in permissions:
                if perm.codename == 'manage_company_assistants':
                    assign_perm(perm, admin_group, instance)
        
        if member_group:
            for perm in permissions:
                if perm.codename == 'view_company':
                    assign_perm(perm, member_group, instance)
        
        logger.info(f"Assigned permissions for company '{instance.name}'")
        
    except Exception as e:
        logger.error(f"Error assigning permissions for company '{instance.name}': {e}")


def _update_company_listing_optimized(instance):
    """
    Update company listing with optimized database operations.
    """
    try:
        should_be_listed = instance.is_public and instance.is_active
        
        # Use get_or_create to avoid race conditions
        listing, created = CompanyListing.objects.get_or_create(
            company=instance,
            defaults={
                'is_listed': should_be_listed,
                'name': instance.name,
                'short_description': _get_short_description(instance),
                'long_description': _get_long_description(instance),
            }
        )
        
        if not created:
            # Update existing listing only if needed
            update_fields = []
            
            if listing.is_listed != should_be_listed:
                listing.is_listed = should_be_listed
                update_fields.append('is_listed')
            
            if listing.name != instance.name:
                listing.name = instance.name
                update_fields.append('name')
            
            new_short_desc = _get_short_description(instance)
            if listing.short_description != new_short_desc:
                listing.short_description = new_short_desc
                update_fields.append('short_description')
            
            new_long_desc = _get_long_description(instance)
            if listing.long_description != new_long_desc:
                listing.long_description = new_long_desc
                update_fields.append('long_description')
            
            if update_fields:
                listing.save(update_fields=update_fields)
                logger.info(f"Updated company listing for '{instance.name}': {update_fields}")
        
    except Exception as e:
        logger.error(f"Error updating company listing for '{instance.name}': {e}")


def _get_short_description(company):
    """Get short description for company listing."""
    try:
        if hasattr(company, 'information') and company.information:
            from django.utils.html import strip_tags
            from django.utils.text import Truncator
            plain_desc = strip_tags(company.information.description or '')
            return Truncator(plain_desc).chars(250, truncate='...')
    except Exception:
        pass
    return f"Company: {company.name}"


def _get_long_description(company):
    """Get long description for company listing."""
    try:
        if hasattr(company, 'information') and company.information:
            return company.information.description or ''
    except Exception:
        pass
    return f"Company: {company.name}"


def _should_generate_qr_code(instance, created):
    """
    Check if QR code should be generated.
    """
    if created:
        return True
    
    if not instance.qr_code:
        return True
    
    if instance.slug and instance.qr_code.name:
        return instance.slug not in instance.qr_code.name
    
    return False


def _schedule_qr_generation(instance):
    """
    Schedule QR code generation (can be made async in the future).
    """
    try:
        from django.urls import reverse
        from utils.qr_generator import generate_model_qr_code
        
        # Generate slug if missing
        if not instance.slug:
            from django.utils.text import slugify
            instance.slug = slugify(instance.name)
            Company.objects.filter(pk=instance.pk).update(slug=instance.slug)
        
        # Generate QR code
        url_path = reverse('accounts:public_company_detail', kwargs={'slug': instance.slug})
        success = generate_model_qr_code(instance, url_path, field_name='qr_code')
        
        if success:
            Company.objects.filter(pk=instance.pk).update(
                qr_code=instance.qr_code,
                updated_at=instance.updated_at
            )
            logger.info(f"Generated QR code for company '{instance.name}'")
        else:
            logger.error(f"Failed to generate QR code for company '{instance.name}'")
            
    except Exception as e:
        logger.error(f"Error generating QR code for company '{instance.name}': {e}")


@receiver(post_save, sender=CompanyInformation)
def optimized_company_info_post_save(sender, instance, created, **kwargs):
    """
    Optimized company information post-save signal.
    """
    try:
        if instance.company:
            _update_company_listing_optimized(instance.company)
    except Exception as e:
        logger.error(f"Error in company info post-save for company info {instance.pk}: {e}")


@receiver(pre_delete, sender=Company)
def optimized_company_pre_delete(sender, instance, **kwargs):
    """
    Clean up related data before company deletion.
    """
    try:
        # Clear cache entries
        cache_keys = [
            f"company_state:{instance.id}",
            f"company_listing:{instance.id}",
        ]
        cache.delete_many(cache_keys)
        
        # Delete company listing
        CompanyListing.objects.filter(company=instance).delete()
        
        logger.info(f"Cleaned up data for deleted company '{instance.name}'")
        
    except Exception as e:
        logger.error(f"Error cleaning up company '{instance.name}': {e}")


# Utility functions for cache management
def clear_company_cache(company_id):
    """Clear all cache entries for a company."""
    cache_keys = [
        f"company_state:{company_id}",
        f"company_listing:{company_id}",
        f"company_perms:{company_id}",
    ]
    cache.delete_many(cache_keys)


def warm_company_cache(company):
    """Pre-populate cache for a company."""
    cache_key = f"company_state:{company.id}"
    state = {
        'name': company.name,
        'is_public': company.is_public,
        'is_active': company.is_active,
        'slug': company.slug,
    }
    cache.set(cache_key, state, SIGNAL_CACHE_TIMEOUT)


# Batch operations for multiple companies
def batch_update_company_listings(company_ids):
    """Update multiple company listings efficiently."""
    companies = Company.objects.filter(id__in=company_ids).select_related('information')
    
    for company in companies:
        _update_company_listing_optimized(company)


def batch_assign_permissions(company_ids):
    """Assign permissions to multiple companies efficiently."""
    companies = Company.objects.filter(id__in=company_ids).select_related('owner')
    
    for company in companies:
        if company.owner:
            _assign_company_permissions_optimized(company)
