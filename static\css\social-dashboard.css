/* Social Media Dashboard Styles */

body {
    background-color: #f0f2f5;
}

/* Dark mode for social dashboard */
[data-theme="dark"] body,
html[data-theme="dark"] body,
body[data-theme="dark"] {
    background-color: #121212 !important;
    background: #121212 !important;
    color: #ffffff !important;
}

/* Force dark background for social dashboard */
html, body {
    background-color: #121212 !important;
    background: #121212 !important;
    color: #ffffff !important;
}

.social-dashboard {
    max-width: 1200px;
    margin: 0 auto;
}

/* Header Styles */
.dashboard-header {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 20px;
}

/* Dark mode header */
[data-theme="dark"] .dashboard-header,
html .dashboard-header,
body .dashboard-header {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Card Styles */
.dashboard-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: box-shadow 0.3s ease;
}

/* Dark mode cards */
[data-theme="dark"] .dashboard-card,
html .dashboard-card,
body .dashboard-card {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.dashboard-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Dark mode card hover */
[data-theme="dark"] .dashboard-card:hover,
html .dashboard-card:hover,
body .dashboard-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;
}

.dashboard-card .card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
}

/* Dark mode card header */
[data-theme="dark"] .dashboard-card .card-header,
html .dashboard-card .card-header,
body .dashboard-card .card-header {
    border-bottom: 1px solid #333333 !important;
    background-color: #252525 !important;
    color: #ffffff !important;
}

.dashboard-card .card-body {
    padding: 20px;
}

/* Dark mode card body */
[data-theme="dark"] .dashboard-card .card-body,
html .dashboard-card .card-body,
body .dashboard-card .card-body {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Post Styles */
.post-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    transition: box-shadow 0.3s ease;
}

/* Dark mode post cards */
[data-theme="dark"] .post-card,
html .post-card,
body .post-card {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.post-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.post-header {
    padding: 12px 16px;
    display: flex;
    align-items: center;
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.post-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.post-user-info {
    flex-grow: 1;
}

.post-username {
    font-weight: 600;
    margin-bottom: 0;
}

.post-time {
    font-size: 0.8rem;
    color: #65676b;
}

/* Dark mode text colors */
[data-theme="dark"] .post-time,
html .post-time,
body .post-time {
    color: #aaaaaa !important;
}

.post-content {
    padding: 0 16px 16px;
}

.post-text {
    margin-bottom: 12px;
    white-space: pre-line;
}

.post-image {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 12px;
}

.post-actions {
    display: flex;
    border-top: 1px solid #e4e6eb;
    padding: 8px 16px;
}

.post-action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 4px;
    background: none;
    border: none;
    color: #65676b;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.post-action-btn:hover {
    background-color: #f2f3f5;
}

/* Dark mode post action buttons */
[data-theme="dark"] .post-action-btn,
html .post-action-btn,
body .post-action-btn {
    color: #aaaaaa !important;
}

[data-theme="dark"] .post-action-btn:hover,
html .post-action-btn:hover,
body .post-action-btn:hover {
    background-color: #333333 !important;
}

.post-action-btn i {
    margin-right: 6px;
}

/* Create Post Card */
.create-post-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 12px 16px;
}

/* Dark mode create post card */
[data-theme="dark"] .create-post-card,
html .create-post-card,
body .create-post-card {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.create-post-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.create-post-input {
    flex-grow: 1;
    margin-left: 12px;
    background-color: #f0f2f5;
    border-radius: 20px;
    padding: 8px 12px;
    border: none;
    cursor: pointer;
}

.create-post-actions {
    display: flex;
    border-top: 1px solid #e4e6eb;
    padding-top: 8px;
}

.create-post-action {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 4px;
    background: none;
    border: none;
    color: #65676b;
    font-weight: 600;
    cursor: pointer;
}

.create-post-action:hover {
    background-color: #f2f3f5;
}

.create-post-action i {
    margin-right: 6px;
}

/* Sidebar Styles */
.sidebar-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Dark mode sidebar cards */
[data-theme="dark"] .sidebar-card,
html .sidebar-card,
body .sidebar-card {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.sidebar-card .card-header {
    padding: 12px 16px;
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.sidebar-card .card-body {
    padding: 12px 16px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu-item {
    padding: 8px 0;
    display: flex;
    align-items: center;
    color: #050505;
    text-decoration: none;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.sidebar-menu-item:hover {
    background-color: #f2f3f5;
}

.sidebar-menu-item i {
    width: 36px;
    font-size: 1.2rem;
    text-align: center;
    margin-right: 8px;
}

/* Stats Cards */
.stats-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

/* Dark mode stats cards */
[data-theme="dark"] .stats-card,
html .stats-card,
body .stats-card {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 1.5rem;
}

.stats-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.stats-info p {
    color: #65676b;
    margin: 0;
}

/* Moderation Tools */
.moderation-tools {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Dark mode moderation tools */
[data-theme="dark"] .moderation-tools,
html .moderation-tools,
body .moderation-tools {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.moderation-tools .card-header {
    padding: 12px 16px;
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.moderation-tools .list-group-item {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 12px 16px;
}

.moderation-tools .list-group-item:last-child {
    border-bottom: none;
}

.moderation-tools .list-group-item i {
    margin-right: 8px;
}

/* User Reputation Badges */
.reputation-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 8px;
}

.reputation-new {
    background-color: #e4e6eb;
    color: #65676b;
}

.reputation-contributor {
    background-color: #e3f2fd;
    color: #0d6efd;
}

.reputation-trusted {
    background-color: #d1e7dd;
    color: #198754;
}

.reputation-expert {
    background-color: #cff4fc;
    color: #0dcaf0;
}

.reputation-leader {
    background-color: #fff3cd;
    color: #ffc107;
}

/* Activity Feed */
.activity-feed {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    padding: 12px 0;
    border-bottom: 1px solid #e4e6eb;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-content {
    display: flex;
    align-items: flex-start;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #e4e6eb;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.activity-text {
    flex-grow: 1;
}

.activity-time {
    font-size: 0.8rem;
    color: #65676b;
    margin-top: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar {
        margin-bottom: 20px;
    }
}

/* Flagged content styles */
.flagged-content {
    border-left: 4px solid #dc3545;
    background-color: #f8f9fa;
    padding: 12px;
    margin-bottom: 16px;
    border-radius: 0 8px 8px 0;
}

.flagged-content .flagged-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.flagged-content .flagged-reason {
    font-style: italic;
    color: #6c757d;
    margin-bottom: 8px;
}

/* Upvote button styles */
.upvote-btn {
    background: none;
    border: none;
    color: #65676b;
    display: flex;
    align-items: center;
    font-weight: 500;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.upvote-btn:hover {
    background-color: #f2f3f5;
}

.upvote-btn.upvoted {
    color: #0d6efd;
}

.upvote-btn i {
    margin-right: 6px;
}

.upvote-count {
    margin-left: 6px;
    font-weight: 600;
}

/* Tabs styling */
.dashboard-tabs {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

/* Dark mode tabs */
[data-theme="dark"] .dashboard-tabs,
html .dashboard-tabs,
body .dashboard-tabs {
    background-color: #1e1e1e !important;
    background: #1e1e1e !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.dashboard-tabs .nav-link {
    padding: 12px 16px;
    font-weight: 500;
    color: #65676b;
    border: none;
}

.dashboard-tabs .nav-link.active {
    color: #0d6efd;
    border-bottom: 3px solid #0d6efd;
    background-color: transparent;
}

.dashboard-tabs .nav-link:hover:not(.active) {
    background-color: #f2f3f5;
}

/* Notification styles */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* User list styles */
.user-list {
    list-style: none;
    padding: 0;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e4e6eb;
}

.user-item:last-child {
    border-bottom: none;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-info {
    flex-grow: 1;
}

.user-name {
    font-weight: 600;
    margin-bottom: 0;
}

.user-meta {
    font-size: 0.8rem;
    color: #65676b;
}

.user-actions {
    margin-left: 8px;
}

/* Moderation action buttons */
.mod-action-btn {
    padding: 4px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* Report card styles */
.report-card {
    border-left: 4px solid #ffc107;
    background-color: #fff;
    padding: 12px;
    margin-bottom: 16px;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.report-card .report-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.report-card .report-content {
    margin-bottom: 8px;
}

.report-card .report-actions {
    display: flex;
    gap: 8px;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-pending {
    background-color: #fff3cd;
    color: #ffc107;
}

.status-approved {
    background-color: #d1e7dd;
    color: #198754;
}

.status-rejected {
    background-color: #f8d7da;
    color: #dc3545;
}

.status-resolved {
    background-color: #cff4fc;
    color: #0dcaf0;
}

/* Contribution card styles */
.contribution-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    padding: 16px;
}

.contribution-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.contribution-title {
    font-weight: 600;
    margin-bottom: 8px;
}

.contribution-content {
    margin-bottom: 12px;
}

.contribution-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #65676b;
}

.contribution-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

/* Tooltip styles */
.tooltip-inner {
    max-width: 200px;
    padding: 8px 12px;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 4px;
}

/* Dropdown menu styles */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dropdown-item {
    padding: 8px 16px;
}

.dropdown-item:hover {
    background-color: #f2f3f5;
}

.dropdown-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* Chart styles */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #0d6efd;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty state styles */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #65676b;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #e4e6eb;
}

.empty-state h4 {
    font-weight: 600;
    margin-bottom: 8px;
}

.empty-state p {
    max-width: 400px;
    margin: 0 auto;
}

/* ========================================
   COMPREHENSIVE DARK MODE STYLES
   ======================================== */

/* Dark mode for all text elements */
[data-theme="dark"] .stats-info p,
[data-theme="dark"] .user-meta,
[data-theme="dark"] .activity-time,
[data-theme="dark"] .contribution-meta,
[data-theme="dark"] .empty-state,
html .stats-info p,
html .user-meta,
html .activity-time,
html .contribution-meta,
html .empty-state,
body .stats-info p,
body .user-meta,
body .activity-time,
body .contribution-meta,
body .empty-state {
    color: #aaaaaa !important;
}

/* Dark mode for borders */
[data-theme="dark"] .post-actions,
[data-theme="dark"] .create-post-actions,
[data-theme="dark"] .activity-item,
[data-theme="dark"] .user-item,
html .post-actions,
html .create-post-actions,
html .activity-item,
html .user-item,
body .post-actions,
body .create-post-actions,
body .activity-item,
body .user-item {
    border-color: #333333 !important;
}

/* Dark mode for input fields */
[data-theme="dark"] .create-post-input,
html .create-post-input,
body .create-post-input {
    background-color: #252525 !important;
    color: #ffffff !important;
    border-color: #333333 !important;
}

/* Dark mode for dropdown menus */
[data-theme="dark"] .dropdown-menu,
html .dropdown-menu,
body .dropdown-menu {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
}

[data-theme="dark"] .dropdown-item,
html .dropdown-item,
body .dropdown-item {
    color: #ffffff !important;
}

[data-theme="dark"] .dropdown-item:hover,
html .dropdown-item:hover,
body .dropdown-item:hover {
    background-color: #333333 !important;
    color: #ffffff !important;
}

/* Dark mode for nav links */
[data-theme="dark"] .dashboard-tabs .nav-link,
html .dashboard-tabs .nav-link,
body .dashboard-tabs .nav-link {
    color: #aaaaaa !important;
}

[data-theme="dark"] .dashboard-tabs .nav-link.active,
html .dashboard-tabs .nav-link.active,
body .dashboard-tabs .nav-link.active {
    color: #0d6efd !important;
}

[data-theme="dark"] .dashboard-tabs .nav-link:hover:not(.active),
html .dashboard-tabs .nav-link:hover:not(.active),
body .dashboard-tabs .nav-link:hover:not(.active) {
    background-color: #333333 !important;
}

/* Dark mode for sidebar menu items */
[data-theme="dark"] .sidebar-menu-item,
html .sidebar-menu-item,
body .sidebar-menu-item {
    color: #ffffff !important;
}

[data-theme="dark"] .sidebar-menu-item:hover,
html .sidebar-menu-item:hover,
body .sidebar-menu-item:hover {
    background-color: #333333 !important;
    color: #ffffff !important;
}

/* Dark mode for create post actions */
[data-theme="dark"] .create-post-action,
html .create-post-action,
body .create-post-action {
    color: #aaaaaa !important;
}

[data-theme="dark"] .create-post-action:hover,
html .create-post-action:hover,
body .create-post-action:hover {
    background-color: #333333 !important;
}

/* Dark mode for upvote buttons */
[data-theme="dark"] .upvote-btn,
html .upvote-btn,
body .upvote-btn {
    color: #aaaaaa !important;
}

[data-theme="dark"] .upvote-btn:hover,
html .upvote-btn:hover,
body .upvote-btn:hover {
    background-color: #333333 !important;
}

/* Dark mode for avatars */
[data-theme="dark"] .post-avatar,
[data-theme="dark"] .user-avatar,
[data-theme="dark"] .activity-icon,
html .post-avatar,
html .user-avatar,
html .activity-icon,
body .post-avatar,
body .user-avatar,
body .activity-icon {
    background-color: #333333 !important;
}

/* Dark mode for flagged content */
[data-theme="dark"] .flagged-content,
html .flagged-content,
body .flagged-content {
    background-color: #2a1f1f !important;
    border-left-color: #dc3545 !important;
}

[data-theme="dark"] .flagged-content .flagged-reason,
html .flagged-content .flagged-reason,
body .flagged-content .flagged-reason {
    color: #aaaaaa !important;
}

/* Dark mode for report cards */
[data-theme="dark"] .report-card,
html .report-card,
body .report-card {
    background-color: #2a2a1f !important;
    border-left-color: #ffc107 !important;
}

/* Dark mode for tooltips */
[data-theme="dark"] .tooltip-inner,
html .tooltip-inner,
body .tooltip-inner {
    background-color: #333333 !important;
    color: #ffffff !important;
}

/* Force dark mode on all elements */
[data-theme="dark"] *,
html *,
body * {
    scrollbar-color: #333333 #121212;
}

/* Dark mode scrollbars for webkit browsers */
[data-theme="dark"] ::-webkit-scrollbar,
html ::-webkit-scrollbar,
body ::-webkit-scrollbar {
    width: 8px;
    background-color: #121212;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb,
html ::-webkit-scrollbar-thumb,
body ::-webkit-scrollbar-thumb {
    background-color: #333333;
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover,
html ::-webkit-scrollbar-thumb:hover,
body ::-webkit-scrollbar-thumb:hover {
    background-color: #444444;
}
