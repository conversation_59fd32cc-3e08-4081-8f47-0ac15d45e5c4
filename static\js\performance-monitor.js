/**
 * Frontend Performance Monitor
 * Real-time performance tracking and optimization
 */

class FrontendPerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.observers = new Map();
        this.thresholds = {
            fcp: 1500,      // First Contentful Paint
            lcp: 2500,      // Largest Contentful Paint
            fid: 100,       // First Input Delay
            cls: 0.1,       // Cumulative Layout Shift
            ttfb: 600,      // Time to First Byte
            tti: 3500       // Time to Interactive
        };
        
        this.config = {
            enableRealTimeMonitoring: true,
            enableWebVitals: true,
            enableResourceTiming: true,
            enableUserTiming: true,
            enableErrorTracking: true,
            reportingInterval: 30000, // 30 seconds
            maxMetricsHistory: 100
        };
        
        this.init();
    }
    
    init() {
        this.setupWebVitalsObserver();
        this.setupResourceObserver();
        this.setupNavigationObserver();
        this.setupErrorTracking();
        this.setupUserInteractionTracking();
        this.startReporting();
        
        console.log('📊 Frontend Performance Monitor initialized');
    }
    
    // Core Web Vitals Monitoring
    setupWebVitalsObserver() {
        if (!('PerformanceObserver' in window)) return;
        
        // First Contentful Paint
        this.observeMetric('paint', (entries) => {
            entries.forEach(entry => {
                if (entry.name === 'first-contentful-paint') {
                    this.recordMetric('fcp', entry.startTime, {
                        threshold: this.thresholds.fcp,
                        good: entry.startTime <= 1800,
                        needsImprovement: entry.startTime <= 3000
                    });
                }
            });
        });
        
        // Largest Contentful Paint
        this.observeMetric('largest-contentful-paint', (entries) => {
            const lastEntry = entries[entries.length - 1];
            this.recordMetric('lcp', lastEntry.startTime, {
                threshold: this.thresholds.lcp,
                good: lastEntry.startTime <= 2500,
                needsImprovement: lastEntry.startTime <= 4000,
                element: lastEntry.element
            });
        });
        
        // First Input Delay
        this.observeMetric('first-input', (entries) => {
            entries.forEach(entry => {
                this.recordMetric('fid', entry.processingStart - entry.startTime, {
                    threshold: this.thresholds.fid,
                    good: entry.processingStart - entry.startTime <= 100,
                    needsImprovement: entry.processingStart - entry.startTime <= 300
                });
            });
        });
        
        // Cumulative Layout Shift
        this.observeMetric('layout-shift', (entries) => {
            let clsValue = 0;
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            });
            
            this.recordMetric('cls', clsValue, {
                threshold: this.thresholds.cls,
                good: clsValue <= 0.1,
                needsImprovement: clsValue <= 0.25
            });
        });
    }
    
    observeMetric(entryType, callback) {
        try {
            const observer = new PerformanceObserver((list) => {
                callback(list.getEntries());
            });
            
            observer.observe({ entryTypes: [entryType] });
            this.observers.set(entryType, observer);
        } catch (error) {
            console.warn(`Failed to observe ${entryType}:`, error);
        }
    }
    
    // Resource Performance Monitoring
    setupResourceObserver() {
        this.observeMetric('resource', (entries) => {
            entries.forEach(entry => {
                this.analyzeResourcePerformance(entry);
            });
        });
    }
    
    analyzeResourcePerformance(entry) {
        const duration = entry.responseEnd - entry.startTime;
        const size = entry.transferSize || 0;
        const type = this.getResourceType(entry.name);
        
        // Record resource metrics
        this.recordResourceMetric(type, {
            url: entry.name,
            duration,
            size,
            cached: entry.transferSize === 0,
            protocol: entry.nextHopProtocol
        });
        
        // Alert on slow resources
        if (duration > 2000) {
            this.reportSlowResource(entry, duration);
        }
        
        // Alert on large resources
        if (size > 1024 * 1024) { // 1MB
            this.reportLargeResource(entry, size);
        }
    }
    
    getResourceType(url) {
        if (url.match(/\.(css)$/i)) return 'css';
        if (url.match(/\.(js)$/i)) return 'javascript';
        if (url.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)$/i)) return 'image';
        if (url.match(/\.(woff|woff2|ttf|otf)$/i)) return 'font';
        if (url.includes('/api/')) return 'api';
        return 'other';
    }
    
    // Navigation Performance
    setupNavigationObserver() {
        this.observeMetric('navigation', (entries) => {
            entries.forEach(entry => {
                this.analyzeNavigationPerformance(entry);
            });
        });
    }
    
    analyzeNavigationPerformance(entry) {
        const metrics = {
            ttfb: entry.responseStart - entry.requestStart,
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            totalTime: entry.loadEventEnd - entry.startTime
        };
        
        Object.entries(metrics).forEach(([name, value]) => {
            this.recordMetric(name, value, {
                threshold: this.thresholds[name] || 1000
            });
        });
        
        // Calculate derived metrics
        this.calculateDerivedMetrics(entry);
    }
    
    calculateDerivedMetrics(entry) {
        // Time to Interactive (simplified calculation)
        const tti = entry.domContentLoadedEventEnd;
        this.recordMetric('tti', tti, {
            threshold: this.thresholds.tti,
            good: tti <= 3800,
            needsImprovement: tti <= 7300
        });
        
        // Speed Index (approximation)
        const speedIndex = (entry.domContentLoadedEventEnd + entry.loadEventEnd) / 2;
        this.recordMetric('speedIndex', speedIndex, {
            threshold: 3000
        });
    }
    
    // Error Tracking
    setupErrorTracking() {
        // JavaScript errors
        window.addEventListener('error', (event) => {
            this.recordError('javascript', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
        });
        
        // Promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.recordError('promise', {
                reason: event.reason,
                stack: event.reason?.stack
            });
        });
        
        // Resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.recordError('resource', {
                    element: event.target.tagName,
                    source: event.target.src || event.target.href,
                    message: 'Failed to load resource'
                });
            }
        }, true);
    }
    
    // User Interaction Tracking
    setupUserInteractionTracking() {
        const interactionTypes = ['click', 'keydown', 'scroll', 'touchstart'];
        
        interactionTypes.forEach(type => {
            document.addEventListener(type, (event) => {
                this.recordInteraction(type, event);
            }, { passive: true });
        });
    }
    
    recordInteraction(type, event) {
        const timestamp = performance.now();
        
        // Measure input delay for clicks
        if (type === 'click') {
            requestAnimationFrame(() => {
                const delay = performance.now() - timestamp;
                if (delay > 16) { // More than one frame
                    this.recordMetric('inputDelay', delay, {
                        threshold: 50,
                        interaction: type
                    });
                }
            });
        }
        
        // Track scroll performance
        if (type === 'scroll') {
            this.trackScrollPerformance();
        }
    }
    
    trackScrollPerformance() {
        if (this.scrollTimeout) return;
        
        const startTime = performance.now();
        
        this.scrollTimeout = setTimeout(() => {
            const duration = performance.now() - startTime;
            this.recordMetric('scrollDuration', duration, {
                threshold: 16 // 60fps
            });
            this.scrollTimeout = null;
        }, 100);
    }
    
    // Metric Recording
    recordMetric(name, value, metadata = {}) {
        const metric = {
            name,
            value,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            ...metadata
        };
        
        if (!this.metrics.has(name)) {
            this.metrics.set(name, []);
        }
        
        const metrics = this.metrics.get(name);
        metrics.push(metric);
        
        // Keep only recent metrics
        if (metrics.length > this.config.maxMetricsHistory) {
            metrics.shift();
        }
        
        // Check thresholds and alert
        this.checkThreshold(metric);
        
        // Trigger real-time updates
        this.triggerMetricUpdate(metric);
    }
    
    recordResourceMetric(type, data) {
        const key = `resource_${type}`;
        
        if (!this.metrics.has(key)) {
            this.metrics.set(key, []);
        }
        
        this.metrics.get(key).push({
            timestamp: Date.now(),
            ...data
        });
    }
    
    recordError(type, data) {
        const key = `error_${type}`;
        
        if (!this.metrics.has(key)) {
            this.metrics.set(key, []);
        }
        
        this.metrics.get(key).push({
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            ...data
        });
        
        // Send error to monitoring service
        this.reportError(type, data);
    }
    
    // Threshold Checking
    checkThreshold(metric) {
        const threshold = metric.threshold;
        if (!threshold) return;
        
        if (metric.value > threshold) {
            this.reportPerformanceIssue(metric);
        }
    }
    
    reportPerformanceIssue(metric) {
        console.warn(`⚠️ Performance issue: ${metric.name} = ${metric.value}ms (threshold: ${metric.threshold}ms)`);
        
        // Trigger optimization suggestions
        this.suggestOptimizations(metric);
    }
    
    suggestOptimizations(metric) {
        const suggestions = {
            fcp: ['Inline critical CSS', 'Optimize fonts', 'Reduce server response time'],
            lcp: ['Optimize images', 'Preload key resources', 'Improve server response time'],
            fid: ['Reduce JavaScript execution time', 'Break up long tasks', 'Use web workers'],
            cls: ['Set image dimensions', 'Preload fonts', 'Avoid dynamic content insertion'],
            ttfb: ['Optimize server performance', 'Use CDN', 'Enable caching']
        };
        
        const metricSuggestions = suggestions[metric.name];
        if (metricSuggestions) {
            console.log(`💡 Optimization suggestions for ${metric.name}:`, metricSuggestions);
        }
    }
    
    // Reporting
    startReporting() {
        if (!this.config.enableRealTimeMonitoring) return;
        
        setInterval(() => {
            this.generateReport();
        }, this.config.reportingInterval);
    }
    
    generateReport() {
        const report = {
            timestamp: Date.now(),
            url: window.location.href,
            metrics: this.getMetricsSummary(),
            resources: this.getResourcesSummary(),
            errors: this.getErrorsSummary(),
            performance: this.getPerformanceScore()
        };
        
        // Send to analytics
        this.sendToAnalytics(report);
        
        return report;
    }
    
    getMetricsSummary() {
        const summary = {};
        
        this.metrics.forEach((values, name) => {
            if (name.startsWith('resource_') || name.startsWith('error_')) return;
            
            const recentValues = values.slice(-10);
            if (recentValues.length > 0) {
                summary[name] = {
                    latest: recentValues[recentValues.length - 1].value,
                    average: recentValues.reduce((sum, m) => sum + m.value, 0) / recentValues.length,
                    count: values.length
                };
            }
        });
        
        return summary;
    }
    
    getResourcesSummary() {
        const summary = {};
        
        this.metrics.forEach((values, name) => {
            if (!name.startsWith('resource_')) return;
            
            const type = name.replace('resource_', '');
            const recentResources = values.slice(-20);
            
            summary[type] = {
                count: recentResources.length,
                totalSize: recentResources.reduce((sum, r) => sum + (r.size || 0), 0),
                averageDuration: recentResources.reduce((sum, r) => sum + (r.duration || 0), 0) / recentResources.length,
                cacheHitRate: recentResources.filter(r => r.cached).length / recentResources.length
            };
        });
        
        return summary;
    }
    
    getErrorsSummary() {
        const summary = {};
        
        this.metrics.forEach((values, name) => {
            if (!name.startsWith('error_')) return;
            
            const type = name.replace('error_', '');
            summary[type] = values.length;
        });
        
        return summary;
    }
    
    getPerformanceScore() {
        const weights = {
            fcp: 0.15,
            lcp: 0.25,
            fid: 0.25,
            cls: 0.25,
            ttfb: 0.10
        };
        
        let totalScore = 0;
        let totalWeight = 0;
        
        Object.entries(weights).forEach(([metric, weight]) => {
            const values = this.metrics.get(metric);
            if (values && values.length > 0) {
                const latest = values[values.length - 1];
                const score = this.calculateMetricScore(metric, latest.value);
                totalScore += score * weight;
                totalWeight += weight;
            }
        });
        
        return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
    }
    
    calculateMetricScore(metric, value) {
        const thresholds = {
            fcp: { good: 1800, poor: 3000 },
            lcp: { good: 2500, poor: 4000 },
            fid: { good: 100, poor: 300 },
            cls: { good: 0.1, poor: 0.25 },
            ttfb: { good: 600, poor: 1500 }
        };
        
        const threshold = thresholds[metric];
        if (!threshold) return 50;
        
        if (value <= threshold.good) return 90;
        if (value <= threshold.poor) return 50;
        return 10;
    }
    
    // Analytics Integration
    sendToAnalytics(data) {
        // Send to Google Analytics
        if (window.gtag) {
            window.gtag('event', 'performance_report', {
                custom_parameter: JSON.stringify(data)
            });
        }
        
        // Send to custom analytics endpoint
        if (window.fetch) {
            fetch('/api/analytics/performance/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            }).catch(error => {
                console.warn('Failed to send analytics:', error);
            });
        }
    }
    
    reportError(type, data) {
        // Send error to monitoring service
        if (window.fetch) {
            fetch('/api/errors/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type,
                    data,
                    url: window.location.href,
                    timestamp: Date.now()
                })
            }).catch(() => {
                // Ignore errors in error reporting
            });
        }
    }
    
    reportSlowResource(entry, duration) {
        console.warn(`🐌 Slow resource: ${entry.name} took ${duration.toFixed(2)}ms`);
    }
    
    reportLargeResource(entry, size) {
        console.warn(`📦 Large resource: ${entry.name} is ${(size / 1024 / 1024).toFixed(2)}MB`);
    }
    
    // Event Triggers
    triggerMetricUpdate(metric) {
        window.dispatchEvent(new CustomEvent('performanceMetric', {
            detail: metric
        }));
    }
    
    // Public API
    getMetrics() {
        return Object.fromEntries(this.metrics);
    }
    
    getMetric(name) {
        return this.metrics.get(name) || [];
    }
    
    clearMetrics() {
        this.metrics.clear();
    }
    
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.metrics.clear();
        
        if (this.scrollTimeout) {
            clearTimeout(this.scrollTimeout);
        }
    }
}

// Initialize performance monitor
const performanceMonitor = new FrontendPerformanceMonitor();

// Export for global access
window.FrontendPerformanceMonitor = FrontendPerformanceMonitor;
window.performanceMonitor = performanceMonitor;
