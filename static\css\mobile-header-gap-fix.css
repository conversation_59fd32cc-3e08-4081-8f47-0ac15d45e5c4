/**
 * Mobile Header Gap Fix CSS
 * Comprehensive fixes for mobile header spacing and layout issues
 */

/* Fix authenticated mobile header gaps and spacing */
@media (max-width: 991.98px) {
    /* Main container spacing optimization */
    .unified-header {
        padding: 0 !important;
    }

    .unified-header .container {
        padding-left: 0.75rem !important;
        padding-right: 0.75rem !important;
        max-width: 100% !important;
    }

    /* Brand section layout fixes */
    .unified-header .navbar-brand-section {
        display: flex !important;
        align-items: center !important;
        flex: 1 1 auto !important;
        min-width: 0 !important;
        margin-right: 0.5rem !important;
    }

    .unified-header .navbar-brand {
        margin-right: 0.5rem !important;
        margin-bottom: 0 !important;
        padding: 0.25rem 0 !important;
        font-size: 1.1rem !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
    }

    /* Company brand inline layout */
    .unified-header .company-brand {
        display: inline-flex !important;
        align-items: center !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        flex-shrink: 1 !important;
        min-width: 0 !important;
    }

    .unified-header .company-header-logo {
        width: 24px !important;
        height: 24px !important;
        margin-right: 0.25rem !important;
        flex-shrink: 0 !important;
    }

    .unified-header .company-name {
        font-size: 0.8rem !important;
        margin: 0 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        max-width: 120px !important;
    }

    /* Navbar toggler positioning */
    .unified-header .navbar-toggler {
        margin-left: auto !important;
        margin-right: 0 !important;
        padding: 0.4rem 0.6rem !important;
        border-radius: 0.375rem !important;
        flex-shrink: 0 !important;
        min-width: 44px !important;
        min-height: 40px !important;
    }

    /* Collapsed content spacing - REMOVE ALL GAPS */
    .unified-header .navbar-collapse {
        margin-top: 0.5rem !important;
        padding-top: 0.5rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        clear: both !important;
        padding-bottom: 0 !important;
        margin-bottom: 0 !important;
    }

    /* Navigation items spacing - AGGRESSIVE GAP REMOVAL */
    .unified-header .navbar-nav {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding: 0 !important;
    }

    .unified-header .navbar-nav .nav-item {
        margin: 0 !important;
        margin-bottom: 0 !important;
        padding: 0 !important;
    }

    .unified-header .nav-link {
        padding: 0.5rem 0.75rem !important;
        margin: 0 !important;
        margin-bottom: 0 !important;
        border-radius: 0.375rem !important;
        font-size: 0.95rem !important;
        display: flex !important;
        align-items: center !important;
        line-height: 1.2 !important;
    }

    .unified-header .nav-icon-container {
        margin-right: 0.5rem !important;
        flex-shrink: 0 !important;
    }

    /* Dropdown menu improvements */
    .unified-header .dropdown-menu {
        margin-top: 0.25rem !important;
        border-radius: 0.5rem !important;
        padding: 0.5rem !important;
    }

    .unified-header .dropdown-item {
        padding: 0.6rem 0.75rem !important;
        font-size: 0.9rem !important;
        border-radius: 0.375rem !important;
        margin-bottom: 0.1rem !important;
    }

    /* Company actions section */
    .unified-header .navbar-company-actions {
        border-left: none !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        padding: 0.75rem 0 0 0 !important;
        margin: 0.75rem 0 0 0 !important;
        width: 100% !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 0.5rem !important;
        justify-content: space-between !important;
    }

    /* User menu section */
    .unified-header .navbar-nav:last-child {
        margin-top: 0.5rem !important;
        padding-top: 0.5rem !important;
        border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin-bottom: 0 !important;
    }
}

/* Extra small screens optimization */
@media (max-width: 576px) {
    .unified-header .container {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
    }

    .unified-header .navbar-brand {
        font-size: 1rem !important;
        margin-right: 0.25rem !important;
    }

    .unified-header .company-name {
        display: none !important; /* Hide on very small screens to save space */
    }

    .unified-header .navbar-toggler {
        padding: 0.35rem 0.5rem !important;
        min-width: 40px !important;
        min-height: 36px !important;
    }

    .unified-header .nav-link {
        font-size: 0.9rem !important;
        padding: 0.55rem 0.6rem !important;
    }

    .unified-header .dropdown-item {
        font-size: 0.85rem !important;
        padding: 0.55rem 0.6rem !important;
    }
}

/* Landscape mobile optimization */
@media (max-width: 991.98px) and (orientation: landscape) and (max-height: 500px) {
    .unified-header .navbar-collapse {
        margin-top: 0.5rem !important;
        padding-top: 0.5rem !important;
    }

    .unified-header .navbar-nav {
        margin-bottom: 0.25rem !important;
    }

    .unified-header .nav-link {
        padding: 0.4rem 0.6rem !important;
        font-size: 0.9rem !important;
    }

    .unified-header .navbar-company-actions {
        padding: 0.5rem 0 0 0 !important;
        margin: 0.5rem 0 0 0 !important;
    }

    .unified-header .navbar-nav:last-child {
        margin-top: 0.25rem !important;
        padding-top: 0.25rem !important;
    }
}

/* Fix for specific gap issues */
@media (max-width: 991.98px) {
    /* Remove any unwanted margins/padding that create gaps */
    .unified-header .navbar-brand-section > * {
        margin-top: 0 !important;
        margin-bottom: 0 !important;
    }

    /* Ensure proper flex behavior */
    .unified-header .d-flex.align-items-center {
        gap: 0 !important;
    }

    /* Fix any Bootstrap default spacing that creates gaps */
    .unified-header .me-3,
    .unified-header .ms-3,
    .unified-header .mx-3 {
        margin-left: 0.5rem !important;
        margin-right: 0.5rem !important;
    }

    .unified-header .me-2,
    .unified-header .ms-2,
    .unified-header .mx-2 {
        margin-left: 0.25rem !important;
        margin-right: 0.25rem !important;
    }

    /* AGGRESSIVE GAP REMOVAL - Override all possible spacing */
    .unified-header .navbar-nav li,
    .unified-header .navbar-nav .nav-item,
    .unified-header .nav-item {
        margin: 0 !important;
        padding: 0 !important;
        margin-bottom: 0 !important;
        margin-top: 0 !important;
    }

    /* Remove Bootstrap's default nav-item spacing */
    .unified-header .navbar-nav > li,
    .unified-header .navbar-nav > .nav-item {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
    }

    /* Remove any dropdown spacing that creates gaps */
    .unified-header .dropdown {
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure no spacing between sections */
    .unified-header .navbar-nav:not(:last-child) {
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
    }

    /* Remove any list-style spacing */
    .unified-header ul.navbar-nav {
        list-style: none !important;
        padding-left: 0 !important;
        margin: 0 !important;
    }

    /* Override any inherited spacing */
    .unified-header .navbar-collapse > * {
        margin-top: 0 !important;
    }

    .unified-header .navbar-collapse > *:not(:first-child) {
        margin-top: 0 !important;
    }
}

/* Dark theme compatibility */
[data-theme="light"] .unified-header .navbar-collapse {
    border-top-color: rgba(0, 0, 0, 0.1) !important;
}

[data-theme="light"] .unified-header .navbar-company-actions {
    border-top-color: rgba(0, 0, 0, 0.1) !important;
}

[data-theme="light"] .unified-header .navbar-nav:last-child {
    border-top-color: rgba(0, 0, 0, 0.1) !important;
}
