{% load static account_tags rating_tags %}

{# Company Card Partial - Enhanced design #}
<div class="list-group-item position-relative directory-card" data-company-id="{{ listing.company.id }}">
    {# --- Conditional Badge Logic based on display_context --- #}
    {# display_context should be 'featured' or 'tier' #}

    {# Show Featured badge ONLY if in 'featured' context AND company is featured #}
    {% if display_context == 'featured' and listing.company.is_featured %}
    <span class="badge bg-success position-absolute top-0 start-0 m-2" style="z-index: 10; font-size: 0.7em; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <i class="bi bi-star-fill me-1"></i>Featured
    </span>
    {% endif %}

    {# Show Tier badge ONLY if in 'tier' context and not Standard tier #}
    {% if display_context == 'tier' %}
        {% if listing.company.tier == 'Gold' %}
            <span class="badge tier-badge tier-gold"><i class="bi bi-trophy-fill me-1"></i>Gold</span>
        {% elif listing.company.tier == 'Silver' %}
            <span class="badge tier-badge tier-silver"><i class="bi bi-award-fill me-1"></i>Silver</span>
        {% elif listing.company.tier == 'Bronze' %}
            <span class="badge tier-badge tier-bronze"><i class="bi bi-award me-1"></i>Bronze</span>
        {% endif %}
        {# No badge needed for Standard tier as it's the default #}
    {% endif %}
    {# --- End Conditional Badge Logic --- #}

    {# ROW STRUCTURE: 12 = 12 (Outer), 3 + 3 + 2 + 2 + 2 = 12 (Full Row) #}
    <div class="row g-4 pt-3" style="height: 100%;">
        {# Link wrapper covers first 3 columns (col-md-8) and contains an inner row #}
        <a href="{% url 'accounts:public_company_detail' slug=listing.company.slug %}" class="directory-item-link-wrapper col-md-8 row g-4 me-0 text-decoration-none">
            {# Column 1: Logo (col-md-3 within link row) #}
            <div class="col-md-3 d-flex justify-content-center align-items-center" style="padding-left: 20px;">
                <div class="logo-container">
                    {% if listing.company.info.logo %}
                        <img src="{{ listing.company.info.logo.url }}" alt="{{ listing.company.name }} logo"
                             onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'logo-placeholder\'><i class=\'bi bi-building-fill\'></i></div>';">
                    {% else %}
                        <div class="logo-placeholder">
                            <i class="bi bi-building-fill"></i>
                        </div>
                    {% endif %}
                </div>
            </div>

            {# Column 2: Name, Industry, Tags/Categories (col-md-4 within link row) #}
            <div class="col-md-4">
                <h6 class="mb-2">
                    {{ listing.company.name }}
                    {% if not listing.company.is_active %}
                    <span class="badge bg-warning text-dark ms-2">Pending Approval</span>
                    {% endif %}
                </h6>
                {% if listing.company.info.industry %}
                <p class="mb-2 text-muted">
                    <i class="bi bi-building me-1"></i>{{ listing.company.info.industry }}
                </p>
                {% endif %}
                <div class="mb-2">
                     {% for category in listing.categories.all %}
                        <span class="badge bg-secondary tag-badge">{{ category.name }}</span>
                     {% endfor %}
                     {% for tag in listing.tags %}
                        <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                     {% endfor %}
                </div>
            </div>

            {# Column 3: Description (col-md-5 within link row) #}
            <div class="col-md-5">
                {% if not listing.company.is_active %}
                <div class="alert alert-warning py-2 mb-2" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Pending Approval:</strong> This company is waiting for administrator approval.
                </div>
                {% endif %}
                <p class="mb-0 item-description">
                    {{ listing.description|default:listing.company.info.description|default:"No description available." }}
                </p>
            </div>
        </a> {# End of clickable area link #}

        {# Column 4: Contact Info (col-md-2) - Outside the link #}
        <div class="col-md-2 d-flex flex-column justify-content-center contact-info-column">
            {# Contact Info - Expanded to show more details #}
            <ul class="list-unstyled small contact-info mb-0" data-tablet-optimized="true" data-responsive="true">
                {% if listing.company.info.address_line1 or listing.company.info.city %}
                    <li class="mb-2 address-item">
                        <i class="bi bi-geo-alt"></i>
                        <div class="contact-text location-text">
                            {% if listing.company.info.city %}
                                {{ listing.company.info.city }}{% if listing.company.info.country %}, {{ listing.company.info.country }}{% endif %}
                            {% else %}
                                {% if listing.company.info.address_line1 %}{{ listing.company.info.address_line1 }}{% if listing.company.info.address_line2 %}, {{ listing.company.info.address_line2 }}{% endif %}{% endif %}
                            {% endif %}
                        </div>
                    </li>
                {% endif %}
                {% if listing.company.info.contact_phone %}
                    <li class="mb-2 phone-item">
                        <i class="bi bi-telephone"></i>
                        <span class="contact-text phone-text">{{ listing.company.info.contact_phone }}</span>
                    </li>
                {% endif %}
                {% if listing.company.info.contact_email %}
                    <li class="mb-2 email-item">
                        <i class="bi bi-envelope"></i>
                        <span class="contact-text email-text">{{ listing.company.info.contact_email }}</span>
                    </li>
                {% endif %}
                {% if listing.website|default:listing.company.info.website %}
                    <li class="website-item">
                        <i class="bi bi-link-45deg"></i>
                        <span class="contact-text website-url">{{ listing.website|default:listing.company.info.website }}</span>
                    </li>
                {% endif %}
            </ul>
        </div>

        {# Column 5: Rating & Actions (col-md-2) - Outside the link #}
        <div class="col-md-2 d-flex flex-column align-items-end justify-content-between">
            <div class="w-100">
                {# Rating Display #}
                {% with avg_rating=listing.avg_rating total_ratings=listing.total_ratings %}
                    {% if avg_rating > 0 %}
                        <div class="rating-display-container mb-3" id="rating-display-company-{{ listing.company.id }}">
                            {% render_stars avg_rating total_ratings %}
                        </div>
                    {% else %}
                        <div class="text-muted fst-italic mb-3 text-end" id="no-rating-placeholder-{{ listing.company.id }}">(No ratings yet)</div>
                    {% endif %}
                {% endwith %}
            </div>

            {# Buttons #}
            <div class="w-100 d-flex justify-content-end align-items-center">
                {% if user.is_authenticated %}
                    <button type="button"
                            class="btn btn-outline-secondary btn-sm rate-company-btn action-btn me-2"
                            data-bs-toggle="modal"
                            data-bs-target="#ratingModal"
                            data-company-id="{{ listing.company.id }}"
                            data-company-name="{{ listing.company.name|escapejs }}">
                        <i class="bi bi-star"></i> Rate
                    </button>

                    <button
                        class="like-button btn btn-sm p-1 {% if listing.company.id in saved_company_ids %}text-danger{% else %}text-secondary{% endif %}"
                        data-item-id="{{ listing.company.id }}"
                        data-item-type="company"
                        title="{% if listing.company.id in saved_company_ids %}Unlike{% else %}Like{% endif %}"
                        style="background: none; border: none; cursor: pointer;">
                        <i class="bi bi-heart-fill" style="font-size: 1.2rem;"></i>
                    </button>
                {% endif %}
            </div>

            <div class="w-100 text-end" style="height: 1em;">
                <span class="rating-update-message text-success small" id="rating-msg-company-{{ listing.company.id }}" style="display: none;"></span>
            </div>
        </div> {# End Rating & Actions Column #}
    </div> {# /row #}
</div> {# /list-group-item #}
