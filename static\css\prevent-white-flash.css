/**
 * Prevent White Flash CSS
 * This file ensures the background is dark during page load to prevent white flash
 */

/* Set dark background on html and body elements */
html, 
body {
  background-color: #121212 !important;
  color: #ffffff !important;
  transition: none !important; /* Disable transitions during load */
}

/* Set dark background on main content */
main {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Set dark background on chat container */
.chat-container, 
.general-chat-container {
  background-color: #1e1e1e !important;
  border-color: #333333 !important;
}

/* Set dark background on chat box */
.chat-box, 
.general-chat-box, 
#chat-box {
  background-color: #252525 !important;
  border-color: #333333 !important;
}

/* Ensure all modals have dark background */
.modal-content {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Ensure all cards have dark background */
.card {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Ensure all inputs have dark background */
input, 
textarea, 
select {
  background-color: #252525 !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

/* Ensure all buttons have dark background */
button {
  background-color: #333333 !important;
  color: #ffffff !important;
  border-color: #444444 !important;
}

/* Override any light backgrounds */
[style*="background-color: white"],
[style*="background-color: #fff"],
[style*="background-color: #ffffff"],
[style*="background-color: rgb(255, 255, 255)"],
[style*="background-color: rgba(255, 255, 255"],
[style*="background-color: #f9f9f9"],
[style*="background-color: #f0f0f0"],
[style*="background-color: #e8f4ff"],
[style*="background: white"],
[style*="background: #fff"],
[style*="background: #ffffff"],
[style*="background: rgb(255, 255, 255)"],
[style*="background: rgba(255, 255, 255"],
[style*="background: #f9f9f9"],
[style*="background: #f0f0f0"],
[style*="background: #e8f4ff"] {
  background-color: #121212 !important;
  background: #121212 !important;
  color: #ffffff !important;
}

/* Ensure footer is immediately dark */
footer,
footer.footer,
.footer {
  background-color: #1a1a1a !important;
  color: #cccccc !important;
  border-top: 1px solid #333333 !important;
}

/* Ensure footer elements are dark */
footer h5,
footer h6,
footer.footer h5,
footer.footer h6 {
  color: #ffffff !important;
}

footer a,
footer.footer a {
  color: #aaaaaa !important;
}

footer a:hover,
footer.footer a:hover {
  color: #6ea8fe !important;
}

/* Ensure accordion elements are dark */
footer .accordion-item,
footer .accordion-button,
footer .accordion-body {
  background-color: transparent !important;
  color: #cccccc !important;
  border-color: #333333 !important;
}

/* Force dark backgrounds on chat assistant page */
.chat-container,
.general-chat-container,
div.chat-container,
div.general-chat-container {
  background-color: #1e1e1e !important;
  background: #1e1e1e !important;
  background-image: none !important;
  border-color: #333333 !important;
}

.chat-box,
.general-chat-box,
#chat-box {
  background-color: #252525 !important;
  background: #252525 !important;
  background-image: none !important;
  border-color: #333333 !important;
}

.assistant-message,
.assistant-message .message-content,
div.message.assistant-message.mb-3 span.message-content,
span.message-content.assistant-message {
  background-color: #1e1e1e !important;
  background: #1e1e1e !important;
  background-image: none !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

#message-input {
  background-color: #252525 !important;
  background: #252525 !important;
  background-image: none !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}

#chat-form {
  background-color: #252525 !important;
  background: #252525 !important;
  background-image: none !important;
  border-color: #333333 !important;
}

.nav-content-bubble {
  background-color: #1e1e1e !important;
  background: #1e1e1e !important;
  background-image: none !important;
  color: #ffffff !important;
  border-color: #333333 !important;
}
