"""
Management command to warm up context cache for popular assistants.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Warm up context cache for popular assistants'

    def add_arguments(self, parser):
        parser.add_argument(
            '--limit',
            type=int,
            default=50,
            help='Number of popular assistants to warm up (default: 50)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force warming even if context is already cached'
        )
        parser.add_argument(
            '--assistant-id',
            type=int,
            help='Warm specific assistant by ID'
        )

    def handle(self, *args, **options):
        limit = options['limit']
        force = options['force']
        specific_assistant_id = options.get('assistant_id')

        self.stdout.write(
            self.style.SUCCESS(f'Starting context cache warming...')
        )

        try:
            from assistants.context_preloader import context_preloader
            from assistants.models import Assistant

            if specific_assistant_id:
                # Warm specific assistant
                try:
                    assistant = Assistant.objects.get(id=specific_assistant_id)
                    self.stdout.write(f'Warming context for assistant: {assistant.name}')
                    
                    if force:
                        context_preloader.invalidate_assistant_context(assistant.id)
                    
                    context_data = context_preloader.preload_assistant_context(assistant.id)
                    
                    if context_data:
                        self.stdout.write(
                            self.style.SUCCESS(f'Successfully warmed context for {assistant.name}')
                        )
                    else:
                        self.stdout.write(
                            self.style.WARNING(f'Failed to warm context for {assistant.name}')
                        )
                        
                except Assistant.DoesNotExist:
                    self.stdout.write(
                        self.style.ERROR(f'Assistant with ID {specific_assistant_id} not found')
                    )
                    return
            else:
                # Warm popular assistants
                popular_assistants = Assistant.objects.filter(
                    is_active=True,
                    is_public=True
                ).annotate(
                    interaction_count=Count('interactions')
                ).order_by('-interaction_count', '-total_interactions')[:limit]

                self.stdout.write(f'Found {len(popular_assistants)} popular assistants to warm')

                warmed_count = 0
                failed_count = 0

                for assistant in popular_assistants:
                    try:
                        self.stdout.write(f'Warming context for: {assistant.name} (ID: {assistant.id})')
                        
                        if force:
                            context_preloader.invalidate_assistant_context(assistant.id)
                        
                        # Check if already cached
                        existing_context = context_preloader.get_preloaded_context(assistant.id)
                        if existing_context and not force:
                            self.stdout.write(f'  - Already cached, skipping')
                            continue
                        
                        context_data = context_preloader.preload_assistant_context(assistant.id)
                        
                        if context_data:
                            warmed_count += 1
                            self.stdout.write(
                                self.style.SUCCESS(f'  - Successfully warmed ({warmed_count}/{len(popular_assistants)})')
                            )
                        else:
                            failed_count += 1
                            self.stdout.write(
                                self.style.WARNING(f'  - Failed to warm context')
                            )
                            
                    except Exception as e:
                        failed_count += 1
                        self.stdout.write(
                            self.style.ERROR(f'  - Error warming {assistant.name}: {e}')
                        )

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Context warming completed. '
                        f'Warmed: {warmed_count}, Failed: {failed_count}, Total: {len(popular_assistants)}'
                    )
                )

        except ImportError:
            self.stdout.write(
                self.style.ERROR('Context preloader not available')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during context warming: {e}')
            )
            logger.exception("Error in warm_context_cache command")
