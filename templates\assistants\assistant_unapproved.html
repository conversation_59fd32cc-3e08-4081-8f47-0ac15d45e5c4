{% extends "base/layout.html" %}
{% load static %}

{% block title %}Assistant Not Available - {{ assistant.name }}{% endblock %}

{% block extra_css %}
<style>
    .warning-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 2rem;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 2px solid #ffc107;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);
        text-align: center;
    }

    .warning-icon {
        font-size: 4rem;
        color: #f39c12;
        margin-bottom: 1rem;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .warning-title {
        color: #856404;
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .warning-message {
        color: #856404;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 2rem;
    }

    .assistant-info {
        background: rgba(255, 255, 255, 0.7);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        border-left: 4px solid #f39c12;
    }

    .assistant-logo {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        margin-bottom: 1rem;
        border: 3px solid #f39c12;
    }

    .assistant-name {
        font-size: 1.5rem;
        font-weight: bold;
        color: #856404;
        margin-bottom: 0.5rem;
    }

    .assistant-company {
        color: #6c757d;
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .approval-status {
        background: #fff;
        border-radius: 8px;
        padding: 1rem;
        margin: 1.5rem 0;
        border: 1px solid #dee2e6;
    }

    .status-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: bold;
        text-transform: uppercase;
        font-size: 0.9rem;
        background: #dc3545;
        color: white;
    }

    .action-buttons {
        margin-top: 2rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
    }

    .btn-outline-secondary {
        border: 2px solid #6c757d;
        color: #6c757d;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        background: transparent;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-2px);
    }

    .info-section {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        text-align: left;
    }

    .info-section h5 {
        color: #856404;
        font-weight: bold;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .info-section h5 i {
        margin-right: 0.5rem;
        color: #f39c12;
    }

    .info-section ul {
        color: #6c757d;
        line-height: 1.6;
    }

    .info-section ul li {
        margin-bottom: 0.5rem;
    }

    .contact-info {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .contact-info h6 {
        color: #1976d2;
        margin-bottom: 0.5rem;
    }

    .contact-info p {
        color: #424242;
        margin-bottom: 0;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="warning-container">
        <!-- Warning Icon -->
        <div class="warning-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>

        <!-- Warning Title -->
        <h1 class="warning-title">Assistant Not Available</h1>

        <!-- Warning Message -->
        <div class="warning-message">
            <p>This assistant is currently pending approval by an administrator and is not available for public use.</p>
        </div>

        <!-- Assistant Information -->
        <div class="assistant-info">
            {% if assistant.logo %}
                <img src="{{ assistant.logo.url }}" alt="{{ assistant.name }} Logo" class="assistant-logo">
            {% elif assistant.company.logo %}
                <img src="{{ assistant.company.logo.url }}" alt="{{ assistant.company.name }} Logo" class="assistant-logo">
            {% else %}
                <div class="assistant-logo d-flex align-items-center justify-content-center" style="background: #f8f9fa;">
                    <i class="fas fa-robot" style="font-size: 2rem; color: #6c757d;"></i>
                </div>
            {% endif %}

            <div class="assistant-name">{{ assistant.name }}</div>
            <div class="assistant-company">by {{ assistant.company.name }}</div>

            {% if assistant.description %}
                <div class="text-muted" style="font-size: 0.9rem;">
                    {{ assistant.description|truncatewords:20 }}
                </div>
            {% endif %}

            <div class="approval-status">
                <span class="status-badge">Pending Approval</span>
            </div>
        </div>

        <!-- Information Section -->
        <div class="info-section">
            <h5><i class="fas fa-info-circle"></i> What does this mean?</h5>
            <ul>
                <li>This assistant has been created but is waiting for administrator approval</li>
                <li>All new assistants must be reviewed before becoming publicly available</li>
                <li>This ensures quality and compliance with our platform guidelines</li>
                <li>The approval process typically takes 1-3 business days</li>
            </ul>
        </div>

        <!-- Contact Information -->
        {% if assistant.company.owner %}
        <div class="contact-info">
            <h6><i class="fas fa-envelope"></i> Need Help?</h6>
            <p>If you're the owner of this assistant or have questions about the approval process, please contact the administrator or check back later.</p>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="/" class="btn btn-primary me-3">
                <i class="fas fa-search me-2"></i>Browse Available Assistants
            </a>
            <a href="javascript:history.back()" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Go Back
            </a>
        </div>

        <!-- Additional Information -->
        <div class="info-section mt-4">
            <h5><i class="fas fa-clock"></i> What happens next?</h5>
            <ul>
                <li><strong>Review Process:</strong> Our team will review the assistant for quality and compliance</li>
                <li><strong>Notification:</strong> The assistant owner will be notified once approved</li>
                <li><strong>Public Access:</strong> Once approved, the assistant will be available at this same URL</li>
                <li><strong>Updates:</strong> You can bookmark this page and check back later</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add some interactive elements
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-refresh every 5 minutes to check if assistant becomes available
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes

        // Add a subtle notification about auto-refresh
        const container = document.querySelector('.warning-container');
        if (container) {
            const refreshNote = document.createElement('div');
            refreshNote.className = 'text-muted mt-3';
            refreshNote.style.fontSize = '0.8rem';
            refreshNote.innerHTML = '<i class="fas fa-sync-alt me-1"></i>This page will automatically refresh every 5 minutes to check for updates.';
            container.appendChild(refreshNote);
        }
    });
</script>
{% endblock %}
