# Enhanced User Session Management

This document describes the enhanced user session management system implemented to ensure proper user isolation and prevent data interference between different users.

## Overview

The enhanced session management system provides:

1. **User Session Isolation**: Each user's chat history and context data is completely isolated
2. **Secure Session Keys**: User-specific session keys prevent cross-user data access
3. **Automatic Cleanup**: Expired sessions and cache data are automatically cleaned up
4. **Anonymous User Support**: Proper handling of anonymous users for public assistants
5. **Data Validation**: Client-side and server-side validation of session data

## Components

### 1. UserSessionManager (`accounts/session_management.py`)

The core class that handles user session management:

- **Session Key Generation**: Creates unique, secure session keys for each user
- **Chat History Management**: Stores and retrieves user-specific chat history
- **Context Data Management**: Manages user-specific context data
- **Session Validation**: Validates session integrity and user permissions

#### Key Methods:

```python
# Generate user-specific session key
UserSessionManager.get_user_session_key(user, assistant_id, context_type)

# Save/retrieve chat history
UserSessionManager.save_user_chat_history(user, assistant_id, history)
UserSessionManager.get_user_chat_history(user, assistant_id)

# Save/retrieve context data
UserSessionManager.save_user_context_data(user, assistant_id, context_key, data)
UserSessionManager.get_user_context_data(user, assistant_id, context_key)

# Validate session
UserSessionManager.validate_user_session(request, assistant_id)
```

### 2. SessionIsolationMiddleware

Middleware that ensures proper session isolation:

- Validates user sessions for assistant interactions
- Adds session manager to request objects
- Handles both ID-based and slug-based URLs

### 3. EnhancedSessionCleanupMiddleware

Enhanced cleanup middleware that:

- Cleans up user sessions on logout
- Performs periodic cleanup of expired sessions
- Removes user-specific cache data

### 4. Client-Side Session Management

Enhanced JavaScript session management:

- User-specific session storage keys
- Session data validation
- Automatic cleanup of invalid sessions

#### Key Features:

```javascript
// User-specific session storage key
const sessionStorageKey = `chatHistory_${userId}_${assistantId}`;

// Session data with user validation
const historyData = {
    userId: userId,
    assistantId: assistantId,
    messages: cleanedHistory,
    lastUpdated: new Date().toISOString()
};
```

## Security Features

### 1. User Isolation

- **Database Level**: All queries are filtered by user ID
- **Cache Level**: User-specific cache keys prevent cross-user access
- **Session Level**: Session data includes user validation

### 2. Data Validation

- **Server-Side**: Validates user ownership of data
- **Client-Side**: Validates session data belongs to current user
- **History Validation**: Ensures chat history belongs to the correct user

### 3. Anonymous User Handling

- **Public Assistants**: Anonymous users can interact with public assistants
- **Session Isolation**: Anonymous sessions are isolated from authenticated users
- **Data Cleanup**: Anonymous data is properly cleaned up

## Implementation Details

### 1. Session Key Format

Session keys follow the pattern:
```
user_session_{context_type}_{hash}
```

Where:
- `context_type`: Type of data (chat_history, context_data, etc.)
- `hash`: SHA256 hash of user ID, username, and context information

### 2. Chat History Storage

Chat history is stored with:
- **User Validation**: Each history entry includes user ID
- **Size Limits**: Maximum 100 messages per session
- **Timestamps**: Each message includes timestamp
- **TTL**: Cache entries expire after 1 hour by default

### 3. Database Queries

All database queries include user filtering:

```python
# Example: Get user interactions
queryset = Interaction.objects.filter(user=user, user_id=user.id)
```

## Configuration

### 1. Middleware Setup

Add to `MIDDLEWARE` in settings.py:

```python
MIDDLEWARE = [
    # ... other middleware
    'accounts.enhanced_session_cleanup.EnhancedSessionCleanupMiddleware',
    'accounts.session_management.SessionIsolationMiddleware',
    # ... other middleware
]
```

### 2. Cache Configuration

The system uses Django's cache framework. Configure appropriately:

```python
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
        'LOCATION': 'django_cache_table',
        'TIMEOUT': 3600,  # 1 hour
    }
}
```

## Testing

### 1. Unit Tests

Run the session management tests:

```bash
python manage.py test accounts.tests_session_management
```

### 2. Integration Tests

The test suite includes:
- User session isolation tests
- Chat history isolation tests
- Context data isolation tests
- Anonymous user handling tests

## Maintenance

### 1. Session Cleanup

Run the cleanup command regularly:

```bash
# Clean up sessions older than 7 days
python manage.py cleanup_user_sessions

# Dry run to see what would be deleted
python manage.py cleanup_user_sessions --dry-run

# Clean up sessions older than 30 days
python manage.py cleanup_user_sessions --days 30
```

### 2. Monitoring

Monitor session usage:
- Check cache hit rates
- Monitor session storage usage
- Track cleanup effectiveness

## Best Practices

### 1. Development

- Always test with multiple users
- Verify session isolation in development
- Test anonymous user scenarios

### 2. Production

- Set up regular session cleanup
- Monitor cache performance
- Implement proper logging

### 3. Security

- Regularly audit session data
- Monitor for session anomalies
- Keep session timeouts reasonable

## Troubleshooting

### 1. Common Issues

**Sessions not isolated**: Check middleware order and configuration
**Cache issues**: Verify cache backend configuration
**Anonymous users**: Ensure public assistant settings are correct

### 2. Debugging

Enable debug logging:

```python
LOGGING = {
    'loggers': {
        'accounts.session_management': {
            'level': 'DEBUG',
            'handlers': ['console'],
        },
    },
}
```

## Future Enhancements

1. **Redis Integration**: Enhanced cache performance with Redis
2. **Session Analytics**: Track session usage patterns
3. **Advanced Cleanup**: More sophisticated cache cleanup strategies
4. **Session Sharing**: Controlled session sharing for team features
