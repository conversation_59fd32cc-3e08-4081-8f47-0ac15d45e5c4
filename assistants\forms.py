from django import forms
from django.forms import inlineformset_factory
from .models import Assistant, Interaction, AssistantFolder, NavigationItem


# Removed the first redundant NavigationItemFormSet definition

class AssistantForm(forms.ModelForm):
    """Form for creating and updating AI assistants."""
    # Fields for directory listing, handled separately
    categories = forms.Char<PERSON>ield(
        required=False, # Make optional for general editing
        help_text='Comma-separated categories (e.g., Finance, Support, Sales)',
        widget=forms.TextInput(attrs={'placeholder': 'Finance, Support, Sales'})
    )
    tags = forms.CharField(
        required=False, # Make optional for general editing
        help_text='Comma-separated tags for searching (e.g., billing, python, api)',
        widget=forms.TextInput(attrs={'placeholder': 'billing, python, api'})
    )



    class Meta:
        model = Assistant
        fields = [
            'name', 'persona_name', 'description', 'assistant_type', 'model',
            'temperature', 'max_tokens', 'system_prompt', 'greeting_message',
            'knowledge_base', 'custom_css', 'logo', 'avatar', #'tier', 'is_featured', # Removed tier/is_featured
            'is_public', 'show_sidebar', 'show_sidebar_public', 'folder', # Removed is_active, only settable by superusers
            # Duration fields (visibility might be handled by template JS)
            'requested_tier_duration', 'requested_featured_duration',
            'extra_context', # Added extra_context
            # OpenAI Compatible fields
            'api_key', 'base_url', 'custom_model_name',
        ]
        # Add new request fields separately
        request_new_tier = forms.ChoiceField(
            # Use TIER_REQUEST_CHOICES to exclude 'Standard'
            choices=[('', '--- No Change ---')] + Assistant.TIER_REQUEST_CHOICES,
            required=False,
            label="Request Tier Upgrade", # Changed label slightly
            help_text="Select a paid tier (Gold, Silver, Bronze) to request an upgrade (requires superadmin approval)."
        )
        request_featured_status = forms.BooleanField(
            required=False,
            label="Request Featured Status",
            help_text="Check this box to request this assistant be featured (requires superadmin approval)."
        )

        widgets = {
            # description widget removed (handled by HTMLField)
            # system_prompt widget removed (handled by HTMLField)
            # greeting_message widget removed (handled by HTMLField)
            'temperature': forms.NumberInput(attrs={'step': '0.1', 'min': '0', 'max': '2'}),
            'max_tokens': forms.NumberInput(attrs={'step': '1', 'min': '1', 'max': '4096'}),
            'custom_css': forms.Textarea(attrs={'rows': 10}), # Removed apply-tinymce class
            'logo': forms.ClearableFileInput(attrs={'accept': 'image/*', 'class': 'form-control'}),
            'avatar': forms.ClearableFileInput(attrs={'accept': 'image/*', 'class': 'form-control'}),
            'folder': forms.Select(attrs={'class': 'form-select'}), # Use Select widget
            'request_new_tier': forms.Select(attrs={'class': 'form-select'}), # Widget for new field
            'request_featured_status': forms.CheckboxInput(attrs={'class': 'form-check-input'}), # Widget for new field
            'requested_tier_duration': forms.Select(attrs={'class': 'form-select'}),
            'requested_featured_duration': forms.Select(attrs={'class': 'form-select'}),
            # OpenAI Compatible fields
            'api_key': forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Enter API key', 'autocomplete': 'off'}, render_value=True),
            'base_url': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://api.openai.com/v1'}),
            'custom_model_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'gpt-3.5-turbo'}),
            # extra_context widget removed (handled by HTMLField)
            # Removed categories/tags widgets as they are handled separately
        }
        help_texts = {
            'name': 'Internal name for identification in lists, URLs, etc.',
            'persona_name': 'Optional: Name the assistant uses in chat (e.g., "Support Bot"). Defaults to internal name if left blank.',
            'assistant_type': 'The primary function of this assistant',
            'model': 'The underlying language model to use',
            'temperature': 'Controls randomness (0.0-2.0). Lower values make responses more focused and deterministic',
            'max_tokens': 'Maximum length of generated responses',
            'system_prompt': 'Instructions that guide the assistant\'s behavior and expertise',
            'greeting_message': 'Optional: Custom initial greeting message displayed in the chat interface.',
            'knowledge_base': 'Optional files that provide domain-specific knowledge',
            'is_active': 'Whether this assistant is available for use',
            'is_public': 'Controls visibility in the public directory and company pages',
            'show_sidebar': 'Whether to show the sidebar in the chat interface. Only applies to non-general assistants.',
            'logo': 'Optional: Upload a specific logo for this assistant (used in listings, headers).',
            'avatar': 'Optional: Upload a specific avatar for the chat interface. Falls back to default/logo if empty.',
            # 'tier': 'Select the display tier (Gold, Silver, etc.) for this assistant in the public directory.', # Removed
            # 'is_featured': 'Check to request featuring this assistant (requires superadmin approval).', # Removed
            'custom_css': 'Custom CSS rules to style the assistant\'s appearance (e.g., chat interface). Use with caution.',
            'folder': 'Optional: Assign this assistant to a folder for organization.', # Added help text for folder
            'requested_tier_duration': 'Select duration if requesting a paid tier (Gold, Silver, Bronze). Only applicable if requesting a tier change.',
            'requested_featured_duration': 'Select duration if requesting featured status. Only applicable if requesting featured status.',
            'extra_context': 'Optional: Provide additional background information or specific instructions for the LLM, especially for Support Assistants.', # Added help text for extra_context
            # OpenAI Compatible fields
            'api_key': 'API key for the OpenAI-compatible API',
            'base_url': 'Base URL for the OpenAI-compatible API (e.g., https://api.openai.com/v1)',
            'custom_model_name': 'Model name to use with the OpenAI-compatible API (e.g., gpt-3.5-turbo)',
        }

    def __init__(self, *args, **kwargs):
        # Pop company before calling super and store it
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        # Add new request fields to the form's fields dictionary
        self.fields['request_new_tier'] = self.Meta.request_new_tier
        self.fields['request_featured_status'] = self.Meta.request_featured_status

        # If this is a community entity, hide the assistant_type field completely
        if self.company and self.company.entity_type == 'community':
            # Hide the assistant_type field by removing it from the form
            if 'assistant_type' in self.fields:
                del self.fields['assistant_type']

            # Add a hidden field to ensure the assistant_type is submitted
            self.fields['assistant_type_hidden'] = forms.CharField(
                widget=forms.HiddenInput(),
                initial=Assistant.TYPE_COMMUNITY,
                required=False
            )

        # Improve logo field handling
        if self.instance and self.instance.pk:
            if self.instance.logo:
                # Add a note about the current logo
                self.fields['logo'].help_text = f'Current logo: {self.instance.logo.name}. Upload a new logo or check "Clear" to remove.'
            else:
                # Add a note about company logo fallback
                company_logo_text = ""
                if self.instance.company and hasattr(self.instance.company, 'info') and self.instance.company.info and self.instance.company.info.logo:
                    company_logo_text = f" Currently using company logo: {self.instance.company.info.logo.name}"
                self.fields['logo'].help_text = f'Upload a logo for this assistant.{company_logo_text}'

            # Improve avatar field handling
            if self.instance.avatar:
                # Add a note about the current avatar
                self.fields['avatar'].help_text = f'Current avatar: {self.instance.avatar.name}. Upload a new avatar or check "Clear" to remove.'
            else:
                # Add a note about logo fallback
                self.fields['avatar'].help_text = 'Upload an avatar for the chat interface. If not set, the assistant logo will be used.'

        # Make duration fields not required by default
        self.fields['requested_tier_duration'].required = False
        self.fields['requested_featured_duration'].required = False
        # Set choices for duration fields
        self.fields['requested_tier_duration'].choices = [('', '--- Select Duration ---')] + Assistant.DURATION_CHOICES
        self.fields['requested_featured_duration'].choices = [('', '--- Select Duration ---')] + Assistant.DURATION_CHOICES

        # Set initial values for the new request fields based on current status
        if self.instance and self.instance.pk:
            # Set initial for request_new_tier to the current tier
            self.fields['request_new_tier'].initial = self.instance.tier
            # Set initial for request_featured_status based on current is_featured
            self.fields['request_featured_status'].initial = self.instance.is_featured

        # Add Bootstrap classes
        for field_name, field in self.fields.items():
            # Skip categories/tags as they are handled separately
            if field_name in ['categories', 'tags']:
                continue
            if isinstance(field.widget, (forms.TextInput, forms.Textarea, forms.Select, forms.NumberInput, forms.ClearableFileInput)):
                current_class = field.widget.attrs.get('class', '')
                # Ensure 'apply-tinymce' isn't duplicated if already present
                if 'apply-tinymce' in current_class and isinstance(field.widget, forms.Textarea):
                     pass # Already has the class, do nothing extra
                elif 'form-control' not in current_class and 'form-select' not in current_class:
                     field.widget.attrs['class'] = current_class + ' form-control' if not isinstance(field.widget, forms.Select) else current_class + ' form-select'
            elif isinstance(field.widget, forms.CheckboxInput):
                 field.widget.attrs['class'] = field.widget.attrs.get('class', '') + ' form-check-input'


        # Dynamically set the folder queryset based on the company
        # Use the company passed during form instantiation or from the instance
        current_company = self.company or (self.instance.pk and self.instance.company)
        if current_company:
            # Order folders by the new 'order' field, then 'name'
            self.fields['folder'].queryset = AssistantFolder.objects.filter(company=current_company).order_by('order', 'name')
            self.fields['folder'].required = False # Make folder optional
            self.fields['folder'].empty_label = "--- Unassigned ---" # Add an empty label
        else:
            self.fields['folder'].queryset = AssistantFolder.objects.none()
            self.fields['folder'].required = False
            self.fields['folder'].empty_label = "--- (No Company Context) ---"

        # Initialize categories/tags from listing if instance exists
        if self.instance and self.instance.pk:
            try:
                # Try to get the listing, create one if it doesn't exist
                from directory.models import AssistantListing
                listing, created = AssistantListing.objects.get_or_create(
                    assistant=self.instance,
                    defaults={
                        'is_listed': self.instance.is_public,
                        'short_description': self.instance.description,
                        'categories': [],
                        'tags': [],
                    }
                )
                # Set initial values for categories and tags
                if listing.categories:
                    self.fields['categories'].initial = ', '.join(listing.categories)
                if listing.tags:
                    self.fields['tags'].initial = ', '.join(listing.tags)
            except Exception as e:
                # If there's any error, just set empty values
                print(f"Warning: Could not initialize categories/tags from listing: {e}")
                self.fields['categories'].initial = ''
                self.fields['tags'].initial = ''


    def clean_description(self):
        description = self.cleaned_data.get('description', '')
        word_count = len(description.split())
        if word_count > 500:
            raise forms.ValidationError(f"Description must be 500 words or less. You entered {word_count} words.")
        return description

    def clean_temperature(self):
        temperature = self.cleaned_data.get('temperature')
        if temperature is not None and (temperature < 0.0 or temperature > 2.0):
            raise forms.ValidationError('Temperature must be between 0.0 and 2.0')
        return temperature

    def clean_max_tokens(self):
        max_tokens = self.cleaned_data.get('max_tokens')
        if max_tokens is not None and (max_tokens < 1 or max_tokens > 4096):
            raise forms.ValidationError('Max tokens must be between 1 and 4096')
        return max_tokens

    def clean_system_prompt(self):
        system_prompt = self.cleaned_data.get('system_prompt')
        if system_prompt and len(system_prompt) > 4000:
            raise forms.ValidationError('System prompt must be less than 4000 characters')
        return system_prompt

    def clean_knowledge_base(self):
        knowledge_base = self.cleaned_data.get('knowledge_base')
        if knowledge_base:
            allowed_types = ['text/plain', 'application/pdf', 'application/json']
            if knowledge_base.content_type not in allowed_types:
                raise forms.ValidationError('Only .txt, .pdf, and .json files are allowed')
            if knowledge_base.size > 10 * 1024 * 1024:
                raise forms.ValidationError('File size must be less than 10MB')
        return knowledge_base

    def clean(self):
        """Handle the assistant_type_hidden field for community entities."""
        cleaned_data = super().clean()

        # If assistant_type is not in the form but assistant_type_hidden is
        if 'assistant_type' not in cleaned_data and 'assistant_type_hidden' in cleaned_data:
            # Get the value from the hidden field
            assistant_type_hidden = cleaned_data.get('assistant_type_hidden')
            if assistant_type_hidden:
                # Set the assistant_type in cleaned_data
                cleaned_data['assistant_type'] = assistant_type_hidden

        return cleaned_data

    def save_categories_and_tags(self, assistant):
        """Save categories and tags to the assistant's listing."""
        try:
            from directory.models import AssistantListing

            # Get or create the listing
            listing, created = AssistantListing.objects.get_or_create(
                assistant=assistant,
                defaults={
                    'is_listed': assistant.is_public,
                    'short_description': assistant.description,
                    'categories': [],
                    'tags': [],
                }
            )

            # Process categories
            categories_str = self.cleaned_data.get('categories', '')
            if categories_str:
                categories = [cat.strip() for cat in categories_str.split(',') if cat.strip()]
                listing.categories = categories
            else:
                listing.categories = []

            # Process tags
            tags_str = self.cleaned_data.get('tags', '')
            if tags_str:
                tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()]
                listing.tags = tags
            else:
                listing.tags = []

            # Update other listing fields
            listing.is_listed = assistant.is_public
            listing.short_description = assistant.description
            listing.save()

            print(f"Saved categories: {listing.categories}, tags: {listing.tags}")

        except Exception as e:
            print(f"Error saving categories/tags to listing: {e}")

    def clean_logo(self):
        """Validate and process logo uploads"""
        logo = self.cleaned_data.get('logo')

        print(f"DEBUG FORM: clean_logo called, logo value: {logo}")
        print(f"DEBUG FORM: self.files content: {self.files}")

        # Simplified validation - just check if there's a file in request.FILES
        if 'logo' in self.files:
            logo_file = self.files['logo']
            print(f"DEBUG FORM: Logo found in self.files: {logo_file.name}")

            # Validate file type and size
            allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml']
            if logo_file.content_type not in allowed_types:
                raise forms.ValidationError('Only JPG, PNG, GIF, and SVG images are allowed')
            if logo_file.size > 5 * 1024 * 1024:  # 5MB limit
                raise forms.ValidationError('Image size must be less than 5MB')

            # Verify the file is a valid image
            try:
                from PIL import Image
                logo_file.seek(0)
                img = Image.open(logo_file)
                img.verify()  # Verify it's a valid image
                logo_file.seek(0)  # Reset file pointer
                print(f"DEBUG FORM: Logo file is a valid image: format={img.format}, size={img.size}")
            except Exception as e:
                print(f"DEBUG FORM: Logo file is not a valid image: {e}")
                raise forms.ValidationError(f"The uploaded file is not a valid image: {e}")

            # Ensure media directories exist
            from .media_utils import ensure_media_directories
            ensure_media_directories()
            print("DEBUG FORM: Ensured media directories exist")

            # Return the file directly
            return logo_file

        # If logo is False, it means the user checked "clear"
        if logo is False:
            print("DEBUG FORM: Logo cleared in form")
            return None

        # If we get here, no new file was uploaded and clear wasn't checked
        # Return None to let the view handle keeping the existing logo
        return None

    def clean_avatar(self):
        """Validate and process avatar uploads"""
        avatar = self.cleaned_data.get('avatar')

        print(f"DEBUG FORM: clean_avatar called, avatar value: {avatar}")

        # Simplified validation - just check if there's a file in request.FILES
        if 'avatar' in self.files:
            avatar_file = self.files['avatar']
            print(f"DEBUG FORM: Avatar found in self.files: {avatar_file.name}")

            # Validate file type and size
            allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml']
            if avatar_file.content_type not in allowed_types:
                raise forms.ValidationError('Only JPG, PNG, GIF, and SVG images are allowed')
            if avatar_file.size > 5 * 1024 * 1024:  # 5MB limit
                raise forms.ValidationError('Image size must be less than 5MB')

            # Verify the file is a valid image
            try:
                from PIL import Image
                avatar_file.seek(0)
                img = Image.open(avatar_file)
                img.verify()  # Verify it's a valid image
                avatar_file.seek(0)  # Reset file pointer
                print(f"DEBUG FORM: Avatar file is a valid image: format={img.format}, size={img.size}")
            except Exception as e:
                print(f"DEBUG FORM: Avatar file is not a valid image: {e}")
                raise forms.ValidationError(f"The uploaded file is not a valid image: {e}")

            # Ensure media directories exist
            from .media_utils import ensure_media_directories
            ensure_media_directories()

            # Return the file directly
            return avatar_file

        # If avatar is False, it means the user checked "clear"
        if avatar is False:
            print("DEBUG FORM: Avatar cleared in form")
            return None

        # If we get here, no new file was uploaded and clear wasn't checked
        # Return None to let the view handle keeping the existing avatar
        return None

    # Removed clean_categories and clean_tags as they are now optional CharFields


# --- Formset for Navigation Items ---

class BaseNavigationItemFormSet(forms.BaseInlineFormSet):
    """Custom base formset to handle the 'order' field conflict."""
    def add_fields(self, form, index):
        super().add_fields(form, index)
        # Make the model's 'order' field not required and hidden,
        # as can_order=True handles the actual ordering via the hidden ORDER field.
        if 'order' in form.fields:
            form.fields['order'].required = False
            form.fields['order'].widget = forms.HiddenInput()

# Custom callback to restrict section_type choices
def navigation_formfield_callback(field, **kwargs):
    if field.name == 'section_type':
        # Get only the 'text' choice from the module-level choices
        # Need to import SECTION_TYPE_CHOICES from models.py or define it here
        from .models import SECTION_TYPE_CHOICES # Import choices
        text_choice = next((choice for choice in SECTION_TYPE_CHOICES if choice[0] == 'text'), None)
        if text_choice:
            return forms.ChoiceField(choices=[text_choice], **kwargs)
        else:
            # Fallback if 'text' choice isn't found for some reason (shouldn't happen)
            return forms.ChoiceField(choices=[('text', 'Text')], **kwargs)
    # For all other fields, use the default formfield creation
    return field.formfield(**kwargs)


class NavigationItemForm(forms.ModelForm):
    """Form for creating/editing Navigation Items with default gallery value."""
    class Meta:
        model = NavigationItem
        fields = ['label', 'visible', 'section_type']
        widgets = {
            'label': forms.TextInput(attrs={'class': 'form-control form-control-sm', 'placeholder': 'Label'}),
            'visible': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'section_type': forms.Select(attrs={'class': 'form-select form-select-sm'}),
        }

    def save(self, commit=True):
        instance = super().save(commit=False)
        # Ensure gallery has a default value
        if not hasattr(instance, 'gallery') or instance.gallery is None:
            instance.gallery = []
        if commit:
            instance.save()
        return instance

NavigationItemFormSet = inlineformset_factory(
    Assistant,              # Parent model
    NavigationItem,         # Child model
    form=NavigationItemForm,
    formset=BaseNavigationItemFormSet,
    formfield_callback=navigation_formfield_callback, # Add the callback here
    extra=0,
    can_delete=True,
    can_order=True,
    widgets={
        'DELETE': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
    }
)

# TODO (Phase 2): Define forms/formsets for WebsiteDataEntry types

# --- Community Assistant Forms ---

from tinymce.widgets import TinyMCE

class CommunityContextForm(forms.Form):
    """Form for adding context to a community assistant."""
    title = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter a descriptive title...'}),
        label="Title",
        help_text="A descriptive title for this knowledge context"
    )
    text_content = forms.CharField(
        widget=TinyMCE(attrs={'cols': 80, 'rows': 10}),
        label="Context Content",
        help_text="Provide information that will help the assistant answer questions (max 500 words). You can format text, add links, and insert images."
    )
    keywords = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'keyword1, keyword2, keyword3'}),
        label="Keywords",
        help_text="Comma-separated keywords to categorize this context",
        required=False
    )
    image = forms.ImageField(
        widget=forms.ClearableFileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
        label="Image",
        help_text="Optional: Upload an image to include with this context",
        required=False
    )

    def clean_text_content(self):
        text_content = self.cleaned_data.get('text_content', '')
        # Strip HTML tags for word count
        import re
        text_without_html = re.sub(r'<[^>]*>', ' ', text_content)
        # Count words
        words = text_without_html.split()
        word_count = len(words)
        if word_count > 500:
            raise forms.ValidationError(f"Content must be 500 words or less. You entered {word_count} words.")
        return text_content

    def clean_keywords(self):
        keywords = self.cleaned_data.get('keywords', '')
        if keywords:
            # Convert comma-separated string to list of trimmed keywords
            return [k.strip() for k in keywords.split(',') if k.strip()]
        return []


class SimpleCommunityContextForm(forms.Form):
    """Simplified form for adding context from the dashboard."""
    title = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Give your knowledge a title'}),
        label="Title",
        required=True
    )
    text_content = forms.CharField(
        widget=TinyMCE(attrs={'cols': 80, 'rows': 6}),
        label="Knowledge Content (max 500 words)",
        required=True,
        help_text="Provide information that will help the assistant answer questions (max 500 words)."
    )

    def clean_text_content(self):
        text_content = self.cleaned_data.get('text_content', '')
        # Strip HTML tags for word count
        import re
        text_without_html = re.sub(r'<[^>]*>', ' ', text_content)
        # Count words
        words = text_without_html.split()
        word_count = len(words)
        if word_count > 500:
            raise forms.ValidationError(f"Content must be 500 words or less. You entered {word_count} words.")
        return text_content


class FlagQuestionForm(forms.Form):
    """Form for flagging a question that received an unsatisfactory answer."""
    reason = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control', 'placeholder': 'Why was this answer unsatisfactory?'}),
        label="Reason",
        help_text="Explain why this answer needs improvement",
        required=False
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': '<EMAIL>'}),
        label="Email (Optional)",
        help_text="Provide your email to receive notifications when this question gets better context",
        required=False
    )


class AssistantInteractionForm(forms.Form):
    """Form for interacting with an AI assistant."""
    message = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 1, 'placeholder': 'Enter your message...'}),
        label=False
    )


class AssistantFolderForm(forms.ModelForm):
    """Form for creating/editing Assistant Folders."""
    class Meta:
        model = AssistantFolder
        fields = ['name', 'order'] # Add the order field
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'order': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '0'}) # Add widget for order
        }
        labels = {
            'name': 'Folder Name',
            'order': 'Display Order' # Add label for order
        }
        help_texts = {
            'order': 'Enter a number to control sorting (lower numbers appear first). Defaults to 0.' # Add help text
        }

    def __init__(self, *args, **kwargs):
        # Need company context to check for uniqueness within that company
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        # Make order field not required explicitly if needed, though default=0 handles it
        self.fields['order'].required = False

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not self.company:
            # This shouldn't happen if the view passes the company, but safety check
            raise forms.ValidationError("Company context is missing.")

        # Check for uniqueness within the company, excluding self if editing
        queryset = AssistantFolder.objects.filter(company=self.company, name__iexact=name)
        if self.instance and self.instance.pk:
            queryset = queryset.exclude(pk=self.instance.pk)
        if queryset.exists():
            raise forms.ValidationError("A folder with this name already exists in this company.")
        return name


class AssistantTrainingForm(forms.Form):
    """Form for training an AI assistant with new data."""
    training_data = forms.FileField(
        widget=forms.ClearableFileInput(attrs={'class': 'form-control', 'accept': '.txt,.pdf,.json'}),
        help_text='Upload .txt, .pdf, or .json files (max 10MB)'
    )
    description = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'Describe what this training data contains...'}),
        required=False,
        max_length=200,
        help_text='Optional description of the training data'
    )

    def clean_training_data(self):
        training_data = self.cleaned_data.get('training_data')
        if training_data:
            allowed_types = ['text/plain', 'application/pdf', 'application/json']
            if training_data.content_type not in allowed_types:
                raise forms.ValidationError('Only .txt, .pdf, and .json files are allowed')
            if training_data.size > 10 * 1024 * 1024:
                raise forms.ValidationError('File size must be less than 10MB')
        return training_data

# --- Forms for Assistant Creation Wizard ---

class AssistantBasicInfoForm(forms.ModelForm):
    """Wizard Step 1: Basic Info"""

    # Add a hidden field for assistant_type to ensure it's submitted when the field is hidden
    assistant_type_hidden = forms.CharField(
        widget=forms.HiddenInput(),
        required=False
    )

    def __init__(self, *args, **kwargs):
        # Extract company from kwargs if present
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        # Apply field customizations
        self._customize_fields()

        # If this is a community entity, hide the assistant_type field and set a default value
        if self.company and self.company.entity_type == 'community':
            # Set the initial value for the hidden field
            self.fields['assistant_type_hidden'].initial = Assistant.TYPE_COMMUNITY

            # Hide the assistant_type field completely by removing it from the form
            if 'assistant_type' in self.fields:
                del self.fields['assistant_type']

    class Meta:
        model = Assistant
        fields = ['name', 'persona_name', 'description', 'assistant_type']
        widgets = {
            # description widget removed (handled by HTMLField)
        }
        help_texts = {
            'name': 'Internal name for identification (e.g., "support-v1").',
            'persona_name': 'Optional: Name the assistant uses in chat (e.g., "Support Bot"). Defaults to internal name.',
            'description': 'A brief description of what this assistant does.',
            'assistant_type': 'Choose the primary function (Customer Support provides website data features).',
        }

    # Add any field customization in the existing __init__ method
    def _customize_fields(self):
        for field_name, field in self.fields.items():
            if isinstance(field.widget, (forms.TextInput, forms.Textarea, forms.Select)):
                current_class = field.widget.attrs.get('class', '')
                # Ensure 'apply-tinymce' isn't duplicated if already present
                if 'apply-tinymce' in current_class and isinstance(field.widget, forms.Textarea):
                     pass # Already has the class, do nothing extra
                elif 'form-control' not in current_class and 'form-select' not in current_class:
                     field.widget.attrs['class'] = current_class + ' form-control' if not isinstance(field.widget, forms.Select) else current_class + ' form-select'

    def clean_description(self):
        description = self.cleaned_data.get('description', '')
        word_count = len(description.split())
        if word_count > 500:
            raise forms.ValidationError(f"Description must be 500 words or less. You entered {word_count} words.")
        return description

    def clean(self):
        """Handle the assistant_type_hidden field for community entities."""
        cleaned_data = super().clean()

        # If this is a community entity and assistant_type is not in the form
        if self.company and self.company.entity_type == 'community' and 'assistant_type' not in cleaned_data:
            # Get the value from the hidden field
            assistant_type_hidden = cleaned_data.get('assistant_type_hidden')
            if assistant_type_hidden:
                # Set the assistant_type in cleaned_data
                cleaned_data['assistant_type'] = assistant_type_hidden
            else:
                # Default to community assistant type
                cleaned_data['assistant_type'] = Assistant.TYPE_COMMUNITY

        return cleaned_data

class AssistantConfigForm(forms.ModelForm):
    """Wizard Step 2: Configuration"""
    categories = forms.CharField(
        required=False,
        help_text='Comma-separated categories (e.g., Finance, Support, Sales)',
        widget=forms.TextInput(attrs={'placeholder': 'Finance, Support, Sales'})
    )
    tags = forms.CharField(
        required=False,
        help_text='Comma-separated tags for searching (e.g., billing, python, api)',
        widget=forms.TextInput(attrs={'placeholder': 'billing, python, api'})
    )

    class Meta:
        model = Assistant
        fields = [
            'model', 'temperature', 'max_tokens', 'system_prompt', 'greeting_message',
            'logo', 'avatar', 'is_public', 'show_sidebar', 'show_sidebar_public',
            # OpenAI Compatible fields
            'api_key', 'base_url', 'custom_model_name',
        ] # Removed is_active, only settable by superusers
        widgets = {
            # system_prompt widget removed (handled by HTMLField)
            # greeting_message widget removed (handled by HTMLField)
            'temperature': forms.NumberInput(attrs={'step': '0.1', 'min': '0', 'max': '2'}),
            'max_tokens': forms.NumberInput(attrs={'step': '1', 'min': '1', 'max': '4096'}),
            'logo': forms.ClearableFileInput(attrs={'accept': 'image/*', 'class': 'form-control'}),
            'avatar': forms.ClearableFileInput(attrs={'accept': 'image/*', 'class': 'form-control'}), # Added avatar widget
            # OpenAI Compatible fields
            'api_key': forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Enter API key', 'autocomplete': 'off'}, render_value=True),
            'base_url': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://api.openai.com/v1'}),
            'custom_model_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'gpt-3.5-turbo'}),
        }
        help_texts = {
            'model': 'The underlying language model to use.',
            'temperature': 'Controls randomness (0.0-2.0). Lower is more focused.',
            'max_tokens': 'Maximum length of generated responses.',
            'system_prompt': 'Instructions guiding behavior (especially for General type).',
            'greeting_message': 'Optional: Custom initial greeting message for chat.',
            'logo': 'Optional: Upload a specific logo for this assistant (used in listings, headers).',
            'avatar': 'Optional: Upload a specific avatar for the chat interface. Falls back to default/logo if empty.', # Added avatar help text
            'is_active': 'Allow users to interact with this assistant.',
            'is_public': 'Make this assistant visible in the public directory.',
            'show_sidebar': 'Whether to show the sidebar in the chat interface. Only applies to non-general assistants.',
            # OpenAI Compatible fields
            'api_key': 'API key for the OpenAI-compatible API',
            'base_url': 'Base URL for the OpenAI-compatible API (e.g., https://api.openai.com/v1)',
            'custom_model_name': 'Model name to use with the OpenAI-compatible API (e.g., gpt-3.5-turbo)',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Initialize categories/tags from listing if instance exists (for wizard)
        if self.instance and self.instance.pk:
            try:
                # Try to get the listing, create one if it doesn't exist
                from directory.models import AssistantListing
                listing, created = AssistantListing.objects.get_or_create(
                    assistant=self.instance,
                    defaults={
                        'is_listed': self.instance.is_public,
                        'short_description': self.instance.description,
                        'categories': [],
                        'tags': [],
                    }
                )
                # Set initial values for categories and tags
                if listing.categories:
                    self.fields['categories'].initial = ', '.join(listing.categories)
                if listing.tags:
                    self.fields['tags'].initial = ', '.join(listing.tags)
            except Exception as e:
                # If there's any error, just set empty values
                print(f"Warning: Could not initialize categories/tags from listing in wizard: {e}")
                self.fields['categories'].initial = ''
                self.fields['tags'].initial = ''

        for field_name, field in self.fields.items():
            if field_name in ['categories', 'tags']: continue # Skip non-model fields
            if isinstance(field.widget, (forms.TextInput, forms.Textarea, forms.Select, forms.NumberInput, forms.ClearableFileInput)):
                 current_class = field.widget.attrs.get('class', '')
                 # Ensure 'apply-tinymce' isn't duplicated if already present
                 if 'apply-tinymce' in current_class and isinstance(field.widget, forms.Textarea):
                     pass # Already has the class, do nothing extra
                 elif 'form-control' not in current_class and 'form-select' not in current_class:
                     field.widget.attrs['class'] = current_class + ' form-control' if not isinstance(field.widget, forms.Select) else current_class + ' form-select'
            elif isinstance(field.widget, forms.CheckboxInput):
                 field.widget.attrs['class'] = field.widget.attrs.get('class', '') + ' form-check-input'

    def clean_temperature(self):
        temperature = self.cleaned_data.get('temperature')
        if temperature is not None and (temperature < 0.0 or temperature > 2.0):
            raise forms.ValidationError('Temperature must be between 0.0 and 2.0')
        return temperature

    def clean_max_tokens(self):
        max_tokens = self.cleaned_data.get('max_tokens')
        if max_tokens is not None and (max_tokens < 1 or max_tokens > 4096):
            raise forms.ValidationError('Max tokens must be between 1 and 4096')
        return max_tokens

    def save_categories_and_tags(self, assistant):
        """Save categories and tags to the assistant's listing."""
        try:
            from directory.models import AssistantListing

            # Get or create the listing
            listing, created = AssistantListing.objects.get_or_create(
                assistant=assistant,
                defaults={
                    'is_listed': assistant.is_public,
                    'short_description': assistant.description,
                    'categories': [],
                    'tags': [],
                }
            )

            # Process categories
            categories_str = self.cleaned_data.get('categories', '')
            if categories_str:
                categories = [cat.strip() for cat in categories_str.split(',') if cat.strip()]
                listing.categories = categories
            else:
                listing.categories = []

            # Process tags
            tags_str = self.cleaned_data.get('tags', '')
            if tags_str:
                tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()]
                listing.tags = tags
            else:
                listing.tags = []

            # Update other listing fields
            listing.is_listed = assistant.is_public
            listing.short_description = assistant.description
            listing.save()

            print(f"Wizard saved categories: {listing.categories}, tags: {listing.tags}")

        except Exception as e:
            print(f"Error saving categories/tags to listing in wizard: {e}")