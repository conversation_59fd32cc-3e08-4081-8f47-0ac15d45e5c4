/* NUP Light Mode Override CSS */
/* This file overrides all dark mode styles to ensure white backgrounds */

/* ===== HIGHEST PRIORITY OVERRIDES ===== */
/* Use maximum specificity to override all dark styles */
html[data-theme="dark"], html[data-theme="light"], html,
body[data-theme="dark"], body[data-theme="light"], body,
main[data-theme="dark"], main[data-theme="light"], main {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== FORCE OFFICIAL NUP LIGHT MODE VARIABLES ===== */
:root, [data-theme="dark"], [data-theme="light"], html, body {
    /* Override all dark mode variables with official NUP colors */
    --dark-bg-primary: #ffffff !important;         /* Neutral White - Primary background */
    --dark-bg-secondary: #f7f7f7 !important;       /* Light Gray - Secondary background */
    --dark-bg-tertiary: #ffffff !important;        /* Neutral White */
    --dark-bg-elevated: #ffffff !important;        /* Neutral White */

    --dark-text-primary: #242424 !important;       /* Dark Charcoal - Main text */
    --dark-text-secondary: #797979 !important;     /* Medium Gray - Secondary text */
    --dark-text-muted: #797979 !important;         /* Medium Gray */

    /* Force official NUP accent colors */
    --dark-accent-primary: #cf2e2e !important;     /* NUP Vivid Red - Primary accent */
    --dark-accent-secondary: #f7bd00 !important;   /* NUP Strong Yellow/Gold - Secondary accent */
    --dark-accent-tertiary: #cf2e2e !important;    /* NUP Vivid Red */

    --dark-border: #e5e5e5 !important;             /* Light borders */
    --dark-shadow: rgba(37, 38, 56, 0.1) !important;       /* Subtle shadows with new color */
    --dark-shadow-light: rgba(37, 38, 56, 0.05) !important; /* Light shadows with new color */

    /* Remove all blue/purple colors */
    --blue: #cf2e2e !important;
    --purple: #252638 !important;
    --indigo: #252638 !important;
    --navy: #252638 !important;
    --primary-blue: #cf2e2e !important;
}

/* ===== AGGRESSIVE BACKGROUND OVERRIDES ===== */
/* Override any element with dark background */
*[style*="background-color: #121212"] *,
*[style*="background-color: #000000"] *,
*[style*="background-color: #1e1e1e"] *,
*[style*="background-color: #252525"] *,
*[style*="background-color: #2c2c2c"] *,
*[style*="background: #121212"] *,
*[style*="background: #000000"] *,
*[style*="background: #1e1e1e"] *,
*[style*="background: #252525"] *,
*[style*="background: #2c2c2c"] * {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== FORCE WHITE BACKGROUNDS ===== */
html, body, main, .container, .container-fluid {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* Override dark mode classes */
.dark-mode, .dark-theme, [data-theme="dark"] {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* Force white backgrounds on all major elements */
.card, .modal, .dropdown-menu, .navbar, .footer {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* Override any specific dark background classes */
.bg-dark, .bg-black, .bg-secondary {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== OVERRIDE INLINE STYLES ===== */
/* Override any inline dark styles */
[style*="background-color: #121212"],
[style*="background-color: #000000"],
[style*="background-color: #1e1e1e"],
[style*="background-color: #252525"],
[style*="background-color: #2c2c2c"],
[style*="background: #121212"],
[style*="background: #000000"],
[style*="background: #1e1e1e"],
[style*="background: #252525"],
[style*="background: #2c2c2c"] {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== TEXT COLOR OVERRIDES ===== */
/* Ensure text is readable on white backgrounds */
[style*="color: #ffffff"],
[style*="color: #cccccc"],
.text-white, .text-light {
    color: #333333 !important;
}

/* ===== HIGH CONTRAST TEXT FIXES ===== */
/* Ensure all text elements have high contrast on white backgrounds */
h1, h2, h3, h4, h5, h6,
.display-1, .display-2, .display-3, .display-4, .display-5, .display-6,
p, span, div, a, label, button,
.lead, .text-muted, .small, .text-sm,
.card-title, .card-text, .card-subtitle,
.form-label, .form-control, .form-text,
.nav-link, .navbar-brand, .dropdown-item,
.btn, .badge, .alert,
.list-group-item, .table td, .table th {
    color: #333333 !important;
}

/* Specific text elements that might be invisible */
.text-primary, .text-secondary, .text-success, .text-info, .text-warning, .text-danger {
    color: #333333 !important;
}

/* Form elements text */
input, textarea, select, option {
    color: #333333 !important;
    background-color: #ffffff !important;
}

/* Placeholder text */
input::placeholder, textarea::placeholder {
    color: #666666 !important;
}

/* Link text */
a, a:hover, a:focus, a:visited {
    color: #cf2e2e !important;
}

/* Button text */
.btn, button {
    color: #ffffff !important;
}

/* ===== NUP BUTTON STYLING WITH BOLD WHITE TEXT ===== */
.btn, button, input[type="button"], input[type="submit"], input[type="reset"] {
    font-weight: 700 !important;
    color: #ffffff !important;
    transition: all 0.3s ease !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 6px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    cursor: pointer !important;
}

.btn-primary, .btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-outline-light):not(.btn-light) {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.btn-primary:hover, .btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-outline-light):not(.btn-light):hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

.btn-secondary {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
}

.btn-secondary:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

.btn-outline-primary {
    border: 2px solid #cf2e2e !important;
    color: #cf2e2e !important;
    background-color: transparent !important;
    font-weight: 700 !important;
}

.btn-outline-primary:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

.btn-outline-secondary {
    border: 2px solid #252638 !important;
    color: #252638 !important;
    background-color: transparent !important;
    font-weight: 700 !important;
}

.btn-outline-secondary:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

.btn-light {
    background-color: #f8f9fa !important;
    border-color: #f8f9fa !important;
    color: #333333 !important;
    font-weight: 700 !important;
}

.btn-light:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* All other button variants follow NUP color scheme */
.btn-success, .btn-danger, .btn-info, .btn-dark {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    font-weight: 700 !important;
}

.btn-success:hover, .btn-danger:hover, .btn-info:hover, .btn-dark:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

.btn-warning {
    background-color: #f7bd00 !important;
    border-color: #f7bd00 !important;
    color: #333333 !important;
    font-weight: 700 !important;
}

.btn-warning:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* Button focus states */
.btn:focus, .btn:active, .btn.active {
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
    outline: none !important;
}

/* Button disabled states */
.btn:disabled, .btn.disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Navigation text */
.navbar-nav .nav-link {
    color: #333333 !important;
}

.navbar-brand {
    color: #333333 !important;
}

/* ===== ENHANCED CARD STYLING WITH 3D EFFECTS ===== */
.card {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 3px solid #333333 !important;
    border-radius: 12px !important;
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.2),
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.card:hover {
    border-color: #cf2e2e !important;
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.25),
        0 6px 12px rgba(0, 0, 0, 0.2),
        0 3px 6px rgba(0, 0, 0, 0.15),
        0 0 0 2px rgba(207, 46, 46, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
}

.card-header {
    color: #333333 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 2px solid #333333 !important;
    border-radius: 10px 10px 0 0 !important;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    position: relative !important;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #333333, transparent) !important;
}

.card-body {
    color: #333333 !important;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
    position: relative !important;
}

.card-footer {
    color: #333333 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-top: 2px solid #333333 !important;
    border-radius: 0 0 10px 10px !important;
    box-shadow:
        0 -2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5) !important;
    position: relative !important;
}

.card-footer::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #333333, transparent) !important;
}

/* Table text */
.table {
    color: #333333 !important;
}

.table th, .table td {
    color: #333333 !important;
}

/* List text */
.list-group-item {
    color: #333333 !important;
    background-color: #ffffff !important;
}

/* Alert text */
.alert {
    color: #333333 !important;
}

/* Badge text - keep white text on colored backgrounds */
.badge {
    color: #ffffff !important;
}

/* Dropdown text */
.dropdown-menu {
    background-color: #ffffff !important;
}

.dropdown-item {
    color: #333333 !important;
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: #f8f9fa !important;
    color: #333333 !important;
}

/* Modal text */
.modal-content {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.modal-header, .modal-body, .modal-footer {
    color: #333333 !important;
}

/* Tooltip and popover text */
.tooltip-inner {
    background-color: #333333 !important;
    color: #ffffff !important;
}

.popover {
    background-color: #ffffff !important;
    color: #333333 !important;
}

/* Breadcrumb text */
.breadcrumb-item {
    color: #333333 !important;
}

.breadcrumb-item.active {
    color: #666666 !important;
}

/* Pagination text */
.page-link {
    color: #cf2e2e !important;
    background-color: #ffffff !important;
}

.page-item.active .page-link {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* ===== HOMEPAGE SPECIFIC FIXES ===== */
/* Fix invisible text on homepage */
.hero-section, .hero-section * {
    color: #333333 !important;
}

.hero-section h1, .hero-section .display-4 {
    color: #333333 !important;
}

.hero-section p, .hero-section .lead {
    color: #333333 !important;
}

/* Search form text */
.search-form-card, .search-form-card * {
    color: #333333 !important;
}

.search-form-card h3, .search-form-card h4, .search-form-card h5 {
    color: #333333 !important;
}

/* Join movement section */
.join-movement, .join-movement * {
    color: #333333 !important;
}

/* CTA section */
.cta-section, .cta-section * {
    color: #333333 !important;
}

/* Company logos section */
.company-logos, .company-logos * {
    color: #333333 !important;
}

/* Features section */
.features-section, .features-section * {
    color: #333333 !important;
}

/* Stats section */
.stats-section, .stats-section * {
    color: #333333 !important;
}

/* Testimonials section */
.testimonials-section, .testimonials-section * {
    color: #333333 !important;
}

/* Any section with white background */
.bg-white, .bg-white * {
    color: #333333 !important;
}

.bg-light, .bg-light * {
    color: #333333 !important;
}

/* Override any remaining white text */
[style*="color: white"],
[style*="color: #fff"],
[style*="color: #ffffff"],
[style*="color: rgb(255, 255, 255)"],
[style*="color: rgba(255, 255, 255"] {
    color: #333333 !important;
}

/* Ensure form elements are visible */
.form-control, .form-select, .form-check-input {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
}

.form-control:focus, .form-select:focus {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #cf2e2e !important;
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
}

/* Ensure labels are visible */
.form-label, label {
    color: #333333 !important;
}

/* Ensure checkboxes and radio buttons are visible */
.form-check-label {
    color: #333333 !important;
}

/* Ensure any text in containers is visible */
.container, .container-fluid, .row, .col,
[class*="col-"] {
    color: #333333 !important;
}

/* Override any computed styles that might make text invisible - EXCLUDE FOOTER AND BUTTONS */
*:not([class*="nup-footer"]):not([id*="nup-footer"]):not([class*="nup-logo"]):not([id*="nup-logo"]):not([class*="nup-social"]):not([id*="nup-social"]):not([class*="nup-news"]):not([id*="nup-news"]):not([class*="nup-button"]):not([id*="nup-button"]):not(.btn):not(button):not([type="button"]):not([type="submit"]):not([type="reset"]):not(.nup-btn-text):not(.nup-button-text) {
    color: #333333 !important;
}

/* Exception for elements that should have white text */
.btn-primary, .btn-success, .btn-danger, .btn-warning, .btn-info, .btn-dark,
.badge, .alert-primary, .alert-success, .alert-danger, .alert-warning, .alert-info,
.bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info, .bg-dark,
.text-bg-primary, .text-bg-success, .text-bg-danger, .text-bg-warning, .text-bg-info, .text-bg-dark {
    color: #ffffff !important;
}

/* Ensure footer elements maintain white text */
[class*="nup-footer"], [id*="nup-footer"],
[class*="nup-logo"], [id*="nup-logo"],
[class*="nup-social"], [id*="nup-social"],
[class*="nup-news"], [id*="nup-news"] {
    color: #ffffff !important;
}

/* ===== BUTTON TEXT SPECIFIC TARGETING ===== */
/* Button text should always be white with specific classes and IDs */
.btn, .btn *,
button, button *,
input[type="button"], input[type="submit"], input[type="reset"],
[class*="nup-button"], [class*="nup-button"] *,
[id*="nup-button"], [id*="nup-button"] *,
.nup-btn-text, .nup-button-text,
.btn-text, .button-text {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* Exception for light and warning buttons */
.btn-light, .btn-light *,
.btn-warning, .btn-warning *,
[class*="nup-button-light"], [class*="nup-button-light"] *,
[id*="nup-button-light"], [id*="nup-button-light"] *,
.nup-btn-light, .nup-button-light {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* Override on hover for light/warning */
.btn-light:hover, .btn-light:hover *,
.btn-warning:hover, .btn-warning:hover *,
[class*="nup-button-light"]:hover, [class*="nup-button-light"]:hover *,
[id*="nup-button-light"]:hover, [id*="nup-button-light"]:hover *,
.nup-btn-light:hover, .nup-button-light:hover {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== BUTTON OVERRIDES ===== */
/* Override dark buttons to use official NUP colors */
.btn-dark, .btn-secondary {
    background-color: #cf2e2e !important;  /* NUP Vivid Red */
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.btn-dark:hover, .btn-secondary:hover {
    background-color: #b82626 !important;  /* Darker red for hover */
    border-color: #b82626 !important;
    color: #ffffff !important;
}

/* ===== NAVIGATION OVERRIDES ===== */
.navbar-dark {
    background-color: #ffffff !important;  /* Neutral White */
}

.navbar-dark .navbar-nav .nav-link {
    color: #242424 !important;  /* Dark Charcoal - Main text */
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #cf2e2e !important;  /* NUP Vivid Red */
}

.navbar-dark .navbar-brand {
    color: #cf2e2e !important;  /* NUP Vivid Red */
}

/* ===== FORM OVERRIDES ===== */
.form-control, .form-select, .form-check-input {
    background-color: #FFFFFF !important;
    border-color: #dee2e6 !important;
    color: #333333 !important;
}

.form-control:focus, .form-select:focus {
    background-color: #FFFFFF !important;
    border-color: #E31B23 !important;
    box-shadow: 0 0 0 0.2rem rgba(227, 27, 35, 0.25) !important;
}

/* ===== TABLE OVERRIDES ===== */
.table-dark {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

.table-dark th, .table-dark td {
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* ===== ALERT OVERRIDES ===== */
.alert-dark {
    background-color: #F8F9FA !important;
    border-color: #dee2e6 !important;
    color: #333333 !important;
}

/* ===== CHAT AND MESSAGE OVERRIDES ===== */
.message, .chat-message, .user-message, .assistant-message {
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
}

/* ===== SIDEBAR AND PANEL OVERRIDES ===== */
.sidebar, .panel, .offcanvas {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== DROPDOWN OVERRIDES ===== */
.dropdown-menu {
    background-color: #FFFFFF !important;
    border-color: #dee2e6 !important;
}

.dropdown-item {
    color: #333333 !important;
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: #F8F9FA !important;
    color: #E31B23 !important;
}

/* ===== MODAL OVERRIDES ===== */
.modal-content {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

.modal-header {
    background-color: #FFFFFF !important;
    border-bottom-color: #dee2e6 !important;
}

.modal-footer {
    background-color: #FFFFFF !important;
    border-top-color: #dee2e6 !important;
}

/* ===== FOOTER OVERRIDES ===== */
.footer, footer, footer.footer {
    background-color: #F8F9FA !important;
    color: #333333 !important;
    border-top-color: #dee2e6 !important;
}

/* Override footer dark mode styles - Use NUP dark footer */
[data-theme="dark"] footer,
[data-theme="dark"] footer.footer,
[data-theme="dark"] .footer {
    background-color: #252638 !important;  /* NUP dark footer color */
    color: #ffffff !important;  /* White text */
    border-top-color: #252638 !important;
}

/* ===== FOOTER PROTECTION - NEVER OVERRIDE FOOTER BACKGROUNDS ===== */
/* Ensure all NUP footer elements maintain dark background regardless of other rules */
#nup-main-footer,
.nup-main-footer,
#nup-footer-top-section,
.nup-footer-top-section,
#nup-footer-bottom-section,
.nup-footer-bottom-section,
#nup-footer-container,
.nup-footer-container,
#nup-footer-row,
.nup-footer-row,
#nup-footer-logo-column,
.nup-footer-logo-column,
#nup-footer-social-column,
.nup-footer-social-column,
#nup-footer-news-column,
.nup-footer-news-column,
#nup-footer-logo-widget,
.nup-footer-logo-widget,
#nup-footer-social-widget,
.nup-footer-social-widget,
#nup-footer-news-widget,
.nup-footer-news-widget,
#nup-footer-bottom-container,
.nup-footer-bottom-container,
#nup-footer-bottom-inner,
.nup-footer-bottom-inner,
#nup-footer-copyright,
.nup-footer-copyright,
[class*="nup-footer"],
[id*="nup-footer"],
[class*="nup-logo"],
[id*="nup-logo"],
[class*="nup-social"],
[id*="nup-social"],
[class*="nup-news"],
[id*="nup-news"] {
    background-color: #252638 !important;
    background: #252638 !important;
    color: #ffffff !important;
}

/* Force all children of footer elements to maintain dark styling */
#nup-main-footer *,
.nup-main-footer *,
#nup-footer-top-section *,
.nup-footer-top-section *,
#nup-footer-bottom-section *,
.nup-footer-bottom-section *,
[class*="nup-footer"] *,
[id*="nup-footer"] *,
[class*="nup-logo"] *,
[id*="nup-logo"] *,
[class*="nup-social"] *,
[id*="nup-social"] *,
[class*="nup-news"] *,
[id*="nup-news"] * {
    color: #ffffff !important;
}

/* Override any background styles that might be applied to footer elements */
#nup-main-footer,
.nup-main-footer,
[class*="nup-footer"],
[id*="nup-footer"],
[class*="nup-logo"],
[id*="nup-logo"],
[class*="nup-social"],
[id*="nup-social"],
[class*="nup-news"],
[id*="nup-news"] {
    background-color: #252638 !important;
    background: #252638 !important;
}

/* Ensure footer elements are never affected by white background overrides */
html[data-theme="dark"] #nup-main-footer,
html[data-theme="dark"] .nup-main-footer,
html[data-theme="dark"] [class*="nup-footer"],
html[data-theme="dark"] [id*="nup-footer"],
html[data-theme="dark"] [class*="nup-logo"],
html[data-theme="dark"] [id*="nup-logo"],
html[data-theme="dark"] [class*="nup-social"],
html[data-theme="dark"] [id*="nup-social"],
html[data-theme="dark"] [class*="nup-news"],
html[data-theme="dark"] [id*="nup-news"],
body[data-theme="dark"] #nup-main-footer,
body[data-theme="dark"] .nup-main-footer,
body[data-theme="dark"] [class*="nup-footer"],
body[data-theme="dark"] [id*="nup-footer"],
body[data-theme="dark"] [class*="nup-logo"],
body[data-theme="dark"] [id*="nup-logo"],
body[data-theme="dark"] [class*="nup-social"],
body[data-theme="dark"] [id*="nup-social"],
body[data-theme="dark"] [class*="nup-news"],
body[data-theme="dark"] [id*="nup-news"] {
    background-color: #252638 !important;
    background: #252638 !important;
    color: #ffffff !important;
}

/* Footer headings */
footer h5, footer h6,
footer.footer h5, footer.footer h6,
.footer h5, .footer h6 {
    color: #ffffff !important;  /* White text for dark footer */
}

/* Footer links */
footer a, footer.footer a, .footer a {
    color: #ffffff !important;  /* White text for dark footer */
}

footer a:hover, footer.footer a:hover, .footer a:hover {
    color: #cf2e2e !important;  /* NUP red on hover */
}

/* Footer text */
footer .text-muted,
footer.footer .text-muted,
.footer .text-muted {
    color: #ffffff !important;  /* White text for dark footer */
}

/* ===== BREADCRUMB OVERRIDES ===== */
.breadcrumb {
    background-color: #F8F9FA !important;
}

.breadcrumb-item a {
    color: #E31B23 !important;
}

/* ===== PAGINATION OVERRIDES ===== */
.page-link {
    background-color: #FFFFFF !important;
    border-color: #dee2e6 !important;
    color: #E31B23 !important;
}

.page-link:hover {
    background-color: #F8F9FA !important;
    border-color: #E31B23 !important;
    color: #c41419 !important;
}

.page-item.active .page-link {
    background-color: #E31B23 !important;
    border-color: #E31B23 !important;
    color: #FFFFFF !important;
}

/* ===== BADGE OVERRIDES ===== */
.badge-dark, .badge-secondary {
    background-color: #E31B23 !important;
    color: #FFFFFF !important;
}

/* ===== PROGRESS BAR OVERRIDES ===== */
.progress {
    background-color: #F8F9FA !important;
}

.progress-bar {
    background-color: #E31B23 !important;
}

/* ===== TOOLTIP OVERRIDES ===== */
.tooltip-inner {
    background-color: #333333 !important;
    color: #FFFFFF !important;
}

/* ===== POPOVER OVERRIDES ===== */
.popover {
    background-color: #FFFFFF !important;
    border-color: #dee2e6 !important;
}

.popover-header {
    background-color: #F8F9FA !important;
    border-bottom-color: #dee2e6 !important;
    color: #333333 !important;
}

.popover-body {
    color: #333333 !important;
}

/* ===== ACCORDION OVERRIDES ===== */
.accordion-item {
    background-color: #FFFFFF !important;
    border-color: #dee2e6 !important;
}

.accordion-header button {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

.accordion-body {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== LIST GROUP OVERRIDES ===== */
.list-group-item {
    background-color: #FFFFFF !important;
    border-color: #dee2e6 !important;
    color: #333333 !important;
}

.list-group-item:hover {
    background-color: #F8F9FA !important;
}

.list-group-item.active {
    background-color: #E31B23 !important;
    border-color: #E31B23 !important;
    color: #FFFFFF !important;
}

/* ===== HOMEPAGE SPECIFIC OVERRIDES ===== */
/* Override homepage dark mode styles */
[data-theme="dark"] .hero-section,
[data-theme="light"] .hero-section,
.hero-section {
    background-color: #FFFFFF !important;
    background-image: none !important;
    background-attachment: scroll !important;
    color: #333333 !important;
}

/* Override any blue backgrounds to NUP red */
[style*="background-color: #0066ff"],
[style*="background-color: rgb(0, 102, 255)"],
[style*="background: #0066ff"],
[style*="background: rgb(0, 102, 255)"],
[style*="background-color: blue"],
[style*="background: blue"],
[class*="bg-primary"],
[class*="bg-blue"] {
    background-color: #E31B23 !important;
    background-image: none !important;
    color: #FFFFFF !important;
}

/* ===== SECTION BACKGROUND OVERRIDES ===== */
/* Force all sections to have white or light backgrounds */
section, .section {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* Override any dark section backgrounds */
[data-theme="dark"] section,
[data-theme="dark"] .section,
.dark-mode section,
.dark-mode .section {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== CONTAINER OVERRIDES ===== */
/* Ensure all containers have white backgrounds */
.container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

[data-theme="dark"] .container,
[data-theme="dark"] .container-fluid,
.dark-mode .container,
.dark-mode .container-fluid {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== FINAL CATCH-ALL ===== */
/* Override any remaining dark elements */
[data-theme="dark"] *,
.dark-mode * {
    /* Only override background colors, not all properties */
}

/* Specific overrides for elements with dark classes */
*[class*="dark"]:not(.btn):not(.badge):not(.alert),
*[class*="black"]:not(.btn):not(.badge):not(.alert) {
    background-color: #FFFFFF !important;
    color: #333333 !important;
}

/* Override any remaining dark backgrounds */
[style*="background-color: #121212"],
[style*="background-color: #1e1e1e"],
[style*="background-color: #252525"],
[style*="background-color: #2c2c2c"],
[style*="background: #121212"],
[style*="background: #1e1e1e"],
[style*="background: #252525"],
[style*="background: #2c2c2c"] {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== NUCLEAR OPTION - OVERRIDE EVERYTHING ===== */
/* Maximum specificity override for all elements */
html html, body body, main main,
div div, section section, article article,
header header, footer footer, nav nav,
.container .container, .row .row, .col .col {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
}

/* Force white on all possible selectors - EXCLUDE FOOTER */
html:not(.nup-main-footer):not(#nup-main-footer),
body:not(.nup-main-footer):not(#nup-main-footer),
main:not(.nup-main-footer):not(#nup-main-footer),
div:not([class*="nup-footer"]):not([id*="nup-footer"]):not([class*="nup-logo"]):not([id*="nup-logo"]):not([class*="nup-social"]):not([id*="nup-social"]):not([class*="nup-news"]):not([id*="nup-news"]),
section:not(.nup-footer-top-section):not(#nup-footer-top-section):not(.nup-footer-bottom-section):not(#nup-footer-bottom-section),
article:not([class*="nup-footer"]):not([id*="nup-footer"]),
header:not([class*="nup-footer"]):not([id*="nup-footer"]),
nav:not([class*="nup-footer"]):not([id*="nup-footer"]),
.container:not([class*="nup-footer"]):not([id*="nup-footer"]),
.container-fluid:not([class*="nup-footer"]):not([id*="nup-footer"]),
.row:not([class*="nup-footer"]):not([id*="nup-footer"]),
.col:not([class*="nup-footer"]):not([id*="nup-footer"]),
[class*="container"]:not([class*="nup-footer"]):not([id*="nup-footer"]),
[class*="row"]:not([class*="nup-footer"]):not([id*="nup-footer"]),
[class*="col"]:not([class*="nup-footer"]):not([id*="nup-footer"]) {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
}

/* Override dark mode classes with maximum specificity */
html.dark-mode, body.dark-mode, main.dark-mode,
html[data-theme="dark"], body[data-theme="dark"], main[data-theme="dark"],
.dark-mode, [data-theme="dark"] {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
}

/* Final nuclear option - override everything with dark in the name */
*[class*="dark"]:not(.btn):not(.badge):not(.text-dark),
*[id*="dark"]:not(.btn):not(.badge):not(.text-dark) {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
}

/* ===== CAROUSEL AND LOGO OVERRIDES ===== */
.company-logo-carousel-container,
.featured-carousel-container,
.logo-container,
.company-logo-placeholder,
.logo-placeholder,
.home-carousel-logo,
.home-carousel-placeholder {
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* Override all dark mode carousel styles */
[data-theme="dark"] .company-logo-carousel-container,
[data-theme="dark"] .featured-carousel-container,
[data-theme="dark"] .logo-container,
[data-theme="dark"] .company-logo-placeholder,
[data-theme="dark"] .logo-placeholder,
[data-theme="dark"] .home-carousel-logo,
[data-theme="dark"] .home-carousel-placeholder,
[data-theme="dark"] .featured-carousel-item .logo-container,
[data-theme="dark"] .company-logo-container,
[data-theme="dark"] .featured-carousel-item .logo-placeholder,
[data-theme="dark"] .company-logo-placeholder,
[data-theme="dark"] .featured-logo,
[data-theme="dark"] img.logo {
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* Override featured carousel dark backgrounds */
[data-theme="dark"] .featured-item-wrapper,
[data-theme="dark"] .featured-carousel-item .featured-item-wrapper {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* Override specific featured carousel dark gradients */
.featured-item-wrapper {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* Override any remaining dark carousel backgrounds */
.featured-section,
.featured-companies-carousel,
.featured-assistants-carousel {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* Override dark mode featured sections */
[data-theme="dark"] .featured-section,
[data-theme="dark"] .featured-companies-carousel,
[data-theme="dark"] .featured-assistants-carousel {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* ===== DIRECTORY AND ASSISTANT LIST OVERRIDES ===== */
/* Override all directory and assistant list dark backgrounds */
.directory-container,
.directory-card,
.list-group-item,
.assistant-card,
.company-card {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* Override dark mode directory styles */
[data-theme="dark"] .directory-container,
[data-theme="dark"] .container,
[data-theme="dark"] .container-fluid,
[data-theme="dark"] .directory-card,
[data-theme="dark"] .card,
[data-theme="dark"] .list-group-item,
[data-theme="dark"] .assistant-card,
[data-theme="dark"] .company-card {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* Override filter forms */
[data-theme="dark"] .filter-form,
.filter-form {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* ===== COMPREHENSIVE FORM OVERRIDES ===== */
/* Override ALL form elements with maximum specificity */
html[data-theme="dark"] form,
html[data-theme="dark"] .filter-form,
html[data-theme="dark"] form.filter-form,
html[data-theme="dark"] #form-filter-form,
html[data-theme="dark"] .form-filter-form,
html[data-theme="dark"] [class*="filter-form"],
html[data-theme="dark"] [id*="filter-form"],
[data-theme="dark"] form,
[data-theme="dark"] .filter-form,
[data-theme="dark"] form.filter-form,
[data-theme="dark"] #form-filter-form,
[data-theme="dark"] .form-filter-form,
[data-theme="dark"] [class*="filter-form"],
[data-theme="dark"] [id*="filter-form"] {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
    border: 1px solid #dee2e6 !important;
}

/* Override ALL form controls */
html[data-theme="dark"] .form-control,
html[data-theme="dark"] .form-select,
html[data-theme="dark"] input[type="text"],
html[data-theme="dark"] input[type="email"],
html[data-theme="dark"] input[type="password"],
html[data-theme="dark"] input[type="search"],
html[data-theme="dark"] input[type="number"],
html[data-theme="dark"] input[type="url"],
html[data-theme="dark"] input[type="tel"],
html[data-theme="dark"] textarea,
html[data-theme="dark"] select,
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select,
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="email"],
[data-theme="dark"] input[type="password"],
[data-theme="dark"] input[type="search"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] input[type="url"],
[data-theme="dark"] input[type="tel"],
[data-theme="dark"] textarea,
[data-theme="dark"] select {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
    border: 1px solid #dee2e6 !important;
}

/* Override form control focus states */
html[data-theme="dark"] .form-control:focus,
html[data-theme="dark"] .form-select:focus,
html[data-theme="dark"] input:focus,
html[data-theme="dark"] textarea:focus,
html[data-theme="dark"] select:focus,
[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus,
[data-theme="dark"] input:focus,
[data-theme="dark"] textarea:focus,
[data-theme="dark"] select:focus {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
    border-color: #E31B23 !important;
    box-shadow: 0 0 0 0.2rem rgba(227, 27, 35, 0.25) !important;
}

/* Override input group elements */
html[data-theme="dark"] .input-group-text,
[data-theme="dark"] .input-group-text {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
    border: 1px solid #dee2e6 !important;
}

/* Override form labels and text */
html[data-theme="dark"] .form-label,
html[data-theme="dark"] .form-text,
html[data-theme="dark"] label,
[data-theme="dark"] .form-label,
[data-theme="dark"] .form-text,
[data-theme="dark"] label {
    color: #333333 !important;
}

/* Override placeholders */
html[data-theme="dark"] ::placeholder,
[data-theme="dark"] ::placeholder {
    color: #6c757d !important;
    opacity: 1 !important;
}

/* Override checkboxes and radio buttons */
html[data-theme="dark"] .form-check-input,
[data-theme="dark"] .form-check-input {
    background-color: #FFFFFF !important;
    border-color: #dee2e6 !important;
}

html[data-theme="dark"] .form-check-input:checked,
[data-theme="dark"] .form-check-input:checked {
    background-color: #E31B23 !important;
    border-color: #E31B23 !important;
}

html[data-theme="dark"] .form-check-label,
[data-theme="dark"] .form-check-label {
    color: #333333 !important;
}

/* Override any remaining dark form backgrounds */
html[data-theme="dark"] .form-floating,
html[data-theme="dark"] .input-group,
[data-theme="dark"] .form-floating,
[data-theme="dark"] .input-group {
    background-color: transparent !important;
}

/* ===== FINAL OVERRIDES KILLER ===== */
/* Override final-overrides.css with maximum specificity */
html body [data-theme="dark"] .filter-form,
html body [data-theme="dark"] form.filter-form,
html body [data-theme="dark"] div.filter-form,
html body [data-theme="dark"] .filter-form .input-group,
html body [data-theme="dark"] form.filter-form .input-group {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
    border: 1px solid #dee2e6 !important;
    display: block !important;
    visibility: visible !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

html body [data-theme="dark"] .filter-form .input-group-text,
html body [data-theme="dark"] form.filter-form .input-group-text {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
    border: 1px solid #dee2e6 !important;
}

html body [data-theme="dark"] .filter-form .form-control,
html body [data-theme="dark"] form.filter-form .form-control {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
    border: 1px solid #dee2e6 !important;
}

html body [data-theme="dark"] .filter-form .btn-primary,
html body [data-theme="dark"] form.filter-form .btn-primary {
    background-color: #E31B23 !important;
    background: #E31B23 !important;
    border-color: #E31B23 !important;
    color: #FFFFFF !important;
}

/* ===== ASSISTANT CHAT PAGE OVERRIDES ===== */
/* Override all assistant chat dark backgrounds */
html body,
html body[style*="background"],
body[style*="background"],
[style*="background-color: #121212"],
[style*="background: #121212"] {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    background-image: none !important;
}

/* Override assistant headers */
html[data-theme="dark"] .assistant-header,
html[data-theme="dark"] .general-assistant-header,
html[data-theme="dark"] div.assistant-header,
html[data-theme="dark"] div.general-assistant-header,
html body [data-theme="dark"] .assistant-header,
html body [data-theme="dark"] .general-assistant-header,
html body [data-theme="dark"] div.assistant-header,
html body [data-theme="dark"] div.general-assistant-header,
.assistant-header,
.general-assistant-header,
div.assistant-header,
div.general-assistant-header {
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(214, 240, 253, 0.85)) !important;
    background-color: #F8F9FA !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
    box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.7) !important;
    color: #333333 !important;
}

/* Override chat containers */
html[data-theme="dark"] .chat-container,
html[data-theme="dark"] .general-chat-container,
html body [data-theme="dark"] .chat-container,
html body [data-theme="dark"] .general-chat-container,
.chat-container,
.general-chat-container,
div.chat-container,
div.general-chat-container {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    background-image: none !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    color: #333333 !important;
}

/* Override chat boxes */
html[data-theme="dark"] .chat-box,
html[data-theme="dark"] .general-chat-box,
html[data-theme="dark"] #chat-box,
html body [data-theme="dark"] .chat-box,
html body [data-theme="dark"] .general-chat-box,
html body [data-theme="dark"] #chat-box,
.chat-box,
.general-chat-box,
#chat-box,
div.chat-box,
div.general-chat-box {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    background-image: none !important;
    border: 1px solid #dee2e6 !important;
    color: #333333 !important;
}

/* Override assistant message bubbles */
html[data-theme="dark"] .assistant-message .message-content,
html[data-theme="dark"] div.message.assistant-message.mb-3 span.message-content,
html[data-theme="dark"] div.message.assistant-message span.message-content,
html[data-theme="dark"] .message.assistant-message .message-content,
html[data-theme="dark"] span.message-content.assistant-message,
html body [data-theme="dark"] .assistant-message .message-content,
html body [data-theme="dark"] div.message.assistant-message.mb-3 span.message-content,
html body [data-theme="dark"] div.message.assistant-message span.message-content,
html body [data-theme="dark"] .message.assistant-message .message-content,
html body [data-theme="dark"] span.message-content.assistant-message,
.assistant-message .message-content,
div.message.assistant-message.mb-3 span.message-content,
div.message.assistant-message span.message-content,
.message.assistant-message .message-content,
span.message-content.assistant-message {
    background-color: #F8F9FA !important;
    background: #F8F9FA !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Override user message bubbles */
html[data-theme="dark"] .user-message .message-content,
html body [data-theme="dark"] .user-message .message-content,
.user-message .message-content {
    background-color: #E31B23 !important;
    background: #E31B23 !important;
    background-image: none !important;
    color: #FFFFFF !important;
    border: 1px solid #E31B23 !important;
}

/* Override any inline styles with maximum specificity */
[style*="background-color: #1e1e1e"],
[style*="background: #1e1e1e"],
[style*="background-color: #252525"],
[style*="background: #252525"],
[style*="background-color: #121212"],
[style*="background: #121212"],
[style*="background: rgb(26, 26, 26)"],
[style*="background-color: rgb(26, 26, 26)"],
[style*="background: rgb(37, 37, 37)"],
[style*="background-color: rgb(37, 37, 37)"],
[style*="background: rgb(18, 18, 18)"],
[style*="background-color: rgb(18, 18, 18)"] {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    background-image: none !important;
}

/* ===== SPECIFIC CHAT ELEMENT OVERRIDES ===== */
/* Override chat box with RGB values */
#chat-box[style*="background: rgb(26, 26, 26)"],
.chat-box[style*="background: rgb(26, 26, 26)"],
div[style*="background: rgb(26, 26, 26)"] {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
}

/* Override welcome text colors */
.initial-greeting[style*="color: rgb(255, 255, 255)"],
.welcome-text-small[style*="color: rgb(255, 255, 255)"],
div[style*="color: rgb(255, 255, 255)"] {
    color: #333333 !important;
}

/* Override all white text in dark backgrounds */
[style*="color: rgb(255, 255, 255)"] {
    color: #333333 !important;
}

/* Override navigation header dark elements */
.navbar-dark,
.navbar[style*="background"],
nav[style*="background"],
header[style*="background"] {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
}

/* Override navigation text colors */
.navbar-dark .navbar-nav .nav-link,
.navbar-dark .navbar-brand,
.navbar-dark .navbar-text {
    color: #333333 !important;
}

/* Force all navigation elements to light theme */
.navbar, .nav, .navigation, header, .header {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* ===== UNIFIED HEADER OVERRIDES ===== */
/* Override unified header and chat header with maximum specificity */
.unified-header,
.unified-chat-header,
nav.unified-header,
nav.unified-chat-header {
    background: rgba(248, 249, 250, 0.95) !important;
    background-color: rgba(248, 249, 250, 0.95) !important;
    border-bottom: 1px solid #dee2e6 !important;
    border-bottom-color: #dee2e6 !important;
}

/* Override unified header text colors */
.unified-header .navbar-brand,
.unified-chat-header .navbar-brand,
.unified-header .company-name,
.unified-chat-header .company-name,
.unified-header .nav-link,
.unified-chat-header .nav-link {
    color: #333333 !important;
}

/* Override unified header hover colors */
.unified-header .navbar-brand:hover,
.unified-chat-header .navbar-brand:hover {
    color: #E31B23 !important;
}

/* Override unified header company brand borders */
.unified-header .company-brand,
.unified-chat-header .company-brand {
    border-left-color: #dee2e6 !important;
}

/* Override unified header company name colors */
.unified-header .company-name,
.unified-chat-header .company-name {
    color: #6c757d !important;
}

/* ===== CTA SECTION OVERRIDES ===== */
/* Override all CTA section dark backgrounds */
[data-theme="dark"] .cta-section,
.cta-section,
section.cta-section,
div.cta-section {
    background: linear-gradient(135deg, #FFFFFF 0%, #F8F9FA 100%) !important;
    background-color: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Override CTA section text colors */
[data-theme="dark"] .cta-section h1,
[data-theme="dark"] .cta-section h2,
[data-theme="dark"] .cta-section h3,
[data-theme="dark"] .cta-section h4,
[data-theme="dark"] .cta-section h5,
[data-theme="dark"] .cta-section h6,
[data-theme="dark"] .cta-section p,
[data-theme="dark"] .cta-section .lead,
.cta-section h1,
.cta-section h2,
.cta-section h3,
.cta-section h4,
.cta-section h5,
.cta-section h6,
.cta-section p,
.cta-section .lead {
    color: #333333 !important;
}

/* Override CTA section buttons */
[data-theme="dark"] .cta-section .btn-primary,
.cta-section .btn-primary {
    background-color: #E31B23 !important;
    background: #E31B23 !important;
    border-color: #E31B23 !important;
    color: #FFFFFF !important;
}

/* Override CTA section white backgrounds */
[data-theme="dark"] .cta-section .bg-white,
.cta-section .bg-white {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

/* ===== ULTIMATE NUCLEAR OPTION ===== */
/* Override any element that might have dark backgrounds */
* {
    /* Only override if the element has a dark background */
}

*[style*="background: rgb(26, 26, 26)"],
*[style*="background-color: rgb(26, 26, 26)"],
*[style*="background: rgb(37, 37, 37)"],
*[style*="background-color: rgb(37, 37, 37)"],
*[style*="background: rgb(18, 18, 18)"],
*[style*="background-color: rgb(18, 18, 18)"],
*[style*="background: #1a1a1a"],
*[style*="background-color: #1a1a1a"],
*[style*="background: #252525"],
*[style*="background-color: #252525"],
*[style*="background: #121212"],
*[style*="background-color: #121212"] {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
    background-image: none !important;
}

/* Override white text that might be on dark backgrounds */
*[style*="color: rgb(255, 255, 255)"],
*[style*="color: #ffffff"],
*[style*="color: white"] {
    color: #333333 !important;
}

/* ===== MAXIMUM SPECIFICITY CHAT BOX OVERRIDE ===== */
/* Override the specific rule: html[data-theme="dark"] body .chat-box */
html[data-theme="dark"] body .chat-box,
html[data-theme="dark"] body .general-chat-box,
html[data-theme="dark"] body #chat-box,
html[data-theme="dark"] body div.chat-box,
html[data-theme="dark"] body div.general-chat-box,
html body [data-theme="dark"] .chat-box,
html body [data-theme="dark"] .general-chat-box,
html body [data-theme="dark"] #chat-box,
html body [data-theme="dark"] div.chat-box,
html body [data-theme="dark"] div.general-chat-box {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
    background-image: none !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    padding: 1.5rem !important;
    color: #333333 !important;
}

/* Override chat containers with maximum specificity */
html[data-theme="dark"] body .chat-container,
html[data-theme="dark"] body .general-chat-container,
html[data-theme="dark"] body div.chat-container,
html[data-theme="dark"] body div.general-chat-container,
html body [data-theme="dark"] .chat-container,
html body [data-theme="dark"] .general-chat-container,
html body [data-theme="dark"] div.chat-container,
html body [data-theme="dark"] div.general-chat-container {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
    background-image: none !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    color: #333333 !important;
}

/* Override any remaining dark gradients */
[data-theme="dark"] .card,
[data-theme="dark"] .directory-card {
    background: #FFFFFF !important;
    background-color: #FFFFFF !important;
    background-image: none !important;
}

/* ===== NUCLEAR OPTION FOR DIRECTORY PAGES ===== */
/* Force all possible elements to white background */
.company-directory-page,
.company-directory-page *,
.assistant-directory-page,
.assistant-directory-page * {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
}

/* Override any dark sections or containers */
section, .section,
.container, .container-fluid,
.row, .col, [class*="col-"] {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
}

/* Override dark mode on all directory elements - EXCLUDE FOOTER */
[data-theme="dark"] section:not(.nup-footer-top-section):not(#nup-footer-top-section):not(.nup-footer-bottom-section):not(#nup-footer-bottom-section),
[data-theme="dark"] .section:not([class*="nup-footer"]):not([id*="nup-footer"]),
[data-theme="dark"] .container:not([class*="nup-footer"]):not([id*="nup-footer"]),
[data-theme="dark"] .container-fluid:not([class*="nup-footer"]):not([id*="nup-footer"]),
[data-theme="dark"] .row:not([class*="nup-footer"]):not([id*="nup-footer"]),
[data-theme="dark"] .col:not([class*="nup-footer"]):not([id*="nup-footer"]),
[data-theme="dark"] [class*="col-"]:not([class*="nup-footer"]):not([id*="nup-footer"]) {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
}

/* Override any remaining dark backgrounds with maximum specificity - EXCLUDE FOOTER */
html[data-theme="dark"] body:not(.nup-main-footer):not(#nup-main-footer),
html[data-theme="dark"] main:not(.nup-main-footer):not(#nup-main-footer),
html[data-theme="dark"] section:not(.nup-footer-top-section):not(#nup-footer-top-section):not(.nup-footer-bottom-section):not(#nup-footer-bottom-section),
html[data-theme="dark"] div:not(.nup-footer-container):not(#nup-footer-container):not(.nup-footer-row):not(#nup-footer-row):not(.nup-footer-logo-column):not(#nup-footer-logo-column):not(.nup-footer-social-column):not(#nup-footer-social-column):not(.nup-footer-news-column):not(#nup-footer-news-column):not(.nup-footer-logo-widget):not(#nup-footer-logo-widget):not(.nup-footer-social-widget):not(#nup-footer-social-widget):not(.nup-footer-news-widget):not(#nup-footer-news-widget):not(.nup-footer-bottom-container):not(#nup-footer-bottom-container):not(.nup-footer-bottom-inner):not(#nup-footer-bottom-inner):not(.nup-footer-copyright):not(#nup-footer-copyright):not([class*="nup-footer"]):not([id*="nup-footer"]):not([class*="nup-logo"]):not([id*="nup-logo"]):not([class*="nup-social"]):not([id*="nup-social"]):not([class*="nup-news"]):not([id*="nup-news"]),
body[data-theme="dark"]:not(.nup-main-footer):not(#nup-main-footer),
main[data-theme="dark"]:not(.nup-main-footer):not(#nup-main-footer),
section[data-theme="dark"]:not(.nup-footer-top-section):not(#nup-footer-top-section):not(.nup-footer-bottom-section):not(#nup-footer-bottom-section),
div[data-theme="dark"]:not([class*="nup-footer"]):not([id*="nup-footer"]):not([class*="nup-logo"]):not([id*="nup-logo"]):not([class*="nup-social"]):not([id*="nup-social"]):not([class*="nup-news"]):not([id*="nup-news"]) {
    background-color: #FFFFFF !important;
    background: #FFFFFF !important;
    color: #333333 !important;
}
