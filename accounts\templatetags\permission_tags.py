from django import template
# Removed unused imports: has_company_permission, get_membership, can_access_assistant, can_edit_assistant
# Import models if needed for type checking, but generally not required for has_perm
# from accounts.models import Company
# from assistants.models import Assistant

register = template.Library()

@register.filter(name='is_in_group')
def is_in_group(user, group_name):
    """
    Template filter to check if a user belongs to a specific group.

    Usage: {% if request.user|is_in_group:"group_name" %}

    Args:
        user: The user object (usually request.user).
        group_name (str): The name of the group to check.

    Returns:
        bool: True if the user is in the group, False otherwise.
    """
    print(f"[DEBUG is_in_group] Checking user '{user}' for group '{group_name}'") # DEBUG
    if not user or not user.is_authenticated:
        print(f"[DEBUG is_in_group] User not authenticated or invalid.") # DEBUG
        return False
    try:
        # Check if user.groups exists and is a manager
        if hasattr(user, 'groups') and hasattr(user.groups, 'filter'):
             result = user.groups.filter(name=group_name).exists()
             print(f"[DEBUG is_in_group] Group check result: {result}") # DEBUG
             return result
        print(f"[DEBUG is_in_group] User object does not have groups manager.") # DEBUG
        return False # User object might not have groups manager (e.g., AnonymousUser)
    except Exception as e:
        # Catch potential errors during group check
        print(f"[DEBUG is_in_group] Error during group check: {e}") # DEBUG
        return False

# Note: Django's built-in {% if perms.app_label.perm_name %} or
# {% if perms.app_label.perm_name object %} is often preferred over custom tags
# for standard permission checks. These custom tags might still be useful if
# they encapsulate more complex logic or specific formatting.

# Let's refactor the existing tags to use user.has_perm for object permissions.

@register.filter(name='has_obj_perm')
def check_object_permission(user, permission_name, obj):
    """
    Template filter to check if a user has a specific permission for a given object.

    Usage: {% if request.user|has_obj_perm:'app_label.codename':object_variable %}

    Args:
        user: The user object (usually request.user).
        permission_name (str): The permission codename (e.g., 'app_label.codename').
        obj: The actual object instance to check permissions against.

    Returns:
        bool: True if the user has the permission for the object, False otherwise.
    """
    # Check if user is valid and has the has_perm method, and if obj is not None
    if not user or not hasattr(user, 'has_perm') or obj is None:
        return False
    try:
        # Ensure the permission name format is correct (app_label.codename)
        if '.' not in permission_name:
             print(f"Warning: Invalid permission format in template filter: {permission_name}")
             return False
        # Perform the permission check
        return user.has_perm(permission_name, obj)
    except Exception as e:
        # Catch potential errors during permission check
        print(f"Unexpected error in has_obj_perm filter: {e}") # Optional debug logging
        return False


# The old `can_access` and `can_edit` filters are now redundant because
# the logic is covered by `has_obj_perm` with the correct permission codename.
# Example usage in template:
# {% if request.user|has_obj_perm:"assistants.view_assistant:assistant" %} ... {% endif %}
# {% if request.user|has_obj_perm:"assistants.change_assistant:assistant" %} ... {% endif %}

# We can keep them as aliases if desired for backward compatibility or clarity,
# but they would just call user.has_perm internally.

@register.filter(name='can_view_assistant')
def check_assistant_view(user, assistant):
    """Alias filter to check view permission for an assistant."""
    if not user or not hasattr(user, 'has_perm') or not assistant:
        return False
    # Public assistants are always viewable by authenticated users
    # Check if assistant object has 'is_public' attribute before accessing
    if hasattr(assistant, 'is_public') and assistant.is_public and user.is_authenticated:
        return True
    return user.has_perm('assistants.view_assistant', assistant)

@register.filter(name='can_change_assistant')
def check_assistant_change(user, assistant):
    """Alias filter to check change permission for an assistant."""
    if not user or not hasattr(user, 'has_perm') or not assistant:
        return False

    try:
        # First check the standard permission
        result = user.has_perm('assistants.change_assistant', assistant)

        # If permission check fails, but user is authenticated and is company owner, allow it
        # This handles cases where impersonation might interfere with permission checks
        if not result and user.is_authenticated and hasattr(assistant, 'company'):
            # Check if user is the company owner
            if user.id == assistant.company.owner_id:
                result = True

            # Also check if user is in company admin group
            elif hasattr(user, 'groups'):
                from accounts.utils import _user_in_company_group
                if _user_in_company_group(user, assistant.company, ["company administrators"]):
                    result = True

        return result
    except Exception as e:
        print(f"[ERROR] Permission check failed for user {user.username if user else 'None'}: {e}")
        # Fallback: if user is company owner, allow access
        try:
            if user and user.is_authenticated and hasattr(assistant, 'company'):
                return user.id == assistant.company.owner_id
        except:
            pass
        return False

@register.filter(name='can_delete_assistant')
def check_assistant_delete(user, assistant):
    """Alias filter to check delete permission for an assistant."""
    if not user or not hasattr(user, 'has_perm') or not assistant:
        return False
    return user.has_perm('assistants.delete_assistant', assistant)


# Refactor the simple tag to use user.has_perm
@register.simple_tag(name='check_perm')
def check_permission_tag(user, permission_name, obj):
    """
    Template tag to check if a user has a specific permission for a given object.
    Replaces the old `check_company_perm_tag`.

    Usage: {% check_perm user 'app_label.permission_codename' object as can_do_something %}
           {% if can_do_something %} ... {% endif %}
    """
    if not user or not hasattr(user, 'has_perm') or not obj:
        return False
    try:
        # Ensure the permission name format is correct (app_label.codename)
        if '.' not in permission_name:
             print(f"Warning: Invalid permission format in template tag: {permission_name}")
             return False
        return user.has_perm(permission_name, obj)
    except Exception as e:
        # Catch potential errors during permission check
        print(f"Error in check_perm tag: {e}") # Optional debug logging
        return False
