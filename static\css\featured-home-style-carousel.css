/**
 * Featured Sections - Home Page Style Carousel
 * Redesigned to match the home page carousel with dark backgrounds and enhanced styling
 * Includes like buttons and ratings while maintaining clean design
 */

/* Featured Section Container */
.featured-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.featured-section h2 {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 1.5rem;
}

/* Enhanced Company Logo Carousel for Featured Sections */
.featured-companies-carousel,
.featured-assistants-carousel {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
    border-radius: 15px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

.featured-companies-carousel .company-logo-carousel-container,
.featured-assistants-carousel .company-logo-carousel-container {
    background: transparent;
    border: none;
    box-shadow: none;
}

/* Enhanced Company Logo Items for Featured Sections */
.featured-company-item,
.featured-assistant-item {
    position: relative;
    background: linear-gradient(145deg, #333333 0%, #2a2a2a 100%);
    border-radius: 15px;
    padding: 1.5rem 1rem;
    margin: 0 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
}

.featured-company-item:hover,
.featured-assistant-item:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.5),
        0 5px 15px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.featured-company-item a,
.featured-assistant-item a {
    color: #ffffff;
    text-decoration: none;
    display: block;
}

.featured-company-item a:hover,
.featured-assistant-item a:hover {
    color: #ffffff;
    text-decoration: none;
}

/* Enhanced Logo Styling */
.featured-company-item .company-logo,
.featured-assistant-item .company-logo {
    filter: brightness(1.1) contrast(1.1);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.featured-company-item .company-logo-placeholder,
.featured-assistant-item .company-logo-placeholder {
    background: linear-gradient(135deg, #404040 0%, #**********%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.featured-company-item .company-logo-placeholder i,
.featured-assistant-item .company-logo-placeholder i {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Enhanced Text Styling */
.featured-company-item .company-name,
.featured-assistant-item .company-name {
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    margin-bottom: 0.5rem;
}

.featured-company-item .company-description,
.featured-company-item .assistant-description,
.featured-assistant-item .company-description,
.featured-assistant-item .assistant-description {
    color: #cccccc;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
}

.featured-company-item .assistant-count,
.featured-assistant-item .assistant-count {
    color: #bbbbbb;
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.featured-company-item .assistant-count i,
.featured-assistant-item .assistant-count i {
    color: #4dabf7;
}

/* Enhanced Rating Display */
.featured-company-item .rating-display-container,
.featured-assistant-item .rating-display-container {
    margin-top: 0.5rem;
}

.featured-company-item .rating-display-container .stars,
.featured-assistant-item .rating-display-container .stars {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
}

.featured-company-item .rating-display-container .text-muted,
.featured-assistant-item .rating-display-container .text-muted {
    color: #999999 !important;
    font-style: italic;
}

/* Enhanced Like Button */
.featured-company-item .like-button,
.featured-assistant-item .like-button {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #444444 0%, #**********%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    z-index: 10;
}

.featured-company-item .like-button:hover,
.featured-assistant-item .like-button:hover {
    transform: scale(1.1);
    background: linear-gradient(135deg, #555555 0%, #**********%);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.featured-company-item .like-button.text-danger,
.featured-assistant-item .like-button.text-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

.featured-company-item .like-button.text-danger:hover,
.featured-assistant-item .like-button.text-danger:hover {
    background: linear-gradient(135deg, #e74c3c 0%, #dc3545 100%);
    transform: scale(1.1);
}

.featured-company-item .like-button i,
.featured-assistant-item .like-button i {
    font-size: 1.1rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
}

/* Animation Enhancements */
.featured-companies-carousel .company-logo-carousel,
.featured-assistants-carousel .company-logo-carousel {
    animation-duration: 60s;
    animation-timing-function: linear;
}

.featured-companies-carousel .company-logo-carousel:hover,
.featured-assistants-carousel .company-logo-carousel:hover {
    animation-play-state: paused;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .featured-section {
        padding: 1.5rem 1rem;
        margin-bottom: 1.5rem;
        border-radius: 15px;
    }

    .featured-companies-carousel,
    .featured-assistants-carousel {
        padding: 0.75rem;
        border-radius: 12px;
    }

    .featured-company-item,
    .featured-assistant-item {
        margin: 0 10px;
        padding: 1rem 0.75rem;
        border-radius: 12px;
    }

    .featured-company-item .like-button,
    .featured-assistant-item .like-button {
        width: 35px;
        height: 35px;
        top: 10px;
        right: 10px;
    }

    .featured-company-item .like-button i,
    .featured-assistant-item .like-button i {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .featured-section {
        padding: 1rem 0.75rem;
        border-radius: 12px;
    }

    .featured-section h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .featured-company-item,
    .featured-assistant-item {
        margin: 0 8px;
        padding: 0.75rem;
        border-radius: 10px;
    }

    .featured-company-item .company-name,
    .featured-assistant-item .company-name {
        font-size: 1rem;
    }

    .featured-company-item .company-description,
    .featured-company-item .assistant-description,
    .featured-assistant-item .company-description,
    .featured-assistant-item .assistant-description {
        font-size: 0.8rem;
    }
}

/* Dark Theme Compatibility */
[data-theme="dark"] .featured-section,
.dark-mode .featured-section {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .featured-companies-carousel,
[data-theme="dark"] .featured-assistants-carousel,
.dark-mode .featured-companies-carousel,
.dark-mode .featured-assistants-carousel {
    background: linear-gradient(135deg, #161b22 0%, #0d1117 100%);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .featured-company-item,
[data-theme="dark"] .featured-assistant-item,
.dark-mode .featured-company-item,
.dark-mode .featured-assistant-item {
    background: linear-gradient(145deg, #21262d 0%, #161b22 100%) !important;
    border-color: rgba(255, 255, 255, 0.15) !important;
}

/* Copy exact styling from company list featured cards */
[data-theme="dark"] .featured-assistant-item,
[data-theme="dark"] .featured-company-item {
    /* Solid dark background to define card area - exact copy from company list */
    background: linear-gradient(145deg,
        rgba(60, 60, 60, 0.95) 0%,
        rgba(45, 45, 45, 0.9) 50%,
        rgba(30, 30, 30, 0.85) 100%) !important;
    background-color: rgba(50, 50, 50, 0.9) !important;

    /* Enhanced glass border effect - exact copy from company list */
    border: 3px solid rgba(255, 255, 255, 0.2) !important;
    border-top: 3px solid rgba(255, 255, 255, 0.4) !important;
    border-left: 3px solid rgba(255, 255, 255, 0.4) !important;
    border-right: 3px solid rgba(0, 0, 0, 0.2) !important;
    border-bottom: 3px solid rgba(0, 0, 0, 0.2) !important;

    /* Enhanced shadow for depth - exact copy from company list */
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        inset 0 3px 0 rgba(255, 255, 255, 0.3),
        inset 3px 0 0 rgba(255, 255, 255, 0.2),
        inset 0 -2px 0 rgba(0, 0, 0, 0.2),
        0 0 20px rgba(255, 255, 255, 0.05) !important;

    /* Glass effect backdrop filter - exact copy from company list */
    backdrop-filter: blur(15px) !important;
    -webkit-backdrop-filter: blur(15px) !important;
}

[data-theme="dark"] .featured-assistant-item:hover,
[data-theme="dark"] .featured-company-item:hover {
    /* Enhanced hover effect - exact copy from company list */
    background: linear-gradient(145deg,
        rgba(70, 70, 70, 0.95) 0%,
        rgba(55, 55, 55, 0.9) 50%,
        rgba(40, 40, 40, 0.85) 100%) !important;
    background-color: rgba(60, 60, 60, 0.9) !important;

    /* Enhanced hover border - exact copy from company list */
    border-top: 3px solid rgba(255, 255, 255, 0.6) !important;
    border-left: 3px solid rgba(255, 255, 255, 0.6) !important;
    border-right: 3px solid rgba(0, 0, 0, 0.3) !important;
    border-bottom: 3px solid rgba(0, 0, 0, 0.3) !important;

    /* Enhanced hover shadow - exact copy from company list */
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.6),
        inset 0 4px 0 rgba(255, 255, 255, 0.4),
        inset 4px 0 0 rgba(255, 255, 255, 0.3),
        inset 0 -3px 0 rgba(0, 0, 0, 0.3),
        0 0 25px rgba(255, 255, 255, 0.1) !important;
}
