/**
 * Dark Mode Performance Fix
 * This script optimizes dark mode detection and application to prevent continuous checking
 * and improve performance.
 */

// Run immediately to stop any existing observers that might be causing performance issues
(function() {
    console.log('Dark Mode Performance Fix: Initializing');

    // Store a reference to the original MutationObserver
    const OriginalMutationObserver = window.MutationObserver;

    // Create a list to track created observers
    window._darkModeObservers = [];

    // Override MutationObserver to track dark mode related observers
    window.MutationObserver = function(callback) {
        const observer = new OriginalMutationObserver(function(mutations, observer) {
            // Check if this is a dark mode related observer by examining the callback code
            const callbackString = callback.toString();
            const isDarkModeObserver =
                callbackString.includes('data-theme') ||
                callbackString.includes('isDarkMode') ||
                callbackString.includes('dark-mode') ||
                callbackString.includes('applyDarkMode');

            if (isDarkModeObserver) {
                // For dark mode observers, limit how often they run
                if (!observer._lastRun || Date.now() - observer._lastRun > 2000) {
                    observer._lastRun = Date.now();
                    callback(mutations, observer);
                }
            } else {
                // For non-dark mode observers, run normally
                callback(mutations, observer);
            }
        });

        // Add custom properties
        observer._callback = callback;
        observer._isTracked = true;

        // Add to our tracking list
        window._darkModeObservers.push(observer);

        return observer;
    };

    // Copy prototype methods from original MutationObserver
    for (const key in OriginalMutationObserver.prototype) {
        window.MutationObserver.prototype[key] = OriginalMutationObserver.prototype[key];
    }

    // Set dark mode once and store in a global variable
    window._isDarkModeActive = true;
    document.documentElement.setAttribute('data-theme', 'dark');
    document.documentElement.classList.add('dark-mode');

    // Only set body attributes if body exists
    if (document.body) {
        document.body.setAttribute('data-theme', 'dark');
        document.body.classList.add('dark-mode');
    }

    // Create a single global function for applying dark mode
    window.applyGlobalDarkMode = function() {
        // Only run if not recently applied
        if (window._lastDarkModeApplication &&
            Date.now() - window._lastDarkModeApplication < 1000) {
            return;
        }

        window._lastDarkModeApplication = Date.now();
        console.log('Dark Mode Performance Fix: Applying global dark mode');

        // Apply to main content
        const mainContent = document.querySelector('main');
        if (mainContent) {
            mainContent.classList.add('dark-mode');
            mainContent.setAttribute('data-theme', 'dark');
            mainContent.style.backgroundColor = '#121212';
            mainContent.style.color = '#ffffff';
        }

        // Apply to body if needed
        if (document.body && document.body.classList.contains('bg-light')) {
            document.body.classList.remove('bg-light');
            document.body.classList.add('bg-dark');
            document.body.style.backgroundColor = '#121212';
        }

        // Apply to specific containers that might need it
        document.querySelectorAll('.card, .modal, .dropdown-menu, .list-group, .toast').forEach(el => {
            el.classList.add('bg-dark');
            el.classList.add('text-light');
            el.classList.remove('bg-light');
            el.classList.remove('text-dark');
        });
    };

    // Apply dark mode immediately
    window.applyGlobalDarkMode();
})();

// When DOM is loaded, ensure dark mode is applied once
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dark Mode Performance Fix: DOM loaded');

    // Apply global dark mode once
    window.applyGlobalDarkMode();

    // Dispatch a single themeChanged event for any listeners
    document.dispatchEvent(new CustomEvent('themeChanged', {
        detail: { theme: 'dark' }
    }));

    // Create a single optimized observer for any new content
    const optimizedObserver = new MutationObserver(function(mutations) {
        // Only check for significant DOM changes
        const significantChange = mutations.some(mutation => {
            return mutation.addedNodes.length > 0 &&
                   Array.from(mutation.addedNodes).some(node =>
                       node.nodeType === 1 &&
                       (node.classList?.contains('card') ||
                        node.classList?.contains('modal') ||
                        node.tagName === 'DIV' && node.childElementCount > 3)
                   );
        });

        if (significantChange) {
            window.applyGlobalDarkMode();
        }
    });

    // Start observing with a more specific configuration
    if (document.body) {
        optimizedObserver.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: false,
            characterData: false
        });
    }

    // Disable any existing dark mode interval timers
    for (let i = 1; i < 10000; i++) {
        window.clearInterval(i);
    }
});
