/**
 * Advanced Lazy Loading System
 * Optimized for images, components, and dynamic content
 */

class AdvancedLazyLoader {
    constructor(options = {}) {
        this.config = {
            rootMargin: '50px 0px',
            threshold: 0.1,
            enableImageLazyLoading: true,
            enableComponentLazyLoading: true,
            enableContentLazyLoading: true,
            enablePrefetching: true,
            prefetchDistance: '200px 0px',
            imageFormats: ['webp', 'avif', 'jpg', 'png'],
            placeholderStrategy: 'blur',
            errorRetryCount: 3,
            errorRetryDelay: 1000,
            ...options
        };
        
        this.observers = new Map();
        this.loadedElements = new Set();
        this.errorCounts = new Map();
        this.prefetchQueue = new Set();
        this.componentCache = new Map();
        
        this.init();
    }
    
    init() {
        this.setupIntersectionObservers();
        this.setupMutationObserver();
        this.processExistingElements();
        this.setupPrefetching();
        
        console.log('🔄 Advanced Lazy Loader initialized');
    }
    
    setupIntersectionObservers() {
        // Main lazy loading observer
        this.observers.set('main', new IntersectionObserver(
            this.handleIntersection.bind(this),
            {
                rootMargin: this.config.rootMargin,
                threshold: this.config.threshold
            }
        ));
        
        // Prefetch observer (larger margin)
        if (this.config.enablePrefetching) {
            this.observers.set('prefetch', new IntersectionObserver(
                this.handlePrefetch.bind(this),
                {
                    rootMargin: this.config.prefetchDistance,
                    threshold: 0
                }
            ));
        }
    }
    
    setupMutationObserver() {
        const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.processElement(node);
                        this.processChildElements(node);
                    }
                });
            });
        });
        
        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        this.observers.set('mutation', mutationObserver);
    }
    
    processExistingElements() {
        this.processChildElements(document.body);
    }
    
    processChildElements(parent) {
        const lazyElements = parent.querySelectorAll('[data-lazy], [loading="lazy"], .lazy-load');
        lazyElements.forEach(element => this.processElement(element));
    }
    
    processElement(element) {
        if (this.loadedElements.has(element)) return;
        
        const lazyType = this.getLazyType(element);
        if (!lazyType) return;
        
        // Add to observers
        this.observers.get('main').observe(element);
        
        if (this.config.enablePrefetching) {
            this.observers.get('prefetch').observe(element);
        }
        
        // Add loading placeholder
        this.addLoadingPlaceholder(element, lazyType);
    }
    
    getLazyType(element) {
        if (element.hasAttribute('data-lazy')) {
            return element.getAttribute('data-lazy');
        }
        
        if (element.tagName === 'IMG' && element.hasAttribute('loading')) {
            return 'image';
        }
        
        if (element.classList.contains('lazy-load')) {
            return element.dataset.type || 'content';
        }
        
        return null;
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.loadElement(entry.target);
            }
        });
    }
    
    handlePrefetch(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                this.prefetchElement(entry.target);
            }
        });
    }
    
    async loadElement(element) {
        if (this.loadedElements.has(element)) return;
        
        const lazyType = this.getLazyType(element);
        if (!lazyType) return;
        
        try {
            element.classList.add('loading');
            
            switch (lazyType) {
                case 'image':
                    await this.loadImage(element);
                    break;
                case 'component':
                    await this.loadComponent(element);
                    break;
                case 'content':
                    await this.loadContent(element);
                    break;
                case 'iframe':
                    await this.loadIframe(element);
                    break;
                case 'video':
                    await this.loadVideo(element);
                    break;
                default:
                    await this.loadGeneric(element);
            }
            
            this.onElementLoaded(element);
            
        } catch (error) {
            this.onElementError(element, error);
        }
    }
    
    async loadImage(img) {
        const src = img.dataset.src || img.src;
        if (!src) throw new Error('No image source found');
        
        // Try to load optimized format
        const optimizedSrc = await this.getOptimizedImageSrc(src, img);
        
        return new Promise((resolve, reject) => {
            const newImg = new Image();
            
            newImg.onload = () => {
                img.src = optimizedSrc;
                img.removeAttribute('data-src');
                resolve();
            };
            
            newImg.onerror = () => {
                reject(new Error(`Failed to load image: ${optimizedSrc}`));
            };
            
            newImg.src = optimizedSrc;
        });
    }
    
    async getOptimizedImageSrc(src, img) {
        const formats = this.config.imageFormats;
        const width = img.offsetWidth || img.dataset.width;
        const height = img.offsetHeight || img.dataset.height;
        
        // Check for WebP support
        if (formats.includes('webp') && await this.supportsFormat('webp')) {
            return this.buildOptimizedUrl(src, 'webp', width, height);
        }
        
        // Check for AVIF support
        if (formats.includes('avif') && await this.supportsFormat('avif')) {
            return this.buildOptimizedUrl(src, 'avif', width, height);
        }
        
        // Fallback to original with optimization parameters
        return this.buildOptimizedUrl(src, null, width, height);
    }
    
    buildOptimizedUrl(src, format, width, height) {
        const url = new URL(src, window.location.origin);
        
        if (width) url.searchParams.set('w', width);
        if (height) url.searchParams.set('h', height);
        if (format) url.searchParams.set('format', format);
        
        url.searchParams.set('auto', 'compress');
        url.searchParams.set('q', '85');
        
        return url.toString();
    }
    
    async supportsFormat(format) {
        const cacheKey = `format-support-${format}`;
        
        if (this.componentCache.has(cacheKey)) {
            return this.componentCache.get(cacheKey);
        }
        
        return new Promise(resolve => {
            const canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            
            canvas.toBlob(blob => {
                const supported = blob && blob.type.includes(format);
                this.componentCache.set(cacheKey, supported);
                resolve(supported);
            }, `image/${format}`);
        });
    }
    
    async loadComponent(element) {
        const componentPath = element.dataset.component;
        if (!componentPath) throw new Error('No component path specified');
        
        // Check cache first
        if (this.componentCache.has(componentPath)) {
            const cachedComponent = this.componentCache.get(componentPath);
            return this.renderComponent(element, cachedComponent);
        }
        
        // Dynamic import
        const module = await import(componentPath);
        const component = module.default || module;
        
        // Cache the component
        this.componentCache.set(componentPath, component);
        
        return this.renderComponent(element, component);
    }
    
    async renderComponent(element, component) {
        if (typeof component === 'function') {
            const result = await component(element);
            
            if (typeof result === 'string') {
                element.innerHTML = result;
            } else if (result instanceof HTMLElement) {
                element.appendChild(result);
            }
        } else if (typeof component === 'string') {
            element.innerHTML = component;
        }
    }
    
    async loadContent(element) {
        const contentUrl = element.dataset.src || element.dataset.url;
        if (!contentUrl) throw new Error('No content URL specified');
        
        const response = await fetch(contentUrl);
        if (!response.ok) {
            throw new Error(`Failed to load content: ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            this.renderJsonContent(element, data);
        } else {
            const html = await response.text();
            element.innerHTML = html;
        }
    }
    
    renderJsonContent(element, data) {
        const template = element.querySelector('[data-template]');
        if (!template) {
            element.textContent = JSON.stringify(data, null, 2);
            return;
        }
        
        const templateHtml = template.innerHTML;
        const rendered = this.interpolateTemplate(templateHtml, data);
        element.innerHTML = rendered;
    }
    
    interpolateTemplate(template, data) {
        return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return data[key] || '';
        });
    }
    
    async loadIframe(iframe) {
        const src = iframe.dataset.src;
        if (!src) throw new Error('No iframe source found');
        
        return new Promise((resolve, reject) => {
            iframe.onload = resolve;
            iframe.onerror = reject;
            iframe.src = src;
            iframe.removeAttribute('data-src');
        });
    }
    
    async loadVideo(video) {
        const src = video.dataset.src;
        const poster = video.dataset.poster;
        
        if (poster) {
            video.poster = poster;
            video.removeAttribute('data-poster');
        }
        
        if (src) {
            video.src = src;
            video.removeAttribute('data-src');
        }
        
        // Load video sources
        const sources = video.querySelectorAll('source[data-src]');
        sources.forEach(source => {
            source.src = source.dataset.src;
            source.removeAttribute('data-src');
        });
        
        video.load();
    }
    
    async loadGeneric(element) {
        const src = element.dataset.src;
        if (src) {
            if (element.tagName === 'LINK') {
                element.href = src;
            } else if (element.tagName === 'SCRIPT') {
                element.src = src;
            } else {
                element.src = src;
            }
            element.removeAttribute('data-src');
        }
    }
    
    prefetchElement(element) {
        if (this.prefetchQueue.has(element)) return;
        
        this.prefetchQueue.add(element);
        
        // Prefetch in next idle period
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => this.performPrefetch(element));
        } else {
            setTimeout(() => this.performPrefetch(element), 100);
        }
    }
    
    async performPrefetch(element) {
        const lazyType = this.getLazyType(element);
        const src = element.dataset.src;
        
        if (!src) return;
        
        try {
            if (lazyType === 'image') {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = src;
                link.as = 'image';
                document.head.appendChild(link);
            } else if (lazyType === 'component') {
                // Prefetch component module
                import(element.dataset.component).catch(() => {
                    // Ignore prefetch errors
                });
            } else {
                // Generic prefetch
                fetch(src, { mode: 'no-cors' }).catch(() => {
                    // Ignore prefetch errors
                });
            }
        } catch (error) {
            // Ignore prefetch errors
        }
    }
    
    addLoadingPlaceholder(element, lazyType) {
        if (element.classList.contains('has-placeholder')) return;
        
        element.classList.add('has-placeholder');
        
        switch (this.config.placeholderStrategy) {
            case 'blur':
                this.addBlurPlaceholder(element, lazyType);
                break;
            case 'skeleton':
                this.addSkeletonPlaceholder(element);
                break;
            case 'spinner':
                this.addSpinnerPlaceholder(element);
                break;
        }
    }
    
    addBlurPlaceholder(element, lazyType) {
        if (lazyType === 'image') {
            const placeholder = element.dataset.placeholder;
            if (placeholder) {
                element.style.backgroundImage = `url(${placeholder})`;
                element.style.backgroundSize = 'cover';
                element.style.filter = 'blur(5px)';
            }
        }
    }
    
    addSkeletonPlaceholder(element) {
        element.classList.add('loading-skeleton');
    }
    
    addSpinnerPlaceholder(element) {
        const spinner = document.createElement('div');
        spinner.className = 'lazy-spinner';
        spinner.innerHTML = '<div class="spinner"></div>';
        element.appendChild(spinner);
    }
    
    onElementLoaded(element) {
        this.loadedElements.add(element);
        element.classList.remove('loading', 'has-placeholder', 'loading-skeleton');
        element.classList.add('loaded', 'fade-in');
        
        // Remove placeholder styles
        element.style.backgroundImage = '';
        element.style.filter = '';
        
        // Remove spinner
        const spinner = element.querySelector('.lazy-spinner');
        if (spinner) {
            spinner.remove();
        }
        
        // Stop observing
        this.observers.get('main').unobserve(element);
        if (this.observers.has('prefetch')) {
            this.observers.get('prefetch').unobserve(element);
        }
        
        // Trigger custom event
        element.dispatchEvent(new CustomEvent('lazyLoaded', {
            detail: { element }
        }));
    }
    
    onElementError(element, error) {
        console.warn('Lazy loading error:', error);
        
        const errorCount = this.errorCounts.get(element) || 0;
        this.errorCounts.set(element, errorCount + 1);
        
        if (errorCount < this.config.errorRetryCount) {
            // Retry after delay
            setTimeout(() => {
                this.loadElement(element);
            }, this.config.errorRetryDelay * (errorCount + 1));
        } else {
            // Give up and show error state
            element.classList.remove('loading');
            element.classList.add('error');
            
            // Show fallback content
            this.showErrorFallback(element);
        }
    }
    
    showErrorFallback(element) {
        const fallback = element.dataset.fallback;
        
        if (fallback) {
            if (element.tagName === 'IMG') {
                element.src = fallback;
            } else {
                element.innerHTML = fallback;
            }
        } else {
            element.innerHTML = '<div class="error-message">Failed to load content</div>';
        }
    }
    
    // Public API
    loadAll() {
        const lazyElements = document.querySelectorAll('[data-lazy], [loading="lazy"], .lazy-load');
        lazyElements.forEach(element => this.loadElement(element));
    }
    
    refresh() {
        this.processExistingElements();
    }
    
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.loadedElements.clear();
        this.errorCounts.clear();
        this.prefetchQueue.clear();
        this.componentCache.clear();
    }
}

// Initialize lazy loader
const lazyLoader = new AdvancedLazyLoader();

// Export for global access
window.AdvancedLazyLoader = AdvancedLazyLoader;
window.lazyLoader = lazyLoader;
