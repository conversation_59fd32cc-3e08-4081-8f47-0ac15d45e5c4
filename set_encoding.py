#!/usr/bin/env python3
"""
Script to set proper UTF-8 encoding environment variables for cPanel deployment.
This helps resolve Unicode encoding issues in Python applications.
"""

import os
import sys

def set_utf8_environment():
    """Set UTF-8 encoding environment variables."""
    
    # Set Python I/O encoding
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # Set locale environment variables
    os.environ['LC_ALL'] = 'en_US.UTF-8'
    os.environ['LANG'] = 'en_US.UTF-8'
    os.environ['LC_CTYPE'] = 'en_US.UTF-8'
    
    # For cPanel specific environment
    os.environ['CPANEL_ENCODING'] = 'utf-8'
    
    print("UTF-8 encoding environment variables set:")
    print(f"PYTHONIOENCODING: {os.environ.get('PYTHONIOENCODING')}")
    print(f"LC_ALL: {os.environ.get('LC_ALL')}")
    print(f"LANG: {os.environ.get('LANG')}")
    print(f"LC_CTYPE: {os.environ.get('LC_CTYPE')}")
    
    # Test Unicode output
    try:
        test_string = "Test Unicode: Hello 世界 🌍 'smart quotes'"
        print(f"Unicode test: {test_string}")
        print("✅ Unicode output working correctly")
    except UnicodeEncodeError as e:
        print(f"❌ Unicode encoding error: {e}")
        return False
    
    return True

def fix_stdout_encoding():
    """Fix stdout/stderr encoding if needed."""
    if hasattr(sys.stdout, 'encoding') and sys.stdout.encoding != 'utf-8':
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
        print("Fixed stdout encoding to UTF-8")
    
    if hasattr(sys.stderr, 'encoding') and sys.stderr.encoding != 'utf-8':
        import codecs
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')
        print("Fixed stderr encoding to UTF-8")

if __name__ == '__main__':
    print("Setting up UTF-8 encoding environment...")
    fix_stdout_encoding()
    success = set_utf8_environment()
    
    if success:
        print("\n✅ Encoding setup completed successfully!")
        print("\nFor cPanel deployment, add these to your .htaccess or environment:")
        print("SetEnv PYTHONIOENCODING utf-8")
        print("SetEnv LC_ALL en_US.UTF-8")
        print("SetEnv LANG en_US.UTF-8")
    else:
        print("\n❌ Encoding setup failed. Check your Python environment.")
        sys.exit(1)
