"""
Advanced LLM utilities with streaming, parallel processing, and intelligent optimization.
"""

import asyncio
import json
import time
import threading
import gzip
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, Any, List, Optional, AsyncGenerator, Callable
import logging
from dataclasses import dataclass
from queue import Queue, Empty
import httpx

from .llm_utils_optimized import (
    openai_client, groq_client, anthropic_client, gemini_client,
    get_token_count_cached
)
from .advanced_data_structures import (
    response_cache, task_queue, record_interaction,
    might_have_seen_query, PriorityQueue
)

logger = logging.getLogger(__name__)


@dataclass
class LLMRequest:
    """Data class for LLM requests."""
    assistant_id: int
    messages: List[Dict]
    temperature: float
    max_tokens: int
    model: str
    priority: int = 0
    callback: Optional[Callable] = None
    request_id: str = None


@dataclass
class LLMResponse:
    """Data class for LLM responses."""
    content: str
    duration: float
    token_count: int
    model: str
    cached: bool = False
    request_id: str = None
    error: Optional[str] = None


class StreamingLLMProcessor:
    """
    Handles streaming LLM responses for real-time user feedback.
    """

    def __init__(self):
        self.active_streams = {}
        self.stream_lock = threading.Lock()

    async def stream_response(self, assistant, messages: List[Dict],
                            temperature: float, max_tokens: int) -> AsyncGenerator[str, None]:
        """Stream LLM response in real-time."""
        model = assistant.model
        stream_id = f"{assistant.id}_{int(time.time())}"

        try:
            with self.stream_lock:
                self.active_streams[stream_id] = True

            if model.startswith('gpt'):
                async for chunk in self._stream_openai(messages, model, temperature, max_tokens):
                    if not self.active_streams.get(stream_id, False):
                        break
                    yield chunk
            elif model.startswith('claude'):
                async for chunk in self._stream_anthropic(messages, model, temperature, max_tokens):
                    if not self.active_streams.get(stream_id, False):
                        break
                    yield chunk
            else:
                # Fallback to non-streaming
                response = await self._generate_non_streaming(assistant, messages, temperature, max_tokens)
                yield response

        except Exception as e:
            logger.error(f"Streaming error: {e}")
            yield f"Error: {str(e)}"
        finally:
            with self.stream_lock:
                self.active_streams.pop(stream_id, None)

    async def _stream_openai(self, messages: List[Dict], model: str,
                           temperature: float, max_tokens: int) -> AsyncGenerator[str, None]:
        """Stream OpenAI response."""
        try:
            stream = await openai_client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )

            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content

        except Exception as e:
            logger.error(f"OpenAI streaming error: {e}")
            yield f"Error: {str(e)}"

    async def _stream_anthropic(self, messages: List[Dict], model: str,
                              temperature: float, max_tokens: int) -> AsyncGenerator[str, None]:
        """Stream Anthropic response."""
        try:
            # Convert messages format for Anthropic
            system_message = ""
            user_messages = []

            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    user_messages.append(msg)

            stream = await anthropic_client.messages.create(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                system=system_message,
                messages=user_messages,
                stream=True
            )

            async for chunk in stream:
                if hasattr(chunk, 'delta') and hasattr(chunk.delta, 'text'):
                    yield chunk.delta.text

        except Exception as e:
            logger.error(f"Anthropic streaming error: {e}")
            yield f"Error: {str(e)}"

    async def _generate_non_streaming(self, assistant, messages: List[Dict],
                                    temperature: float, max_tokens: int) -> str:
        """Fallback non-streaming generation."""
        from .llm_utils_optimized import _generate_llm_response_optimized
        return _generate_llm_response_optimized(assistant, messages, temperature, max_tokens)

    def stop_stream(self, stream_id: str) -> None:
        """Stop an active stream."""
        with self.stream_lock:
            self.active_streams[stream_id] = False


class ParallelLLMProcessor:
    """
    Handles parallel LLM requests for improved throughput.
    """

    def __init__(self, max_workers: int = 5):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.request_queue = PriorityQueue()
        self.processing = False
        self.results = {}

    def submit_request(self, request: LLMRequest) -> str:
        """Submit LLM request for parallel processing."""
        request_id = request.request_id or f"req_{int(time.time())}_{id(request)}"
        request.request_id = request_id

        # Add to priority queue
        task_queue.put(request, request.priority)

        # Start processing if not already running
        if not self.processing:
            self._start_processing()

        return request_id

    def get_result(self, request_id: str, timeout: float = 30.0) -> Optional[LLMResponse]:
        """Get result for a request."""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if request_id in self.results:
                return self.results.pop(request_id)
            time.sleep(0.1)

        return None

    def _start_processing(self) -> None:
        """Start background processing of requests."""
        if self.processing:
            return

        self.processing = True
        threading.Thread(target=self._process_requests, daemon=True).start()

    def _process_requests(self) -> None:
        """Process requests from queue."""
        while self.processing:
            try:
                request = task_queue.get()
                if request is None:
                    time.sleep(0.1)
                    continue

                # Submit to thread pool
                future = self.executor.submit(self._process_single_request, request)

                # Store result when complete
                def store_result(fut):
                    try:
                        result = fut.result()
                        self.results[request.request_id] = result
                    except Exception as e:
                        error_response = LLMResponse(
                            content="",
                            duration=0,
                            token_count=0,
                            model=request.model,
                            request_id=request.request_id,
                            error=str(e)
                        )
                        self.results[request.request_id] = error_response

                future.add_done_callback(store_result)

            except Exception as e:
                logger.error(f"Error processing request queue: {e}")
                time.sleep(1)

    def _process_single_request(self, request: LLMRequest) -> LLMResponse:
        """Process a single LLM request."""
        start_time = time.time()

        try:
            # Check cache first
            cache_key = self._generate_cache_key(request)
            cached_response = response_cache.get(cache_key)

            if cached_response:
                return LLMResponse(
                    content=cached_response['content'],
                    duration=cached_response['duration'],
                    token_count=cached_response['token_count'],
                    model=request.model,
                    cached=True,
                    request_id=request.request_id
                )

            # Generate new response
            from .llm_utils_optimized import _generate_llm_response_optimized

            # Create mock assistant object
            class MockAssistant:
                def __init__(self, model):
                    self.model = model
                    self.id = request.assistant_id

            assistant = MockAssistant(request.model)
            content = _generate_llm_response_optimized(
                assistant, request.messages, request.temperature, request.max_tokens
            )

            duration = time.time() - start_time
            token_count = get_token_count_cached(content, request.model)

            response = LLMResponse(
                content=content,
                duration=duration,
                token_count=token_count,
                model=request.model,
                request_id=request.request_id
            )

            # Cache the response
            response_cache.put(cache_key, {
                'content': content,
                'duration': duration,
                'token_count': token_count
            })

            return response

        except Exception as e:
            logger.error(f"Error processing LLM request: {e}")
            return LLMResponse(
                content="",
                duration=time.time() - start_time,
                token_count=0,
                model=request.model,
                request_id=request.request_id,
                error=str(e)
            )

    def _generate_cache_key(self, request: LLMRequest) -> str:
        """Generate cache key for request."""
        import hashlib
        key_data = f"{request.assistant_id}_{request.messages}_{request.temperature}_{request.max_tokens}"
        return hashlib.md5(key_data.encode()).hexdigest()


class SmartBatchProcessor:
    """
    Batches similar requests for efficient processing.
    """

    def __init__(self, batch_size: int = 5, batch_timeout: float = 2.0):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.pending_requests = []
        self.batch_lock = threading.Lock()
        self.last_batch_time = time.time()

    def add_request(self, request: LLMRequest) -> None:
        """Add request to batch."""
        with self.batch_lock:
            self.pending_requests.append(request)

            # Process batch if full or timeout reached
            if (len(self.pending_requests) >= self.batch_size or
                time.time() - self.last_batch_time > self.batch_timeout):
                self._process_batch()

    def _process_batch(self) -> None:
        """Process current batch of requests."""
        if not self.pending_requests:
            return

        batch = self.pending_requests.copy()
        self.pending_requests.clear()
        self.last_batch_time = time.time()

        # Group by model for efficient processing
        model_groups = {}
        for request in batch:
            if request.model not in model_groups:
                model_groups[request.model] = []
            model_groups[request.model].append(request)

        # Process each model group
        for model, requests in model_groups.items():
            threading.Thread(
                target=self._process_model_group,
                args=(model, requests),
                daemon=True
            ).start()

    def _process_model_group(self, model: str, requests: List[LLMRequest]) -> None:
        """Process a group of requests for the same model."""
        try:
            # Use parallel processor for the group
            parallel_processor = ParallelLLMProcessor(max_workers=len(requests))

            # Submit all requests
            request_ids = []
            for request in requests:
                request_id = parallel_processor.submit_request(request)
                request_ids.append((request_id, request))

            # Collect results and call callbacks
            for request_id, request in request_ids:
                result = parallel_processor.get_result(request_id)
                if result and request.callback:
                    request.callback(result)

        except Exception as e:
            logger.error(f"Error processing model group {model}: {e}")


class IntelligentRetryManager:
    """
    Manages intelligent retry logic with exponential backoff.
    """

    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.retry_counts = {}

    async def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with intelligent retry logic."""
        func_key = f"{func.__name__}_{hash(str(args))}"
        retry_count = self.retry_counts.get(func_key, 0)

        for attempt in range(self.max_retries + 1):
            try:
                result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)

                # Reset retry count on success
                self.retry_counts.pop(func_key, None)
                return result

            except Exception as e:
                if attempt == self.max_retries:
                    logger.error(f"Max retries exceeded for {func.__name__}: {e}")
                    raise

                # Calculate delay with exponential backoff
                delay = self.base_delay * (2 ** attempt)
                logger.warning(f"Retry {attempt + 1} for {func.__name__} after {delay}s: {e}")

                await asyncio.sleep(delay) if asyncio.iscoroutinefunction(func) else time.sleep(delay)
                self.retry_counts[func_key] = attempt + 1


class ResponseCompressor:
    """
    Compresses LLM responses for faster transmission and storage.
    """

    @staticmethod
    def compress_response(response: str) -> bytes:
        """Compress response using gzip."""
        return gzip.compress(response.encode('utf-8'))

    @staticmethod
    def decompress_response(compressed_data: bytes) -> str:
        """Decompress response."""
        return gzip.decompress(compressed_data).decode('utf-8')

    @staticmethod
    def should_compress(response: str, threshold: int = 1000) -> bool:
        """Determine if response should be compressed."""
        return len(response) > threshold


# Global instances
streaming_processor = StreamingLLMProcessor()
parallel_processor = ParallelLLMProcessor()
batch_processor = SmartBatchProcessor()
retry_manager = IntelligentRetryManager()
compressor = ResponseCompressor()


# Enhanced API functions
async def generate_streaming_response(assistant, user_input: str, history: List[Dict] = None):
    """Generate streaming LLM response."""
    from .llm_utils_optimized import _build_messages_optimized

    messages = _build_messages_optimized(assistant, user_input, history)

    async for chunk in streaming_processor.stream_response(
        assistant, messages, assistant.temperature, assistant.max_tokens
    ):
        yield chunk


def generate_parallel_response(assistant, user_input: str, history: List[Dict] = None,
                             priority: int = 0, callback: Callable = None) -> str:
    """Generate LLM response using parallel processing."""
    from .llm_utils_optimized import _build_messages_optimized

    messages = _build_messages_optimized(assistant, user_input, history)

    request = LLMRequest(
        assistant_id=assistant.id,
        messages=messages,
        temperature=assistant.temperature,
        max_tokens=assistant.max_tokens,
        model=assistant.model,
        priority=priority,
        callback=callback
    )

    return parallel_processor.submit_request(request)


def generate_batch_response(assistant, user_input: str, history: List[Dict] = None,
                          callback: Callable = None) -> None:
    """Add request to batch processor."""
    from .llm_utils_optimized import _build_messages_optimized

    messages = _build_messages_optimized(assistant, user_input, history)

    request = LLMRequest(
        assistant_id=assistant.id,
        messages=messages,
        temperature=assistant.temperature,
        max_tokens=assistant.max_tokens,
        model=assistant.model,
        callback=callback
    )

    batch_processor.add_request(request)


async def generate_response_with_retry(assistant, user_input: str, history: List[Dict] = None):
    """Generate response with intelligent retry logic."""
    from .llm_utils_optimized import generate_assistant_response_optimized

    return await retry_manager.execute_with_retry(
        generate_assistant_response_optimized,
        assistant=assistant,
        user_input=user_input,
        history=history,
        user=None,
        use_cache=True
    )


def compress_and_cache_response(key: str, response: str, ttl: int = 3600) -> None:
    """Compress and cache response if beneficial."""
    if compressor.should_compress(response):
        compressed_data = compressor.compress_response(response)
        response_cache.put(f"{key}_compressed", compressed_data, ttl)
    else:
        response_cache.put(key, response, ttl)


def get_and_decompress_response(key: str) -> Optional[str]:
    """Get and decompress cached response."""
    # Try compressed version first
    compressed_data = response_cache.get(f"{key}_compressed")
    if compressed_data:
        return compressor.decompress_response(compressed_data)

    # Fall back to uncompressed
    return response_cache.get(key)
