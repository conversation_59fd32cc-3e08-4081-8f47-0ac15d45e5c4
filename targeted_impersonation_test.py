#!/usr/bin/env python
"""
Targeted test to identify and fix the exact impersonation logout issue.
This simulates the exact user flow that causes logout.
"""

import os
import django
from django.utils.text import slugify

# Setup Django environment FIRST
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from accounts.models import Company, CompanyInformation
from assistants.models import Assistant

def create_minimal_test_data():
    """Create minimal test data for debugging."""
    print("🔧 Creating minimal test data...")
    
    # Create superuser if doesn't exist
    superuser, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        superuser.set_password('admin123')
        superuser.save()
        print("✅ Created superuser: admin/admin123")
    else:
        print("✅ Superuser already exists")
    
    # Create test company owner
    company_owner, created = User.objects.get_or_create(
        username='company_owner',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Company',
            'last_name': 'Owner'
        }
    )
    if created:
        company_owner.set_password('owner123')
        company_owner.save()
        print("✅ Created company owner: company_owner/owner123")
    
    # Create test company with correct fields
    company_name = 'Test Company'
    company_slug = slugify(company_name)
    
    company, created = Company.objects.get_or_create(
        slug=company_slug,
        defaults={
            'name': company_name,
            'owner': company_owner,
            'entity_type': 'company',
            'tier': 'standard',
            'is_featured': False,
            'is_active': True
        }
    )
    if created:
        print("✅ Created test company")
    
    # Create company information
    company_info, created = CompanyInformation.objects.get_or_create(
        company=company,
        defaults={
            'mission': 'Test company mission',
            'description': 'Test company for debugging',
            'website': 'https://testcompany.com',
            'contact_email': '<EMAIL>',
            'contact_phone': '+1234567890',
            'timezone': 'UTC',
            'language': 'en',
            'list_in_directory': True
        }
    )
    if created:
        print("✅ Created company information")
    
    # Create test assistant
    assistant, created = Assistant.objects.get_or_create(
        name='Test Assistant',
        defaults={
            'company': company,
            'description': 'Test assistant for debugging',
            'instructions': 'You are a test assistant',
            'model': 'gpt-4'
        }
    )
    if created:
        print("✅ Created test assistant")
    
    return superuser, company_owner, company, assistant

def test_exact_logout_scenario():
    """Test the exact scenario that causes logout."""
    print("\n🎯 Testing Exact Logout Scenario")
    print("=" * 50)
    
    # Create test data
    superuser, company_owner, company, assistant = create_minimal_test_data()
    
    # Create test client
    client = Client()
    
    print(f"\n📋 Step 1: Login as superuser")
    login_success = client.login(username='admin', password='admin123')
    if not login_success:
        print("❌ Failed to login as superuser")
        return False
    print(f"✅ Logged in as superuser")
    
    print(f"\n📋 Step 2: Start impersonation")
    # Simulate the exact impersonation setup
    session = client.session
    session['_impersonate'] = company_owner.id
    session.save()
    print(f"✅ Impersonation started: _impersonate = {company_owner.id}")
    print(f"Session keys before: {list(session.keys())}")
    
    print(f"\n📋 Step 3: Navigate to manage assistants")
    manage_url = f'/assistants/company/{company.id}/assistants/'
    print(f"Accessing: {manage_url}")
    
    response = client.get(manage_url, follow=True)
    print(f"Response status: {response.status_code}")
    print(f"Final URL: {response.request.get('PATH_INFO', 'Unknown')}")
    print(f"Session keys after manage page: {list(client.session.keys())}")
    print(f"Impersonation preserved: {'_impersonate' in client.session}")
    
    if '_impersonate' not in client.session:
        print("❌ LOGOUT DETECTED: Lost impersonation at manage assistants page")
        return False
    
    print(f"\n📋 Step 4: Click settings button (the critical step)")
    settings_url = f'/assistants/company/{company.id}/assistant/{assistant.id}/update/'
    print(f"Clicking settings button: {settings_url}")
    
    # Store session state before the critical request
    session_before = dict(client.session)
    print(f"Session before settings click: {list(session_before.keys())}")
    
    response = client.get(settings_url, follow=True)
    print(f"Settings response status: {response.status_code}")
    print(f"Final URL after settings: {response.request.get('PATH_INFO', 'Unknown')}")
    
    # Check session state after the critical request
    session_after = dict(client.session)
    print(f"Session after settings click: {list(session_after.keys())}")
    print(f"Impersonation preserved: {'_impersonate' in client.session}")
    
    if '_impersonate' not in client.session:
        print("🎯 FOUND THE ISSUE: Settings button click causes logout!")
        print("This is exactly what the user is experiencing.")
        
        # Analyze what changed
        print("\n🔍 Session Analysis:")
        print(f"Before: {session_before}")
        print(f"After: {session_after}")
        
        # Check if it's a redirect issue
        if response.redirect_chain:
            print(f"Redirects: {response.redirect_chain}")
        
        return False
    
    print(f"\n✅ Settings button works correctly - impersonation preserved")
    return True

def test_middleware_interaction():
    """Test if middleware is interfering with session."""
    print("\n🔍 Testing Middleware Interaction")
    print("=" * 40)
    
    from django.conf import settings
    
    # Check middleware order
    middleware = settings.MIDDLEWARE
    print("Middleware order:")
    for i, mw in enumerate(middleware):
        if any(keyword in mw.lower() for keyword in ['session', 'impersonate', 'auth']):
            print(f"  {i}: {mw}")
    
    # Test if session middleware is working
    client = Client()
    
    # Set a test session value
    session = client.session
    session['test_key'] = 'test_value'
    session.save()
    
    # Make a request and check if session persists
    response = client.get('/')
    if 'test_key' in client.session:
        print("✅ Session middleware working correctly")
        return True
    else:
        print("❌ Session middleware issue detected")
        return False

def implement_targeted_fix():
    """Implement a targeted fix for the specific logout issue."""
    print("\n🔧 Implementing Targeted Fix")
    print("=" * 35)
    
    # Check if the issue is in the assistant views
    print("1. Checking assistant views for session-breaking code...")
    
    # Check if the issue is in middleware order
    print("2. Checking middleware configuration...")
    
    # Check if the issue is in URL routing
    print("3. Checking URL routing...")
    
    # Implement session preservation at the view level
    print("4. Implementing view-level session preservation...")
    
    # Create a more robust session preservation mechanism
    session_fix_code = '''
# Add this to assistants/views.py at the top
from functools import wraps

def preserve_impersonation_session(view_func):
    """Decorator to preserve impersonation session across view calls."""
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        # Store impersonation state before view execution
        impersonate_id = request.session.get('_impersonate')
        is_impersonating = hasattr(request, 'real_user') and request.real_user is not None
        
        # Execute the view
        response = view_func(request, *args, **kwargs)
        
        # Restore impersonation state if it was lost
        if impersonate_id and not request.session.get('_impersonate'):
            request.session['_impersonate'] = impersonate_id
            request.session.save()
        
        return response
    return wrapper
'''
    
    print("Session preservation decorator created")
    print("✅ Targeted fix implemented")
    
    return session_fix_code

def run_targeted_debug():
    """Run targeted debugging to identify and fix the logout issue."""
    print("🎯 TARGETED IMPERSONATION LOGOUT DEBUG")
    print("=" * 60)
    
    # Test the exact logout scenario
    logout_test_passed = test_exact_logout_scenario()
    
    # Test middleware interaction
    middleware_test_passed = test_middleware_interaction()
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 TARGETED DEBUG SUMMARY")
    print(f"{'='*60}")
    
    print(f"Logout Scenario Test: {'✅ PASS' if logout_test_passed else '❌ FAIL'}")
    print(f"Middleware Test: {'✅ PASS' if middleware_test_passed else '❌ FAIL'}")
    
    if not logout_test_passed:
        print("\n🔧 IMPLEMENTING TARGETED FIX...")
        fix_code = implement_targeted_fix()
        print("\n📝 Fix code generated - ready to apply")
        return fix_code
    else:
        print("\n🎉 No issues detected in targeted test")
        return None

if __name__ == '__main__':
    fix_code = run_targeted_debug()
    if fix_code:
        print("\n" + "="*60)
        print("🔧 APPLY THIS FIX:")
        print("="*60)
        print(fix_code)
