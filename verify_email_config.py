#!/usr/bin/env python
"""
Verify that email configuration has been updated to use 24seven.site domain.
"""

import os
import sys

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    from django.conf import settings
    
    print("📧 Email Configuration Verification")
    print("=" * 50)
    
    # Check email settings
    print("✅ Email Settings:")
    print(f"  EMAIL_BACKEND: {settings.EMAIL_BACKEND}")
    print(f"  EMAIL_HOST: {settings.EMAIL_HOST}")
    print(f"  EMAIL_PORT: {settings.EMAIL_PORT}")
    print(f"  EMAIL_USE_SSL: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    print(f"  EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', False)}")
    print(f"  EMAIL_HOST_USER: {settings.EMAIL_HOST_USER}")
    print(f"  DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}")
    
    # Verify the domain change
    print("\n🔍 Domain Verification:")
    
    # Check if old domain is still present
    old_domain_found = False
    if 'smartlib.site' in settings.EMAIL_HOST:
        print("  ❌ EMAIL_HOST still contains 'smartlib.site'")
        old_domain_found = True
    else:
        print("  ✅ EMAIL_HOST updated (no 'smartlib.site' found)")
    
    if 'smartlib.site' in settings.EMAIL_HOST_USER:
        print("  ❌ EMAIL_HOST_USER still contains 'smartlib.site'")
        old_domain_found = True
    else:
        print("  ✅ EMAIL_HOST_USER updated (no 'smartlib.site' found)")
    
    if 'smartlib.site' in settings.DEFAULT_FROM_EMAIL:
        print("  ❌ DEFAULT_FROM_EMAIL still contains 'smartlib.site'")
        old_domain_found = True
    else:
        print("  ✅ DEFAULT_FROM_EMAIL updated (no 'smartlib.site' found)")
    
    # Check if new domain is present
    new_domain_found = True
    if '24seven.site' not in settings.EMAIL_HOST:
        print("  ⚠️  EMAIL_HOST does not contain '24seven.site'")
        new_domain_found = False
    else:
        print("  ✅ EMAIL_HOST contains '24seven.site'")
    
    if '24seven.site' not in settings.EMAIL_HOST_USER:
        print("  ⚠️  EMAIL_HOST_USER does not contain '24seven.site'")
        new_domain_found = False
    else:
        print("  ✅ EMAIL_HOST_USER contains '24seven.site'")
    
    if '24seven.site' not in settings.DEFAULT_FROM_EMAIL:
        print("  ⚠️  DEFAULT_FROM_EMAIL does not contain '24seven.site'")
        new_domain_found = False
    else:
        print("  ✅ DEFAULT_FROM_EMAIL contains '24seven.site'")
    
    print("\n" + "=" * 50)
    
    if old_domain_found:
        print("❌ ISSUE FOUND: Some email settings still contain 'smartlib.site'")
        print("   Please update the remaining references manually.")
        sys.exit(1)
    elif not new_domain_found:
        print("⚠️  WARNING: Some email settings don't contain '24seven.site'")
        print("   This might be intentional if using custom email providers.")
    else:
        print("🎉 SUCCESS: All email settings updated to use '24seven.site'!")
    
    print("\n📋 Summary of Changes:")
    print("  ✅ EMAIL_HOST: mail.smartlib.site → mail.24seven.site")
    print("  ✅ EMAIL_HOST_USER: <EMAIL> → <EMAIL>")
    print("  ✅ DEFAULT_FROM_EMAIL: 24seven <<EMAIL>> → 24seven <<EMAIL>>")
    
    print("\n🔧 Next Steps:")
    print("  1. Update your email server configuration to handle mail.24seven.site")
    print("  2. <NAME_EMAIL> email account")
    print("  3. Update EMAIL_HOST_PASSWORD in your .env file")
    print("  4. Test email functionality with the new configuration")
    
    # Check if .env file exists and has email settings
    if os.path.exists('.env'):
        print("\n📄 .env File Check:")
        with open('.env', 'r') as f:
            env_content = f.read()
            if 'EMAIL_HOST=mail.24seven.site' in env_content:
                print("  ✅ .env file contains updated EMAIL_HOST")
            else:
                print("  ⚠️  .env file may need EMAIL_HOST update")
            
            if 'EMAIL_HOST_USER=<EMAIL>' in env_content:
                print("  ✅ .env file contains updated EMAIL_HOST_USER")
            else:
                print("  ⚠️  .env file may need EMAIL_HOST_USER update")
    else:
        print("\n📄 .env file not found - using settings.py defaults")

except Exception as e:
    print(f"❌ Error verifying email configuration: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
