# Unicode Encoding Fix for cPanel Deployment

## Problem
The application was encountering `UnicodeEncodeError` when trying to print Unicode characters (like smart quotes, emojis, or non-ASCII characters) to stdout. The error occurred because the Python environment's default encoding was set to ASCII instead of UTF-8.

```
Exception Type: UnicodeEncodeError
Exception Value: 'ascii' codec can't encode character '\u2019' in position 37: ordinal not in range(128)
```

## Root Cause
- cPanel hosting environments often default to ASCII encoding
- Python's `pprint.pprint()` function tries to output to stdout using the system's default encoding
- When Unicode characters are present in the data, ASCII encoding fails

## Solutions Implemented

### 1. Fixed the Immediate Issue in `assistants/views.py`
- Replaced `pprint.pprint(request.POST)` with a safer Unicode-aware printing method
- Added proper error handling for Unicode characters
- Used `json.dumps()` with `ensure_ascii=False` for better Unicode support

### 2. Added UTF-8 Encoding Configuration
Updated the following files to ensure UTF-8 encoding:

#### `company_assistant/settings.py`
- Added stdout/stderr encoding fixes at the top of the file
- Ensures UTF-8 encoding is used throughout the Django application

#### `company_assistant/wsgi.py`
- Added UTF-8 encoding configuration for WSGI environment
- Set environment variables for proper locale settings

#### `company_assistant/asgi.py`
- Added UTF-8 encoding configuration for ASGI environment
- Ensures async applications handle Unicode correctly

#### `manage.py`
- Added UTF-8 encoding for Django management commands
- Prevents encoding issues during migrations, collectstatic, etc.

### 3. Environment Configuration
#### Updated `.env.example`
Added encoding-related environment variables:
```
PYTHONIOENCODING=utf-8
LC_ALL=en_US.UTF-8
LANG=en_US.UTF-8
```

#### Created `.htaccess.example`
Template for cPanel deployment with proper encoding settings:
```apache
SetEnv PYTHONIOENCODING utf-8
SetEnv LC_ALL en_US.UTF-8
SetEnv LANG en_US.UTF-8
```

### 4. Utility Scripts
#### `set_encoding.py`
- Script to test and configure UTF-8 encoding
- Helps verify that Unicode characters work correctly
- Provides guidance for cPanel deployment

## Deployment Instructions

### For cPanel Hosting:

1. **Copy the .htaccess file:**
   ```bash
   cp .htaccess.example public_html/.htaccess
   ```

2. **Set environment variables in cPanel:**
   - Go to cPanel → Software → Environment Variables
   - Add:
     - `PYTHONIOENCODING` = `utf-8`
     - `LC_ALL` = `en_US.UTF-8`
     - `LANG` = `en_US.UTF-8`

3. **Test the encoding:**
   ```bash
   python set_encoding.py
   ```

### For Other Hosting Providers:

1. **Set environment variables in your hosting control panel or shell:**
   ```bash
   export PYTHONIOENCODING=utf-8
   export LC_ALL=en_US.UTF-8
   export LANG=en_US.UTF-8
   ```

2. **Add to your .env file:**
   ```
   PYTHONIOENCODING=utf-8
   LC_ALL=en_US.UTF-8
   LANG=en_US.UTF-8
   ```

## Testing

After implementing the fix, test with Unicode characters:

1. **Test in Django shell:**
   ```python
   python manage.py shell
   >>> print("Test: Hello 世界 🌍 'smart quotes'")
   ```

2. **Test the application:**
   - Submit forms with Unicode characters
   - Check that debug output works correctly
   - Verify no encoding errors in logs

## Prevention

To prevent similar issues in the future:

1. **Always use UTF-8 encoding** in production environments
2. **Test with Unicode data** during development
3. **Use proper logging** instead of print statements for debugging
4. **Set encoding environment variables** in all deployment environments

## Files Modified

- `assistants/views.py` - Fixed the immediate pprint issue
- `company_assistant/settings.py` - Added UTF-8 encoding configuration
- `company_assistant/wsgi.py` - Added WSGI encoding fixes
- `company_assistant/asgi.py` - Added ASGI encoding fixes
- `manage.py` - Added management command encoding fixes
- `.env.example` - Added encoding environment variables
- `.htaccess.example` - Created cPanel deployment template
- `set_encoding.py` - Created encoding test utility
- `ENCODING_FIX_README.md` - This documentation

## Additional Notes

- The fix is backward compatible and won't affect existing functionality
- UTF-8 encoding is the standard for modern web applications
- These changes improve overall Unicode support throughout the application
- The fixes handle both development and production environments
