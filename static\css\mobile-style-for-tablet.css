/**
 * Mobile Style For Tablet CSS
 * Applies mobile styling to tablet and landscape modes
 */

/* Apply mobile styling to tablet mode (between 769px and 991.98px) */
@media (min-width: 769px) and (max-width: 991.98px) {
  /* Make cards full width and use mobile layout */
  .directory-card, .list-group-item {
    width: 100% !important;
    padding: 1.25rem !important;
    border-radius: 0.5rem !important;
    margin-bottom: 1rem !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  /* Adjust logo container size like mobile - increased size */
  .logo-container {
    height: 140px !important;
    width: 140px !important;
    min-height: 140px !important;
    min-width: 140px !important;
    margin: 0 auto 1rem auto !important;
  }

  /* Center logo like on mobile */
  .directory-item-link-wrapper .col-md-3 {
    display: flex !important;
    justify-content: center !important;
    width: 100% !important;
    margin-bottom: 1rem !important;
    padding: 0 !important;
  }

  /* Make name column full width and centered */
  .directory-item-link-wrapper .col-md-4 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 1rem !important;
  }

  /* Make description column full width and centered */
  .directory-item-link-wrapper .col-md-5 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 1rem !important;
  }

  /* Adjust contact info column to be full width */
  .directory-card .col-md-2.d-flex.flex-column.justify-content-center,
  .directory-card .contact-info-column {
    width: 100% !important;
    text-align: center !important;
    margin-top: 1rem !important;
    padding: 0 1rem !important;
  }

  /* Adjust rating column to be full width */
  .directory-card .col-md-2.d-flex.flex-column.align-items-end {
    width: 100% !important;
    text-align: center !important;
    margin-top: 0.5rem !important;
    align-items: center !important;
  }

  /* Use column layout for rows */
  .directory-card .row {
    flex-direction: column !important;
    flex-wrap: nowrap !important;
  }

  /* Adjust link wrapper to be full width */
  .directory-item-link-wrapper {
    width: 100% !important;
    flex-direction: column !important;
    margin-right: 0 !important;
  }

  /* Center contact info items */
  .contact-info {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
  }

  .contact-info li {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
  }

  /* Improve contact text display */
  .contact-info .contact-text {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    text-align: center !important;
  }

  /* Style contact info icons */
  .contact-info i {
    margin-right: 0.5rem !important;
    font-size: 1rem !important;
    width: auto !important;
    min-width: 16px !important;
    text-align: center !important;
  }

  /* Specific icon colors for different contact types */
  .contact-info .address-item i {
    color: #ff6b6b !important; /* Bright red for location */
  }

  .contact-info .phone-item i {
    color: #4ade80 !important; /* Bright green for phone */
  }

  .contact-info .email-item i {
    color: #c084fc !important; /* Bright purple for email */
  }

  .contact-info .website-item i {
    color: #67e8f9 !important; /* Bright cyan for website */
  }

  /* Center rating display */
  .rating-display-container {
    display: flex !important;
    justify-content: center !important;
    transform: none !important;
  }

  /* Adjust headings and text for better mobile-like display */
  .directory-card h6 {
    font-size: 1.1rem !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
  }

  .directory-card p {
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
  }

  /* Center badges */
  .directory-card .mb-2 {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 0.25rem !important;
  }

  /* Dark mode styling */
  [data-theme="dark"] .directory-card {
    background: linear-gradient(145deg, #1e1e1e, #252525) !important;
    border: 1px solid #333333 !important;
  }

  [data-theme="dark"] .directory-card h6 {
    color: #ffffff !important;
  }

  [data-theme="dark"] .directory-card p {
    color: #cccccc !important;
  }
}

/* Apply mobile styling to landscape mode */
@media (max-width: 991.98px) and (orientation: landscape) {
  /* Make cards full width and use mobile layout */
  .directory-card, .list-group-item {
    width: 100% !important;
    padding: 1rem !important;
    border-radius: 0.5rem !important;
    margin-bottom: 0.75rem !important;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1) !important;
  }

  /* Adjust logo container size like mobile but slightly smaller for landscape */
  .logo-container {
    height: 80px !important;
    width: 80px !important;
    min-height: 80px !important;
    min-width: 80px !important;
    margin: 0 auto 0.75rem auto !important;
  }

  /* Center logo like on mobile */
  .directory-item-link-wrapper .col-md-3 {
    display: flex !important;
    justify-content: center !important;
    width: 100% !important;
    margin-bottom: 0.75rem !important;
    padding: 0 !important;
  }

  /* Make name column full width and centered */
  .directory-item-link-wrapper .col-md-4 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 1rem !important;
  }

  /* Make description column full width and centered */
  .directory-item-link-wrapper .col-md-5 {
    width: 100% !important;
    text-align: center !important;
    padding: 0 1rem !important;
  }

  /* Adjust contact info column to be full width */
  .directory-card .col-md-2.d-flex.flex-column.justify-content-center,
  .directory-card .contact-info-column {
    width: 100% !important;
    text-align: center !important;
    margin-top: 0.75rem !important;
    padding: 0 1rem !important;
  }

  /* Adjust rating column to be full width */
  .directory-card .col-md-2.d-flex.flex-column.align-items-end {
    width: 100% !important;
    text-align: center !important;
    margin-top: 0.5rem !important;
    align-items: center !important;
  }

  /* Use column layout for rows */
  .directory-card .row {
    flex-direction: column !important;
    flex-wrap: nowrap !important;
    padding-top: 0.25rem !important;
  }

  /* Adjust link wrapper to be full width */
  .directory-item-link-wrapper {
    width: 100% !important;
    flex-direction: column !important;
    margin-right: 0 !important;
  }

  /* Center contact info items */
  .contact-info {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
  }

  .contact-info li {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 0.4rem !important;
    text-align: center !important;
  }

  /* Improve contact text display */
  .contact-info .contact-text {
    color: #ffffff !important;
    font-weight: 500 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    text-align: center !important;
  }

  /* Style contact info icons */
  .contact-info i {
    margin-right: 0.4rem !important;
    font-size: 0.9rem !important;
    width: auto !important;
    min-width: 14px !important;
    text-align: center !important;
  }

  /* Specific icon colors for different contact types */
  .contact-info .address-item i {
    color: #ff6b6b !important; /* Bright red for location */
  }

  .contact-info .phone-item i {
    color: #4ade80 !important; /* Bright green for phone */
  }

  .contact-info .email-item i {
    color: #c084fc !important; /* Bright purple for email */
  }

  .contact-info .website-item i {
    color: #67e8f9 !important; /* Bright cyan for website */
  }

  /* Center rating display */
  .rating-display-container {
    display: flex !important;
    justify-content: center !important;
    transform: none !important;
  }

  /* Adjust headings and text for better mobile-like display */
  .directory-card h6 {
    font-size: 1rem !important;
    margin-bottom: 0.4rem !important;
    text-align: center !important;
  }

  .directory-card p {
    font-size: 0.85rem !important;
    margin-bottom: 0.4rem !important;
    text-align: center !important;
  }

  /* Center badges */
  .directory-card .mb-2 {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 0.25rem !important;
  }

  /* Dark mode styling */
  [data-theme="dark"] .directory-card {
    background: linear-gradient(145deg, #1e1e1e, #252525) !important;
    border: 1px solid #333333 !important;
  }

  [data-theme="dark"] .directory-card h6 {
    color: #ffffff !important;
  }

  [data-theme="dark"] .directory-card p {
    color: #cccccc !important;
  }
}
