/**
 * Text Visibility Fix CSS
 * Ensures all text has high contrast and is readable on white backgrounds
 * This file is loaded last to override any conflicting styles
 */

/* ===== UNIVERSAL TEXT VISIBILITY ===== */
/* Ensure all text elements are visible on white backgrounds */
body, html {
    color: #333333 !important;
}

/* Headings */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6,
.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    color: #333333 !important;
    font-weight: 600 !important;
}

/* Paragraphs and text elements */
p, span, div, article, section, main,
.text, .content, .description {
    color: #333333 !important;
}

/* Links */
a, a:link, a:visited {
    color: #cf2e2e !important;
    text-decoration: none !important;
}

a:hover, a:focus, a:active {
    color: #b82626 !important;
    text-decoration: underline !important;
}

/* Form elements */
.form-control, .form-select, .form-check-input, .form-range {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
}

.form-control:focus, .form-select:focus {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #cf2e2e !important;
    box-shadow: 0 0 0 0.2rem rgba(207, 46, 46, 0.25) !important;
}

.form-label, label, .form-text {
    color: #333333 !important;
}

.form-check-label {
    color: #333333 !important;
}

input::placeholder, textarea::placeholder, select::placeholder {
    color: #666666 !important;
    opacity: 1 !important;
}

/* Buttons - Bold white text with NUP hover effects */
.btn, button, input[type="button"], input[type="submit"], input[type="reset"] {
    font-weight: 700 !important;
    color: #ffffff !important;
    transition: all 0.3s ease !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 6px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.btn-primary, .btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-outline-light):not(.btn-light) {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.btn-primary:hover, .btn:not(.btn-outline-primary):not(.btn-outline-secondary):not(.btn-outline-light):not(.btn-light):hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

.btn-secondary {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
}

.btn-secondary:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

.btn-outline-primary {
    border: 2px solid #cf2e2e !important;
    color: #cf2e2e !important;
    background-color: transparent !important;
}

.btn-outline-primary:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

.btn-outline-secondary {
    border: 2px solid #252638 !important;
    color: #252638 !important;
    background-color: transparent !important;
}

.btn-outline-secondary:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

.btn-light {
    background-color: #f8f9fa !important;
    border-color: #f8f9fa !important;
    color: #333333 !important;
}

.btn-light:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* Special button variants */
.btn-success {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.btn-success:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

.btn-danger {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

.btn-danger:hover {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(37, 38, 56, 0.3) !important;
}

.btn-warning {
    background-color: #f7bd00 !important;
    border-color: #f7bd00 !important;
    color: #333333 !important;
}

.btn-warning:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

.btn-info {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
}

.btn-info:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

.btn-dark {
    background-color: #252638 !important;
    border-color: #252638 !important;
    color: #ffffff !important;
}

.btn-dark:hover {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(207, 46, 46, 0.3) !important;
}

/* Navigation */
.navbar-brand {
    color: #333333 !important;
}

.navbar-nav .nav-link {
    color: #333333 !important;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link:focus {
    color: #cf2e2e !important;
}

.dropdown-menu {
    background-color: #ffffff !important;
    border: 1px solid #dee2e6 !important;
}

.dropdown-item {
    color: #333333 !important;
}

.dropdown-item:hover,
.dropdown-item:focus {
    background-color: #f8f9fa !important;
    color: #333333 !important;
}

/* Cards */
.card {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
}

.card-header, .card-body, .card-footer {
    color: #333333 !important;
}

.card-title {
    color: #333333 !important;
}

.card-text {
    color: #333333 !important;
}

/* Tables */
.table {
    color: #333333 !important;
}

.table th, .table td {
    color: #333333 !important;
    border-color: #dee2e6 !important;
}

.table-striped > tbody > tr:nth-of-type(odd) > td,
.table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: rgba(0, 0, 0, 0.05) !important;
}

/* Lists */
.list-group-item {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
}

.list-group-item:hover {
    background-color: #f8f9fa !important;
}

/* Alerts */
.alert {
    color: #333333 !important;
}

.alert-primary {
    background-color: #cfe2ff !important;
    border-color: #b6d4fe !important;
    color: #084298 !important;
}

.alert-secondary {
    background-color: #e2e3e5 !important;
    border-color: #d3d6d8 !important;
    color: #41464b !important;
}

/* Badges - keep white text on colored backgrounds */
.badge {
    color: #ffffff !important;
    font-weight: 500 !important;
}

.badge.bg-light {
    color: #333333 !important;
}

/* Breadcrumbs */
.breadcrumb-item {
    color: #333333 !important;
}

.breadcrumb-item.active {
    color: #6c757d !important;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: #6c757d !important;
}

/* Pagination */
.page-link {
    color: #cf2e2e !important;
    background-color: #ffffff !important;
    border: 1px solid #dee2e6 !important;
}

.page-link:hover {
    color: #b82626 !important;
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
}

.page-item.active .page-link {
    background-color: #cf2e2e !important;
    border-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* Modals */
.modal-content {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.modal-header, .modal-body, .modal-footer {
    color: #333333 !important;
}

.modal-title {
    color: #333333 !important;
}

/* Tooltips and Popovers */
.tooltip-inner {
    background-color: #333333 !important;
    color: #ffffff !important;
}

.popover {
    background-color: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #dee2e6 !important;
}

.popover-header {
    background-color: #f8f9fa !important;
    color: #333333 !important;
}

.popover-body {
    color: #333333 !important;
}

/* Progress bars */
.progress {
    background-color: #e9ecef !important;
}

.progress-bar {
    background-color: #cf2e2e !important;
    color: #ffffff !important;
}

/* Accordion */
.accordion-item {
    background-color: #ffffff !important;
    border: 1px solid #dee2e6 !important;
}

.accordion-button {
    background-color: #ffffff !important;
    color: #333333 !important;
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9fa !important;
    color: #333333 !important;
}

.accordion-body {
    background-color: #ffffff !important;
    color: #333333 !important;
}

/* Tabs */
.nav-tabs .nav-link {
    color: #333333 !important;
}

.nav-tabs .nav-link.active {
    background-color: #ffffff !important;
    color: #333333 !important;
    border-color: #dee2e6 #dee2e6 #ffffff !important;
}

.tab-content {
    background-color: #ffffff !important;
    color: #333333 !important;
}

/* Override any remaining invisible text */
[style*="color: white"],
[style*="color: #fff"],
[style*="color: #ffffff"],
[style*="color: rgb(255, 255, 255)"],
[style*="color: rgba(255, 255, 255"] {
    color: #333333 !important;
}

/* Ensure text in common layout elements is visible */
.container, .container-fluid, .row, .col,
[class*="col-"], .section, .wrapper, .content-area {
    color: #333333 !important;
}

/* Homepage specific elements */
.hero-section, .hero-section * {
    color: #333333 !important;
}

.search-form-card, .search-form-card * {
    color: #333333 !important;
}

/* Ensure all text elements are visible regardless of parent styling - EXCLUDE BUTTONS */
body *:not([class*="nup-footer"]):not([id*="nup-footer"]):not([class*="nup-logo"]):not([id*="nup-logo"]):not([class*="nup-social"]):not([id*="nup-social"]):not([class*="nup-news"]):not([id*="nup-news"]):not([class*="nup-button"]):not([id*="nup-button"]):not(.btn):not(button):not([type="button"]):not([type="submit"]):not([type="reset"]):not(.nup-btn-text):not(.nup-button-text) {
    color: #333333 !important;
}

/* Exception: Elements that should keep white text */
.btn-primary, .btn-success, .btn-danger, .btn-warning, .btn-info, .btn-dark,
.badge:not(.bg-light), .alert-primary .badge, .alert-success .badge, .alert-danger .badge,
.bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info, .bg-dark,
.text-bg-primary, .text-bg-success, .text-bg-danger, .text-bg-warning, .text-bg-info, .text-bg-dark,
[class*="nup-footer"], [id*="nup-footer"],
[class*="nup-logo"], [id*="nup-logo"],
[class*="nup-social"], [id*="nup-social"],
[class*="nup-news"], [id*="nup-news"] {
    color: #ffffff !important;
}

/* ===== BUTTON TEXT SPECIFIC CLASSES AND IDS ===== */
/* Button text should always be white with specific targeting */
.btn, .btn *,
button, button *,
input[type="button"], input[type="submit"], input[type="reset"],
[class*="nup-button"], [class*="nup-button"] *,
[id*="nup-button"], [id*="nup-button"] *,
.nup-btn-text, .nup-button-text,
.btn-text, .button-text {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* Exception for light and warning buttons */
.btn-light, .btn-light *,
.btn-warning, .btn-warning *,
[class*="nup-button-light"], [class*="nup-button-light"] *,
[id*="nup-button-light"], [id*="nup-button-light"] *,
.nup-btn-light, .nup-button-light {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* Override on hover for light/warning */
.btn-light:hover, .btn-light:hover *,
.btn-warning:hover, .btn-warning:hover *,
[class*="nup-button-light"]:hover, [class*="nup-button-light"]:hover *,
[id*="nup-button-light"]:hover, [id*="nup-button-light"]:hover *,
.nup-btn-light:hover, .nup-button-light:hover {
    color: #ffffff !important;
    font-weight: 700 !important;
}
