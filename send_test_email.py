#!/usr/bin/env python
"""
Send a test <NAME_EMAIL> to verify email configuration.
"""

import os
import sys
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')

try:
    import django
    django.setup()
    
    from django.core.mail import send_mail, send_mass_mail
    from django.conf import settings
    from accounts.email_utils import send_html_email
    
    print("📧 Sending Test <NAME_EMAIL>")
    print("=" * 60)
    
    # Display current email configuration
    print("✅ Current Email Configuration:")
    print(f"  From: {settings.DEFAULT_FROM_EMAIL}")
    print(f"  SMTP Host: {settings.EMAIL_HOST}")
    print(f"  SMTP Port: {settings.EMAIL_PORT}")
    print(f"  SSL Enabled: {getattr(settings, 'EMAIL_USE_SSL', False)}")
    print(f"  Username: {settings.EMAIL_HOST_USER}")
    
    # Check if password is configured
    if not settings.EMAIL_HOST_PASSWORD:
        print("\n❌ EMAIL_HOST_PASSWORD not configured!")
        print("Please update your .env file with:")
        print("EMAIL_HOST_PASSWORD=your-actual-email-password")
        sys.exit(1)
    else:
        print("  ✅ Email password is configured")
    
    print("\n🚀 Sending Test Email...")
    
    # Test email details
    recipient = '<EMAIL>'
    subject = 'Django Email Configuration Test'
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Plain text message
    message = f"""
Django Email Configuration Test

This is a test email sent from your Django application to verify that the cPanel email configuration is working correctly.

Test Details:
- Sent at: {timestamp}
- From: {settings.DEFAULT_FROM_EMAIL}
- To: {recipient}
- SMTP Server: {settings.EMAIL_HOST}:{settings.EMAIL_PORT}
- SSL/TLS: {'SSL' if getattr(settings, 'EMAIL_USE_SSL', False) else 'TLS' if getattr(settings, 'EMAIL_USE_TLS', False) else 'None'}

Configuration Summary:
- Email Host: {settings.EMAIL_HOST}
- Email Port: {settings.EMAIL_PORT}
- Email User: {settings.EMAIL_HOST_USER}
- From Address: {settings.DEFAULT_FROM_EMAIL}

If you receive this email, your Django email configuration is working correctly!

Best regards,
Django Application
24seven.site
"""

    # HTML message
    html_message = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Django Email Test</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background-color: #f8f9fa; }}
        .details {{ background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }}
        .success {{ color: #28a745; font-weight: bold; }}
        .footer {{ text-align: center; padding: 20px; color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Django Email Configuration Test</h1>
        </div>
        
        <div class="content">
            <p>This is a test email sent from your Django application to verify that the cPanel email configuration is working correctly.</p>
            
            <div class="details">
                <h3>📋 Test Details</h3>
                <ul>
                    <li><strong>Sent at:</strong> {timestamp}</li>
                    <li><strong>From:</strong> {settings.DEFAULT_FROM_EMAIL}</li>
                    <li><strong>To:</strong> {recipient}</li>
                    <li><strong>SMTP Server:</strong> {settings.EMAIL_HOST}:{settings.EMAIL_PORT}</li>
                    <li><strong>Security:</strong> {'SSL' if getattr(settings, 'EMAIL_USE_SSL', False) else 'TLS' if getattr(settings, 'EMAIL_USE_TLS', False) else 'None'}</li>
                </ul>
            </div>
            
            <div class="details">
                <h3>⚙️ Configuration Summary</h3>
                <ul>
                    <li><strong>Email Host:</strong> {settings.EMAIL_HOST}</li>
                    <li><strong>Email Port:</strong> {settings.EMAIL_PORT}</li>
                    <li><strong>Email User:</strong> {settings.EMAIL_HOST_USER}</li>
                    <li><strong>From Address:</strong> {settings.DEFAULT_FROM_EMAIL}</li>
                </ul>
            </div>
            
            <p class="success">✅ If you receive this email, your Django email configuration is working correctly!</p>
        </div>
        
        <div class="footer">
            <p>Best regards,<br>
            Django Application<br>
            <strong>24seven.site</strong></p>
        </div>
    </div>
</body>
</html>
"""

    try:
        # Method 1: Using Django's send_mail
        print("📤 Attempting to send email using Django send_mail...")
        
        result = send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient],
            fail_silently=False,
            html_message=html_message
        )
        
        if result == 1:
            print("✅ Email sent successfully using Django send_mail!")
            print(f"   📧 Sent to: {recipient}")
            print(f"   📝 Subject: {subject}")
            print(f"   🕒 Time: {timestamp}")
        else:
            print("❌ Email sending failed (send_mail returned 0)")
            
    except Exception as e:
        print(f"❌ Error sending email with send_mail: {e}")
        
        # Method 2: Try using custom email utility
        try:
            print("\n📤 Attempting to send email using custom email utility...")
            
            send_html_email(
                to_email=recipient,
                subject=subject,
                text_content=message,
                html_content=html_message
            )
            
            print("✅ Email sent successfully using custom email utility!")
            print(f"   📧 Sent to: {recipient}")
            print(f"   📝 Subject: {subject}")
            print(f"   🕒 Time: {timestamp}")
            
        except Exception as e2:
            print(f"❌ Error sending email with custom utility: {e2}")
            
            # Method 3: Try basic connection test
            try:
                print("\n🔧 Testing SMTP connection...")
                from django.core.mail import get_connection
                
                connection = get_connection()
                connection.open()
                print("✅ SMTP connection successful!")
                connection.close()
                
                print("\n❌ Email sending failed but connection works.")
                print("This might be a configuration issue.")
                
            except Exception as e3:
                print(f"❌ SMTP connection failed: {e3}")
                print("\n🔍 Troubleshooting suggestions:")
                print("1. Verify EMAIL_HOST_PASSWORD is correct")
                print("2. <NAME_EMAIL> email account exists in cPanel")
                print("3. Verify mail.24seven.site DNS resolution")
                print("4. Check firewall allows port 465 outbound")
                print("5. Confirm cPanel email service is running")
    
    print("\n" + "=" * 60)
    print("📋 Email Test Summary:")
    print(f"  Target: {recipient}")
    print(f"  From: {settings.DEFAULT_FROM_EMAIL}")
    print(f"  Server: {settings.EMAIL_HOST}:{settings.EMAIL_PORT}")
    print(f"  Security: {'SSL' if getattr(settings, 'EMAIL_USE_SSL', False) else 'TLS' if getattr(settings, 'EMAIL_USE_TLS', False) else 'None'}")
    
    print("\n🔧 Next Steps:")
    print("1. <NAME_EMAIL> inbox for the test email")
    print("2. If email not received, check spam/junk folder")
    print("3. Verify cPanel email account configuration")
    print("4. Check email server logs for any delivery issues")

except Exception as e:
    print(f"❌ Error setting up Django or sending email: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
