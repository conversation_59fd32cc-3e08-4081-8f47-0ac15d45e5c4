#!/usr/bin/env python3
"""
Simple Font Test Script
Test the downloaded fonts to ensure they work with PIL/Pillow.
"""

import os
from PIL import Image, ImageDraw, ImageFont

def test_fonts():
    """Test the downloaded fonts."""
    print("=== Simple Font Test ===")
    
    # Font directory
    font_dir = "static/fonts/system"
    
    # Test fonts
    test_fonts = [
        "DejaVuSans.ttf",
        "DejaVuSans-Bold.ttf",
        "DejaVuSansMono.ttf"
    ]
    
    working_fonts = []
    
    for font_name in test_fonts:
        font_path = os.path.join(font_dir, font_name)
        
        if os.path.exists(font_path):
            try:
                # Try to load the font
                font = ImageFont.truetype(font_path, 24)
                
                # Create a test image
                img = Image.new('RGB', (200, 100), 'white')
                draw = ImageDraw.Draw(img)
                
                # Draw test text
                draw.text((10, 30), "Font Test: A", font=font, fill='black')
                
                # Save test image
                test_filename = f"font_test_{font_name.replace('.ttf', '')}.png"
                img.save(test_filename)
                
                working_fonts.append(font_name)
                print(f"✓ {font_name} works - saved {test_filename}")
                
            except Exception as e:
                print(f"✗ {font_name} failed: {e}")
        else:
            print(f"✗ {font_name} not found at {font_path}")
    
    print(f"\nTotal working fonts: {len(working_fonts)}")
    
    if working_fonts:
        print("\n✅ Font installation successful!")
        print("Your fonts are ready for use in QR code generation.")
    else:
        print("\n❌ No fonts are working. Please check the installation.")
    
    return working_fonts

if __name__ == "__main__":
    test_fonts()
