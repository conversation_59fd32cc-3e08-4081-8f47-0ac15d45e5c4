#!/usr/bin/env python
"""
Quick cPanel URL update script - no inputs required.
Updates all URLs to 24seven.site for cPanel deployment.
"""

import os
import sys
import django
import re
from pathlib import Path

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.sites.models import Site

def quick_update():
    """Quick update for cPanel deployment."""
    domain = "24seven.site"
    site_name = "24seven Platform"

    print(f"🚀 Quick cPanel Update for {domain}")
    print("=" * 50)

    success_count = 0

    # 1. Update Django Site
    try:
        site = Site.objects.get_current()
        site.domain = domain
        site.name = site_name
        site.save()
        print("✅ Django Site updated")
        success_count += 1
    except Exception as e:
        print(f"❌ Django Site failed: {e}")

    # 2. Update settings.py
    try:
        settings_path = Path('company_assistant/settings.py')
        with open(settings_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Update ALLOWED_HOSTS
        content = re.sub(
            r"ALLOWED_HOSTS = \[([^\]]+)\]",
            f"ALLOWED_HOSTS = ['*', '{domain}', 'www.{domain}', 'localhost', '127.0.0.1']",
            content
        )

        # Update CSRF_TRUSTED_ORIGINS
        content = re.sub(
            r"CSRF_TRUSTED_ORIGINS = \[([^\]]+)\]",
            f"CSRF_TRUSTED_ORIGINS = ['http://localhost:8000', 'http://127.0.0.1:8000', 'https://{domain}', 'http://{domain}', 'https://www.{domain}', 'http://www.{domain}']",
            content
        )

        with open(settings_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Settings.py updated")
        success_count += 1
    except Exception as e:
        print(f"❌ Settings.py failed: {e}")

    # 3. Update passenger_wsgi.py
    try:
        wsgi_path = Path('passenger_wsgi.py')
        with open(wsgi_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if 'CPANEL_ENV' not in content:
            env_setup = '''
# Set cPanel environment variables
os.environ['CPANEL_ENV'] = 'True'
os.environ['PRODUCTION'] = 'True'
os.environ['DEBUG'] = 'False'

'''
            content = re.sub(
                r"(os\.environ\.setdefault\('DJANGO_SETTINGS_MODULE')",
                env_setup + r'\1',
                content
            )

            with open(wsgi_path, 'w', encoding='utf-8') as f:
                f.write(content)
        print("✅ Passenger WSGI updated")
        success_count += 1
    except Exception as e:
        print(f"❌ Passenger WSGI failed: {e}")

    # 4. Environment configuration reminder
    try:
        print("✅ Environment configuration:")
        print(f"   Update .env file with production settings:")
        print(f"   - Set DEBUG=False, CPANEL_ENV=True, PRODUCTION=True")
        print(f"   - Update domain settings for {domain}")
        success_count += 1
    except Exception as e:
        print(f"❌ Environment configuration failed: {e}")

    print(f"\n🎉 {success_count}/4 updates completed!")

    if success_count == 4:
        print(f"\n📋 Next steps:")
        print(f"1. Upload files to cPanel")
        print(f"2. Set environment variables in cPanel")
        print(f"3. Update .env with real production credentials")
        print(f"4. Run: python manage.py migrate")
        print(f"5. Run: python manage.py collectstatic --noinput")
        print(f"6. Visit: https://{domain}")
    else:
        print(f"\n⚠️  Some updates failed. Check errors above.")

if __name__ == "__main__":
    quick_update()
