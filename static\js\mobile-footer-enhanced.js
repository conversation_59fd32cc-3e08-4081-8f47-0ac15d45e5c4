/**
 * Mobile Footer Enhanced JavaScript
 * Adds interactive enhancements to the mobile footer accordion
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run on mobile devices
    if (window.innerWidth <= 767.98) {
        initializeMobileFooterEnhancements();
    }

    // Re-initialize on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 767.98) {
            initializeMobileFooterEnhancements();
        }
    });
});

function initializeMobileFooterEnhancements() {
    const footerAccordion = document.querySelector('footer.footer .accordion');
    if (!footerAccordion) return;

    // Add enhanced touch feedback
    addTouchFeedback();
    
    // Add smooth scroll to footer when accordion is opened
    addScrollToFooter();
    
    // Add keyboard navigation enhancements
    addKeyboardNavigation();
    
    // Add visual feedback for accordion state
    addVisualFeedback();
    
    // Add analytics tracking (optional)
    addAnalyticsTracking();
}

function addTouchFeedback() {
    const accordionButtons = document.querySelectorAll('footer.footer .accordion-button');
    
    accordionButtons.forEach(button => {
        // Add touch start feedback
        button.addEventListener('touchstart', function(e) {
            this.style.transform = 'translateX(6px) scale(0.98)';
            this.style.transition = 'all 0.1s ease';
        }, { passive: true });
        
        // Reset on touch end
        button.addEventListener('touchend', function(e) {
            setTimeout(() => {
                this.style.transform = '';
                this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            }, 100);
        }, { passive: true });
        
        // Reset on touch cancel
        button.addEventListener('touchcancel', function(e) {
            this.style.transform = '';
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        }, { passive: true });
    });
}

function addScrollToFooter() {
    const accordionButtons = document.querySelectorAll('footer.footer .accordion-button');
    
    accordionButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Small delay to allow accordion animation to start
            setTimeout(() => {
                const footer = document.querySelector('footer.footer');
                if (footer && !this.classList.contains('collapsed')) {
                    // Smooth scroll to footer with offset
                    const footerTop = footer.offsetTop - 20;
                    window.scrollTo({
                        top: footerTop,
                        behavior: 'smooth'
                    });
                }
            }, 150);
        });
    });
}

function addKeyboardNavigation() {
    const accordionButtons = document.querySelectorAll('footer.footer .accordion-button');
    const footerLinks = document.querySelectorAll('footer.footer .accordion-body a');
    
    // Enhanced keyboard navigation for accordion buttons
    accordionButtons.forEach((button, index) => {
        button.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    const nextButton = accordionButtons[index + 1];
                    if (nextButton) {
                        nextButton.focus();
                    } else {
                        accordionButtons[0].focus(); // Loop to first
                    }
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    const prevButton = accordionButtons[index - 1];
                    if (prevButton) {
                        prevButton.focus();
                    } else {
                        accordionButtons[accordionButtons.length - 1].focus(); // Loop to last
                    }
                    break;
                    
                case 'Home':
                    e.preventDefault();
                    accordionButtons[0].focus();
                    break;
                    
                case 'End':
                    e.preventDefault();
                    accordionButtons[accordionButtons.length - 1].focus();
                    break;
            }
        });
    });
    
    // Enhanced keyboard navigation for footer links
    footerLinks.forEach(link => {
        link.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
}

function addVisualFeedback() {
    const accordionItems = document.querySelectorAll('footer.footer .accordion-item');
    
    accordionItems.forEach(item => {
        const button = item.querySelector('.accordion-button');
        const collapse = item.querySelector('.accordion-collapse');
        
        if (button && collapse) {
            // Add visual feedback when accordion opens/closes
            collapse.addEventListener('show.bs.collapse', function() {
                button.style.background = 'linear-gradient(135deg, rgba(13, 110, 253, 0.15) 0%, rgba(13, 110, 253, 0.2) 100%)';
                
                // Add a subtle pulse animation
                button.style.animation = 'footerPulse 0.6s ease-out';
                setTimeout(() => {
                    button.style.animation = '';
                }, 600);
            });
            
            collapse.addEventListener('hide.bs.collapse', function() {
                button.style.background = '';
            });
            
            // Add hover sound effect (optional - can be enabled if needed)
            button.addEventListener('mouseenter', function() {
                // Uncomment below to add hover sound
                // playHoverSound();
            });
        }
    });
}

function addAnalyticsTracking() {
    const accordionButtons = document.querySelectorAll('footer.footer .accordion-button');
    const footerLinks = document.querySelectorAll('footer.footer .accordion-body a');
    
    // Track accordion interactions
    accordionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const sectionName = this.textContent.trim();
            const isExpanding = this.classList.contains('collapsed');
            
            // Track with Google Analytics if available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'footer_accordion_interaction', {
                    'section_name': sectionName,
                    'action': isExpanding ? 'expand' : 'collapse',
                    'event_category': 'mobile_footer'
                });
            }
            
            // Track with custom analytics if available
            if (typeof trackEvent !== 'undefined') {
                trackEvent('mobile_footer', 'accordion_' + (isExpanding ? 'expand' : 'collapse'), sectionName);
            }
            
            console.log(`[Mobile Footer] ${isExpanding ? 'Expanded' : 'Collapsed'} section: ${sectionName}`);
        });
    });
    
    // Track footer link clicks
    footerLinks.forEach(link => {
        link.addEventListener('click', function() {
            const linkText = this.textContent.trim();
            const linkUrl = this.href;
            
            // Track with Google Analytics if available
            if (typeof gtag !== 'undefined') {
                gtag('event', 'footer_link_click', {
                    'link_text': linkText,
                    'link_url': linkUrl,
                    'event_category': 'mobile_footer'
                });
            }
            
            // Track with custom analytics if available
            if (typeof trackEvent !== 'undefined') {
                trackEvent('mobile_footer', 'link_click', linkText);
            }
            
            console.log(`[Mobile Footer] Clicked link: ${linkText} (${linkUrl})`);
        });
    });
}

// Optional: Add CSS animation for pulse effect
function addPulseAnimation() {
    if (!document.getElementById('mobile-footer-animations')) {
        const style = document.createElement('style');
        style.id = 'mobile-footer-animations';
        style.textContent = `
            @keyframes footerPulse {
                0% { transform: translateX(4px) scale(1); }
                50% { transform: translateX(6px) scale(1.02); }
                100% { transform: translateX(4px) scale(1); }
            }
            
            @keyframes footerLinkHover {
                0% { transform: translateX(8px); }
                50% { transform: translateX(12px); }
                100% { transform: translateX(8px); }
            }
        `;
        document.head.appendChild(style);
    }
}

// Optional: Hover sound effect function (disabled by default)
function playHoverSound() {
    // Uncomment and customize if you want to add sound effects
    /*
    try {
        const audio = new Audio('/static/sounds/hover.mp3');
        audio.volume = 0.1;
        audio.play().catch(e => {
            // Ignore audio play errors (user interaction required)
        });
    } catch (e) {
        // Ignore audio errors
    }
    */
}

// Initialize animations
addPulseAnimation();

// Export functions for potential external use
window.MobileFooterEnhanced = {
    initialize: initializeMobileFooterEnhancements,
    addTouchFeedback: addTouchFeedback,
    addScrollToFooter: addScrollToFooter,
    addKeyboardNavigation: addKeyboardNavigation,
    addVisualFeedback: addVisualFeedback
};
