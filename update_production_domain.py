#!/usr/bin/env python
"""
Enhanced script to update all domain references for production deployment.
This script updates:
1. Django Site framework domain
2. CSRF_TRUSTED_ORIGINS in settings
3. Email template references
4. Any hardcoded localhost/127.0.0.1 URLs
"""

import os
import sys
import django
import re
from pathlib import Path

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.sites.models import Site
from django.conf import settings

def update_django_site(new_domain, new_name=None):
    """Update the Django Site framework domain."""
    try:
        site = Site.objects.get_current()
        old_domain = site.domain
        old_name = site.name
        
        site.domain = new_domain
        site.name = new_name if new_name else new_domain
        site.save()
        
        print(f"✅ Django Site updated:")
        print(f"   Old domain: {old_domain}")
        print(f"   New domain: {site.domain}")
        print(f"   Old name: {old_name}")
        print(f"   New name: {site.name}")
        
        return True
    except Exception as e:
        print(f"❌ Error updating Django Site: {e}")
        return False

def update_settings_file(new_domain):
    """Update hardcoded domains in settings.py."""
    settings_path = Path('company_assistant/settings.py')
    
    if not settings_path.exists():
        print(f"❌ Settings file not found: {settings_path}")
        return False
    
    try:
        with open(settings_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Update CSRF_TRUSTED_ORIGINS
        csrf_pattern = r"CSRF_TRUSTED_ORIGINS = \[([^\]]+)\]"
        new_csrf_origins = f"CSRF_TRUSTED_ORIGINS = ['http://localhost:8000', 'http://127.0.0.1:8000', 'https://{new_domain}', 'http://{new_domain}']"
        content = re.sub(csrf_pattern, new_csrf_origins, content)
        
        # Update ALLOWED_HOSTS to include new domain
        allowed_hosts_pattern = r"ALLOWED_HOSTS = \[([^\]]+)\]"
        if new_domain not in content:
            # Add new domain to ALLOWED_HOSTS if not already present
            def replace_allowed_hosts(match):
                current_hosts = match.group(1)
                if new_domain not in current_hosts:
                    # Add the new domain
                    return f"ALLOWED_HOSTS = [{current_hosts}, '{new_domain}', 'www.{new_domain}']"
                return match.group(0)
            
            content = re.sub(allowed_hosts_pattern, replace_allowed_hosts, content)
        
        # Write back if changes were made
        if content != original_content:
            with open(settings_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Updated settings.py with new domain: {new_domain}")
            return True
        else:
            print(f"ℹ️  No changes needed in settings.py")
            return True
            
    except Exception as e:
        print(f"❌ Error updating settings.py: {e}")
        return False

def update_email_utils(new_domain):
    """Update get_site_url function to use production domain."""
    email_utils_path = Path('accounts/email_utils.py')
    
    if not email_utils_path.exists():
        print(f"❌ Email utils file not found: {email_utils_path}")
        return False
    
    try:
        with open(email_utils_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Update the get_site_url function to handle production properly
        old_function = '''def get_site_url():
    """Get the site URL from the Site model or settings."""
    try:
        site = Site.objects.get_current()
        site_url = f"https://{site.domain}"
        if settings.DEBUG:
            # In DEBUG mode, always use localhost regardless of site domain
            site_url = "http://localhost:8000"
    except Exception as e:
        logger.warning(f"Error getting site URL: {e}")
        site_url = settings.SITE_URL if hasattr(settings, 'SITE_URL') else "http://localhost:8000"

    return site_url'''

        new_function = '''def get_site_url():
    """Get the site URL from the Site model or settings."""
    try:
        site = Site.objects.get_current()
        if settings.DEBUG:
            # In DEBUG mode, use localhost for development
            site_url = "http://localhost:8000"
        else:
            # In production, use the site domain with HTTPS
            site_url = f"https://{site.domain}"
    except Exception as e:
        logger.warning(f"Error getting site URL: {e}")
        if settings.DEBUG:
            site_url = "http://localhost:8000"
        else:
            site_url = f"https://{new_domain}"

    return site_url'''
        
        content = content.replace(old_function, new_function)
        
        # Write back if changes were made
        if content != original_content:
            with open(email_utils_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Updated email_utils.py for production domain handling")
            return True
        else:
            print(f"ℹ️  No changes needed in email_utils.py")
            return True
            
    except Exception as e:
        print(f"❌ Error updating email_utils.py: {e}")
        return False

def scan_for_hardcoded_urls():
    """Scan for any remaining hardcoded localhost/127.0.0.1 URLs."""
    print("\n🔍 Scanning for hardcoded development URLs...")
    
    # Files to scan (excluding certain directories and file types)
    exclude_patterns = [
        '*.pyc', '__pycache__', '.git', 'venv', 'env', 'node_modules',
        '*.log', '*.md', 'test_*.py', '*test*.py', 'update_*.py'
    ]
    
    hardcoded_urls = []
    project_root = Path('.')
    
    for file_path in project_root.rglob('*.py'):
        # Skip excluded files
        if any(pattern.replace('*', '') in str(file_path) for pattern in exclude_patterns):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Look for hardcoded URLs
            localhost_matches = re.findall(r'http://localhost:8000[^\s\'"]*', content)
            ip_matches = re.findall(r'http://127\.0\.0\.1:8000[^\s\'"]*', content)
            
            if localhost_matches or ip_matches:
                hardcoded_urls.append({
                    'file': str(file_path),
                    'localhost': localhost_matches,
                    'ip': ip_matches
                })
                
        except Exception as e:
            # Skip files that can't be read
            continue
    
    if hardcoded_urls:
        print("⚠️  Found hardcoded development URLs:")
        for item in hardcoded_urls:
            print(f"   📄 {item['file']}")
            for url in item['localhost']:
                print(f"      🔗 {url}")
            for url in item['ip']:
                print(f"      🔗 {url}")
        print("\n💡 These URLs will automatically use the production domain when DEBUG=False")
    else:
        print("✅ No hardcoded development URLs found")
    
    return hardcoded_urls

def main():
    """Main function to update all domain references."""
    print("🚀 Production Domain Update Script")
    print("=" * 50)
    
    # Get domain from command line arguments
    if len(sys.argv) > 1:
        new_domain = sys.argv[1].strip()
    else:
        new_domain = "24seven.site"
    
    # Remove protocol and trailing slash if present
    new_domain = new_domain.replace('https://', '').replace('http://', '').rstrip('/')
    
    print(f"🎯 Target domain: {new_domain}")
    
    # Get optional site name
    site_name = None
    if len(sys.argv) > 2:
        site_name = sys.argv[2].strip()
    else:
        site_name = "24seven Platform"
    
    print(f"🏷️  Site name: {site_name}")
    print()
    
    # Update Django Site framework
    print("1️⃣ Updating Django Site framework...")
    site_success = update_django_site(new_domain, site_name)
    
    # Update settings.py
    print("\n2️⃣ Updating settings.py...")
    settings_success = update_settings_file(new_domain)
    
    # Update email_utils.py
    print("\n3️⃣ Updating email utilities...")
    email_success = update_email_utils(new_domain)
    
    # Scan for hardcoded URLs
    scan_for_hardcoded_urls()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DOMAIN UPDATE SUMMARY")
    print("=" * 50)
    
    if site_success and settings_success and email_success:
        print("✅ All updates completed successfully!")
        print(f"🎯 Production domain: https://{new_domain}")
        print(f"🏷️  Site name: {site_name}")
        
        print("\n📋 Next steps for production deployment:")
        print("1. Set environment variables:")
        print("   PRODUCTION=True")
        print("   DEBUG=False")
        print("   CPANEL_ENV=True")
        print("2. Upload files to your production server")
        print("3. Run migrations: python manage.py migrate")
        print("4. Collect static files: python manage.py collectstatic")
        print("5. Restart your application")
        
        print("\n✨ Your application is now configured for production!")
        
    else:
        print("❌ Some updates failed. Please check the error messages above.")
        print("🔧 Manual fixes may be required.")
    
    print("\n💡 Note: Development URLs (localhost:8000) will still work in DEBUG mode")
    print("   Production URLs will be used automatically when DEBUG=False")

if __name__ == "__main__":
    main()
