/**
 * Progressive Icon Placeholders CSS
 * Unified responsive scaling for all icon placeholders across all card types
 * Ensures icons scale properly and look good on all screen sizes
 */

/* ===== BASE ICON PLACEHOLDER STYLES ===== */
.logo-placeholder,
.company-logo-placeholder,
.assistant-logo-placeholder,
.featured-carousel-item .logo-placeholder,
.company-logo-item .logo-placeholder {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: #f8f9fa !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
    width: 100% !important;
    height: 100% !important;
}

.logo-placeholder i,
.company-logo-placeholder i,
.assistant-logo-placeholder i,
.featured-carousel-item .logo-placeholder i,
.company-logo-item .logo-placeholder i {
    color: #6c757d !important;
    transition: all 0.3s ease !important;
    line-height: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
    display: block !important;
    text-align: center !important;
}

/* ===== DESKTOP STYLES (992px and up) ===== */
@media (min-width: 992px) {
    /* Directory Cards - Large Icons */
    .directory-card .logo-placeholder i,
    .list-group-item .logo-placeholder i {
        font-size: 120px !important;
    }
    
    /* Featured Carousel - Medium Icons */
    .featured-carousel-item .logo-placeholder i {
        font-size: 24px !important;
    }
    
    /* Company Logo Carousel - Small Icons */
    .company-logo-item .logo-placeholder i {
        font-size: 24px !important;
    }
}

/* ===== TABLET STYLES (768px - 991.98px) ===== */
@media (min-width: 768px) and (max-width: 991.98px) {
    /* Directory Cards - Medium-Large Icons */
    .directory-card .logo-placeholder i,
    .list-group-item .logo-placeholder i {
        font-size: 100px !important;
    }
    
    /* Featured Carousel - Small-Medium Icons */
    .featured-carousel-item .logo-placeholder i {
        font-size: 22px !important;
    }
    
    /* Company Logo Carousel - Small Icons */
    .company-logo-item .logo-placeholder i {
        font-size: 22px !important;
    }
}

/* ===== MOBILE STYLES (576px - 767.98px) ===== */
@media (min-width: 576px) and (max-width: 767.98px) {
    /* Directory Cards - Medium Icons */
    .directory-card .logo-placeholder i,
    .list-group-item .logo-placeholder i {
        font-size: 80px !important;
    }
    
    /* Featured Carousel - Small Icons */
    .featured-carousel-item .logo-placeholder i {
        font-size: 20px !important;
    }
    
    /* Company Logo Carousel - Small Icons */
    .company-logo-item .logo-placeholder i {
        font-size: 20px !important;
    }
}

/* ===== SMALL MOBILE STYLES (up to 575.98px) ===== */
@media (max-width: 575.98px) {
    /* Directory Cards - Small-Medium Icons */
    .directory-card .logo-placeholder i,
    .list-group-item .logo-placeholder i {
        font-size: 60px !important;
    }
    
    /* Featured Carousel - Extra Small Icons */
    .featured-carousel-item .logo-placeholder i {
        font-size: 18px !important;
    }
    
    /* Company Logo Carousel - Extra Small Icons */
    .company-logo-item .logo-placeholder i {
        font-size: 18px !important;
    }
}

/* ===== SPECIFIC ICON TYPES ===== */
/* Company Icons */
.logo-placeholder .bi-building,
.logo-placeholder .bi-building-fill,
.company-logo-placeholder .bi-building,
.company-logo-placeholder .bi-building-fill {
    color: #0d6efd !important;
}

/* Assistant Icons */
.logo-placeholder .bi-robot,
.logo-placeholder .bi-person-circle,
.assistant-logo-placeholder .bi-robot,
.assistant-logo-placeholder .bi-person-circle {
    color: #198754 !important;
}

/* Community Assistant Icons */
.logo-placeholder .bi-people,
.logo-placeholder .bi-people-fill {
    color: #6f42c1 !important;
}

/* ===== HOVER EFFECTS ===== */
.logo-placeholder:hover,
.company-logo-placeholder:hover,
.assistant-logo-placeholder:hover {
    background-color: #e9ecef !important;
    transform: scale(1.02) !important;
}

.logo-placeholder:hover i,
.company-logo-placeholder:hover i,
.assistant-logo-placeholder:hover i {
    transform: scale(1.1) !important;
}

/* ===== DARK MODE SUPPORT ===== */
[data-theme="dark"] .logo-placeholder,
[data-theme="dark"] .company-logo-placeholder,
[data-theme="dark"] .assistant-logo-placeholder,
[data-theme="dark"] .featured-carousel-item .logo-placeholder,
[data-theme="dark"] .company-logo-item .logo-placeholder {
    background-color: #2d3748 !important;
}

[data-theme="dark"] .logo-placeholder i,
[data-theme="dark"] .company-logo-placeholder i,
[data-theme="dark"] .assistant-logo-placeholder i,
[data-theme="dark"] .featured-carousel-item .logo-placeholder i,
[data-theme="dark"] .company-logo-item .logo-placeholder i {
    color: #a0aec0 !important;
}

[data-theme="dark"] .logo-placeholder:hover,
[data-theme="dark"] .company-logo-placeholder:hover,
[data-theme="dark"] .assistant-logo-placeholder:hover {
    background-color: #4a5568 !important;
}

/* Company Icons in Dark Mode */
[data-theme="dark"] .logo-placeholder .bi-building,
[data-theme="dark"] .logo-placeholder .bi-building-fill,
[data-theme="dark"] .company-logo-placeholder .bi-building,
[data-theme="dark"] .company-logo-placeholder .bi-building-fill {
    color: #63b3ed !important;
}

/* Assistant Icons in Dark Mode */
[data-theme="dark"] .logo-placeholder .bi-robot,
[data-theme="dark"] .logo-placeholder .bi-person-circle,
[data-theme="dark"] .assistant-logo-placeholder .bi-robot,
[data-theme="dark"] .assistant-logo-placeholder .bi-person-circle {
    color: #68d391 !important;
}

/* Community Assistant Icons in Dark Mode */
[data-theme="dark"] .logo-placeholder .bi-people,
[data-theme="dark"] .logo-placeholder .bi-people-fill {
    color: #b794f6 !important;
}

/* ===== CAROUSEL SPECIFIC FIXES ===== */
/* Featured Carousel Items */
.featured-carousel-item .logo-container {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
}

/* Company Logo Carousel Items */
.company-logo-item .logo-container {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    overflow: hidden !important;
}

/* ===== PROGRESSIVE SCALING ANIMATION ===== */
@keyframes iconScale {
    0% { transform: scale(0.8); opacity: 0.7; }
    50% { transform: scale(1.05); opacity: 0.9; }
    100% { transform: scale(1); opacity: 1; }
}

.logo-placeholder i,
.company-logo-placeholder i,
.assistant-logo-placeholder i {
    animation: iconScale 0.5s ease-out !important;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.logo-placeholder,
.company-logo-placeholder,
.assistant-logo-placeholder {
    position: relative !important;
}

.logo-placeholder::after,
.company-logo-placeholder::after,
.assistant-logo-placeholder::after {
    content: attr(aria-label) !important;
    position: absolute !important;
    bottom: -20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    font-size: 0.7rem !important;
    color: #6c757d !important;
    white-space: nowrap !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.logo-placeholder:hover::after,
.company-logo-placeholder:hover::after,
.assistant-logo-placeholder:hover::after {
    opacity: 1 !important;
}

/* ===== OVERRIDE CONFLICTING STYLES ===== */
/* Remove any conflicting font-size declarations */
.logo-container .logo-placeholder i {
    font-size: inherit !important;
}

.featured-carousel-item .logo-container .logo-placeholder i {
    font-size: inherit !important;
}

.company-logo-item .logo-container .logo-placeholder i {
    font-size: inherit !important;
}
