#!/usr/bin/env python
"""
Test script to check what data is in the CompanyCreationForm cleaned_data.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.forms import CompanyCreationForm

User = get_user_model()

def test_form_data():
    """Test what data is actually in the form's cleaned_data."""
    
    print("Testing CompanyCreationForm Data Processing...")
    print("=" * 50)
    
    # Create a test user
    test_user, created = User.objects.get_or_create(
        username='testuser3',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    # Test data for company creation
    form_data = {
        'name': 'Test Data Company',
        'entity_type': 'company',
        'mission': 'Test mission data',
        'description': 'Test description data',
        'website': 'https://testdata.com',
        'contact_email': '<EMAIL>',
        'contact_phone': '+**********',
        'timezone': 'UTC',
        'language': 'en',
        'industry': 'Technology',
        'size': '10-50',
        'city': 'Test City',
        'country': 'Test Country',
        'founded': 2020,
        'list_in_directory': True
    }
    
    print("1. Creating form with test data...")
    form = CompanyCreationForm(data=form_data, user=test_user)
    
    print("2. Checking form validity...")
    if form.is_valid():
        print("   ✓ Form is valid")
        
        print("\n3. Checking cleaned_data contents:")
        for field_name, value in form.cleaned_data.items():
            print(f"   {field_name}: {value}")
        
        print("\n4. Checking if all expected fields are present:")
        expected_fields = [
            'name', 'entity_type', 'mission', 'description', 'website', 
            'contact_email', 'contact_phone', 'timezone', 'language', 
            'industry', 'size', 'city', 'country', 'founded', 'list_in_directory'
        ]
        
        missing_fields = []
        for field in expected_fields:
            if field not in form.cleaned_data:
                missing_fields.append(field)
            else:
                print(f"   ✓ {field}: {form.cleaned_data[field]}")
        
        if missing_fields:
            print(f"\n   ✗ Missing fields in cleaned_data: {missing_fields}")
        else:
            print("\n   ✓ All expected fields are present in cleaned_data")
            
    else:
        print("   ✗ Form is invalid:")
        for field, errors in form.errors.items():
            print(f"     {field}: {errors}")
    
    # Clean up
    if created:
        test_user.delete()

if __name__ == '__main__':
    test_form_data()
