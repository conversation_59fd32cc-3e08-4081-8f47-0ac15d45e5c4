#!/usr/bin/env python
"""
Initialize Frontend Performance Optimizations
Sets up all frontend optimization components and validates their functionality.
"""

import os
import sys
import django
import json
from pathlib import Path
from django.conf import settings
from django.template.loader import render_to_string

# Add the project directory to Python path
project_dir = Path(__file__).parent
sys.path.insert(0, str(project_dir))

# Set up Django environment with performance optimizations
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'company_assistant.settings')
os.environ['PRODUCTION'] = 'True'  # Enable production optimizations
django.setup()


class FrontendOptimizationInitializer:
    """Initializes all frontend performance optimizations."""

    def __init__(self):
        self.initialized_components = []
        self.errors = []
        self.static_dir = Path(settings.STATIC_ROOT or 'static')
        self.templates_dir = Path(settings.BASE_DIR) / 'templates'

    def initialize_all(self):
        """Initialize all frontend optimization components."""
        print("🚀 Starting Frontend Optimization Initialization...")
        print("=" * 60)

        # Initialize components
        self.validate_static_files()
        self.create_optimized_templates()
        self.setup_service_worker_config()
        self.create_performance_dashboard()
        self.validate_browser_support()
        self.create_optimization_manifest()

        # Report results
        self.report_initialization_results()

    def validate_static_files(self):
        """Validate that all static optimization files exist."""
        try:
            print("📁 Validating static optimization files...")

            required_files = [
                'js/performance/core-performance.js',
                'js/sw.js',
                'css/critical.css',
                'js/workers/data-processor.js',
                'js/performance-monitor.js',
                'js/lazy-loading/advanced-lazy-loader.js'
            ]

            missing_files = []
            existing_files = []

            for file_path in required_files:
                full_path = self.static_dir / file_path
                if full_path.exists():
                    existing_files.append(file_path)
                    print(f"  ✅ {file_path}")
                else:
                    missing_files.append(file_path)
                    print(f"  ❌ {file_path} - MISSING")

            if missing_files:
                self.errors.append(f"Missing static files: {', '.join(missing_files)}")
            else:
                self.initialized_components.append("Static Files Validation")

        except Exception as e:
            error_msg = f"Failed to validate static files: {e}"
            print(f"  ❌ {error_msg}")
            self.errors.append(error_msg)

    def create_optimized_templates(self):
        """Create optimized template includes."""
        try:
            print("\n📄 Creating optimized template includes...")

            # Create performance includes template
            performance_template = """
<!-- Frontend Performance Optimization Includes -->
{% load static %}

<!-- Critical CSS (inline for fastest rendering) -->
<style>
{% include 'performance/critical_inline.css' %}
</style>

<!-- Preload critical resources -->
<link rel="preload" href="{% static 'js/performance/core-performance.js' %}" as="script">
<link rel="preload" href="{% static 'css/critical.css' %}" as="style">
<link rel="preload" href="{% static 'fonts/main.woff2' %}" as="font" type="font/woff2" crossorigin>

<!-- DNS prefetch for external resources -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

<!-- Performance monitoring (load early) -->
<script src="{% static 'js/performance-monitor.js' %}" defer></script>

<!-- Core performance system -->
<script src="{% static 'js/performance/core-performance.js' %}" defer></script>

<!-- Lazy loading system -->
<script src="{% static 'js/lazy-loading/advanced-lazy-loader.js' %}" defer></script>

<!-- Performance configuration -->
<script>
window.PERFORMANCE_CONFIG = {
    enableLazyLoading: true,
    enableServiceWorker: true,
    enableVirtualScrolling: true,
    enableImageOptimization: true,
    enableCriticalCSS: true,
    cacheStrategy: 'aggressive',
    performanceThreshold: {
        fcp: 1500,
        lcp: 2500,
        fid: 100,
        cls: 0.1
    }
};
</script>
"""

            # Create template directory if it doesn't exist
            performance_templates_dir = self.templates_dir / 'performance'
            performance_templates_dir.mkdir(exist_ok=True)

            # Write performance includes template
            includes_file = performance_templates_dir / 'includes.html'
            with open(includes_file, 'w') as f:
                f.write(performance_template)

            print(f"  ✅ Created {includes_file}")

            # Create critical CSS inline template
            critical_css_file = performance_templates_dir / 'critical_inline.css'
            if not critical_css_file.exists():
                # Copy critical CSS content
                source_css = self.static_dir / 'css' / 'critical.css'
                if source_css.exists():
                    with open(source_css, 'r') as src, open(critical_css_file, 'w') as dst:
                        dst.write(src.read())
                    print(f"  ✅ Created {critical_css_file}")

            self.initialized_components.append("Optimized Templates")

        except Exception as e:
            error_msg = f"Failed to create optimized templates: {e}"
            print(f"  ❌ {error_msg}")
            self.errors.append(error_msg)

    def setup_service_worker_config(self):
        """Set up service worker configuration."""
        try:
            print("\n⚙️ Setting up service worker configuration...")

            # Create service worker manifest
            sw_config = {
                "name": "AI Assistant Platform",
                "short_name": "AI Assistant",
                "description": "High-performance AI assistant platform",
                "start_url": "/",
                "display": "standalone",
                "background_color": "#0f0f0f",
                "theme_color": "#3b82f6",
                "icons": [
                    {
                        "src": "/static/images/icon-192.png",
                        "sizes": "192x192",
                        "type": "image/png"
                    },
                    {
                        "src": "/static/images/icon-512.png",
                        "sizes": "512x512",
                        "type": "image/png"
                    }
                ],
                "cache_strategy": {
                    "static_assets": "cache-first",
                    "api_requests": "stale-while-revalidate",
                    "html_pages": "network-first",
                    "images": "cache-first"
                }
            }

            # Write manifest file
            manifest_file = self.static_dir / 'manifest.json'
            with open(manifest_file, 'w') as f:
                json.dump(sw_config, f, indent=2)

            print(f"  ✅ Created service worker manifest: {manifest_file}")

            # Create offline page template
            offline_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - AI Assistant Platform</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0f0f0f;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            text-align: center;
        }
        .offline-container {
            max-width: 400px;
            padding: 2rem;
        }
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .offline-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .offline-message {
            color: #cccccc;
            line-height: 1.6;
        }
        .retry-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            margin-top: 1rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            It looks like you've lost your internet connection.
            Don't worry, you can still browse cached content.
        </p>
        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>
    </div>
</body>
</html>
"""

            offline_file = self.templates_dir / 'offline.html'
            with open(offline_file, 'w') as f:
                f.write(offline_template)

            print(f"  ✅ Created offline page: {offline_file}")

            self.initialized_components.append("Service Worker Configuration")

        except Exception as e:
            error_msg = f"Failed to setup service worker: {e}"
            print(f"  ❌ {error_msg}")
            self.errors.append(error_msg)

    def create_performance_dashboard(self):
        """Create performance monitoring dashboard."""
        try:
            print("\n📊 Creating performance dashboard...")

            dashboard_template = """
{% extends 'base.html' %}
{% load static %}

{% block title %}Performance Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/performance-dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="performance-dashboard">
    <h1>Frontend Performance Dashboard</h1>

    <div class="metrics-grid">
        <div class="metric-card">
            <h3>Performance Score</h3>
            <div class="metric-value" id="performance-score">--</div>
            <div class="metric-unit">/ 100</div>
        </div>

        <div class="metric-card">
            <h3>First Contentful Paint</h3>
            <div class="metric-value" id="fcp-value">--</div>
            <div class="metric-unit">ms</div>
        </div>

        <div class="metric-card">
            <h3>Largest Contentful Paint</h3>
            <div class="metric-value" id="lcp-value">--</div>
            <div class="metric-unit">ms</div>
        </div>

        <div class="metric-card">
            <h3>First Input Delay</h3>
            <div class="metric-value" id="fid-value">--</div>
            <div class="metric-unit">ms</div>
        </div>

        <div class="metric-card">
            <h3>Cumulative Layout Shift</h3>
            <div class="metric-value" id="cls-value">--</div>
            <div class="metric-unit"></div>
        </div>

        <div class="metric-card">
            <h3>Cache Hit Rate</h3>
            <div class="metric-value" id="cache-hit-rate">--</div>
            <div class="metric-unit">%</div>
        </div>
    </div>

    <div class="charts-section">
        <div class="chart-container">
            <h3>Performance Trends</h3>
            <canvas id="performance-chart"></canvas>
        </div>

        <div class="chart-container">
            <h3>Resource Loading</h3>
            <canvas id="resources-chart"></canvas>
        </div>
    </div>

    <div class="recommendations-section">
        <h3>Optimization Recommendations</h3>
        <div id="recommendations-list"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/performance-dashboard.js' %}"></script>
{% endblock %}
"""

            dashboard_file = self.templates_dir / 'performance' / 'dashboard.html'
            with open(dashboard_file, 'w') as f:
                f.write(dashboard_template)

            print(f"  ✅ Created performance dashboard: {dashboard_file}")

            self.initialized_components.append("Performance Dashboard")

        except Exception as e:
            error_msg = f"Failed to create performance dashboard: {e}"
            print(f"  ❌ {error_msg}")
            self.errors.append(error_msg)

    def validate_browser_support(self):
        """Validate browser support for optimization features."""
        try:
            print("\n🌐 Validating browser support...")

            # Create browser support detection script
            support_script = """
// Browser Support Detection for Performance Optimizations
const BrowserSupport = {
    features: {
        serviceWorker: 'serviceWorker' in navigator,
        intersectionObserver: 'IntersectionObserver' in window,
        performanceObserver: 'PerformanceObserver' in window,
        webWorkers: typeof Worker !== 'undefined',
        webp: false,
        avif: false
    },

    async detectImageFormats() {
        // Detect WebP support
        this.features.webp = await this.supportsFormat('webp');

        // Detect AVIF support
        this.features.avif = await this.supportsFormat('avif');
    },

    supportsFormat(format) {
        return new Promise(resolve => {
            const canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            canvas.toBlob(blob => {
                resolve(blob && blob.type.includes(format));
            }, `image/${format}`);
        });
    },

    getCompatibilityReport() {
        const total = Object.keys(this.features).length;
        const supported = Object.values(this.features).filter(Boolean).length;
        const percentage = Math.round((supported / total) * 100);

        return {
            percentage,
            supported,
            total,
            features: this.features,
            recommendations: this.getRecommendations()
        };
    },

    getRecommendations() {
        const recommendations = [];

        if (!this.features.serviceWorker) {
            recommendations.push('Service Worker not supported - caching will be limited');
        }

        if (!this.features.intersectionObserver) {
            recommendations.push('Intersection Observer not supported - lazy loading will use fallback');
        }

        if (!this.features.performanceObserver) {
            recommendations.push('Performance Observer not supported - metrics will be limited');
        }

        if (!this.features.webp) {
            recommendations.push('WebP not supported - images will use fallback formats');
        }

        return recommendations;
    }
};

// Initialize detection
BrowserSupport.detectImageFormats().then(() => {
    const report = BrowserSupport.getCompatibilityReport();
    console.log('Browser Compatibility Report:', report);

    // Store for dashboard
    window.browserCompatibility = report;
});
"""

            support_file = self.static_dir / 'js' / 'browser-support.js'
            with open(support_file, 'w') as f:
                f.write(support_script)

            print(f"  ✅ Created browser support detection: {support_file}")

            self.initialized_components.append("Browser Support Validation")

        except Exception as e:
            error_msg = f"Failed to validate browser support: {e}"
            print(f"  ❌ {error_msg}")
            self.errors.append(error_msg)

    def create_optimization_manifest(self):
        """Create optimization manifest for tracking."""
        try:
            print("\n📋 Creating optimization manifest...")

            manifest = {
                "version": "2.0.0",
                "timestamp": "2024-01-01T00:00:00Z",
                "optimizations": {
                    "core_performance": {
                        "enabled": True,
                        "features": [
                            "performance_monitoring",
                            "lazy_loading",
                            "image_optimization",
                            "virtual_scrolling",
                            "service_worker"
                        ]
                    },
                    "caching": {
                        "enabled": True,
                        "strategies": [
                            "cache_first",
                            "network_first",
                            "stale_while_revalidate"
                        ]
                    },
                    "compression": {
                        "enabled": True,
                        "formats": ["webp", "avif", "gzip", "brotli"]
                    },
                    "monitoring": {
                        "enabled": True,
                        "metrics": ["fcp", "lcp", "fid", "cls", "ttfb"]
                    }
                },
                "performance_targets": {
                    "fcp": 1500,
                    "lcp": 2500,
                    "fid": 100,
                    "cls": 0.1,
                    "cache_hit_rate": 80
                },
                "browser_support": {
                    "minimum": "Chrome 60, Firefox 55, Safari 12, Edge 79",
                    "optimal": "Chrome 90+, Firefox 88+, Safari 14+, Edge 90+"
                }
            }

            manifest_file = self.static_dir / 'optimization-manifest.json'
            with open(manifest_file, 'w') as f:
                json.dump(manifest, f, indent=2)

            print(f"  ✅ Created optimization manifest: {manifest_file}")

            self.initialized_components.append("Optimization Manifest")

        except Exception as e:
            error_msg = f"Failed to create optimization manifest: {e}"
            print(f"  ❌ {error_msg}")
            self.errors.append(error_msg)

    def report_initialization_results(self):
        """Report the results of initialization."""
        print("\n" + "=" * 60)
        print("🎯 FRONTEND OPTIMIZATION INITIALIZATION COMPLETE")
        print("=" * 60)

        print(f"✅ Successfully initialized {len(self.initialized_components)} components:")
        for component in self.initialized_components:
            print(f"  ✅ {component}")

        if self.errors:
            print(f"\n⚠️ Encountered {len(self.errors)} errors:")
            for error in self.errors:
                print(f"  ❌ {error}")

        print("\n" + "=" * 60)

        # Show next steps
        self.show_next_steps()

    def show_next_steps(self):
        """Show next steps for implementation."""
        print("🚀 NEXT STEPS:")
        print("1. Include performance templates in your base template:")
        print("   {% include 'performance/includes.html' %}")
        print("\n2. Add manifest to your HTML head:")
        print('   <link rel="manifest" href="/static/manifest.json">')
        print("\n3. Test performance optimizations:")
        print("   - Open browser dev tools")
        print("   - Check Network tab for caching")
        print("   - Monitor Performance tab for metrics")
        print("\n4. Access performance dashboard:")
        print("   - Add URL pattern for /performance/dashboard/")
        print("   - Monitor real-time metrics")
        print("\n5. Validate optimizations:")
        print("   - Run Lighthouse audit")
        print("   - Check Core Web Vitals")
        print("   - Test on different devices/networks")


def main():
    """Main function to run the initialization."""
    try:
        initializer = FrontendOptimizationInitializer()
        initializer.initialize_all()

        # Create status file
        status_file = project_dir / "frontend_optimization_status.txt"
        with open(status_file, 'w') as f:
            f.write("Frontend optimizations initialized successfully\n")
            f.write(f"Components: {', '.join(initializer.initialized_components)}\n")
            f.write(f"Errors: {len(initializer.errors)}\n")

        print(f"\n📄 Status written to {status_file}")

        return len(initializer.errors) == 0

    except Exception as e:
        print(f"❌ Critical error during initialization: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
