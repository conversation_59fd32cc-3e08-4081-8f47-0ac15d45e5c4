"""
Email utility functions for the accounts app.
This module provides functions for sending various types of emails:
- Sign-in approval emails
- Team invitation emails
- Bulk invitation emails
"""

import logging
import os
from django.conf import settings
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.contrib.sites.models import Site

logger = logging.getLogger(__name__)

def get_site_url():
    """Get the site URL from the Site model or settings."""
    try:
        site = Site.objects.get_current()

        # Check if we should force production domain even in DEBUG mode
        force_production_domain = os.environ.get('FORCE_PRODUCTION_DOMAIN', 'False').lower() == 'true'

        if settings.DEBUG and not force_production_domain:
            # In DEBUG mode, use localhost for development (unless forced)
            site_url = "http://localhost:8000"
        else:
            # In production or when forced, use the site domain with HTTPS
            protocol = "https" if not settings.DEBUG else "http"
            site_url = f"{protocol}://{site.domain}"
    except Exception as e:
        logger.warning(f"Error getting site URL: {e}")
        force_production_domain = os.environ.get('FORCE_PRODUCTION_DOMAIN', 'False').lower() == 'true'

        if settings.DEBUG and not force_production_domain:
            site_url = "http://localhost:8000"
        else:
            site_url = "https://24seven.site"

    return site_url

def send_html_email(to_email, subject, html_content=None, text_content=None, template_html=None, template_txt=None, context=None, from_email=None):
    """
    Send an HTML email with a text alternative.

    This function supports both direct content and template-based emails.

    Args:
        to_email (str or list): Recipient email address(es)
        subject (str): Email subject
        html_content (str): Direct HTML content (optional)
        text_content (str): Direct text content (optional)
        template_html (str): Path to HTML template (optional)
        template_txt (str): Path to text template (optional)
        context (dict): Context for rendering templates
        from_email (str): Sender email address (defaults to settings.DEFAULT_FROM_EMAIL)

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    if context is None:
        context = {}

    # Add site_url to context if not present
    if 'site_url' not in context:
        context['site_url'] = get_site_url()

    # Add site_config to context for configurable URLs
    if 'site_config' not in context:
        try:
            from site_settings.models import SiteConfiguration
            context['site_config'] = SiteConfiguration.load()
        except Exception as e:
            logger.warning(f"Could not load site configuration: {e}")
            context['site_config'] = None

    # Render content from templates or use direct content
    if template_html and not html_content:
        html_content = render_to_string(template_html, context)

    if template_txt and not text_content:
        text_content = render_to_string(template_txt, context)
    elif not text_content and html_content:
        # Generate text content from HTML if not provided
        text_content = strip_tags(html_content)

    # Use default from email if not specified
    if from_email is None:
        from_email = settings.DEFAULT_FROM_EMAIL

    # Create email message
    if isinstance(to_email, str):
        to_email = [to_email]

    try:
        if html_content:
            msg = EmailMultiAlternatives(subject, text_content, from_email, to_email)
            msg.attach_alternative(html_content, "text/html")
            msg.send()
        else:
            send_mail(subject, text_content, from_email, to_email)

        logger.info(f"Email sent to {to_email}")
        return True
    except Exception as e:
        logger.error(f"Error sending email to {to_email}: {e}")
        return False

def send_signin_approval_email(user, approval_url, expiry_hours=24):
    """
    Send a sign-in approval email with a verification link.

    Args:
        user: User object
        approval_url: URL for approving the sign-in
        expiry_hours: Hours until the approval link expires

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    subject = "Sign-in Verification Required"
    to_email = user.email

    context = {
        'user': user,
        'approval_url': approval_url,
        'expiry_hours': expiry_hours,
    }

    return send_html_email(
        subject=subject,
        to_email=to_email,
        template_html='accounts/email/signin_approval.html',
        template_txt='accounts/email/signin_approval.txt',
        context=context
    )

def send_team_invitation_email(invitation, site_config=None):
    """
    Send a team invitation email.

    Args:
        invitation: TeamInvitation object
        site_config: SiteConfiguration object (optional)

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    site_url = get_site_url()
    accept_url = f"{site_url}/accounts/invitations/accept/{invitation.token}/"

    # Calculate expiry days
    from django.utils import timezone
    expiry_days = (invitation.expires_at - timezone.now()).days
    if expiry_days < 1:
        expiry_days = 1

    # Get inviter name
    inviter_name = "A team member"
    if invitation.invited_by:
        inviter_name = invitation.invited_by.get_full_name() or invitation.invited_by.username

    subject = f"You've Been Invited to Join {invitation.company.name} on 24seven"

    context = {
        'invite': invitation,
        'company': invitation.company,
        'inviter_name': inviter_name,
        'accept_url': accept_url,
        'expiry_days': expiry_days,
        'site_url': site_url,
        'site_config': site_config,
    }

    return send_html_email(
        subject=subject,
        to_email=invitation.email,
        template_html='accounts/email/team_invitation.html',
        template_txt='accounts/email/team_invitation.txt',
        context=context
    )

def send_bulk_invitation_emails(invitations, site_config=None):
    """
    Send invitation emails to multiple recipients.

    Args:
        invitations: List of TeamInvitation objects
        site_config: SiteConfiguration object (optional)

    Returns:
        tuple: (success_count, failed_emails)
    """
    success_count = 0
    failed_emails = []

    for invitation in invitations:
        success = send_team_invitation_email(invitation, site_config)
        if success:
            success_count += 1
        else:
            failed_emails.append(invitation.email)

    return success_count, failed_emails


def send_welcome_email(user):
    """
    Send a welcome email to a new user.

    Args:
        user: User object

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    subject = f"Welcome to 24seven - {user.get_full_name() or user.username}"

    context = {
        'user': user,
    }

    return send_html_email(
        to_email=user.email,
        subject=subject,
        template_html='accounts/email/welcome.html',
        context=context
    )


def send_notification_email(user, notification_data):
    """
    Send a notification email to a user.

    Args:
        user: User object
        notification_data: Dictionary containing notification details
            - title: Notification title
            - message: Main message
            - content: Detailed content
            - type: 'success', 'warning', 'error', or 'info'
            - icon: Emoji icon (optional)
            - subtitle: Subtitle (optional)
            - action_url: URL for action button (optional)
            - action_text: Text for action button (optional)
            - details: Additional details (optional)
            - timestamp: Notification timestamp (optional)
            - related_items: List of related items (optional)
            - unsubscribe_info: Unsubscribe information (optional)

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    title = notification_data.get('title', 'Notification')
    subject = f"{title} - 24seven"

    # Create notification object for template
    notification = type('Notification', (), notification_data)()

    context = {
        'user': user,
        'notification': notification,
    }

    return send_html_email(
        to_email=user.email,
        subject=subject,
        template_html='accounts/email/notification.html',
        context=context
    )


def send_password_reset_email(user, reset_url, protocol='https', domain=None):
    """
    Send a password reset email using the enhanced template.

    Args:
        user: User object
        reset_url: Password reset URL
        protocol: 'http' or 'https'
        domain: Domain name

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    subject = "Reset Your Password - 24seven"

    if not domain:
        domain = get_site_url().replace('https://', '').replace('http://', '')

    context = {
        'user': user,
        'protocol': protocol,
        'domain': domain,
        'uid': 'placeholder_uid',  # This should be replaced with actual UID
        'token': 'placeholder_token',  # This should be replaced with actual token
    }

    return send_html_email(
        to_email=user.email,
        subject=subject,
        template_html='accounts/password_reset_email.html',
        context=context
    )


def send_enhanced_email(to_email, subject, email_type='info', **kwargs):
    """
    Send an enhanced email using the base template with custom content.

    Args:
        to_email (str): Recipient email address
        subject (str): Email subject
        email_type (str): Type of email ('info', 'success', 'warning', 'error')
        **kwargs: Additional template variables
            - heading: Email heading
            - subtitle: Email subtitle
            - icon: Email icon
            - content: Main content (HTML)
            - button_url: Action button URL
            - button_text: Action button text
            - info_boxes: List of info boxes
            - footer_text: Custom footer text

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    # Default values based on email type
    type_defaults = {
        'info': {'icon': 'ℹ️', 'heading': 'Information'},
        'success': {'icon': '✅', 'heading': 'Success'},
        'warning': {'icon': '⚠️', 'heading': 'Warning'},
        'error': {'icon': '❌', 'heading': 'Error'},
    }

    defaults = type_defaults.get(email_type, type_defaults['info'])

    # Merge defaults with provided kwargs
    for key, value in defaults.items():
        kwargs.setdefault(key, value)

    # Create HTML content using the base template structure
    html_content = f"""
    <p style="font-size: 18px; margin-bottom: 24px;">
        <strong>Hello,</strong>
    </p>

    {kwargs.get('content', '<p>This is a notification from 24seven.</p>')}

    {f'<div class="button-container"><a href="{kwargs.get("button_url")}" class="button">{kwargs.get("button_text", "Take Action")}</a></div>' if kwargs.get('button_url') else ''}
    """

    context = {
        'email_title': subject,
        'email_icon': kwargs.get('icon', '📧'),
        'email_heading': kwargs.get('heading', 'Notification'),
        'email_subtitle': kwargs.get('subtitle', 'Update from 24seven'),
        'email_content': html_content,
    }

    return send_html_email(
        to_email=to_email,
        subject=subject,
        template_html='accounts/email/base_email.html',
        context=context
    )
