/**
 * Button Text Targeting CSS
 * Provides specific classes and IDs for button text targeting
 * Ensures button text is always white while other text remains properly colored
 */

/* ===== BUTTON TEXT SPECIFIC CLASSES ===== */
.nup-btn-text,
.nup-button-text,
.btn-text,
.button-text {
    color: #ffffff !important;
    font-weight: 700 !important;
    text-shadow: none !important;
}

/* ===== BUTTON CONTAINER CLASSES ===== */
.nup-button,
.nup-btn,
.nup-button-container,
.nup-btn-container {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.nup-button *,
.nup-btn *,
.nup-button-container *,
.nup-btn-container * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== BUTTON VARIANT CLASSES ===== */
.nup-button-primary,
.nup-btn-primary,
.nup-button-secondary,
.nup-btn-secondary,
.nup-button-success,
.nup-btn-success,
.nup-button-danger,
.nup-btn-danger,
.nup-button-info,
.nup-btn-info,
.nup-button-dark,
.nup-btn-dark {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.nup-button-primary *,
.nup-btn-primary *,
.nup-button-secondary *,
.nup-btn-secondary *,
.nup-button-success *,
.nup-btn-success *,
.nup-button-danger *,
.nup-btn-danger *,
.nup-button-info *,
.nup-btn-info *,
.nup-button-dark *,
.nup-btn-dark * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== LIGHT BUTTON VARIANTS ===== */
.nup-button-light,
.nup-btn-light,
.nup-button-warning,
.nup-btn-warning {
    color: #333333 !important;
    font-weight: 700 !important;
}

.nup-button-light *,
.nup-btn-light *,
.nup-button-warning *,
.nup-btn-warning * {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* ===== LIGHT BUTTON HOVER STATES ===== */
.nup-button-light:hover,
.nup-btn-light:hover,
.nup-button-warning:hover,
.nup-btn-warning:hover {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.nup-button-light:hover *,
.nup-btn-light:hover *,
.nup-button-warning:hover *,
.nup-btn-warning:hover * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== BUTTON IDS ===== */
#nup-button,
#nup-btn,
#nup-button-primary,
#nup-btn-primary,
#nup-button-secondary,
#nup-btn-secondary,
#nup-button-success,
#nup-btn-success,
#nup-button-danger,
#nup-btn-danger,
#nup-button-info,
#nup-btn-info,
#nup-button-dark,
#nup-btn-dark {
    color: #ffffff !important;
    font-weight: 700 !important;
}

#nup-button *,
#nup-btn *,
#nup-button-primary *,
#nup-btn-primary *,
#nup-button-secondary *,
#nup-btn-secondary *,
#nup-button-success *,
#nup-btn-success *,
#nup-button-danger *,
#nup-btn-danger *,
#nup-button-info *,
#nup-btn-info *,
#nup-button-dark *,
#nup-btn-dark * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== LIGHT BUTTON IDS ===== */
#nup-button-light,
#nup-btn-light,
#nup-button-warning,
#nup-btn-warning {
    color: #333333 !important;
    font-weight: 700 !important;
}

#nup-button-light *,
#nup-btn-light *,
#nup-button-warning *,
#nup-btn-warning * {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* ===== LIGHT BUTTON ID HOVER STATES ===== */
#nup-button-light:hover,
#nup-btn-light:hover,
#nup-button-warning:hover,
#nup-btn-warning:hover {
    color: #ffffff !important;
    font-weight: 700 !important;
}

#nup-button-light:hover *,
#nup-btn-light:hover *,
#nup-button-warning:hover *,
#nup-btn-warning:hover * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== WILDCARD BUTTON CLASSES ===== */
[class*="nup-button"],
[class*="nup-btn"] {
    color: #ffffff !important;
    font-weight: 700 !important;
}

[class*="nup-button"] *,
[class*="nup-btn"] * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== WILDCARD LIGHT BUTTON CLASSES ===== */
[class*="nup-button-light"],
[class*="nup-btn-light"],
[class*="nup-button-warning"],
[class*="nup-btn-warning"] {
    color: #333333 !important;
    font-weight: 700 !important;
}

[class*="nup-button-light"] *,
[class*="nup-btn-light"] *,
[class*="nup-button-warning"] *,
[class*="nup-btn-warning"] * {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* ===== WILDCARD LIGHT BUTTON HOVER ===== */
[class*="nup-button-light"]:hover,
[class*="nup-btn-light"]:hover,
[class*="nup-button-warning"]:hover,
[class*="nup-btn-warning"]:hover {
    color: #ffffff !important;
    font-weight: 700 !important;
}

[class*="nup-button-light"]:hover *,
[class*="nup-btn-light"]:hover *,
[class*="nup-button-warning"]:hover *,
[class*="nup-btn-warning"]:hover * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== WILDCARD BUTTON IDS ===== */
[id*="nup-button"],
[id*="nup-btn"] {
    color: #ffffff !important;
    font-weight: 700 !important;
}

[id*="nup-button"] *,
[id*="nup-btn"] * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== WILDCARD LIGHT BUTTON IDS ===== */
[id*="nup-button-light"],
[id*="nup-btn-light"],
[id*="nup-button-warning"],
[id*="nup-btn-warning"] {
    color: #333333 !important;
    font-weight: 700 !important;
}

[id*="nup-button-light"] *,
[id*="nup-btn-light"] *,
[id*="nup-button-warning"] *,
[id*="nup-btn-warning"] * {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* ===== WILDCARD LIGHT BUTTON ID HOVER ===== */
[id*="nup-button-light"]:hover,
[id*="nup-btn-light"]:hover,
[id*="nup-button-warning"]:hover,
[id*="nup-btn-warning"]:hover {
    color: #ffffff !important;
    font-weight: 700 !important;
}

[id*="nup-button-light"]:hover *,
[id*="nup-btn-light"]:hover *,
[id*="nup-button-warning"]:hover *,
[id*="nup-btn-warning"]:hover * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== STANDARD BUTTON ELEMENTS WITH NUP CLASSES ===== */
.btn.nup-button,
.btn.nup-btn,
button.nup-button,
button.nup-btn,
input[type="button"].nup-button,
input[type="submit"].nup-button,
input[type="reset"].nup-button {
    color: #ffffff !important;
    font-weight: 700 !important;
}

.btn.nup-button *,
.btn.nup-btn *,
button.nup-button *,
button.nup-btn * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== BUTTON TEXT ELEMENTS ===== */
.btn .nup-btn-text,
.btn .nup-button-text,
button .nup-btn-text,
button .nup-button-text,
.nup-button .btn-text,
.nup-btn .btn-text {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== MAXIMUM SPECIFICITY OVERRIDES ===== */
html body .nup-button,
html body .nup-btn,
html body [class*="nup-button"],
html body [class*="nup-btn"],
html body [id*="nup-button"],
html body [id*="nup-btn"] {
    color: #ffffff !important;
    font-weight: 700 !important;
}

html body .nup-button *,
html body .nup-btn *,
html body [class*="nup-button"] *,
html body [class*="nup-btn"] *,
html body [id*="nup-button"] *,
html body [id*="nup-btn"] * {
    color: #ffffff !important;
    font-weight: 700 !important;
}

/* ===== LIGHT BUTTON MAXIMUM SPECIFICITY ===== */
html body .nup-button-light,
html body .nup-btn-light,
html body [class*="nup-button-light"],
html body [class*="nup-btn-light"],
html body [id*="nup-button-light"],
html body [id*="nup-btn-light"] {
    color: #333333 !important;
    font-weight: 700 !important;
}

html body .nup-button-light *,
html body .nup-btn-light *,
html body [class*="nup-button-light"] *,
html body [class*="nup-btn-light"] *,
html body [id*="nup-button-light"] *,
html body [id*="nup-btn-light"] * {
    color: #333333 !important;
    font-weight: 700 !important;
}

/* ===== LIGHT BUTTON HOVER MAXIMUM SPECIFICITY ===== */
html body .nup-button-light:hover,
html body .nup-btn-light:hover,
html body [class*="nup-button-light"]:hover,
html body [class*="nup-btn-light"]:hover,
html body [id*="nup-button-light"]:hover,
html body [id*="nup-btn-light"]:hover {
    color: #ffffff !important;
    font-weight: 700 !important;
}

html body .nup-button-light:hover *,
html body .nup-btn-light:hover *,
html body [class*="nup-button-light"]:hover *,
html body [class*="nup-btn-light"]:hover *,
html body [id*="nup-button-light"]:hover *,
html body [id*="nup-btn-light"]:hover * {
    color: #ffffff !important;
    font-weight: 700 !important;
}
