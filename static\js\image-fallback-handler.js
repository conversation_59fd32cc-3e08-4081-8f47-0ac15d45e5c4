/**
 * Image Fallback Handler
 * Provides robust image loading with fallbacks for company logos and other images
 */

class ImageFallbackHandler {
    constructor() {
        this.defaultImages = {
            company: '/static/img/default-company-logo.svg',
            assistant: '/static/img/default-assistant-logo.png',
            fallback: '/static/img/logos/company-1.svg'
        };

        this.init();
    }

    init() {
        console.log('[ImageFallback] Initializing image fallback handler');
        this.setupImageErrorHandlers();
        this.preloadDefaultImages();
    }

    /**
     * Preload default images to ensure they're available when needed
     */
    preloadDefaultImages() {
        Object.values(this.defaultImages).forEach(src => {
            const img = new Image();
            img.src = src;
            console.log('[ImageFallback] Preloading:', src);
        });
    }

    /**
     * Setup error handlers for existing images on the page
     */
    setupImageErrorHandlers() {
        // Handle company logos
        this.setupCompanyLogoHandlers();

        // Handle assistant logos
        this.setupAssistantLogoHandlers();

        // Setup observer for dynamically added images
        this.setupMutationObserver();
    }

    /**
     * Setup handlers for company logos
     */
    setupCompanyLogoHandlers() {
        const companyImages = document.querySelectorAll('img[src*="company_logos"], img[alt*="Logo"], .logo-container img, .company-logo-img, .featured-company-logo-img');
        console.log('[ImageFallback] Found', companyImages.length, 'company logo images');

        companyImages.forEach(img => {
            this.setupImageFallback(img, 'company');

            // Force check if image is already broken
            if (img.complete && img.naturalWidth === 0) {
                console.log('[ImageFallback] Image already broken, applying fallback:', img.src);
                this.handleImageError(img);
            }
        });
    }

    /**
     * Setup handlers for assistant logos
     */
    setupAssistantLogoHandlers() {
        const assistantImages = document.querySelectorAll('img[src*="assistant_logos"], img[alt*="Assistant"]');
        console.log('[ImageFallback] Found', assistantImages.length, 'assistant logo images');

        assistantImages.forEach(img => {
            this.setupImageFallback(img, 'assistant');
        });
    }

    /**
     * Setup fallback for a specific image
     */
    setupImageFallback(img, type = 'company') {
        if (img.dataset.fallbackSetup) {
            return; // Already setup
        }

        img.dataset.fallbackSetup = 'true';
        img.dataset.originalSrc = img.src;
        img.dataset.imageType = type;

        // Remove existing error handlers to avoid conflicts
        img.onerror = null;
        img.onload = null;

        // Setup new handlers
        img.addEventListener('error', (e) => this.handleImageError(e.target));
        img.addEventListener('load', (e) => this.handleImageLoad(e.target));

        // Check if image is already broken
        if (img.complete && img.naturalWidth === 0) {
            this.handleImageError(img);
        }
    }

    /**
     * Handle image load success
     */
    handleImageLoad(img) {
        console.log('[ImageFallback] Image loaded successfully:', img.src);
        img.style.display = '';

        // Remove any placeholder that might exist
        const placeholder = img.parentNode.querySelector('.logo-placeholder');
        if (placeholder) {
            placeholder.remove();
        }
    }

    /**
     * Handle image load error
     */
    handleImageError(img) {
        const originalSrc = img.dataset.originalSrc || img.src;
        const imageType = img.dataset.imageType || 'company';

        console.error('[ImageFallback] Image failed to load:', originalSrc);

        // Try fallback images in order
        if (!img.dataset.fallbackAttempt) {
            img.dataset.fallbackAttempt = '1';
            const fallbackSrc = this.defaultImages[imageType];
            console.log('[ImageFallback] Trying fallback:', fallbackSrc);
            img.src = fallbackSrc;
            return;
        }

        if (img.dataset.fallbackAttempt === '1') {
            img.dataset.fallbackAttempt = '2';
            const fallbackSrc = this.defaultImages.fallback;
            console.log('[ImageFallback] Trying secondary fallback:', fallbackSrc);
            img.src = fallbackSrc;
            return;
        }

        // All fallbacks failed, show placeholder
        console.warn('[ImageFallback] All fallbacks failed, showing placeholder');
        this.showPlaceholder(img);
    }

    /**
     * Show placeholder when all image fallbacks fail
     */
    showPlaceholder(img) {
        const container = img.parentNode;
        const companyName = this.extractCompanyName(img);

        // Hide the broken image
        img.style.display = 'none';

        // Check if placeholder already exists
        if (container.querySelector('.logo-placeholder')) {
            return;
        }

        // Create placeholder
        const placeholder = document.createElement('div');
        placeholder.className = 'logo-placeholder d-flex flex-column align-items-center justify-content-center h-100';
        placeholder.innerHTML = `
            <i class="bi bi-building-fill text-muted" style="font-size: 2.5rem;"></i>
            ${companyName ? `<span class="d-block mt-2 small text-muted text-center">${companyName}</span>` : ''}
        `;

        container.appendChild(placeholder);
    }

    /**
     * Extract company name from image alt text or nearby elements
     */
    extractCompanyName(img) {
        // Try alt text first
        if (img.alt && img.alt.includes('Logo')) {
            return img.alt.replace(/\s*Logo\s*/i, '').trim();
        }

        // Try to find company name in nearby elements
        const card = img.closest('.directory-card, .featured-carousel-item');
        if (card) {
            const nameElement = card.querySelector('h5, h6, .item-info h5');
            if (nameElement) {
                return nameElement.textContent.trim();
            }
        }

        return null;
    }

    /**
     * Setup mutation observer to handle dynamically added images
     */
    setupMutationObserver() {
        if (!document.body) {
            // If body doesn't exist yet, wait for it
            document.addEventListener('DOMContentLoaded', () => {
                this.setupMutationObserver();
            });
            return;
        }

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the added node is an image
                        if (node.tagName === 'IMG') {
                            this.setupImageFallback(node);
                        }

                        // Check for images within the added node
                        const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                        images.forEach(img => this.setupImageFallback(img));
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * Manually trigger fallback setup for specific containers
     */
    refreshContainer(container) {
        const images = container.querySelectorAll('img');
        images.forEach(img => {
            // Reset fallback setup
            delete img.dataset.fallbackSetup;
            delete img.dataset.fallbackAttempt;
            this.setupImageFallback(img);
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.imageFallbackHandler = new ImageFallbackHandler();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading
} else {
    // DOM is already loaded
    window.imageFallbackHandler = new ImageFallbackHandler();
}
