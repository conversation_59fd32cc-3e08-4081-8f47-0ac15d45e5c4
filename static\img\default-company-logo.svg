<?xml version="1.0" encoding="UTF-8"?>
<svg width="240" height="240" viewBox="0 0 240 240" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0d6efd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0b5ed7;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background -->
  <rect width="240" height="240" fill="url(#bgGradient)" rx="8" ry="8"/>

  <!-- Border -->
  <rect width="240" height="240" fill="none" stroke="#dee2e6" stroke-width="2" rx="8" ry="8"/>

  <!-- Building Icon -->
  <g transform="translate(120, 120)">
    <!-- Main building -->
    <rect x="-50" y="-60" width="100" height="120" fill="url(#iconGradient)" rx="6"/>

    <!-- Windows row 1 -->
    <rect x="-35" y="-45" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>
    <rect x="-7.5" y="-45" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>
    <rect x="20" y="-45" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>

    <!-- Windows row 2 -->
    <rect x="-35" y="-20" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>
    <rect x="-7.5" y="-20" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>
    <rect x="20" y="-20" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>

    <!-- Windows row 3 -->
    <rect x="-35" y="5" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>
    <rect x="-7.5" y="5" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>
    <rect x="20" y="5" width="15" height="15" fill="#ffffff" opacity="0.9" rx="2"/>

    <!-- Door -->
    <rect x="-12" y="30" width="24" height="30" fill="#ffffff" opacity="0.95" rx="3"/>
    <circle cx="8" cy="45" r="2" fill="#0d6efd"/>

    <!-- Roof -->
    <polygon points="-55,-65 0,-85 55,-65" fill="#dc3545" opacity="0.8"/>
  </g>

  <!-- Company text placeholder -->
  <text x="120" y="210" font-family="Arial, sans-serif" font-size="16" font-weight="600" text-anchor="middle" fill="#495057">Company</text>
</svg>
